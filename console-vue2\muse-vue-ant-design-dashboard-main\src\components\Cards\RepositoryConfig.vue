<template>
  <div>
    <a-card :bordered="false" class="header-solid repository-config-card">
    <template #title>
      <a-row type="flex" align="middle">
        <a-col :span="12">
          <h6 class="font-semibold m-0">{{ $t('repositoryConfig.title') }}</h6>
        </a-col>
        <a-col :span="12" class="text-right">
          <!-- 在表格上方添加分组按钮布局 -->
          <div class="button-groups">
            <div class="button-group">
              <a-button
                  class="nav-style-button action-button"
                  icon="plus"
                  @click="addNewRow">
                {{ $t('repositoryConfig.addRepository') }}
              </a-button>

              <a-button
                icon="export"
                class="nav-style-button action-button"
                :disabled="selectedRowKeys.length === 0"
                @click="exportSelectedRepositories"
              >
                {{ $t('repositoryConfig.exportSelected') }}
              </a-button>

              <a-button
                icon="download"
                class="nav-style-button action-button"
                :disabled="selectedRowKeys.length === 0"
                @click="downloadSelectedRepositories"
              >
                {{ $t('repositoryConfig.downloadSelected') }}
              </a-button>

              <a-button
                type="danger"
                icon="delete"
                class="nav-style-button action-button delete-button"
                :disabled="selectedRowKeys.length === 0"
                @click="deleteSelectedRepositories"
              >
                {{ $t('repositoryConfig.deleteSelected') }}
              </a-button>
            </div>

            <div class="button-group">
              <a-button
                type="default"
                icon="download"
                class="nav-style-button"
                @click="downloadTemplate"
              >
                {{ $t('repositoryConfig.downloadTemplate') }}
              </a-button>

              <a-upload
                :show-upload-list="false"
                :custom-request="handleUpload"
                accept=".csv"
              >
                <a-button
                    type="default"
                    icon="upload"
                    class="nav-style-button"
                >
                  {{ $t('repositoryConfig.uploadTemplate') }}
                </a-button>
              </a-upload>
            </div>
          </div>
        </a-col>
      </a-row>
    </template>

    <div class="config-table">
      <a-table
        :columns="columns"
        :data-source="repositories"
        :rowKey="(record) => record.key"
        :pagination="{
          current: currentPage,
          pageSize: pageSize,
          total: repositories.length,
          onChange: onPageChange,
        }"
        :loading="loading"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: record => ({
            disabled: record.editable || record.isNew
          })
        }"
      >
      <template
        v-for="col in editableColumns"
        :slot="col"
        slot-scope="text, record, index"
      >
        <div :key="col">
          <a-input
            v-if="record.editable"
            style="margin: -5px 0"
            :value="text"
            @change="e => handleChange(e.target.value, record.key, col)"
            :placeholder="`Enter ${getColumnTitle(col)}`"
          />
          <span v-else style="display: flex; align-items: center;">
            <a-icon 
              v-if="col === 'repository_url' && text"
              type="copy" 
              style="cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;"
              @click="copyText(text)"
              @mouseenter="$event.target.style.opacity = '1'"
              @mouseleave="$event.target.style.opacity = '0.6'"
            />
            <span 
              :style="col === 'repository_url' && text ? 'cursor: pointer' : ''"
              @click="col === 'repository_url' && text ? copyText(text) : null"
            >{{ text || '-' }}</span>            
          </span>
        </div>
      </template>

      <template #operation="text, record, index">
        <div class="editable-row-operations">
          <template v-if="record.editable">
            <a-button type="link" @click="() => save(record.key)">{{ $t('common.save') }}</a-button>
            <a-popconfirm
              title="Discard changes?"
              @confirm="() => cancel(record.key)"
            >
              <a-button type="link" danger>{{ $t('common.cancel') }}</a-button>
            </a-popconfirm>
          </template>
          <template v-else>
            <a-button type="link" @click="() => edit(record.key)">{{ $t('common.edit') }}</a-button>
            <a-button type="link" @click="() => copyRepository(record)">
              {{ $t('common.copy') }}
            </a-button>
            <a-popconfirm
              title="Confirm deletion?"
              @confirm="() => deleteRepository(record)"
            >
              <a-button type="link" danger>{{ $t('common.delete') }}</a-button>
            </a-popconfirm>
          </template>
        </div>
      </template>
      </a-table>
    </div>
  </a-card>

  <!-- 下载结果显示 -->
  <repository-download-results />
  </div>
</template>

<script>
// 使用 ant-design-vue 内置的图标
import { Icon } from 'ant-design-vue';
import axios from '@/api/axiosInstance';
import {mapState} from "vuex";
import RepositoryDownloadResults from './RepositoryDownloadResults.vue';
import CopyMixin from '@/mixins/CopyMixin';

let cacheData = [];

export default {
  components: {
    AIcon: Icon,
    RepositoryDownloadResults,
  },
  mixins: [CopyMixin],
  computed: {
  },
  data() {
    return {
      repositories: [],
      saving: false,
      loading: false,
      currentPage: 1,
      pageSize: 50,
      editableColumns: [
        'microservice_name',
        'repository_url',
        'branch_name',
      ],
      selectedRowKeys: [],
      currentDbFile: localStorage.getItem('currentProject'),
      downloadTaskId: null,
      downloadPolling: false,
      downloadPollInterval: null,
      columns: [
        {
          title: '#',
          dataIndex: 'index',
          width: 80,
          customRender: (text, record, index) => {
            return ((this.currentPage - 1) * this.pageSize) + index + 1;
          },
        },
        {
          title: this.$t('repositoryConfig.columns.microservice'),
          dataIndex: 'microservice_name',
          scopedSlots: { customRender: 'microservice_name' },
          width: 200,
        },
        {
          title: this.$t('repositoryConfig.columns.repositoryUrl'),
          dataIndex: 'repository_url',
          scopedSlots: { customRender: 'repository_url' },
          width: 300,
        },
        {
          title: this.$t('repositoryConfig.columns.branchName'),
          dataIndex: 'branch_name',
          scopedSlots: { customRender: 'branch_name' },
          width: 150,
        },
        {
          title: this.$t('common.actions'),
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          width: 150,
          align: 'center',
        },
      ],
    };
  },
  created() {
    if (!this.currentDbFile) {
      this.$message.warning('Please select a project first');
      this.$router.push('/projects');
      return;
    }
    this.fetchRepositoryConfig();
  },
  methods: {
    copyRepository(record) {
      const newKey = `new-${Date.now()}`;
      const newRecord = {
        ...record,
        key: newKey,
        editable: true,
        isNew: true,
        microservice_name: `${record.microservice_name}_copy`,
      };
      
      this.repositories = [newRecord, ...this.repositories];
      this.currentPage = 1;
      cacheData = this.repositories.map((item) => ({ ...item }));
      this.selectedRowKeys = [];
    },

    getColumnTitle(col) {
      const titleMap = {
        'microservice_name': this.$t('repositoryConfig.columns.microservice'),
        'repository_url': this.$t('repositoryConfig.columns.repositoryUrl'),
        'branch_name': this.$t('repositoryConfig.columns.branchName'),
      };
      return titleMap[col] || col;
    },

    handleChange(value, key, column) {
      const newData = [...this.repositories];
      const target = newData.find((item) => item.key === key);
      if (target) {
        target[column] = value;
        this.repositories = newData;
      }
    },

    edit(key) {
      const newData = [...this.repositories];
      const target = newData.find((item) => item.key === key);
      if (target) {
        target.editable = true;
        this.repositories = newData;
      }
    },

    async save(key) {
      const newData = [...this.repositories];
      const target = newData.find((item) => item.key === key);
      if (target) {
        delete target.editable;
        this.repositories = newData;
        cacheData = newData.map((item) => ({ ...item }));

        try {
          this.saving = true;
          const repositoryData = {
            microservice_name: target.microservice_name,
            repository_url: target.repository_url,
            branch_name: target.branch_name,
          };

          const response = await axios.post('/api/repositories', {
            repositories: [repositoryData]
          }, {
            params: { dbFile: this.currentDbFile }
          });

          if (response.data.success) {
            const { successful, failed } = response.data.data;

            if (failed.length > 0) {
              // 显示验证错误
              const errorMsg = failed[0].error;
              this.$message.error(`${this.$t('repositoryConfig.validation.parseError')}: ${errorMsg}`);
              return;
            }

            await this.fetchRepositoryConfig();
            this.$message.success('Repository saved successfully');
          } else {
            this.$message.error(response.data.error || 'Failed to save repository');
          }
        } catch (error) {
          this.$message.error(error.response?.data?.error || 'Failed to save repository');
          await this.fetchRepositoryConfig();
        } finally {
          this.saving = false;
        }
      }
    },

    cancel(key) {
      const targetIndex = this.repositories.findIndex((item) => item.key === key);
      if (targetIndex === -1) return;
      
      const target = this.repositories[targetIndex];
      
      if (target.isNew) {
        // For new rows, remove them completely
        this.repositories = this.repositories.filter(item => item.key !== key);
      } else {
        // For existing rows, revert changes
        const newData = [...this.repositories];
        const cachedItem = cacheData.find((item) => item.key === key);
        if (cachedItem) {
          Object.assign(target, { ...cachedItem });
          delete target.editable;
          this.repositories = newData;
        }
      }
    },

    addNewRow() {
      this.repositories = [
        {
          key: `new-${Date.now()}`,
          microservice_name: '',
          repository_url: '',
          branch_name: 'main',
          editable: true,
          isNew: true,
        },
        ...this.repositories,
      ];
      this.currentPage = 1;
      cacheData = this.repositories.map((item) => ({ ...item }));
      this.selectedRowKeys = [];
    },

    async fetchRepositoryConfig() {
      try {
        this.loading = true;
        const response = await axios.get(`/api/repositories`, {
          params: {
            detail: true,
            dbFile: this.currentDbFile
          }
        });
        this.repositories = response.data.data.map((item) => ({
          ...item,
          key: item.id?.toString() || `repo_${item.microservice_name}`,
          isNew: false,
        }));
        cacheData = this.repositories.map((item) => ({ ...item }));
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to load repositories');
      } finally {
        this.loading = false;
      }
    },

    onPageChange(page) {
      this.currentPage = page;
    },

    async deleteRepository(record) {
      try {
        if (record.id) {
          await axios.delete(`/api/repositories/${record.id}`, {
            params: { dbFile: this.currentDbFile }
          });

          this.repositories = this.repositories.filter((r) => r.key !== record.key);
          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);
          this.$message.success('Deleted successfully');
        } else {
          this.repositories = this.repositories.filter((r) => r.key !== record.key);
          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);
        }
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to delete repository');
        await this.fetchRepositoryConfig();
      }
    },

    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    async deleteSelectedRepositories() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('Please select repositories to delete');
        return;
      }

      try {
        const selectedIds = this.selectedRowKeys
          .map(key => this.repositories.find(r => r.key === key))
          .filter(repo => repo && repo.id)
          .map(repo => repo.id);

        if (selectedIds.length > 0) {
          await axios.post('/api/repositories/batch-delete', {
            ids: selectedIds
          }, {
            params: { dbFile: this.currentDbFile }
          });
        }

        this.repositories = this.repositories.filter(r => !this.selectedRowKeys.includes(r.key));
        this.selectedRowKeys = [];
        this.$message.success('Selected repositories deleted successfully');
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to delete repositories');
        await this.fetchRepositoryConfig();
      }
    },

    async exportSelectedRepositories() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('Please select repositories to export');
        return;
      }

      try {
        const selectedIds = this.selectedRowKeys
          .map(key => this.repositories.find(r => r.key === key))
          .filter(repo => repo && repo.id)
          .map(repo => repo.id);

        const response = await axios.post('/api/repositories/export', {
          ids: selectedIds
        }, {
          params: { dbFile: this.currentDbFile },
          responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'repositories_export.csv');
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        this.$message.success('Repositories exported successfully');
      } catch (error) {
        this.$message.error('Failed to export repositories');
      }
    },

    async downloadTemplate() {
      try {
        const response = await axios.get('/api/repositories/template', {
          responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'repository_template.csv');
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        this.$message.success('Template downloaded successfully');
      } catch (error) {
        this.$message.error('Failed to download template');
      }
    },

    async handleUpload(options) {
      const { file } = options;

      if (!file.name.endsWith('.csv')) {
        this.$message.error('Please upload CSV file');
        return;
      }

      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('dbFile', this.currentDbFile);

        const response = await axios.post('/api/repositories/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        if (response.data.success) {
          const { successful, failed } = response.data.data;

          if (failed.length > 0) {
            // 显示验证失败的代码仓
            const failedMessages = failed.map(repo =>
              `${repo.microservice_name || 'Unknown'}: ${repo.error}`
            ).join('\n');

            this.$confirm({
              title: this.$t('repositoryConfig.validation.parseError'),
              content: failedMessages,
              showCancelButton: false,
              confirmButtonText: 'OK',
              type: 'warning'
            });
          }

          if (successful.length > 0) {
            await this.fetchRepositoryConfig();
            this.$message.success(`Successfully imported ${successful.length} repositories`);
          }

          if (failed.length > 0 && successful.length === 0) {
            this.$message.error('No valid repositories found in the file');
          }
        } else {
          this.$message.error(response.data.error || 'Failed to import repositories');
        }
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to import repositories');
      }
    },

    async downloadSelectedRepositories() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('Please select repositories to download');
        return;
      }

      // 获取选中的代码仓
      const selectedRepositories = this.selectedRowKeys
        .map(key => this.repositories.find(r => r.key === key))
        .filter(repo => repo && repo.id);

      if (selectedRepositories.length === 0) {
        this.$message.warning('No valid repositories selected');
        return;
      }

      // 弹出输入框让用户输入下载路径
      const downloadPath = prompt(this.$t('repositoryConfig.download.selectPath'), 'D:\\downloads');

      if (!downloadPath || !downloadPath.trim()) {
        return;
      }

      // 显示开始下载的消息
      this.$message.loading(this.$t('repositoryConfig.download.starting'), 0);

      try {
        const requestData = {
          repositories: selectedRepositories.map(repo => ({
            id: repo.id,
            microservice_name: repo.microservice_name,
            repository_url: repo.repository_url,
            branch_name: repo.branch_name
          })),
          download_path: downloadPath.trim()
        };

        const response = await axios.post('/api/repositories/download', requestData, {
          params: { dbFile: this.currentDbFile }
        });

        this.$message.destroy();

        if (response.data.success) {
          const taskId = response.data.data.task_id;
          this.downloadTaskId = taskId;

          // 初始化下载结果状态 - 显示正在进行的状态
          const initialResults = {
            task_id: taskId,
            status: 'running',
            successful: [],
            failed: [],
            repositories: selectedRepositories.reduce((acc, repo) => {
              const key = `${repo.microservice_name}_${repo.repository_url}`;
              acc[key] = {
                microservice_name: repo.microservice_name,
                repository_url: repo.repository_url,
                branch_name: repo.branch_name,
                status: 'pending',
                progress: 0,
                error_detail: null,
                download_path: null
              };
              return acc;
            }, {}),
            timestamp: new Date().toLocaleString()
          };

          // 存储初始状态到store
          this.$store.dispatch('updateRepositoryDownloadResults', initialResults);

          this.$message.success('Repository download task started successfully');

          // 开始轮询任务状态
          this.startDownloadPolling(taskId);
        } else {
          this.$message.error(response.data.error || this.$t('repositoryConfig.download.failed'));
        }
      } catch (error) {
        this.$message.destroy();
        this.$message.error(error.response?.data?.error || error.message || this.$t('repositoryConfig.download.failed'));
      }
    },

    startDownloadPolling(taskId) {
      if (this.downloadPollInterval) {
        clearInterval(this.downloadPollInterval);
      }

      this.downloadPolling = true;

      // 立即执行一次查询
      this.pollDownloadStatus(taskId);

      // 每5秒轮询一次
      this.downloadPollInterval = setInterval(() => {
        this.pollDownloadStatus(taskId);
      }, 5000);

      console.log(`开始轮询代码仓下载任务 ${taskId}，轮询间隔: 5秒`);
    },

    async pollDownloadStatus(taskId) {
      try {
        const response = await axios.get(`/api/repositories/download/status/${taskId}`, {
          params: { dbFile: this.currentDbFile }
        });

        if (response.data.success) {
          const taskData = response.data.data;

          // 更新store中的下载结果
          this.$store.dispatch('updateRepositoryDownloadResults', {
            ...taskData,
            timestamp: new Date().toLocaleString()
          });

          // 检查任务是否完成
          if (taskData.status === 'success' || taskData.status === 'failed' || taskData.status === 'partial_success') {
            this.stopDownloadPolling();

            // 显示完成消息
            if (taskData.status === 'success') {
              this.$message.success(this.$t('repositoryConfig.download.success'));
            } else if (taskData.status === 'failed') {
              this.$message.error(this.$t('repositoryConfig.download.failed'));
            } else {
              this.$message.warning(this.$t('repositoryConfig.download.partialSuccess'));
            }

            console.log('代码仓下载任务完成，停止轮询');
          }
        } else {
          console.error('获取下载状态失败:', response.data.error);
        }
      } catch (error) {
        console.error('轮询下载状态出错:', error);
        
        // 如果是404错误，可能任务不存在，停止轮询
        if (error.response?.status === 404) {
          this.stopDownloadPolling();
          this.$message.error('Download task not found');
        }
      }
    },

    stopDownloadPolling() {
      if (this.downloadPollInterval) {
        clearInterval(this.downloadPollInterval);
        this.downloadPollInterval = null;
      }
      this.downloadPolling = false;
      this.downloadTaskId = null;
    },

    checkActiveDownloadTask() {
      // 检查是否有活跃的下载任务
      const taskInfo = localStorage.getItem(`repositoryDownloadTask_${this.currentDbFile}`);
      if (taskInfo) {
        try {
          const { taskId, projectFile } = JSON.parse(taskInfo);
          if (projectFile === this.currentDbFile) {
            this.downloadTaskId = taskId;
            this.startDownloadPolling(taskId);
          }
        } catch (e) {
          console.error('Error parsing repository download task info:', e);
          localStorage.removeItem(`repositoryDownloadTask_${this.currentDbFile}`);
        }
      }
    },

  },

  mounted() {
    this.fetchRepositoryConfig();
    // 检查是否有活跃的下载任务
    this.checkActiveDownloadTask();
  },

  beforeDestroy() {
    // 组件销毁前停止轮询
    this.stopDownloadPolling();
  },
};
</script>

<style scoped>
.repository-config-card {
  margin: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-groups {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
  flex-wrap: wrap;
  gap: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
  align-items: center;
}



::v-deep .ant-upload-select {
  display: inline-block;
}



/* 删除按钮保持红色 */
::v-deep .delete-button {
  color: #ff4d4f !important;
}

::v-deep .delete-button .anticon {
  color: #ff4d4f !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .button-groups {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
