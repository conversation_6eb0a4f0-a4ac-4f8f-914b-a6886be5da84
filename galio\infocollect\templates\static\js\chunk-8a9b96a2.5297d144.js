(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8a9b96a2"],{"352d":function(e,t,a){"use strict";a("9cca")},"4c4c":function(e,t,a){"use strict";a("71af")},"71af":function(e,t,a){},"9cca":function(e,t,a){},b854:function(e,t,a){"use strict";var s=function(){var e=this,t=e._self._c;return t("div")},i=[],n=(a("0643"),a("4e3e"),a("a026")),o=a("838b"),r=a.n(o),l={name:"JsonDetailModal",methods:{showDetailModal(e,t,a={}){const s=this.$createElement,i=Math.min(a.width||1200,.9*window.innerWidth),o=Math.min(700,.6*window.innerHeight),l={search:"search-"+Date.now(),counter:"counter-"+Date.now(),theme:"theme-"+Date.now()};let d=!0;const c=s("div",{style:"display: flex; justify-content: space-between; align-items: center;"},[s("div",{style:"display: flex; align-items: center;"},[s("a-icon",{attrs:{type:"code"},style:"margin-right: 8px; font-size: 16px;"}),s("span",{style:"font-weight: 500;"},[e])]),s("div",{style:"display: flex; align-items: center;"},[s("div",{attrs:{id:l.counter},style:"margin-right: 10px; min-width: 60px; text-align: right; color: #666;"}),s("a-input",{attrs:{id:l.search,placeholder:"搜索 (Enter: ↓  Shift+Enter: ↑)",allowClear:!0,prefix:s("a-icon",{attrs:{type:"search"},style:"color: rgba(0,0,0,.25)"})},style:"width: 250px;"}),s("a-button",{attrs:{id:l.theme,type:"link",icon:"bg-colors",title:"切换主题"},style:"margin-left: 8px; color: #1890ff; font-size: 16px;"}),s("a-button",{attrs:{id:"copy-btn",type:"link",icon:"copy",title:"复制内容"},style:"margin-left: 8px; color: #1890ff; font-size: 16px;"})])]),m="object"===typeof t?s("div",{style:`height: ${o}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`,class:"json-container theme-dark",attrs:{id:"json-container"}}):s("div",{style:`height: ${o}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`},[String(t)]);this.$root.$confirm({title:c,content:m,width:i,okText:a.okText||"关闭",icon:null,cancelButtonProps:{style:{display:"none"}},class:"detail-modal",maskClosable:!1,getContainer:()=>document.body.appendChild(document.createElement("div"))}),setTimeout(()=>{if("object"===typeof t){const e=document.getElementById("json-container");if(e){const a=new n["a"]({render:e=>e(r.a,{props:{data:t,deep:1/0,showDoubleQuotes:!0,showLength:!0,showLineNumbers:!0},style:{height:"100%",overflow:"auto"}})});a.$mount(),e.appendChild(a.$el)}}const e=document.getElementById(l.search),a=document.getElementById(l.counter);let s=[],i=-1;if(e&&a){const t=e=>{if(s=[],i=-1,a.textContent="",e)try{const t=document.querySelectorAll(".vjs-key, .vjs-value"),i=new RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");if(document.querySelectorAll(".vjs-search-match").forEach(e=>{e.classList.remove("vjs-search-match")}),document.querySelectorAll(".vjs-search-current").forEach(e=>{e.classList.remove("vjs-search-current")}),t.forEach(e=>{const t=e.textContent;let a;i.lastIndex=0;while(null!==(a=i.exec(t)))s.push({node:e,text:a[0]}),a.index===i.lastIndex&&i.lastIndex++}),0===s.length)return void(a.textContent="无匹配项");a.textContent="0/"+s.length,s.forEach(e=>{e.node.classList.add("vjs-search-match")})}catch(t){console.error("搜索错误:",t)}},n=e=>{if(0===s.length)return;e=Math.max(0,Math.min(s.length-1,e)),i>=0&&i<s.length&&s[i].node.classList.remove("vjs-search-current"),i=e,a.textContent=`${i+1}/${s.length}`;const t=s[i];if(t){t.node.classList.add("vjs-search-current");let e=t.node.parentElement;while(e){if(e.classList&&e.classList.contains("vjs-tree-node")&&!e.classList.contains("is-expanded")){const t=e.querySelector(".vjs-tree-brackets");t&&t.click()}e=e.parentElement}t.node.scrollIntoView({behavior:"smooth",block:"center"})}};let o;e.addEventListener("input",e=>{o&&clearTimeout(o),o=setTimeout(()=>{t(e.target.value.trim())},300)}),e.addEventListener("keydown",e=>{"Enter"===e.key&&(e.preventDefault(),n(e.shiftKey?i-1:i+1))})}const o=document.getElementById(l.theme);o&&o.addEventListener("click",()=>{const e=document.querySelector(".json-container");e&&(e.classList.contains("theme-light")?(e.classList.remove("theme-light"),e.classList.add("theme-dark"),e.style.backgroundColor="#1e1e1e",d=!0):(e.classList.remove("theme-dark"),e.classList.add("theme-light"),e.style.backgroundColor="#fff",d=!1))});const c=document.getElementById("copy-btn");c&&c.addEventListener("click",()=>{try{const e="object"===typeof t?JSON.stringify(t,null,2):String(t);if(navigator.clipboard&&window.isSecureContext)navigator.clipboard.writeText(e).then(()=>{this.$message.success(this.$t("common.copiedToClipboard"))}).catch(e=>{console.error("复制失败:",e),this.$message.error("复制失败")});else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select();const a=document.execCommand("copy");document.body.removeChild(t),a?this.$message.success(this.$t("common.copiedToClipboard")):this.$message.error(this.$t("common.copyFailed"))}}catch(e){this.$message.error(this.$t("common.copyFailed"))}})},300)}}},d=l,c=(a("4c4c"),a("2877")),m=Object(c["a"])(d,s,i,!1,null,null,null);t["a"]=m.exports},e2cd:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("KubernetesInfo")],1)],1)],1)},i=[],n=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full kubernetes-card",attrs:{bordered:!1,bodyStyle:{padding:0},headStyle:{borderBottom:"1px solid #e8e8e8"}},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 32 32"}},[t("path",{attrs:{fill:"currentColor",d:"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.k8s")))])]),t("div",[t("RefreshButton",{on:{refresh:e.fetchActiveTabData}})],1)])]},proxy:!0}])},[t("JsonDetailModal",{ref:"jsonDetailModal"}),t("a-tabs",{attrs:{"default-active-key":"k8s_api_server"},on:{change:e.handleTabChange}},[t("a-tab-pane",{key:"k8s_api_server",attrs:{tab:"API Servers"}},["k8s_api_server"===e.activeTab?t("a-table",{attrs:{columns:e.apiServerColumns,"data-source":e.apiServerData,rowKey:e=>e.address,pagination:e.pagination,loading:e.loadingApiServers}}):e._e()],1),t("a-tab-pane",{key:"k8s_ingress",attrs:{tab:"Ingresses"}},["k8s_ingress"===e.activeTab?t("a-table",{attrs:{columns:e.ingressColumns,"data-source":e.ingressData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingIngresses}}):e._e()],1),t("a-tab-pane",{key:"k8s_gateway",attrs:{tab:"Gateways"}},["k8s_gateway"===e.activeTab?t("a-table",{attrs:{columns:e.gatewayColumns,"data-source":e.gatewayData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingGateways}}):e._e()],1),t("a-tab-pane",{key:"k8s_virtual_service",attrs:{tab:"Virtual Services"}},["k8s_virtual_service"===e.activeTab?t("a-table",{attrs:{columns:e.virtualServiceColumns,"data-source":e.virtualServiceData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingVirtualServices}}):e._e()],1),t("a-tab-pane",{key:"k8s_service",attrs:{tab:"Services"}},["k8s_service"===e.activeTab?t("a-table",{attrs:{columns:e.serviceColumns,"data-source":e.serviceData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingServices}}):e._e()],1),t("a-tab-pane",{key:"k8s_network_policy",attrs:{tab:"Network Policies"}},["k8s_network_policy"===e.activeTab?t("a-table",{attrs:{columns:e.networkPolicyColumns,"data-source":e.networkPolicyData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingNetworkPolicies}}):e._e()],1),t("a-tab-pane",{key:"k8s_pod",attrs:{tab:"Pods"}},["k8s_pod"===e.activeTab?t("a-table",{attrs:{columns:e.podColumns,"data-source":e.podData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingPods,scroll:{x:1500}}}):e._e()],1),t("a-tab-pane",{key:"k8s_node",attrs:{tab:"Nodes"}},["k8s_node"===e.activeTab?t("a-table",{attrs:{columns:e.nodeColumns,"data-source":e.nodeData,rowKey:e=>e.name,pagination:e.pagination,loading:e.loadingNodes}}):e._e()],1),t("a-tab-pane",{key:"k8s_secret",attrs:{tab:"Secrets"}},["k8s_secret"===e.activeTab?t("a-table",{attrs:{columns:e.secretColumns,"data-source":e.secretData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingSecrets}}):e._e()],1),t("a-tab-pane",{key:"k8s_config_map",attrs:{tab:"ConfigMaps"}},["k8s_config_map"===e.activeTab?t("a-table",{attrs:{columns:e.configMapColumns,"data-source":e.configMapData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingConfigMaps}}):e._e()],1),t("a-tab-pane",{key:"k8s_role",attrs:{tab:"Roles"}},["k8s_role"===e.activeTab?t("a-table",{attrs:{columns:e.roleColumns,"data-source":e.roleData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingRole}}):e._e()],1),t("a-tab-pane",{key:"k8s_role_binding",attrs:{tab:"Role Bindings"}},["k8s_role_binding"===e.activeTab?t("a-table",{attrs:{columns:e.roleBindingColumns,"data-source":e.roleBindingData,rowKey:e=>`${e.namespace}-${e.name}`,pagination:e.pagination,loading:e.loadingRoleBinding}}):e._e()],1),t("a-tab-pane",{key:"k8s_cluster_role",attrs:{tab:"Cluster Roles"}},["k8s_cluster_role"===e.activeTab?t("a-table",{attrs:{columns:e.clusterRoleColumns,"data-source":e.clusterRoleData,rowKey:e=>e.name,pagination:e.pagination,loading:e.loadingClusterRole}}):e._e()],1),t("a-tab-pane",{key:"k8s_cluster_role_binding",attrs:{tab:"Cluster Role Bindings"}},["k8s_cluster_role_binding"===e.activeTab?t("a-table",{attrs:{columns:e.clusterRoleBindingColumns,"data-source":e.clusterRoleBindingData,rowKey:e=>e.name,pagination:e.pagination,loading:e.loadingClusterRoleBinding}}):e._e()],1),t("a-tab-pane",{key:"k8s_serviceaccount_permissions",attrs:{tab:"ServiceAccount Perms"}},["k8s_serviceaccount_permissions"===e.activeTab?t("a-table",{attrs:{columns:e.serviceAccountPermissionsColumns,"data-source":e.serviceAccountPermissionsData,rowKey:e=>`${e.namespace}-${e.pod_name}-${e.service_account}`,pagination:e.pagination,loading:e.loadingServiceAccountPermissions,scroll:{x:1200}}}):e._e()],1)],1)],1)},o=[],r=(a("0643"),a("a573"),a("2f62")),l=a("fec3"),d=a("f188"),c=a("b854"),m={components:{RefreshButton:d["a"],JsonDetailModal:c["a"]},name:"KubernetesInfo",data(){const e=this.$createElement;return{activeTab:"k8s_api_server",apiServerData:[],ingressData:[],gatewayData:[],virtualServiceData:[],serviceData:[],networkPolicyData:[],podData:[],nodeData:[],secretData:[],configMapData:[],roleData:[],roleBindingData:[],clusterRoleData:[],clusterRoleBindingData:[],serviceAccountPermissionsData:[],loadingApiServers:!1,loadingIngresses:!1,loadingGateways:!1,loadingVirtualServices:!1,loadingServices:!1,loadingNetworkPolicies:!1,loadingPods:!1,loadingNodes:!1,loadingSecrets:!1,loadingConfigMaps:!1,loadingRole:!1,loadingRoleBinding:!1,loadingClusterRole:!1,loadingClusterRoleBinding:!1,loadingServiceAccountPermissions:!1,apiServerColumns:[{title:"Node ID",dataIndex:"node_id",key:"node_id"},{title:"Address",dataIndex:"address",key:"address"}],ingressColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Spec")},{title:"Status",dataIndex:"ingress_status",key:"ingress_status",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Ingress Status")}],gatewayColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:300,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Gateway Spec")},{title:"Status",dataIndex:"gateway_status",key:"gateway_status",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Gateway Status")}],virtualServiceColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:300,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Virtual Service Spec")},{title:"Status",dataIndex:"virtual_service_status",key:"virtual_service_status",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Virtual Service Status")}],serviceColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Spec")},{title:"Status",dataIndex:"service_status",key:"service_status",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Service Status")}],networkPolicyColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Spec")}],podColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:200,customRender:e=>this.renderComplexData(e,"Spec")},{title:"Status",dataIndex:"pod_status",key:"pod_status",width:200,customRender:e=>this.renderComplexData(e,"Pod Status")}],nodeColumns:[{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Spec",dataIndex:"spec",key:"spec",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Spec")},{title:"Status",dataIndex:"pod_status",key:"pod_status",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Node Status")}],secretColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Data",dataIndex:"data",key:"data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Data")},{title:"Type",dataIndex:"secret_type",key:"secret_type",width:120}],configMapColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Data",dataIndex:"data",key:"data",width:300,ellipsis:!0,customRender:e=>this.renderComplexData(e,"ConfigMap Data")}],roleColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Rules",dataIndex:"rules",key:"rules",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"RBAC Rules")}],roleBindingColumns:[{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Role Ref",dataIndex:"roleRef",key:"roleRef",width:250,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Role Reference")},{title:"Subjects",dataIndex:"subjects",key:"subjects",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"RBAC Subjects")}],clusterRoleColumns:[{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Aggregation Rule",dataIndex:"aggregationRule",key:"aggregationRule",width:250,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Aggregation Rule")},{title:"Rules",dataIndex:"rules",key:"rules",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"RBAC Rules")}],clusterRoleBindingColumns:[{title:"Name",dataIndex:"name",key:"name",width:150},{title:"Metadata",dataIndex:"meta_data",key:"meta_data",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Metadata")},{title:"Role Ref",dataIndex:"roleRef",key:"roleRef",width:250,ellipsis:!0,customRender:e=>this.renderComplexData(e,"Role Reference")},{title:"Subjects",dataIndex:"subjects",key:"subjects",width:200,ellipsis:!0,customRender:e=>this.renderComplexData(e,"RBAC Subjects")}],serviceAccountPermissionsColumns:[{title:"Pod Name",dataIndex:"pod_name",key:"pod_name",width:150},{title:"Namespace",dataIndex:"namespace",key:"namespace",width:120},{title:"Service Account",dataIndex:"service_account",key:"service_account",width:150},{title:"Permissions",dataIndex:"permissions",key:"permissions",width:300,ellipsis:!0,customRender:t=>{try{const a=Array.isArray(t)?t:t?JSON.parse(t):[];let s="No permissions";if(a&&a.length>0){const e=a.map(e=>`${e.type}: ${e.name}`).join(", ");s=e.length>50?e.slice(0,50)+"...":e}return e("div",{style:"display: flex; align-items: center; justify-content: space-between;"},[e("span",{style:"flex: 1; overflow: hidden; text-overflow: ellipsis;"},[s]),e("a-button",{attrs:{type:"link"},style:"flex-shrink: 0; padding: 0 8px; min-width: 50px;",on:{click:()=>this.showDetailModal("ServiceAccount Permissions",a)}},["View"])])}catch(a){return console.error("Error parsing permissions:",a),"Error parsing data"}}}],pagination:{pageSize:100},initialLoad:!0}},computed:{...Object(r["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(e){this.resetData(),this.initialLoad=!0,e&&this.fetchActiveTabData()}},mounted(){this.selectedNodeIp&&this.fetchActiveTabData()},methods:{handleTabChange(e){this.activeTab=e,this.fetchActiveTabData()},async fetchActiveTabData(){if(!this.selectedNodeIp)return console.error("Node IP is not defined"),void this.resetData();const e=this.activeTab;this["loading"+this.capitalizeFirstLetter(e)]=!0;try{let t;t=await l["a"].get(`/api/k8s/${e}/${this.selectedNodeIp}`,{params:{dbFile:this.currentProject}});const a=t.data;this[""+this.camelCaseToDataName(e)]=a}catch(t){console.error(`Error fetching ${e}:`,t),this[""+this.camelCaseToDataName(e)]=[]}finally{this["loading"+this.capitalizeFirstLetter(e)]=!1}},camelCaseToDataName(e){const t=e.replace("k8s_","");if("serviceaccount_permissions"===t)return"serviceAccountPermissionsData";const a=t.replace(/_([a-z])/g,(e,t)=>t.toUpperCase());return a+"Data"},capitalizeFirstLetter(e){return e.charAt(0).toUpperCase()+e.slice(1)},resetData(){this.apiServerData=[],this.ingressData=[],this.gatewayData=[],this.virtualServiceData=[],this.serviceData=[],this.networkPolicyData=[],this.podData=[],this.nodeData=[],this.secretData=[],this.configMapData=[],this.roleData=[],this.roleBindingData=[],this.clusterRoleData=[],this.clusterRoleBindingData=[],this.serviceAccountPermissionsData=[]},renderComplexData(e,t){const a=this.$createElement;if(!e||"None"===e)return"-";const s=Array.isArray(e)?`Array(${e.length})`:"object"===typeof e&&null!==e?Object.entries(e).slice(0,2).map(([e,t])=>`${e}: ${"object"===typeof t?JSON.stringify(t).substring(0,15):t}`).join(", ")+(Object.keys(e).length>2?"...":""):String(e).length>50?String(e).slice(0,50)+"...":String(e);return a("div",{style:"display: flex; align-items: center; justify-content: space-between; padding-right: 8px;"},[a("span",{style:"flex: 1; overflow: hidden; text-overflow: ellipsis;"},[s]),a("a-button",{attrs:{type:"link"},style:"flex-shrink: 0; padding: 0 8px; min-width: 50px;",on:{click:()=>this.showDetailModal(t,e)}},["View"])])},showDetailModal(e,t){this.$refs.jsonDetailModal.showDetailModal(e,t)}}},p=m,u=(a("352d"),a("2877")),h=Object(u["a"])(p,n,o,!1,null,"656af809",null),g=h.exports,y={components:{KubernetesInfo:g}},x=y,w=Object(u["a"])(x,s,i,!1,null,null,null);t["default"]=w.exports},f188:function(e,t,a){"use strict";var s=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},i=[],n=a("2f62"),o={computed:{...Object(n["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},r=o,l=a("2877"),d=Object(l["a"])(r,s,i,!1,null,"80cb1374",null);t["a"]=d.exports}}]);
//# sourceMappingURL=chunk-8a9b96a2.5297d144.js.map