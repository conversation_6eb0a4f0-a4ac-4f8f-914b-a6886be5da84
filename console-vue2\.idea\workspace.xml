<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c4764e69-8a17-4f35-89bf-992033578a8a" name="Changes" comment="导航信息变更/修复大量console因语法或异常导致的无用error信息/">
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/FilesystemInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/FilesystemInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HardwareInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HardwareInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HostConfig.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HostConfig.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/PackageInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/PackageInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/PortInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/PortInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/ProcessInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/ProcessInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/RepositoryConfig.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/RepositoryConfig.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/RepositoryDownloadResults.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/RepositoryDownloadResults.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/SmartOrchestrationInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/SmartOrchestrationInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/TestCaseInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/TestCaseInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSettingsDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSettingsDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSidebar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSidebar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/common/DifyChatBot.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/common/DifyChatBot.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/i18n/locales/en-US.js" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/i18n/locales/en-US.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/i18n/locales/zh-CN.js" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/i18n/locales/zh-CN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/scss/base/_dark-theme.scss" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/scss/base/_dark-theme.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/scss/components/_table.scss" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/scss/components/_table.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/views/ProjectManager.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/views/ProjectManager.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2z5IXw2cdzZMQIyfLf76bN1dirg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/_Projects_python/aimodule",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\Program Files\\JetBrains\\PyCharm 2022.1\\plugins\\JavaScriptLanguage\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c4764e69-8a17-4f35-89bf-992033578a8a" name="Changes" comment="" />
      <created>1751014550491</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751014550491</updated>
      <workItem from="1751014552976" duration="21393000" />
      <workItem from="1751619588116" duration="15555000" />
      <workItem from="1752572033811" duration="73000" />
      <workItem from="1752755170363" duration="28256000" />
      <workItem from="1753146660004" duration="11182000" />
    </task>
    <task id="LOCAL-00001" summary="fix">
      <created>1751248871666</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751248871666</updated>
    </task>
    <task id="LOCAL-00002" summary="状态更新，增加用例分析模块">
      <created>1752030588453</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752030588453</updated>
    </task>
    <task id="LOCAL-00003" summary="导航信息变更/修复大量console因语法或异常导致的无用error信息/">
      <created>1753087557610</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753087557610</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix" />
    <MESSAGE value="状态更新，增加用例分析模块" />
    <MESSAGE value="导航信息变更/修复大量console因语法或异常导致的无用error信息/" />
    <option name="LAST_COMMIT_MESSAGE" value="导航信息变更/修复大量console因语法或异常导致的无用error信息/" />
  </component>
</project>