import pandas as pd
import numpy as np
from typing import Dict, Optional, List
from sqlalchemy import or_

from infocollect.datamodel.testcase_datamodel import TestCase
from infocollect.log.logger import log_debug, log_info, log_error, log_warning
from infocollect.db.init_db import get_db


NAME_MAPPING = {
    "Testcase_Name": "用例名称",
    "Depth": "层级关系",
    "Feature_Name": "特性名称",
    "Feature_Number": "特性编号",
    "isFeature": "是否为特性",
    "Testcase_Number": "用例编号",
    "Testcase_Stage": "测试阶段",
    "Testcase_AutoType": "自动化类型",
    "Testcase_TobeAutomated": "可自动化用例",
    "Testcase_Level": "等级",
    "Testcase_PrepareCondition": "前提条件",
    "Testcase_TestSteps": "测试步骤",
    "Testcase_ExpectedResult": "预期结果",
    "Testcase_ScriptPath": "脚本路径",
    "Testcase_TestType": "测试类型",
    "Testcase_EnvType": "测试环境类型",
    "Testcase_Exeplatform": "执行平台",
    "Testcase_MapRestrict": "约束条件",
    "Testcase_TestcaseProject": "测试工程",
    "Testcase_Tags": "用例标签",
    "Testcase_Activity": "自动化测试平台",
    "Testcase_Remark": "备注",
    "lastChangeTime": "上次修改时间",
    "lastModified": "修改时间",
    "executeLatestTime": "最后执行时间",
    "Security_Test_Requirements": "安全测试需求",
    "Security_Test_Rules": "安全测试规则",
    "lastModifier": "上次修改人员",
    "creationDate": "创建时间",
    "keywords": "关键字",
    "drRelationID": "需求/缺陷关联关系",
    "market": "适用市场",
    "testBaseNum": "测试基数",
    "Testcase_NetworkScriptName": "网络脚本名",
    "Testcase_Description": "用例描述",
    "Testcase_NetworkProblemId": "网络问题ID",
    "interfaceName": "接口名",
    "Testcase_Uri": "用例uri（存在系统内，用户不可见）",
    "Testcase_LastResult": "最新结果",
    "detectType": "检测类型",
    "AnalyseField": "分析领域",
    "Reason_Of_Fail": "失败原因",
    "executeParam": "执行参数",
    "author": "作者",
    "timecost": "用例上次执行消耗时间",
    "designer": "用例设计者",
    "Last_Executor": "最后执行人",
    "SceneFlag": "场景标识",
    "BaseFlag": "门槛标识",
    "CloudCarrier": "运营商",
    "TestFactorNumber": "测试因子编号",
    "Testcase_TestActivity": "测试活动",
    "customField1": "用户自定义字段1",
    "customField2": "用户自定义字段2",
    "customField3": "用户自定义字段3",
    "customField4": "用户自定义字段4",
    "customField5": "用户自定义字段5",
    "customField6": "用户自定义字段6",
    "customField7": "用户自定义字段7",
    "customField8": "用户自定义字段8",
    "testPatternNumber": "模式编号",
    "HTSM": "HTSM",
    "testCaseRelationID": "需求/缺陷关联关系(老旧)",
    "is_directory": "是否为目录",
    "Testcase_Feature": "用例特性"
}

REVERSE_NAME_MAPPING = {v: k for k, v in NAME_MAPPING.items()}


class TestcaseService:
    def __init__(self):
        pass

    @staticmethod
    def get_all_testcases(page: int = 1, page_size: int = 100, search_params: Dict = None) -> Dict:
        try:
            with get_db() as db:
                query = db.query(TestCase).filter(TestCase.is_directory == False)

                # 构建查询条件
                if search_params:
                    if search_params.get('name'):
                        query = query.filter(TestCase.Testcase_Name.like(f"%{search_params['name']}%"))

                    if search_params.get('level'):
                        query = query.filter(TestCase.Testcase_Level == search_params['level'])

                    if search_params.get('prepare_condition'):
                        query = query.filter(TestCase.Testcase_PrepareCondition.like(f"%{search_params['prepare_condition']}%"))

                    if search_params.get('test_steps'):
                        query = query.filter(TestCase.Testcase_TestSteps.like(f"%{search_params['test_steps']}%"))

                    if search_params.get('expected_result'):
                        query = query.filter(TestCase.Testcase_ExpectedResult.like(f"%{search_params['expected_result']}%"))

                    if search_params.get('keyword'):
                        keyword = search_params['keyword']
                        query = query.filter(
                            or_(
                                TestCase.Testcase_Name.like(f"%{keyword}%"),
                                TestCase.Testcase_TestSteps.like(f"%{keyword}%"),
                                TestCase.Testcase_ExpectedResult.like(f"%{keyword}%"),
                                TestCase.Testcase_PrepareCondition.like(f"%{keyword}%"),
                                TestCase.Testcase_Tags.like(f"%{keyword}%"),
                                TestCase.customField1.like(f"%{keyword}%"),
                                TestCase.customField2.like(f"%{keyword}%"),
                                TestCase.customField3.like(f"%{keyword}%")
                            )
                        )

                total = query.count()

                # 获取分页数据
                query = query.order_by(TestCase.Testcase_Number)
                query = query.offset((page - 1) * page_size).limit(page_size)

                testcases = query.all()

                testcase_dicts = []
                for testcase in testcases:
                    testcase_dict = {
                        'id': testcase.id,
                        'Testcase_Number': testcase.Testcase_Number,
                        'Testcase_Name': testcase.Testcase_Name,
                        'Testcase_Level': testcase.Testcase_Level,
                        'Testcase_PrepareCondition': testcase.Testcase_PrepareCondition,
                        'Testcase_TestSteps': testcase.Testcase_TestSteps,
                        'Testcase_ExpectedResult': testcase.Testcase_ExpectedResult
                    }
                    testcase_dicts.append(testcase_dict)

                return {
                    'total': total,
                    'data': testcase_dicts
                }
        except Exception as e:
            log_error(f"Error fetching testcases: {e}")
            return {
                'total': 0,
                'data': []
            }

    @staticmethod
    def get_testcase_by_number(testcase_number: str) -> Optional[Dict]:
        try:
            with get_db() as db:
                testcase = db.query(TestCase).filter(
                    TestCase.Testcase_Number == testcase_number,
                    TestCase.is_directory == False
                ).first()

                if not testcase:
                    return None

                testcase_dict = testcase.to_dict()
                return testcase_dict
        except Exception as e:
            log_error(f"Error fetching testcase: {e}")
            return None

    @staticmethod
    def get_testcase_by_id(testcase_id: int) -> Optional[Dict]:
        """根据主键ID获取测试用例"""
        try:
            with get_db() as db:
                testcase = db.query(TestCase).filter(
                    TestCase.id == testcase_id,
                    TestCase.is_directory == False
                ).first()

                if not testcase:
                    return None

                testcase_dict = testcase.to_dict()
                return testcase_dict
        except Exception as e:
            log_error(f"Error fetching testcase by id {testcase_id}: {e}")
            return None

    @staticmethod
    def create_testcase(testcase_data: Dict) -> Optional[Dict]:
        try:
            with get_db() as db:
                testcase = TestCase(**testcase_data)
                db.add(testcase)
                db.commit()
                db.refresh(testcase)
                return testcase.to_dict()
        except Exception as e:
            log_error(f"Error creating testcase: {e}")
            return None

    @staticmethod
    def update_testcase(testcase_number: str, update_data: Dict) -> Optional[Dict]:
        try:
            with get_db() as db:
                testcase = db.query(TestCase).filter(TestCase.Testcase_Number == testcase_number).first()
                if not testcase:
                    return None

                for key, value in update_data.items():
                    if hasattr(testcase, key):
                        setattr(testcase, key, value)
                db.commit()
                db.refresh(testcase)
                return testcase.to_dict()
        except Exception as e:
            log_error(f"Error updating testcase {testcase_number}: {e}")
            return None

    @staticmethod
    def import_testcases_from_excel(file_path: str) -> int:
        try:
            # 读取Excel文件（双行表头，第一行为分类，第二行为中文列名）
            df = pd.read_excel(file_path, header=[0, 1])
            df.columns = df.columns.get_level_values(1)

            # 将中文列名转换为英文列名
            rename_dict = {}
            for col in df.columns:
                if col in REVERSE_NAME_MAPPING:
                    rename_dict[col] = REVERSE_NAME_MAPPING[col]

            if rename_dict:
                df = df.rename(columns=rename_dict)
            else:
                log_warning("No Chinese column names found for conversion")

            required_columns = ['Depth', 'Feature_Name', 'Testcase_Number', 'Testcase_Name']
            for col in required_columns:
                if col not in df.columns:
                    log_warning(f"Missing required column in Excel: {col}. Creating an empty column.")
                    df[col] = None

            # 标记目录行
            df['is_directory'] = df['Feature_Name'].notna()

            # 初始化一个空列表来存储层次路径
            hierarchical_paths = []
            current_path = []

            # 遍历每一行来构建层次路径
            for index, row in df.iterrows():
                try:
                    if pd.isna(row['Depth']):
                        depth = 0
                    else:
                        depth = str(row['Depth']).count('.')

                    if row['is_directory']:
                        if depth > 1:
                            current_path = current_path[:depth - 1]  # 调整current_path长度
                        current_path.append(str(row['Feature_Name']))  # 作为字符串附加

                    hierarchical_paths.append('/'.join(current_path))  # 与根路径连接
                except Exception as e:
                    log_warning(f"Error processing row {index + 1}: {e}. Using empty path.")
                    hierarchical_paths.append('')

            df['Depth'] = hierarchical_paths

            df = df.replace({np.nan: None})

            # 导入到数据库
            success_count = 0
            error_count = 0
            skipped_count = 0

            with get_db() as db:
                for index, row in df.iterrows():
                    try:
                        if row['is_directory']:
                            continue

                        if not row['Testcase_Number']:
                            log_warning(f"Row {index + 1} is missing Testcase_Number. Skipping.")
                            continue

                        # 创建测试用例对象
                        testcase_data = {}
                        for col in row.index:
                            if col in TestCase.__table__.columns.keys():
                                testcase_data[col] = row[col]

                        existing = db.query(TestCase).filter(TestCase.Testcase_Number == row['Testcase_Number']).first()
                        if existing:
                            skipped_count += 1
                            continue

                        testcase = TestCase(**testcase_data)
                        db.add(testcase)
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        log_error(f"Error importing row {index + 1}: {e}")
                        continue

                db.commit()

            log_debug(
                f"Successfully imported {success_count} test cases, {error_count} failed, "
                f"{skipped_count} skipped (already exist).")

            return success_count
        except Exception as e:
            log_error(f"Error importing test cases: {e}")
            return 0
