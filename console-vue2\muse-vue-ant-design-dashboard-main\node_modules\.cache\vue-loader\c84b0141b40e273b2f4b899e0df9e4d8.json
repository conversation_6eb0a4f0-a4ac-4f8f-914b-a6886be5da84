{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue", "mtime": 1753169785200}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProjectManager.vue"], "names": [], "mappings": ";AAoBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectManager.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card title=\"项目列表\">\r\n      <template #extra>\r\n        <a-button type=\"primary\" @click=\"createNewProject\">\r\n          创建新项目\r\n        </a-button>\r\n      </template>\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"projects\"\r\n        :row-key=\"record => record.dbFile\"\r\n        :customRow=\"onCustomRow\"\r\n        class=\"project-table\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProjectManager',\r\n  data() {\r\n    return {\r\n      columns: [\r\n        {\r\n          title: '项目名称',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '250px',\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            return (\r\n            <span>\r\n            <a-icon type=\"folder\" style=\"margin-right: 8px;\" />\r\n            {text}\r\n            </span>\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: '数据库文件',\r\n          dataIndex: 'dbFile',\r\n          key: 'dbFile',\r\n          width: '280px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          dataIndex: 'createdAt',\r\n          key: 'createdAt',\r\n          width: '160px',\r\n          customRender: (text) => {\r\n            return new Date(text).toLocaleString('zh-CN', {\r\n              year: 'numeric',\r\n              month: '2-digit',\r\n              day: '2-digit',\r\n              hour: '2-digit',\r\n              minute: '2-digit'\r\n            });\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: '180px',\r\n          align: 'center',\r\n          fixed: 'right',\r\n          customRender: (text, record) => {\r\n            return (\r\n              <a-space size={8}>\r\n                <a-button\r\n                  type=\"primary\"\r\n                  onClick={() => this.selectProject(record)}\r\n                >\r\n                  进入项目\r\n                </a-button>\r\n                <a-popconfirm\r\n                  title=\"确定要删除这个项目吗？\"\r\n                  onConfirm={() => this.deleteProject(record)}\r\n                  okText=\"确定\"\r\n                  cancelText=\"取消\"\r\n                >\r\n                  <a-button type=\"danger\">删除</a-button>\r\n                </a-popconfirm>\r\n              </a-space>\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      projects: [],\r\n      tempProjectName: ''\r\n    };\r\n  },\r\n  methods: {\r\n    onCustomRow(record) {\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            // 如果需要行点击事件的话，在这里处理\r\n          }\r\n        }\r\n      };\r\n    },\r\n    async fetchProjects() {\r\n      try {\r\n        const response = await axios.get('/api/projects');\r\n        if (Array.isArray(response.data)) {\r\n          this.projects = response.data.map(project => ({\r\n            name: project.name || '',\r\n            dbFile: project.dbFile || '',\r\n            createdAt: project.createdAt || '',\r\n            key: project.dbFile || Date.now().toString()\r\n          }));\r\n        } else {\r\n          this.projects = [];\r\n          console.error('项目数据格式无效：', response.data);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败：', error);\r\n        this.$message.error('获取项目列表失败');\r\n        this.projects = [];\r\n      }\r\n    },\r\n\r\n    async selectProject(project) {\r\n      if (!project?.dbFile) {\r\n        console.error('项目数据无效：', project);\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const encodedDbFile = encodeURIComponent(project.dbFile);\r\n        const validationUrl = `/api/projects/validate/${encodedDbFile}`;\r\n\r\n        const response = await axios.get(validationUrl);\r\n        if (response.data.valid) {\r\n          await this.$store.dispatch('switchProject', { \r\n            dbFile: project.dbFile, \r\n            projectName: project.name \r\n          });\r\n          await this.$store.dispatch('fetchNodes');\r\n\r\n          // 清除所有任务相关的localStorage\r\n          localStorage.removeItem('activeTaskId');\r\n          localStorage.removeItem('taskCompleted');\r\n          localStorage.removeItem('activeUploadTaskId');\r\n          localStorage.removeItem('activeDownloadTaskId');\r\n          localStorage.removeItem('activeToolTaskId');\r\n\r\n          await this.$router.push('/task');\r\n          this.$message.success('成功进入项目');\r\n        } else {\r\n          console.error('验证失败：', response.data.error);\r\n          this.$message.error(response.data.error || '数据库文件无效或已损坏');\r\n        }\r\n      } catch (error) {\r\n        console.error('项目验证出错：', error);\r\n        this.$message.error(error.response?.data?.error || '验证项目失败');\r\n      }\r\n    },\r\n\r\n    async deleteProject(project) {\r\n      if (!project?.dbFile) {\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await axios.delete(`/api/projects/${encodeURIComponent(project.dbFile)}`);\r\n        this.$message.success('项目删除成功');\r\n        await this.fetchProjects();\r\n      } catch (error) {\r\n        console.error('Error deleting project:', error);\r\n        this.$message.error('删除项目失败');\r\n      }\r\n    },\r\n\r\n    async createNewProject() {\r\n      try {\r\n        const projectName = await new Promise((resolve, reject) => {\r\n          this.$confirm({\r\n            title: '创建新项目',\r\n            content: h => (\r\n              <div>\r\n                <a-input\r\n                  placeholder=\"请输入项目名称\"\r\n                  onChange={(e) => {\r\n                    const value = e.target.value.replace(/[^a-zA-Z0-9_-]/g, '');\r\n                    this.tempProjectName = value;\r\n                    e.target.value = value;\r\n                  }}\r\n                />\r\n                <div class=\"project-hint-text\" style=\"font-size: 12px; margin-top: 8px;\">\r\n                  只允许输入大小写字母、数字、下划线和连字符\r\n                </div>\r\n              </div>\r\n            ),\r\n            okText: '确定',\r\n            cancelText: '取消',\r\n            onOk: () => {\r\n              if (!this.tempProjectName) {\r\n                this.$message.warning('请输入项目名称');\r\n                return Promise.reject();\r\n              }\r\n              // 验证项目名称格式\r\n              if (!/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)) {\r\n                this.$message.warning('项目名称只能包含大小写字母、数字、下划线和连字符');\r\n                return Promise.reject();\r\n              }\r\n              resolve(this.tempProjectName);\r\n            },\r\n            onCancel: () => {\r\n              reject();\r\n            }\r\n          });\r\n        });\r\n\r\n        if (projectName) {\r\n          const response = await axios.post('/api/projects/new', {\r\n            name: projectName\r\n          });\r\n\r\n          await this.fetchProjects();\r\n          this.$message.success('新项目创建成功');\r\n          if (response.data?.dbFile) {\r\n            await this.selectProject(response.data);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error) { // 用户取消操作时不显示错误\r\n          console.error('Error creating new project:', error);\r\n          this.$message.error('创建新项目失败');\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjects();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}