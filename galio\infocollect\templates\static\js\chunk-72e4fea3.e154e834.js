(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72e4fea3"],{"143e":function(a,t,e){"use strict";e("801f")},"801f":function(a,t,e){},dccb:function(a,t,e){"use strict";e.r(t);var n=function(){var a=this,t=a._self._c;return t("div",[t("a-card",{staticClass:"header-solid h-full",attrs:{bordered:!1,bodyStyle:{padding:"24px"}}},[t("a-row",{attrs:{type:"flex",align:"middle"}},[t("a-col",{attrs:{span:24}},[t("h5",{staticClass:"font-semibold"},[a._v("代码信息")]),t("p",[a._v("此页面用于代码信息功能，敬请期待！")])])],1)],1)],1)},s=[],d={name:"CodeInfo",data(){return{}}},o=d,r=(e("143e"),e("2877")),c=Object(r["a"])(o,n,s,!1,null,"aa1aa6e8",null);t["default"]=c.exports}}]);
//# sourceMappingURL=chunk-72e4fea3.e154e834.js.map