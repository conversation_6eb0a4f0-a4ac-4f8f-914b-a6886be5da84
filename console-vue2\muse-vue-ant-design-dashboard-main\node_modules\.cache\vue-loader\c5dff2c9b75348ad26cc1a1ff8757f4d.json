{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue?vue&type=style&index=0&id=efcbd2b0&scoped=true&lang=css", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue", "mtime": 1753187071945}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucmVwb3NpdG9yeS1jb25maWctY2FyZCB7DQogIG1hcmdpbjogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouYnV0dG9uLWdyb3VwcyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBnYXA6IDE2cHg7DQp9DQoNCi5idXR0b24tZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KDQoNCjo6di1kZWVwIC5hbnQtdXBsb2FkLXNlbGVjdCB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCn0NCg0KDQoNCi8qIOWIoOmZpOaMiemSruS/neaMgee6ouiJsiAqLw0KOjp2LWRlZXAgLmRlbGV0ZS1idXR0b24gew0KICBjb2xvcjogI2ZmNGQ0ZiAhaW1wb3J0YW50Ow0KfQ0KDQo6OnYtZGVlcCAuZGVsZXRlLWJ1dHRvbiAuYW50aWNvbiB7DQogIGNvbG9yOiAjZmY0ZDRmICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOWTjeW6lOW8j+iwg+aVtCAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5idXR0b24tZ3JvdXBzIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["RepositoryConfig.vue"], "names": [], "mappings": ";AAuuBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RepositoryConfig.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card :bordered=\"false\" class=\"header-solid repository-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('repositoryConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('repositoryConfig.addRepository') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"download\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"downloadSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"deleteSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                type=\"default\"\r\n                icon=\"download\"\r\n                class=\"nav-style-button\"\r\n                @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                :show-upload-list=\"false\"\r\n                :custom-request=\"handleUpload\"\r\n                accept=\".csv\"\r\n              >\r\n                <a-button\r\n                    type=\"default\"\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('repositoryConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"repositories\"\r\n        :rowKey=\"(record) => record.key\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: repositories.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"col === 'repository_url' && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"col === 'repository_url' && text ? 'cursor: pointer' : ''\"\r\n              @click=\"col === 'repository_url' && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyRepository(record)\">\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteRepository(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n\r\n  <!-- 下载结果显示 -->\r\n  <repository-download-results />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport RepositoryDownloadResults from './RepositoryDownloadResults.vue';\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n    RepositoryDownloadResults,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n  },\r\n  data() {\r\n    return {\r\n      repositories: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'microservice_name',\r\n        'repository_url',\r\n        'branch_name',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      downloadTaskId: null,\r\n      downloadPolling: false,\r\n      downloadPollInterval: null,\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.microservice'),\r\n          dataIndex: 'microservice_name',\r\n          scopedSlots: { customRender: 'microservice_name' },\r\n          width: 200,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.repositoryUrl'),\r\n          dataIndex: 'repository_url',\r\n          scopedSlots: { customRender: 'repository_url' },\r\n          width: 300,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.branchName'),\r\n          dataIndex: 'branch_name',\r\n          scopedSlots: { customRender: 'branch_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchRepositoryConfig();\r\n  },\r\n  methods: {\r\n    copyRepository(record) {\r\n      const newKey = `new-${Date.now()}`;\r\n      const newRecord = {\r\n        ...record,\r\n        key: newKey,\r\n        editable: true,\r\n        isNew: true,\r\n        microservice_name: `${record.microservice_name}_copy`,\r\n      };\r\n      \r\n      this.repositories = [newRecord, ...this.repositories];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    getColumnTitle(col) {\r\n      const titleMap = {\r\n        'microservice_name': this.$t('repositoryConfig.columns.microservice'),\r\n        'repository_url': this.$t('repositoryConfig.columns.repositoryUrl'),\r\n        'branch_name': this.$t('repositoryConfig.columns.branchName'),\r\n      };\r\n      return titleMap[col] || col;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    edit(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target.editable = true;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        delete target.editable;\r\n        this.repositories = newData;\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n\r\n        try {\r\n          this.saving = true;\r\n          const repositoryData = {\r\n            microservice_name: target.microservice_name,\r\n            repository_url: target.repository_url,\r\n            branch_name: target.branch_name,\r\n          };\r\n\r\n          const response = await axios.post('/api/repositories', {\r\n            repositories: [repositoryData]\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          if (response.data.success) {\r\n            const { successful, failed } = response.data.data;\r\n\r\n            if (failed.length > 0) {\r\n              // 显示验证错误\r\n              const errorMsg = failed[0].error;\r\n              this.$message.error(`${this.$t('repositoryConfig.validation.parseError')}: ${errorMsg}`);\r\n              return;\r\n            }\r\n\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success('Repository saved successfully');\r\n          } else {\r\n            this.$message.error(response.data.error || 'Failed to save repository');\r\n          }\r\n        } catch (error) {\r\n          this.$message.error(error.response?.data?.error || 'Failed to save repository');\r\n          await this.fetchRepositoryConfig();\r\n        } finally {\r\n          this.saving = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.repositories.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.repositories[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.repositories = this.repositories.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.repositories];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.repositories = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.repositories = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          microservice_name: '',\r\n          repository_url: '',\r\n          branch_name: 'main',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.repositories,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    async fetchRepositoryConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/repositories`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.repositories = response.data.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `repo_${item.microservice_name}`,\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.repositories.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load repositories');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteRepository(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/repositories/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repository');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async deleteSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to delete');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        if (selectedIds.length > 0) {\r\n          await axios.post('/api/repositories/batch-delete', {\r\n            ids: selectedIds\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n        }\r\n\r\n        this.repositories = this.repositories.filter(r => !this.selectedRowKeys.includes(r.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Selected repositories deleted successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repositories');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    async exportSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to export');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        const response = await axios.post('/api/repositories/export', {\r\n          ids: selectedIds\r\n        }, {\r\n          params: { dbFile: this.currentDbFile },\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repositories_export.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Repositories exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export repositories');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/repositories/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repository_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        const response = await axios.post('/api/repositories/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const { successful, failed } = response.data.data;\r\n\r\n          if (failed.length > 0) {\r\n            // 显示验证失败的代码仓\r\n            const failedMessages = failed.map(repo =>\r\n              `${repo.microservice_name || 'Unknown'}: ${repo.error}`\r\n            ).join('\\n');\r\n\r\n            this.$confirm({\r\n              title: this.$t('repositoryConfig.validation.parseError'),\r\n              content: failedMessages,\r\n              showCancelButton: false,\r\n              confirmButtonText: 'OK',\r\n              type: 'warning'\r\n            });\r\n          }\r\n\r\n          if (successful.length > 0) {\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success(`Successfully imported ${successful.length} repositories`);\r\n          }\r\n\r\n          if (failed.length > 0 && successful.length === 0) {\r\n            this.$message.error('No valid repositories found in the file');\r\n          }\r\n        } else {\r\n          this.$message.error(response.data.error || 'Failed to import repositories');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import repositories');\r\n      }\r\n    },\r\n\r\n    async downloadSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to download');\r\n        return;\r\n      }\r\n\r\n      // 获取选中的代码仓\r\n      const selectedRepositories = this.selectedRowKeys\r\n        .map(key => this.repositories.find(r => r.key === key))\r\n        .filter(repo => repo && repo.id);\r\n\r\n      if (selectedRepositories.length === 0) {\r\n        this.$message.warning('No valid repositories selected');\r\n        return;\r\n      }\r\n\r\n      // 弹出输入框让用户输入下载路径\r\n      const downloadPath = prompt(this.$t('repositoryConfig.download.selectPath'), 'D:\\\\downloads');\r\n\r\n      if (!downloadPath || !downloadPath.trim()) {\r\n        return;\r\n      }\r\n\r\n      // 显示开始下载的消息\r\n      this.$message.loading(this.$t('repositoryConfig.download.starting'), 0);\r\n\r\n      try {\r\n        const requestData = {\r\n          repositories: selectedRepositories.map(repo => ({\r\n            id: repo.id,\r\n            microservice_name: repo.microservice_name,\r\n            repository_url: repo.repository_url,\r\n            branch_name: repo.branch_name\r\n          })),\r\n          download_path: downloadPath.trim()\r\n        };\r\n\r\n        const response = await axios.post('/api/repositories/download', requestData, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        this.$message.destroy();\r\n\r\n        if (response.data.success) {\r\n          const taskId = response.data.data.task_id;\r\n          this.downloadTaskId = taskId;\r\n\r\n          // 初始化下载结果状态 - 显示正在进行的状态\r\n          const initialResults = {\r\n            task_id: taskId,\r\n            status: 'running',\r\n            successful: [],\r\n            failed: [],\r\n            repositories: selectedRepositories.reduce((acc, repo) => {\r\n              const key = `${repo.microservice_name}_${repo.repository_url}`;\r\n              acc[key] = {\r\n                microservice_name: repo.microservice_name,\r\n                repository_url: repo.repository_url,\r\n                branch_name: repo.branch_name,\r\n                status: 'pending',\r\n                progress: 0,\r\n                error_detail: null,\r\n                download_path: null\r\n              };\r\n              return acc;\r\n            }, {}),\r\n            timestamp: new Date().toLocaleString()\r\n          };\r\n\r\n          // 存储初始状态到store\r\n          this.$store.dispatch('updateRepositoryDownloadResults', initialResults);\r\n\r\n          this.$message.success('Repository download task started successfully');\r\n\r\n          // 开始轮询任务状态\r\n          this.startDownloadPolling(taskId);\r\n        } else {\r\n          this.$message.error(response.data.error || this.$t('repositoryConfig.download.failed'));\r\n        }\r\n      } catch (error) {\r\n        this.$message.destroy();\r\n        this.$message.error(error.response?.data?.error || error.message || this.$t('repositoryConfig.download.failed'));\r\n      }\r\n    },\r\n\r\n    startDownloadPolling(taskId) {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n      }\r\n\r\n      this.downloadPolling = true;\r\n\r\n      // 立即执行一次查询\r\n      this.pollDownloadStatus(taskId);\r\n\r\n      // 每5秒轮询一次\r\n      this.downloadPollInterval = setInterval(() => {\r\n        this.pollDownloadStatus(taskId);\r\n      }, 5000);\r\n\r\n      console.log(`开始轮询代码仓下载任务 ${taskId}，轮询间隔: 5秒`);\r\n    },\r\n\r\n    async pollDownloadStatus(taskId) {\r\n      try {\r\n        const response = await axios.get(`/api/repositories/download/status/${taskId}`, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const taskData = response.data.data;\r\n\r\n          // 更新store中的下载结果\r\n          this.$store.dispatch('updateRepositoryDownloadResults', {\r\n            ...taskData,\r\n            timestamp: new Date().toLocaleString()\r\n          });\r\n\r\n          // 检查任务是否完成\r\n          if (taskData.status === 'success' || taskData.status === 'failed' || taskData.status === 'partial_success') {\r\n            this.stopDownloadPolling();\r\n\r\n            // 显示完成消息\r\n            if (taskData.status === 'success') {\r\n              this.$message.success(this.$t('repositoryConfig.download.success'));\r\n            } else if (taskData.status === 'failed') {\r\n              this.$message.error(this.$t('repositoryConfig.download.failed'));\r\n            } else {\r\n              this.$message.warning(this.$t('repositoryConfig.download.partialSuccess'));\r\n            }\r\n\r\n            console.log('代码仓下载任务完成，停止轮询');\r\n          }\r\n        } else {\r\n          console.error('获取下载状态失败:', response.data.error);\r\n        }\r\n      } catch (error) {\r\n        console.error('轮询下载状态出错:', error);\r\n        \r\n        // 如果是404错误，可能任务不存在，停止轮询\r\n        if (error.response?.status === 404) {\r\n          this.stopDownloadPolling();\r\n          this.$message.error('Download task not found');\r\n        }\r\n      }\r\n    },\r\n\r\n    stopDownloadPolling() {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n        this.downloadPollInterval = null;\r\n      }\r\n      this.downloadPolling = false;\r\n      this.downloadTaskId = null;\r\n    },\r\n\r\n    checkActiveDownloadTask() {\r\n      // 检查是否有活跃的下载任务\r\n      const taskInfo = localStorage.getItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n      if (taskInfo) {\r\n        try {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n          if (projectFile === this.currentDbFile) {\r\n            this.downloadTaskId = taskId;\r\n            this.startDownloadPolling(taskId);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing repository download task info:', e);\r\n          localStorage.removeItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n        }\r\n      }\r\n    },\r\n\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRepositoryConfig();\r\n    // 检查是否有活跃的下载任务\r\n    this.checkActiveDownloadTask();\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 组件销毁前停止轮询\r\n    this.stopDownloadPolling();\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.repository-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}