{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\i18n\\locales\\en-US.js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\i18n\\locales\\en-US.js", "mtime": 1753234294799}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["common", "home", "title", "selectNode", "selectProject", "settings", "notifications", "clearAll", "noNotifications", "language", "configureNodes", "configureProxy", "detectReachableIps", "taskProgress", "refresh", "darkMode", "lightMode", "selectedNodes", "copiedToClipboard", "copyFailed", "clear", "save", "cancel", "actions", "edit", "copy", "delete", "configuratorset", "sidebarColor", "sidenavType", "navbarFixed", "configurator", "headTop<PERSON>", "process", "package", "hardware", "mount", "port", "docker", "k8s", "fileUpload", "fileDownload", "aiBash", "testcase", "tool", "configureTool", "spiderTool", "generalTool", "uploadToolPackage", "selectToolPackage", "editScript", "start", "editShellScript", "confirmScript", "scriptReady", "localSaveDirectory", "viewResult", "selectReachableIp", "columns", "hostName", "ip", "status", "progress", "result", "errorDetails", "speed", "fileSize", "failed", "sidebar", "taskPanel", "envAwareness", "processInfo", "packageInfo", "hardwareInfo", "filesystemInfo", "portInfo", "dockerInfo", "k8sInfo", "codeInfo", "materialInfo", "securityTool", "fileDown", "llmAutoTesting", "executeCase", "testcaseManagement", "aiTaintAnalysis", "smartOrchestration", "toolPanel", "hostConfig", "cbhConfig", "repositoryConfig", "selectFile", "clickToSelect", "uploadPath", "enterUploadPath", "startUpload", "uploadProgress", "uploadResults", "enterDownloadPath", "startDownload", "downloadProgress", "addHost", "exportSelected", "deleteSelected", "downloadTemplate", "uploadTemplate", "ip<PERSON><PERSON><PERSON>", "sshPort", "loginUser", "loginPassword", "switchRootCmd", "switchRootPwd", "addRepository", "downloadSelected", "selectDownloadPath", "microservice", "repositoryUrl", "branchName", "validation", "invalidUrl", "unsupportedFormat", "missing<PERSON><PERSON><PERSON>", "parseError", "download", "selectPath", "downloading", "starting", "success", "partialSuccess", "cloneError", "repositoryDownload", "total", "pending", "completed", "log", "viewLogs", "currentNode", "noNodeSelected", "selectLevel", "noLogs", "fetchError", "detail", "searchButton", "resetButton", "clearResults", "caseColumn", "number", "name", "level", "similarity", "prepareCondition", "testSteps", "expectedResult", "feature", "smartAnalysis", "startAnalysis", "caseAnalysis", "naturalLanguageQuery", "queryPlaceholder", "inputRequired", "searchFailed", "searchError", "resultsCleared", "topK", "scoreThreshold", "searchResults", "foundResults"], "sources": ["D:/_Projects_python/Tools/console-vue2/muse-vue-ant-design-dashboard-main/src/i18n/locales/en-US.js"], "sourcesContent": ["export default {\r\n  common: {\r\n    home: 'Home',\r\n    title: 'SecureTest Copilot',\r\n    selectNode: 'Select Node',\r\n    selectProject: 'Select Project',\r\n    settings: 'Settings',\r\n    notifications: 'Notifications',\r\n    clearAll: 'Clear All',\r\n    noNotifications: 'No Notifications',\r\n    language: 'Language',\r\n    configureNodes: 'Node Configuration',\r\n    configureProxy: 'Proxy Configuration',\r\n    detectReachableIps: 'Detect Reachable IPs',\r\n    taskProgress: 'Task Progress',\r\n    refresh: 'Refresh',\r\n    darkMode: 'Dark Mode',\r\n    lightMode: 'Light Mode',\r\n    selectedNodes: 'Selected {count} nodes',\r\n    copiedToClipboard: 'Copied to clipboard',\r\n    copyFailed: 'Copy failed',\r\n    clear: 'Clear',\r\n    save: 'Save',\r\n    cancel: 'Cancel',\r\n    actions: 'Actions',\r\n    edit: 'Edit',\r\n    copy: 'Copy',\r\n    delete: 'Delete'\r\n  },\r\n  configuratorset:{\r\n    sidebarColor: 'Sidebar Color',\r\n    sidenavType: 'Sidenav Type',\r\n    navbarFixed: 'Navbar Fixed',\r\n    configurator: 'Configurator',\r\n  },\r\n  headTopic:{\r\n    process: 'Process Table',\r\n    package: 'Package Table',\r\n    hardware: 'Hardware Table',\r\n    mount: 'Mount Table',\r\n    port: 'Network Ports & Sockets',\r\n    docker: 'Docker Table',\r\n    k8s: 'Kubernetes Table',\r\n    fileUpload: 'File Upload',\r\n    fileDownload: 'File Download',\r\n    aiBash: 'Command Line',\r\n    testcase: 'Test Case Table',\r\n  },\r\n  tool: {\r\n    configureTool: 'Configure Tool',\r\n    spiderTool: 'SSP',\r\n    generalTool: 'GeneralTool',\r\n    uploadToolPackage: 'Upload Tool Package (zip)',\r\n    selectToolPackage: 'upload',\r\n    editScript: 'Edit script',\r\n    start: 'start',\r\n    editShellScript: 'Edit Shell',\r\n    confirmScript: 'confirm',\r\n    scriptReady: 'Script Ready',\r\n    localSaveDirectory: 'Local Save Directory',\r\n    viewResult: 'View Result',\r\n    selectReachableIp: 'Select a reachable IP',\r\n    columns: {\r\n      hostName: 'Host Name',\r\n      ip: 'IP',\r\n      status: 'Status',\r\n      progress: 'Progress',\r\n      result: 'Result',\r\n      errorDetails: 'Error Details',\r\n      speed: 'Speed',\r\n      fileSize: 'File Size'\r\n    },\r\n    status: {\r\n      failed: 'Failed'\r\n    }\r\n  },\r\n  sidebar: {\r\n    taskPanel: 'Task Panel',\r\n    envAwareness: 'Env Awareness',\r\n    processInfo: 'Process Info',\r\n    packageInfo: 'Package Info',\r\n    hardwareInfo: 'Hardware Info',\r\n    filesystemInfo: 'Filesys Info',\r\n    portInfo: 'Port Info',\r\n    dockerInfo: 'Docker Info',\r\n    k8sInfo: 'K8S Info',\r\n    codeInfo: '代码信息',\r\n    materialInfo: '资料信息',\r\n    securityTool: 'AI Security Tool',\r\n    fileUpload: 'File Upload',\r\n    fileDown: 'File Down',\r\n    aiBash: 'AI Bash',\r\n    llmAutoTesting: 'LLM Auto Testing',\r\n    executeCase: 'Execute Case',\r\n    testcaseManagement: 'Case Management',\r\n    aiTaintAnalysis: 'AI Taint Analysis',\r\n    smartOrchestration: '预留',\r\n    toolPanel: 'Tool Panel',\r\n    hostConfig: 'Host Config',\r\n    cbhConfig: 'CBH Config',\r\n    repositoryConfig: 'Repo Config',\r\n  },\r\n  fileUpload: {\r\n    selectFile: 'Select File',\r\n    clickToSelect: 'Select File',\r\n    uploadPath: 'Upload Path',\r\n    enterUploadPath: 'Enter Upload Directory',\r\n    startUpload: 'Start Upload',\r\n    uploadProgress: 'Upload Progress',\r\n    uploadResults: 'Upload Results'\r\n  },\r\n  fileDownload: {\r\n    enterDownloadPath: 'Enter remote file path',\r\n    startDownload: 'Start Download',\r\n    downloadProgress: 'Download Progress',\r\n  },\r\n  hostConfig: {\r\n    title: 'Host Configuration',\r\n    addHost: 'Add Host',\r\n    exportSelected: 'Export Selected',\r\n    deleteSelected: 'Delete Selected',\r\n    downloadTemplate: 'Download Tpl',\r\n    uploadTemplate: 'Upload Tpl',\r\n    columns: {\r\n      hostName: 'Host Name',\r\n      ipAddress: 'IP Address',\r\n      sshPort: 'SSH Port',\r\n      loginUser: 'Login User',\r\n      loginPassword: 'Login Password',\r\n      switchRootCmd: 'Switch root cmd',\r\n      switchRootPwd: 'Switch root pwd'\r\n    },\r\n  },\r\n  repositoryConfig: {\r\n    title: 'Repository Configuration',\r\n    addRepository: 'Add Repository',\r\n    exportSelected: 'Export Selected',\r\n    deleteSelected: 'Delete Selected',\r\n    downloadSelected: 'Download Selected',\r\n    downloadTemplate: 'Download Tpl',\r\n    uploadTemplate: 'Upload Tpl',\r\n    selectDownloadPath: 'Select Download Path',\r\n    downloadProgress: 'Download Progress',\r\n    columns: {\r\n      microservice: 'Microservice',\r\n      repositoryUrl: 'Repository URL',\r\n      branchName: 'Branch Name'\r\n    },\r\n    validation: {\r\n      invalidUrl: 'Invalid repository URL',\r\n      unsupportedFormat: 'Unsupported repository format',\r\n      missingBranch: 'Missing branch name',\r\n      parseError: 'Parse failed'\r\n    },\r\n    download: {\r\n      selectPath: 'Please select download path',\r\n      downloading: 'Downloading...',\r\n      starting: 'Starting download...',\r\n      success: 'Download successful',\r\n      failed: 'Download failed',\r\n      partialSuccess: 'Partial download successful',\r\n      cloneError: 'Git clone failed'\r\n    }\r\n  },\r\n  repositoryDownload: {\r\n    title: 'Repository Download Results',\r\n    total: 'Total',\r\n    success: 'Success',\r\n    failed: 'Failed',\r\n    downloading: 'Downloading',\r\n    pending: 'Pending',\r\n    completed: 'Completed',\r\n    progress: 'Progress'\r\n  },\r\n  log: {\r\n    title: 'Log Viewer',\r\n    viewLogs: 'View Logs',\r\n    currentNode: 'Node',\r\n    noNodeSelected: 'No node selected',\r\n    selectLevel: 'Select Level',\r\n    refresh: 'Refresh',\r\n    noLogs: 'No logs available',\r\n    fetchError: 'Failed to fetch logs',\r\n  },\r\n  testcase: {\r\n    title: 'Test Case',\r\n    detail: 'Case Details',\r\n    searchButton: 'Search',\r\n    resetButton: 'Reset',\r\n    clearResults: 'Clear Results',\r\n  },\r\n  caseColumn: {\r\n    number: 'Case Number',\r\n    name: 'Case Name',\r\n    level: 'Case Level',\r\n    similarity: 'Similarity',\r\n    prepareCondition: 'Precondition',\r\n    testSteps: 'Test Steps',\r\n    expectedResult: 'Expected Result',\r\n    feature: 'Case Feature',\r\n  },\r\n  smartOrchestration: {\r\n    title: 'Smart Orchestration',\r\n    smartAnalysis: 'Smart Test Case Analysis',\r\n    startAnalysis: 'Start Analysis',\r\n    caseAnalysis: 'Case Analysis',\r\n    naturalLanguageQuery: 'Natural Language Query',\r\n    queryPlaceholder: 'Please enter natural language query',\r\n    inputRequired: 'Please enter a query',\r\n    searchFailed: 'Search failed',\r\n    searchError: 'Search error',\r\n    resultsCleared: 'Search results cleared',\r\n    topK: 'Top K',\r\n    scoreThreshold: 'Score Threshold',\r\n    searchResults: 'Search Results',\r\n    foundResults: 'Found {count} results',\r\n  },\r\n};"], "mappings": "AAAA,eAAe;EACbA,MAAM,EAAE;IACNC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,oBAAoB;IAC3BC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE,gBAAgB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,eAAe;IAC9BC,QAAQ,EAAE,WAAW;IACrBC,eAAe,EAAE,kBAAkB;IACnCC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,oBAAoB;IACpCC,cAAc,EAAE,qBAAqB;IACrCC,kBAAkB,EAAE,sBAAsB;IAC1CC,YAAY,EAAE,eAAe;IAC7BC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,wBAAwB;IACvCC,iBAAiB,EAAE,qBAAqB;IACxCC,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,eAAe,EAAC;IACdC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE;EAChB,CAAC;EACDC,SAAS,EAAC;IACRC,OAAO,EAAE,eAAe;IACxBC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE,gBAAgB;IAC1BC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,yBAAyB;IAC/BC,MAAM,EAAE,cAAc;IACtBC,GAAG,EAAE,kBAAkB;IACvBC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,eAAe;IAC7BC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE;IACJC,aAAa,EAAE,gBAAgB;IAC/BC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,aAAa;IAC1BC,iBAAiB,EAAE,2BAA2B;IAC9CC,iBAAiB,EAAE,QAAQ;IAC3BC,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,YAAY;IAC7BC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,cAAc;IAC3BC,kBAAkB,EAAE,sBAAsB;IAC1CC,UAAU,EAAE,aAAa;IACzBC,iBAAiB,EAAE,uBAAuB;IAC1CC,OAAO,EAAE;MACPC,QAAQ,EAAE,WAAW;MACrBC,EAAE,EAAE,IAAI;MACRC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,eAAe;MAC7BC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDL,MAAM,EAAE;MACNM,MAAM,EAAE;IACV;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,eAAe;IAC7BC,cAAc,EAAE,cAAc;IAC9BC,QAAQ,EAAE,WAAW;IACrBC,UAAU,EAAE,aAAa;IACzBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,kBAAkB;IAChCxC,UAAU,EAAE,aAAa;IACzByC,QAAQ,EAAE,WAAW;IACrBvC,MAAM,EAAE,SAAS;IACjBwC,cAAc,EAAE,kBAAkB;IAClCC,WAAW,EAAE,cAAc;IAC3BC,kBAAkB,EAAE,iBAAiB;IACrCC,eAAe,EAAE,mBAAmB;IACpCC,kBAAkB,EAAE,IAAI;IACxBC,SAAS,EAAE,YAAY;IACvBC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,YAAY;IACvBC,gBAAgB,EAAE;EACpB,CAAC;EACDlD,UAAU,EAAE;IACVmD,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE,aAAa;IAC5BC,UAAU,EAAE,aAAa;IACzBC,eAAe,EAAE,wBAAwB;IACzCC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,iBAAiB;IACjCC,aAAa,EAAE;EACjB,CAAC;EACDxD,YAAY,EAAE;IACZyD,iBAAiB,EAAE,wBAAwB;IAC3CC,aAAa,EAAE,gBAAgB;IAC/BC,gBAAgB,EAAE;EACpB,CAAC;EACDZ,UAAU,EAAE;IACVtF,KAAK,EAAE,oBAAoB;IAC3BmG,OAAO,EAAE,UAAU;IACnBC,cAAc,EAAE,iBAAiB;IACjCC,cAAc,EAAE,iBAAiB;IACjCC,gBAAgB,EAAE,cAAc;IAChCC,cAAc,EAAE,YAAY;IAC5B/C,OAAO,EAAE;MACPC,QAAQ,EAAE,WAAW;MACrB+C,SAAS,EAAE,YAAY;MACvBC,OAAO,EAAE,UAAU;MACnBC,SAAS,EAAE,YAAY;MACvBC,aAAa,EAAE,gBAAgB;MAC/BC,aAAa,EAAE,iBAAiB;MAChCC,aAAa,EAAE;IACjB;EACF,CAAC;EACDrB,gBAAgB,EAAE;IAChBxF,KAAK,EAAE,0BAA0B;IACjC8G,aAAa,EAAE,gBAAgB;IAC/BV,cAAc,EAAE,iBAAiB;IACjCC,cAAc,EAAE,iBAAiB;IACjCU,gBAAgB,EAAE,mBAAmB;IACrCT,gBAAgB,EAAE,cAAc;IAChCC,cAAc,EAAE,YAAY;IAC5BS,kBAAkB,EAAE,sBAAsB;IAC1Cd,gBAAgB,EAAE,mBAAmB;IACrC1C,OAAO,EAAE;MACPyD,YAAY,EAAE,cAAc;MAC5BC,aAAa,EAAE,gBAAgB;MAC/BC,UAAU,EAAE;IACd,CAAC;IACDC,UAAU,EAAE;MACVC,UAAU,EAAE,wBAAwB;MACpCC,iBAAiB,EAAE,+BAA+B;MAClDC,aAAa,EAAE,qBAAqB;MACpCC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRC,UAAU,EAAE,6BAA6B;MACzCC,WAAW,EAAE,gBAAgB;MAC7BC,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,qBAAqB;MAC9B5D,MAAM,EAAE,iBAAiB;MACzB6D,cAAc,EAAE,6BAA6B;MAC7CC,UAAU,EAAE;IACd;EACF,CAAC;EACDC,kBAAkB,EAAE;IAClBhI,KAAK,EAAE,6BAA6B;IACpCiI,KAAK,EAAE,OAAO;IACdJ,OAAO,EAAE,SAAS;IAClB5D,MAAM,EAAE,QAAQ;IAChB0D,WAAW,EAAE,aAAa;IAC1BO,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtBvE,QAAQ,EAAE;EACZ,CAAC;EACDwE,GAAG,EAAE;IACHpI,KAAK,EAAE,YAAY;IACnBqI,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,kBAAkB;IAClCC,WAAW,EAAE,cAAc;IAC3B5H,OAAO,EAAE,SAAS;IAClB6H,MAAM,EAAE,mBAAmB;IAC3BC,UAAU,EAAE;EACd,CAAC;EACDjG,QAAQ,EAAE;IACRzC,KAAK,EAAE,WAAW;IAClB2I,MAAM,EAAE,cAAc;IACtBC,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,OAAO;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,MAAM,EAAE,aAAa;IACrBC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,YAAY;IACnBC,UAAU,EAAE,YAAY;IACxBC,gBAAgB,EAAE,cAAc;IAChCC,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,iBAAiB;IACjCC,OAAO,EAAE;EACX,CAAC;EACDnE,kBAAkB,EAAE;IAClBpF,KAAK,EAAE,qBAAqB;IAC5BwJ,aAAa,EAAE,0BAA0B;IACzCC,aAAa,EAAE,gBAAgB;IAC/BC,YAAY,EAAE,eAAe;IAC7BC,oBAAoB,EAAE,wBAAwB;IAC9CC,gBAAgB,EAAE,qCAAqC;IACvDC,aAAa,EAAE,sBAAsB;IACrCC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,wBAAwB;IACxCC,IAAI,EAAE,OAAO;IACbC,cAAc,EAAE,iBAAiB;IACjCC,aAAa,EAAE,gBAAgB;IAC/BC,YAAY,EAAE;EAChB;AACF,CAAC", "ignoreList": []}]}