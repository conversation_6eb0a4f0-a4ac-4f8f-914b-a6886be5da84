{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue?vue&type=style&index=0&id=be68c572&lang=scss&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue", "mtime": 1753165961985}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjpkZWVwKC5hbnQtdGFibGUtdGhlYWQgPiB0ciA+IHRoKSB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIAogICY6aG92ZXIgewogICAgLnJlc2l6ZS1oYW5kbGUgewogICAgICBvcGFjaXR5OiAxOwogICAgfQogIH0KfQoKOmRlZXAoLnJlc2l6ZS1oYW5kbGUpIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgcmlnaHQ6IC01cHg7CiAgdG9wOiAwOwogIGJvdHRvbTogMDsKICB3aWR0aDogMTBweDsKICBjdXJzb3I6IGNvbC1yZXNpemU7CiAgei1pbmRleDogMTsKICBvcGFjaXR5OiAwOwogIHRyYW5zaXRpb246IG9wYWNpdHkgMC4yczsKICAKICAmOmhvdmVyIHsKICAgIG9wYWNpdHk6IDE7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICB9CiAgCiAgJjphY3RpdmUgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNCwgMTQ0LCAyNTUsIDAuNCk7CiAgfQp9Cg=="}, {"version": 3, "sources": ["ResizableTable.vue"], "names": [], "mappings": ";AAkHA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ResizableTable.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\n  <a-table\n    v-bind=\"$attrs\"\n    :columns=\"resizableColumns\"\n    :components=\"tableComponents\"\n    bordered\n    v-on=\"$listeners\"\n  >\n    <template v-for=\"slot in Object.keys($scopedSlots)\" :slot=\"slot\" slot-scope=\"scope\">\n      <slot :name=\"slot\" v-bind=\"scope\"></slot>\n    </template>\n  </a-table>\n</template>\n\n<script>\nexport default {\n  name: 'ResizableTable',\n  props: {\n    columns: {\n      type: Array,\n      required: true\n    }\n  },\n  data() {\n    return {\n      resizableColumns: []\n    };\n  },\n  watch: {\n    columns: {\n      handler(newColumns) {\n        this.resizableColumns = [...newColumns];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  computed: {\n    tableComponents() {\n      return {\n        header: {\n          cell: (h, props, children) => {\n            const { key, ...restProps } = props;\n            const col = this.resizableColumns.find(col => {\n              const k = col.dataIndex || col.key;\n              return k === key;\n            });\n            \n            if (!col || !col.width) {\n              return h('th', { ...restProps }, children);\n            }\n\n            return h(\n              'th',\n              {\n                ...restProps,\n                style: { position: 'relative' }\n              },\n              [\n                ...children,\n                h('div', {\n                  class: 'resize-handle',\n                  on: {\n                    mousedown: (e) => {\n                      e.preventDefault();\n                      const startX = e.clientX;\n                      const startWidth = col.width;\n                      \n                      const onMouseMove = (e) => {\n                        const newWidth = startWidth + (e.clientX - startX);\n                        if (newWidth > 50) {\n                          this.updateColumnWidth(key, newWidth);\n                        }\n                      };\n                      \n                      const onMouseUp = () => {\n                        document.removeEventListener('mousemove', onMouseMove);\n                        document.removeEventListener('mouseup', onMouseUp);\n                        document.body.style.cursor = '';\n                        document.body.style.userSelect = '';\n                      };\n                      \n                      document.body.style.cursor = 'col-resize';\n                      document.body.style.userSelect = 'none';\n                      document.addEventListener('mousemove', onMouseMove);\n                      document.addEventListener('mouseup', onMouseUp);\n                    }\n                  }\n                })\n              ]\n            );\n          }\n        }\n      };\n    }\n  },\n  methods: {\n    updateColumnWidth(key, width) {\n      this.resizableColumns = this.resizableColumns.map(col => {\n        const k = col.dataIndex || col.key;\n        if (k === key) {\n          return { ...col, width };\n        }\n        return col;\n      });\n      \n      // 触发父组件的列宽变化事件\n      this.$emit('columns-change', this.resizableColumns);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n:deep(.ant-table-thead > tr > th) {\n  position: relative;\n  \n  &:hover {\n    .resize-handle {\n      opacity: 1;\n    }\n  }\n}\n\n:deep(.resize-handle) {\n  position: absolute;\n  right: -5px;\n  top: 0;\n  bottom: 0;\n  width: 10px;\n  cursor: col-resize;\n  z-index: 1;\n  opacity: 0;\n  transition: opacity 0.2s;\n  \n  &:hover {\n    opacity: 1;\n    background-color: rgba(24, 144, 255, 0.2);\n  }\n  \n  &:active {\n    background-color: rgba(24, 144, 255, 0.4);\n  }\n}\n</style>\n"]}]}