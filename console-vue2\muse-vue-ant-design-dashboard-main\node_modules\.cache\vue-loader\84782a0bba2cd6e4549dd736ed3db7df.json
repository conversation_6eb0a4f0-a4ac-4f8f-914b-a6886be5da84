{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753233876968}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBheGlvcyBmcm9tICdAL2FwaS9heGlvc0luc3RhbmNlJzsKaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQnOwppbXBvcnQge21hcFN0YXRlfSBmcm9tICJ2dWV4IjsKaW1wb3J0IFJlZnJlc2hCdXR0b24gZnJvbSAnLi4vV2lkZ2V0cy9SZWZyZXNoQnV0dG9uLnZ1ZSc7CmltcG9ydCBUZXN0Q2FzZURldGFpbE1vZGFsIGZyb20gJy4uL1dpZGdldHMvVGVzdENhc2VEZXRhaWxNb2RhbC52dWUnOwppbXBvcnQgUmVzaXphYmxlVGFibGUgZnJvbSAnLi4vY29tbW9uL1Jlc2l6YWJsZVRhYmxlLnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgUmVmcmVzaEJ1dHRvbiwKICAgIFRlc3RDYXNlRGV0YWlsTW9kYWwsCiAgICBSZXNpemFibGVUYWJsZQogIH0sCiAgbmFtZTogJ1Rlc3RDYXNlcycsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICB0ZXN0Y2FzZXM6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIGRldGFpbHNWaXNpYmxlOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRUZXN0Y2FzZTogbnVsbCwKICAgICAgc2VhcmNoRm9ybTogewogICAgICAgIG5hbWU6ICcnLAogICAgICAgIGxldmVsOiB1bmRlZmluZWQsCiAgICAgICAgcHJlcGFyZV9jb25kaXRpb246ICcnLAogICAgICAgIHRlc3Rfc3RlcHM6ICcnLAogICAgICAgIGV4cGVjdGVkX3Jlc3VsdDogJycsCiAgICAgICAgdGVzdGNhc2VfZmVhdHVyZTogJycsCiAgICAgIH0sCiAgICAgIHRhYmxlQ29sdW1uczogW10sCiAgICAgIC8vIOeUqOS+i+eJueaAp+ebuOWFs+eKtuaAgQogICAgICBmZWF0dXJlVmlld1Zpc2libGU6IGZhbHNlLAogICAgICBmZWF0dXJlRWRpdFZpc2libGU6IGZhbHNlLAogICAgICBzZWxlY3RlZEZlYXR1cmU6ICcnLAogICAgICBlZGl0aW5nRmVhdHVyZTogJycsCiAgICAgIGN1cnJlbnRFZGl0aW5nUmVjb3JkOiBudWxsLAogICAgICBmZWF0dXJlU2F2aW5nOiBmYWxzZSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5pbml0aWFsaXplQ29sdW1ucygpOwogICAgdGhpcy5mZXRjaFRlc3RjYXNlcygpOwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcFN0YXRlKFsnc2VsZWN0ZWROb2RlSXAnLCAnY3VycmVudFByb2plY3QnLCAnc2lkZWJhckNvbG9yJ10pCiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0aWFsaXplQ29sdW1ucygpIHsKICAgICAgdGhpcy50YWJsZUNvbHVtbnMgPSBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICcjJywKICAgICAgICAgIGRhdGFJbmRleDogJ2luZGV4JywKICAgICAgICAgIGtleTogJ2luZGV4JywKICAgICAgICAgIHdpZHRoOiAxMDAsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBjdXN0b21SZW5kZXI6IChfLCBfXywgaW5kZXgpID0+IHsKICAgICAgICAgICAgcmV0dXJuICgodGhpcy5jdXJyZW50UGFnZSAtIDEpICogMTAwKSArIGluZGV4ICsgMTsKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCdjYXNlQ29sdW1uLm51bWJlcicpLAogICAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTnVtYmVyJywKICAgICAgICAgIGtleTogJ1Rlc3RjYXNlX051bWJlcicsCiAgICAgICAgICB3aWR0aDogMTMwLAogICAgICAgICAgZWxsaXBzaXM6IHRydWUsCiAgICAgICAgICBjdXN0b21SZW5kZXI6ICh0ZXh0LCByZWNvcmQpID0+IHsKICAgICAgICAgICAgcmV0dXJuIDxhIG9uQ2xpY2s9eygpID0+IHRoaXMudmlld0RldGFpbHMocmVjb3JkKX0gc3R5bGU9ImNvbG9yOiAjMTg5MGZmOyBjdXJzb3I6IHBvaW50ZXI7Ij57dGV4dH08L2E+OwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoJ2Nhc2VDb2x1bW4ubmFtZScpLAogICAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTmFtZScsCiAgICAgICAgICBrZXk6ICdUZXN0Y2FzZV9OYW1lJywKICAgICAgICAgIHdpZHRoOiAyMDAsCiAgICAgICAgICAvLyBlbGxpcHNpczogdHJ1ZSwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCdjYXNlQ29sdW1uLmxldmVsJyksCiAgICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAgICBrZXk6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAgICB3aWR0aDogMTAwLAogICAgICAgICAgc2xvdHM6IHsgY3VzdG9tUmVuZGVyOiAnVGVzdGNhc2VfTGV2ZWwnIH0sCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgnY2FzZUNvbHVtbi5wcmVwYXJlQ29uZGl0aW9uJyksCiAgICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9QcmVwYXJlQ29uZGl0aW9uJywKICAgICAgICAgIGtleTogJ1Rlc3RjYXNlX1ByZXBhcmVDb25kaXRpb24nLAogICAgICAgICAgd2lkdGg6IDI1MCwKICAgICAgICAgIGVsbGlwc2lzOiB0cnVlLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoJ2Nhc2VDb2x1bW4udGVzdFN0ZXBzJyksCiAgICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9UZXN0U3RlcHMnLAogICAgICAgICAga2V5OiAnVGVzdGNhc2VfVGVzdFN0ZXBzJywKICAgICAgICAgIHdpZHRoOiA0MDAsCiAgICAgICAgICBlbGxpcHNpczogdHJ1ZSwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCdjYXNlQ29sdW1uLmV4cGVjdGVkUmVzdWx0JyksCiAgICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9FeHBlY3RlZFJlc3VsdCcsCiAgICAgICAgICBrZXk6ICdUZXN0Y2FzZV9FeHBlY3RlZFJlc3VsdCcsCiAgICAgICAgICB3aWR0aDogNDAwLAogICAgICAgICAgZWxsaXBzaXM6IHRydWUsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgnY2FzZUNvbHVtbi5mZWF0dXJlJyksCiAgICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9GZWF0dXJlJywKICAgICAgICAgIGtleTogJ1Rlc3RjYXNlX0ZlYXR1cmUnLAogICAgICAgICAgd2lkdGg6IDIwMCwKICAgICAgICAgIHNsb3RzOiB7IGN1c3RvbVJlbmRlcjogJ1Rlc3RjYXNlX0ZlYXR1cmUnIH0sCiAgICAgICAgfSwgICAgICAgIAogICAgICBdOwogICAgfSwKCiAgICBoYW5kbGVDb2x1bW5zQ2hhbmdlKGNvbHVtbnMpIHsKICAgICAgdGhpcy50YWJsZUNvbHVtbnMgPSBjb2x1bW5zOwogICAgfSwKCiAgICBhc3luYyBmZXRjaFRlc3RjYXNlcyhwYWdlID0gMSkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIC8vIOaehOW7uuafpeivouWPguaVsAogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsKICAgICAgICAgIHBhZ2U6IHBhZ2UsCiAgICAgICAgICBwYWdlX3NpemU6IDEwMAogICAgICAgIH07CgogICAgICAgIC8vIOa3u+WKoOaQnOe0ouWPguaVsAogICAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0ubmFtZSkgcGFyYW1zLm5hbWUgPSB0aGlzLnNlYXJjaEZvcm0ubmFtZTsKICAgICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLmxldmVsKSBwYXJhbXMubGV2ZWwgPSB0aGlzLnNlYXJjaEZvcm0ubGV2ZWw7CiAgICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5wcmVwYXJlX2NvbmRpdGlvbikgcGFyYW1zLnByZXBhcmVfY29uZGl0aW9uID0gdGhpcy5zZWFyY2hGb3JtLnByZXBhcmVfY29uZGl0aW9uOwogICAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0udGVzdF9zdGVwcykgcGFyYW1zLnRlc3Rfc3RlcHMgPSB0aGlzLnNlYXJjaEZvcm0udGVzdF9zdGVwczsKICAgICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLmV4cGVjdGVkX3Jlc3VsdCkgcGFyYW1zLmV4cGVjdGVkX3Jlc3VsdCA9IHRoaXMuc2VhcmNoRm9ybS5leHBlY3RlZF9yZXN1bHQ7CiAgICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS50ZXN0Y2FzZV9mZWF0dXJlKSBwYXJhbXMudGVzdGNhc2VfZmVhdHVyZSA9IHRoaXMuc2VhcmNoRm9ybS50ZXN0Y2FzZV9mZWF0dXJlOwoKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldCgnL2FwaS90ZXN0Y2FzZS8nLCB7IHBhcmFtcyB9KTsKICAgICAgICB0aGlzLnRlc3RjYXNlcyA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB0ZXN0Y2FzZXM6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHRlc3QgY2FzZXMnKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICAvLyDnlKjkvovnibnmgKfmk43kvZzmlrnms5UKICAgIHZpZXdGZWF0dXJlKHJlY29yZCkgewogICAgICB0aGlzLnNlbGVjdGVkRmVhdHVyZSA9IHJlY29yZC5UZXN0Y2FzZV9GZWF0dXJlIHx8ICcnOwogICAgICB0aGlzLmZlYXR1cmVWaWV3VmlzaWJsZSA9IHRydWU7CiAgICB9LAoKICAgIGVkaXRGZWF0dXJlKHJlY29yZCkgewogICAgICB0aGlzLmN1cnJlbnRFZGl0aW5nUmVjb3JkID0gcmVjb3JkOwogICAgICB0aGlzLmVkaXRpbmdGZWF0dXJlID0gcmVjb3JkLlRlc3RjYXNlX0ZlYXR1cmUgfHwgJyc7CiAgICAgIHRoaXMuZmVhdHVyZUVkaXRWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCgogICAgYXN5bmMgc2F2ZUZlYXR1cmUoKSB7CiAgICAgIGlmICghdGhpcy5jdXJyZW50RWRpdGluZ1JlY29yZCkgcmV0dXJuOwoKICAgICAgdGhpcy5mZWF0dXJlU2F2aW5nID0gdHJ1ZTsKICAgICAgdHJ5IHsKICAgICAgICBhd2FpdCBheGlvcy5wdXQoYC9hcGkvdGVzdGNhc2UvJHt0aGlzLmN1cnJlbnRFZGl0aW5nUmVjb3JkLlRlc3RjYXNlX051bWJlcn1gLCB7CiAgICAgICAgICBUZXN0Y2FzZV9GZWF0dXJlOiB0aGlzLmVkaXRpbmdGZWF0dXJlCiAgICAgICAgfSk7CgogICAgICAgIC8vIOabtOaWsOacrOWcsOaVsOaNrgogICAgICAgIHRoaXMuY3VycmVudEVkaXRpbmdSZWNvcmQuVGVzdGNhc2VfRmVhdHVyZSA9IHRoaXMuZWRpdGluZ0ZlYXR1cmU7CgogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn55So5L6L54m55oCn5pu05paw5oiQ5YqfJyk7CiAgICAgICAgdGhpcy5mZWF0dXJlRWRpdFZpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmN1cnJlbnRFZGl0aW5nUmVjb3JkID0gbnVsbDsKICAgICAgICB0aGlzLmVkaXRpbmdGZWF0dXJlID0gJyc7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgZmVhdHVyZTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign55So5L6L54m55oCn5pu05paw5aSx6LSlOiAnICsgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2UpKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmZlYXR1cmVTYXZpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICBjYW5jZWxFZGl0RmVhdHVyZSgpIHsKICAgICAgdGhpcy5mZWF0dXJlRWRpdFZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5jdXJyZW50RWRpdGluZ1JlY29yZCA9IG51bGw7CiAgICAgIHRoaXMuZWRpdGluZ0ZlYXR1cmUgPSAnJzsKICAgIH0sCgogICAgYXN5bmMgY2xlYXJGZWF0dXJlKHJlY29yZCkgewogICAgICB0aGlzLiRjb25maXJtKHsKICAgICAgICB0aXRsZTogJ+ehruiupOa4heepuicsCiAgICAgICAgY29udGVudDogJ+ehruWumuimgea4heepuuivpeeUqOS+i+eahOeJueaAp+WGheWuueWQl++8nycsCiAgICAgICAgb25PazogYXN5bmMgKCkgPT4gewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgYXdhaXQgYXhpb3MucHV0KGAvYXBpL3Rlc3RjYXNlLyR7cmVjb3JkLlRlc3RjYXNlX051bWJlcn1gLCB7CiAgICAgICAgICAgICAgVGVzdGNhc2VfRmVhdHVyZTogJycKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDmlbDmja4KICAgICAgICAgICAgcmVjb3JkLlRlc3RjYXNlX0ZlYXR1cmUgPSAnJzsKCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn55So5L6L54m55oCn5bey5riF56m6Jyk7CiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhcmluZyBmZWF0dXJlOicsIGVycm9yKTsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5riF56m655So5L6L54m55oCn5aSx6LSlOiAnICsgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2UpKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDmkJzntKLlpITnkIblh73mlbAKICAgIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7IC8vIOmHjee9ruWIsOesrOS4gOmhtQogICAgICB0aGlzLmZldGNoVGVzdGNhc2VzKDEpOwogICAgfSwKCiAgICAvLyDph43nva7mkJzntKLooajljZUKICAgIHJlc2V0U2VhcmNoKCkgewogICAgICB0aGlzLnNlYXJjaEZvcm0gPSB7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgbGV2ZWw6IHVuZGVmaW5lZCwKICAgICAgICBwcmVwYXJlX2NvbmRpdGlvbjogJycsCiAgICAgICAgdGVzdF9zdGVwczogJycsCiAgICAgICAgZXhwZWN0ZWRfcmVzdWx0OiAnJywKICAgICAgICB0ZXN0Y2FzZV9mZWF0dXJlOiAnJywKICAgICAgfTsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuZmV0Y2hUZXN0Y2FzZXMoMSk7CiAgICB9LAogICAgZm9ybWF0RGF0ZShkYXRlKSB7CiAgICAgIHJldHVybiBkYXRlID8gbW9tZW50KGRhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbScpIDogJ04vQSc7CiAgICB9LAogICAgZ2V0UmVzdWx0Q29sb3IocmVzdWx0KSB7CiAgICAgIGNvbnN0IGNvbG9ycyA9IHsKICAgICAgICAnUEFTUyc6ICdzdWNjZXNzJywKICAgICAgICAnRkFJTCc6ICdlcnJvcicsCiAgICAgICAgJ0JMT0NLRUQnOiAnd2FybmluZycsCiAgICAgICAgJ05PVCBSVU4nOiAnZGVmYXVsdCcsCiAgICAgIH07CiAgICAgIHJldHVybiBjb2xvcnNbcmVzdWx0XSB8fCAnZGVmYXVsdCc7CiAgICB9LAogICAgZ2V0TGV2ZWxDb2xvcihsZXZlbCkgewogICAgICBjb25zdCBjb2xvcnMgPSB7CiAgICAgICAgJ2xldmVsIDAnOiAncmVkJywKICAgICAgICAnbGV2ZWwgMSc6ICdvcmFuZ2UnLAogICAgICAgICdsZXZlbCAyJzogJ2dyZWVuJywKICAgICAgICAnbGV2ZWwgMyc6ICdibHVlJywKICAgICAgICAnbGV2ZWwgNCc6ICdwdXJwbGUnLAogICAgICB9OwogICAgICByZXR1cm4gY29sb3JzW2xldmVsXSB8fCAnZGVmYXVsdCc7CiAgICB9LAogICAgdmlld0RldGFpbHMocmVjb3JkKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUZXN0Y2FzZSA9IHJlY29yZDsKICAgICAgdGhpcy5kZXRhaWxzVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlUGFnZUNoYW5nZShwYWdlKSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSBwYWdlOwogICAgICB0aGlzLmZldGNoVGVzdGNhc2VzKHBhZ2UpOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";AAiLA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\n  <div class=\"layout-content\">\n    <a-card :bordered=\"false\">\n      <template #title>\n        <div class=\"card-header-wrapper\">\n          <div class=\"header-wrapper\">\n            <div class=\"logo-wrapper\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\n              </svg>\n            </div>\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\n          </div>\n          <div>\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\n          </div>\n        </div>\n      </template>\n\n      <!-- 搜索表单 -->\n      <div class=\"search-form\">\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\n          <a-form-item :label=\"$t('caseColumn.name')\">\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.level')\">\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.feature')\">\n            <a-input v-model=\"searchForm.testcase_feature\" :placeholder=\"$t('caseColumn.feature')\" allowClear />\n          </a-form-item>\n          <a-form-item>\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              {{ $t('testcase.searchButton') }}\n            </a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\n              <a-icon type=\"reload\" />\n              {{ $t('testcase.resetButton') }}\n            </a-button>\n          </a-form-item>\n        </a-form>\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <ResizableTable\n        :columns=\"tableColumns\"\n        :data-source=\"testcases\"\n        :loading=\"loading\"\n        :pagination=\"{\n          total: total,\n          pageSize: 100,\n          current: currentPage,\n          showSizeChanger: false,\n          showQuickJumper: true,\n          onChange: handlePageChange\n        }\"\n        :scroll=\"{ x: 1500 }\"\n        @columns-change=\"handleColumnsChange\"\n      >\n        <!-- Custom column renders -->\n        <template #Testcase_LastResult=\"{ text }\">\n          <a-tag :color=\"getResultColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Level=\"{ text }\">\n          <a-tag :color=\"getLevelColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Feature=\"{ text, record }\">\n          <a-space>\n            <a-button\n              type=\"link\"\n              size=\"small\"\n              @click=\"viewFeature(record)\"\n              :disabled=\"!text\"\n            >\n              <a-icon type=\"eye\" />\n              查看\n            </a-button>\n            <a-button\n              type=\"link\"\n              size=\"small\"\n              @click=\"editFeature(record)\"\n            >\n              <a-icon type=\"edit\" />\n              编辑\n            </a-button>\n            <a-button\n              type=\"link\"\n              size=\"small\"\n              @click=\"clearFeature(record)\"\n              :disabled=\"!text\"\n            >\n              <a-icon type=\"delete\" />\n              清空\n            </a-button>\n          </a-space>\n        </template>\n\n        <template #lastModified=\"{ text }\">\n          {{ formatDate(text) }}\n        </template>\n\n        <template #action=\"{ record }\">\n          <a-space>\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\n              View Details\n            </a-button>\n          </a-space>\n        </template>\n      </ResizableTable>\n\n      <!-- Details Modal -->\n      <TestCaseDetailModal\n        :visible=\"detailsVisible\"\n        :testcase=\"selectedTestcase\"\n        @close=\"detailsVisible = false\"\n      />\n\n      <!-- Feature View Modal -->\n      <a-modal\n        :visible=\"featureViewVisible\"\n        title=\"查看用例特性\"\n        @cancel=\"featureViewVisible = false\"\n        :footer=\"null\"\n        width=\"600px\"\n      >\n        <div class=\"feature-content\">\n          <pre>{{ selectedFeature || '暂无特性内容' }}</pre>\n        </div>\n      </a-modal>\n\n      <!-- Feature Edit Modal -->\n      <a-modal\n        :visible=\"featureEditVisible\"\n        title=\"编辑用例特性\"\n        @ok=\"saveFeature\"\n        @cancel=\"cancelEditFeature\"\n        :confirmLoading=\"featureSaving\"\n        width=\"800px\"\n      >\n        <a-form-item label=\"用例特性\">\n          <a-textarea\n            v-model=\"editingFeature\"\n            :rows=\"10\"\n            placeholder=\"请输入用例特性内容...\"\n          />\n        </a-form-item>\n      </a-modal>\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\nimport moment from 'moment';\nimport {mapState} from \"vuex\";\nimport RefreshButton from '../Widgets/RefreshButton.vue';\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\nimport ResizableTable from '../common/ResizableTable.vue';\n\nexport default {\n  components: {\n    RefreshButton,\n    TestCaseDetailModal,\n    ResizableTable\n  },\n  name: 'TestCases',\n  data() {\n    return {\n      loading: false,\n      testcases: [],\n      total: 0,\n      currentPage: 1,\n      detailsVisible: false,\n      selectedTestcase: null,\n      searchForm: {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      },\n      tableColumns: [],\n      // 用例特性相关状态\n      featureViewVisible: false,\n      featureEditVisible: false,\n      selectedFeature: '',\n      editingFeature: '',\n      currentEditingRecord: null,\n      featureSaving: false,\n    };\n  },\n  created() {\n    this.initializeColumns();\n    this.fetchTestcases();\n  },\n  computed: {\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\n  },\n  methods: {\n    initializeColumns() {\n      this.tableColumns = [\n        {\n          title: '#',\n          dataIndex: 'index',\n          key: 'index',\n          width: 100,\n          align: 'center',\n          customRender: (_, __, index) => {\n            return ((this.currentPage - 1) * 100) + index + 1;\n          }\n        },\n        {\n          title: this.$t('caseColumn.number'),\n          dataIndex: 'Testcase_Number',\n          key: 'Testcase_Number',\n          width: 130,\n          ellipsis: true,\n          customRender: (text, record) => {\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\n          }\n        },\n        {\n          title: this.$t('caseColumn.name'),\n          dataIndex: 'Testcase_Name',\n          key: 'Testcase_Name',\n          width: 200,\n          // ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.level'),\n          dataIndex: 'Testcase_Level',\n          key: 'Testcase_Level',\n          width: 100,\n          slots: { customRender: 'Testcase_Level' },\n        },\n        {\n          title: this.$t('caseColumn.prepareCondition'),\n          dataIndex: 'Testcase_PrepareCondition',\n          key: 'Testcase_PrepareCondition',\n          width: 250,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.testSteps'),\n          dataIndex: 'Testcase_TestSteps',\n          key: 'Testcase_TestSteps',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.expectedResult'),\n          dataIndex: 'Testcase_ExpectedResult',\n          key: 'Testcase_ExpectedResult',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.feature'),\n          dataIndex: 'Testcase_Feature',\n          key: 'Testcase_Feature',\n          width: 200,\n          slots: { customRender: 'Testcase_Feature' },\n        },        \n      ];\n    },\n\n    handleColumnsChange(columns) {\n      this.tableColumns = columns;\n    },\n\n    async fetchTestcases(page = 1) {\n      this.loading = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: page,\n          page_size: 100\n        };\n\n        // 添加搜索参数\n        if (this.searchForm.name) params.name = this.searchForm.name;\n        if (this.searchForm.level) params.level = this.searchForm.level;\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\n        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;\n\n        const response = await axios.get('/api/testcase/', { params });\n        this.testcases = response.data.data;\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Error fetching testcases:', error);\n        this.$message.error('Failed to load test cases');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 用例特性操作方法\n    viewFeature(record) {\n      this.selectedFeature = record.Testcase_Feature || '';\n      this.featureViewVisible = true;\n    },\n\n    editFeature(record) {\n      this.currentEditingRecord = record;\n      this.editingFeature = record.Testcase_Feature || '';\n      this.featureEditVisible = true;\n    },\n\n    async saveFeature() {\n      if (!this.currentEditingRecord) return;\n\n      this.featureSaving = true;\n      try {\n        await axios.put(`/api/testcase/${this.currentEditingRecord.Testcase_Number}`, {\n          Testcase_Feature: this.editingFeature\n        });\n\n        // 更新本地数据\n        this.currentEditingRecord.Testcase_Feature = this.editingFeature;\n\n        this.$message.success('用例特性更新成功');\n        this.featureEditVisible = false;\n        this.currentEditingRecord = null;\n        this.editingFeature = '';\n      } catch (error) {\n        console.error('Error updating feature:', error);\n        this.$message.error('用例特性更新失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        this.featureSaving = false;\n      }\n    },\n\n    cancelEditFeature() {\n      this.featureEditVisible = false;\n      this.currentEditingRecord = null;\n      this.editingFeature = '';\n    },\n\n    async clearFeature(record) {\n      this.$confirm({\n        title: '确认清空',\n        content: '确定要清空该用例的特性内容吗？',\n        onOk: async () => {\n          try {\n            await axios.put(`/api/testcase/${record.Testcase_Number}`, {\n              Testcase_Feature: ''\n            });\n\n            // 更新本地数据\n            record.Testcase_Feature = '';\n\n            this.$message.success('用例特性已清空');\n          } catch (error) {\n            console.error('Error clearing feature:', error);\n            this.$message.error('清空用例特性失败: ' + (error.response?.data?.message || error.message));\n          }\n        }\n      });\n    },\n\n    // 搜索处理函数\n    handleSearch() {\n      this.currentPage = 1; // 重置到第一页\n      this.fetchTestcases(1);\n    },\n\n    // 重置搜索表单\n    resetSearch() {\n      this.searchForm = {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      };\n      this.currentPage = 1;\n      this.fetchTestcases(1);\n    },\n    formatDate(date) {\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\n    },\n    getResultColor(result) {\n      const colors = {\n        'PASS': 'success',\n        'FAIL': 'error',\n        'BLOCKED': 'warning',\n        'NOT RUN': 'default',\n      };\n      return colors[result] || 'default';\n    },\n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange',\n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n      };\n      return colors[level] || 'default';\n    },\n    viewDetails(record) {\n      this.selectedTestcase = record;\n      this.detailsVisible = true;\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchTestcases(page);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n\n// .criclebox {\n//   background: #fff;\n//   border-radius: 12px;\n// }\n\n.search-form {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fafafa;\n  border-radius: 8px;\n\n  .ant-form-item {\n    margin-bottom: 12px;\n  }\n\n  .search-result-count {\n    margin-top: 1px;\n    padding: 0 1px;\n  }\n}\n\n\n</style>\n"]}]}