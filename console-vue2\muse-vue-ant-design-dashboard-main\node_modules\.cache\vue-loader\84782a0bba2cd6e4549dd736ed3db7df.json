{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753165665823}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";AAiHA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item :label=\"$t('caseColumn.name')\">\r\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.level')\">\r\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\r\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\r\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              {{ $t('testcase.searchButton') }}\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('testcase.resetButton') }}\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n        bordered\r\n        :components=\"tableComponents\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      tableComponents: {\r\n        header: {\r\n          cell: (h, props, children) => {\r\n            const { key, ...restProps } = props;\r\n            const col = this.columns.find(col => {\r\n              const k = col.dataIndex || col.key;\r\n              return k === key;\r\n            });\r\n            if (!col || !col.width) {\r\n              return h('th', { ...restProps }, children);\r\n            }\r\n\r\n            return h(\r\n              'th',\r\n              {\r\n                ...restProps,\r\n                style: { position: 'relative' }\r\n              },\r\n              [\r\n                ...children,\r\n                h('div', {\r\n                  style: {\r\n                    position: 'absolute',\r\n                    right: '-5px',\r\n                    top: 0,\r\n                    bottom: 0,\r\n                    width: '10px',\r\n                    cursor: 'col-resize',\r\n                    zIndex: 1\r\n                  },\r\n                  on: {\r\n                    mousedown: (e) => {\r\n                      const startX = e.clientX;\r\n                      const startWidth = col.width;\r\n                      const onMouseMove = (e) => {\r\n                        const newWidth = startWidth + (e.clientX - startX);\r\n                        if (newWidth > 50) {\r\n                          this.columns = this.columns.map(c => {\r\n                            if (c.key === key || c.dataIndex === key) {\r\n                              return { ...c, width: newWidth };\r\n                            }\r\n                            return c;\r\n                          });\r\n                        }\r\n                      };\r\n                      const onMouseUp = () => {\r\n                        document.removeEventListener('mousemove', onMouseMove);\r\n                        document.removeEventListener('mouseup', onMouseUp);\r\n                      };\r\n                      document.addEventListener('mousemove', onMouseMove);\r\n                      document.addEventListener('mouseup', onMouseUp);\r\n                    }\r\n                  }\r\n                })\r\n              ]\r\n            );\r\n          }\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    columns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (_, __, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n      ];\r\n    },\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n// 列拖动样式\r\n:deep(.ant-table-thead > tr > th) {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    .resize-handle {\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.resize-handle) {\r\n  position: absolute;\r\n  right: -5px;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 10px;\r\n  cursor: col-resize;\r\n  z-index: 1;\r\n  opacity: 0;\r\n  transition: opacity 0.2s;\r\n\r\n  &:hover {\r\n    opacity: 1;\r\n    background-color: rgba(24, 144, 255, 0.2);\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}