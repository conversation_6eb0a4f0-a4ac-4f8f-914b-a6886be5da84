{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753239386765}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";AAqIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\n  <div class=\"layout-content\">\n    <a-card :bordered=\"false\">\n      <template #title>\n        <div class=\"card-header-wrapper\">\n          <div class=\"header-wrapper\">\n            <div class=\"logo-wrapper\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\n              </svg>\n            </div>\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\n          </div>\n          <div>\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\n          </div>\n        </div>\n      </template>\n\n      <!-- 搜索表单 -->\n      <div class=\"search-form\">\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\n          <a-form-item :label=\"$t('caseColumn.name')\">\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.level')\">\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\n          </a-form-item>\n          <a-form-item>\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              {{ $t('testcase.searchButton') }}\n            </a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\n              <a-icon type=\"reload\" />\n              {{ $t('testcase.resetButton') }}\n            </a-button>\n          </a-form-item>\n        </a-form>\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <ResizableTable\n        :columns=\"tableColumns\"\n        :data-source=\"testcases\"\n        :loading=\"loading\"\n        :pagination=\"{\n          total: total,\n          pageSize: 100,\n          current: currentPage,\n          showSizeChanger: false,\n          showQuickJumper: true,\n          onChange: handlePageChange\n        }\"\n        :scroll=\"{ x: 1500 }\"\n        @columns-change=\"handleColumnsChange\"\n      >\n        <!-- Custom column renders -->\n        <template #Testcase_LastResult=\"{ text }\">\n          <a-tag :color=\"getResultColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Level=\"{ text }\">\n          <a-tag :color=\"getLevelColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #lastModified=\"{ text }\">\n          {{ formatDate(text) }}\n        </template>\n\n        <template #action=\"{ record }\">\n          <a-space>\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\n              View Details\n            </a-button>\n          </a-space>\n        </template>\n      </ResizableTable>\n\n      <!-- Details Modal -->\n      <TestCaseDetailModal\n        :visible=\"detailsVisible\"\n        :testcase=\"selectedTestcase\"\n        @close=\"detailsVisible = false\"\n      />\n\n      <!-- Feature Modal -->\n      <a-modal\n        :visible=\"featureModalVisible\"\n        :title=\"featureModalMode === 'view' ? '查看用例特性' : '编辑用例特性'\"\n        @ok=\"handleModalOk\"\n        @cancel=\"closeFeatureModal\"\n        :footer=\"featureModalMode === 'view' ? null : undefined\"\n        :confirmLoading=\"featureSaving\"\n        width=\"700px\"\n      >\n        <div v-if=\"featureModalMode === 'view'\" class=\"feature-content\">\n          {{ selectedFeature || '暂无特性内容' }}\n        </div>\n        <a-textarea\n          v-else\n          v-model=\"editingFeature\"\n          :rows=\"8\"\n          placeholder=\"请输入用例特性内容...\"\n        />\n      </a-modal>\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\nimport moment from 'moment';\nimport {mapState} from \"vuex\";\nimport RefreshButton from '../Widgets/RefreshButton.vue';\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\nimport ResizableTable from '../common/ResizableTable.vue';\n\nexport default {\n  components: {\n    RefreshButton,\n    TestCaseDetailModal,\n    ResizableTable\n  },\n  name: 'TestCases',\n  data() {\n    return {\n      loading: false,\n      testcases: [],\n      total: 0,\n      currentPage: 1,\n      detailsVisible: false,\n      selectedTestcase: null,\n      searchForm: {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      },\n      tableColumns: [],\n      // 用例特性相关状态\n      featureModalVisible: false,\n      featureModalMode: 'view', // 'view' 或 'edit'\n      selectedFeature: '',\n      editingFeature: '',\n      currentEditingRecord: null,\n      featureSaving: false,\n    };\n  },\n  created() {\n    this.initializeColumns();\n    this.fetchTestcases();\n  },\n  computed: {\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\n  },\n  methods: {\n    initializeColumns() {\n      this.tableColumns = [\n        {\n          title: '#',\n          dataIndex: 'index',\n          key: 'index',\n          width: 100,\n          align: 'center',\n          customRender: (_, __, index) => {\n            return ((this.currentPage - 1) * 100) + index + 1;\n          }\n        },\n        {\n          title: this.$t('caseColumn.number'),\n          dataIndex: 'Testcase_Number',\n          key: 'Testcase_Number',\n          width: 130,\n          ellipsis: true,\n          customRender: (text, record) => {\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\n          }\n        },\n        {\n          title: this.$t('caseColumn.name'),\n          dataIndex: 'Testcase_Name',\n          key: 'Testcase_Name',\n          width: 200,\n          // ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.level'),\n          dataIndex: 'Testcase_Level',\n          key: 'Testcase_Level',\n          width: 100,\n          slots: { customRender: 'Testcase_Level' },\n        },\n        {\n          title: this.$t('caseColumn.prepareCondition'),\n          dataIndex: 'Testcase_PrepareCondition',\n          key: 'Testcase_PrepareCondition',\n          width: 250,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.testSteps'),\n          dataIndex: 'Testcase_TestSteps',\n          key: 'Testcase_TestSteps',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.expectedResult'),\n          dataIndex: 'Testcase_ExpectedResult',\n          key: 'Testcase_ExpectedResult',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.feature'),\n          dataIndex: 'Testcase_Feature',\n          key: 'Testcase_Feature',\n          width: 200,\n          customRender: (text, record) => {\n            const featureText = text || ''; // 确保 text 始终是字符串\n            return (\n              <div style=\"display: flex; gap: 1px;\">\n                <a-button type=\"link\" onClick={() => this.viewFeature(record)}>\n                  查看\n                </a-button>\n                <a-button type=\"link\" onClick={() => this.editFeature(record)}>\n                  编辑\n                </a-button>\n                <a-button type=\"link\" onClick={() => this.clearFeature(record)} disabled={!featureText}>\n                  清空\n                </a-button>\n              </div>\n            );\n          },\n        },        \n      ];\n    },\n\n    handleColumnsChange(columns) {\n      this.tableColumns = columns;\n    },\n\n    async fetchTestcases(page = 1) {\n      this.loading = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: page,\n          page_size: 100\n        };\n\n        // 添加搜索参数\n        if (this.searchForm.name) params.name = this.searchForm.name;\n        if (this.searchForm.level) params.level = this.searchForm.level;\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\n        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;\n\n        const response = await axios.get('/api/testcase/', { params });\n        this.testcases = response.data.data.map(item => ({\n          ...item,\n          Testcase_Feature: item.Testcase_Feature || ''\n        }));\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Error fetching testcases:', error);\n        this.$message.error('Failed to load test cases');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 用例特性操作方法\n    viewFeature(record) {\n      this.selectedFeature = record.Testcase_Feature || '';\n      this.featureModalMode = 'view';\n      this.featureModalVisible = true;\n    },\n\n    editFeature(record) {\n      this.currentEditingRecord = record;\n      this.editingFeature = record.Testcase_Feature || '';\n      this.featureModalMode = 'edit';\n      this.featureModalVisible = true;\n    },\n\n    handleModalOk() {\n      if (this.featureModalMode === 'edit') {\n        this.saveFeature();\n      }\n    },\n\n    async saveFeature() {\n      this.featureSaving = true;\n      try {\n        console.log('Saving feature:', this.editingFeature);\n        console.log('Testcase Number:', this.currentEditingRecord.Testcase_Number);\n\n        await axios.put(`/api/testcase/${this.currentEditingRecord.Testcase_Number}`, {\n          Testcase_Feature: this.editingFeature\n        });\n        this.currentEditingRecord.Testcase_Feature = this.editingFeature;\n        this.$message.success('保存成功');\n        this.closeFeatureModal();\n      } catch (error) {\n        console.error('Save error:', error);\n        this.$message.error('保存失败');\n      } finally {\n        this.featureSaving = false;\n      }\n    },\n\n    closeFeatureModal() {\n      this.featureModalVisible = false;\n      this.currentEditingRecord = null;\n      this.editingFeature = '';\n      this.selectedFeature = '';\n    },\n\n    clearFeature(record) {\n      this.$confirm({\n        title: '确认清空',\n        content: '确定要清空该用例的特性内容吗？',\n        onOk: async () => {\n          try {\n            await axios.put(`/api/testcase/${record.Testcase_Number}`, { Testcase_Feature: '' });\n            record.Testcase_Feature = '';\n            this.$message.success('已清空');\n          } catch (error) {\n            this.$message.error('清空失败');\n          }\n        }\n      });\n    },\n\n    // 搜索处理函数\n    handleSearch() {\n      this.currentPage = 1; // 重置到第一页\n      this.fetchTestcases(1);\n    },\n\n    // 重置搜索表单\n    resetSearch() {\n      this.searchForm = {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      };\n      this.currentPage = 1;\n      this.fetchTestcases(1);\n    },\n    formatDate(date) {\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\n    },\n    getResultColor(result) {\n      const colors = {\n        'PASS': 'success',\n        'FAIL': 'error',\n        'BLOCKED': 'warning',\n        'NOT RUN': 'default',\n      };\n      return colors[result] || 'default';\n    },\n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange',\n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n      };\n      return colors[level] || 'default';\n    },\n    viewDetails(record) {\n      this.selectedTestcase = record;\n      this.detailsVisible = true;\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchTestcases(page);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.search-form {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fafafa;\n  border-radius: 8px;\n\n  .ant-form-item {\n    margin-bottom: 12px;\n  }\n\n  .search-result-count {\n    margin-top: 1px;\n    padding: 0 1px;\n  }\n}\n\n// .feature-content {\n//   background-color: #f5f5f5;\n//   padding: 6px;\n//   border-radius: 1px;\n//   white-space: pre-wrap;\n//   word-wrap: break-word;\n//   max-height: 100px;\n//   overflow-y: auto;\n//   line-height: 1.5;\n// }\n\n\n</style>\n"]}]}