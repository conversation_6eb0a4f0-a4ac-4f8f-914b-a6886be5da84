export default {
  common: {
    home: '首页',
    title: '安全测试助手',
    selectNode: '选择节点',
    selectProject: '选择项目',
    settings: '设置',
    notifications: '通知',
    clearAll: '清除全部',
    noNotifications: '暂无通知',
    language: '语言',
    configureNodes: '节点配置',
    configureProxy: '代理配置',
    detectReachableIps: '检测可用IP',
    taskProgress: '任务进度',
    refresh: '刷新',
    darkMode: '夜间模式',
    lightMode: '日间模式',
    selectedNodes: "已选择 {count} 个节点",
    copiedToClipboard: '已复制到剪贴板',
    copyFailed: '复制失败',
    clear: '清除',
    save: '保存',
    cancel: '取消',
    actions: '操作',
    edit: '编辑',
    copy: '复制',
    delete: '删除'
  },
  configuratorSet:{
    sidebarColor: '侧边栏颜色',
    sidenavType: '侧边栏类型',
    navbarFixed: '导航栏固定',
    configurator: '配置器',
  },
  headTopic:{
    process: '进程列表',
    package: '安装包列表',
    hardware: '硬件列表',
    mount: '挂载列表',
    port: '端口列表',
    docker: '容器列表',
    k8s: '集群列表',
    fileUpload: '文件上传',
    fileDownload: '文件下载',
    aiBash: '命令行',
    testcase: '用例列表',
  },
  tool: {
    configureTool: '工具配置',
    spiderTool: 'SSP工具',
    generalTool: '通用工具',
    uploadToolPackage: '上传工具包 (zip)',
    selectToolPackage: '选择包',
    editScript: '编辑脚本',
    start: '启动',
    editShellScript: '编辑Shell',
    confirmScript: '确认',
    scriptReady: '脚本已就绪',
    localSaveDirectory: '本地保存目录',
    viewResult: '查看结果',
    selectReachableIp: '选择可访问的IP',
    columns: {
      hostName: '主机名',
      ip: 'IP地址',
      status: '状态',
      progress: '进度',
      result: '结果',
      errorDetails: '错误详情',
      speed: '速度',
      fileSize: '文件大小'
    },
    status: {
      failed: '失败'
    }
  },
  sidebar: {
    taskPanel: '任务面板',
    envAwareness: '环境感知',
    processInfo: '进程信息',
    packageInfo: '安装包信息',
    hardwareInfo: '硬件信息',
    filesystemInfo: '文件系统信息',
    portInfo: '端口信息',
    dockerInfo: '容器信息',
    k8sInfo: '集群信息',
    codeInfo: '代码信息',
    materialInfo: '资料信息',
    securityTool: 'AI安全工具',
    fileUpload: '文件上传',
    fileDown: '文件下载',
    aiBash: 'AI Bash',
    llmAutoTesting: 'LLM自动化测试',
    testcaseManagement: '用例管理',
    executeCase: '用例执行',
    aiTaintAnalysis: '智能污点分析',
    smartOrchestration: '预留',
    toolPanel: '工具面板',
    hostConfig: '主机配置',
    cbhConfig: '堡垒机配置',
    repositoryConfig: '代码仓配置',
  },
  fileUpload: {
    selectFile: '选择文件',
    clickToSelect: '选择文件',
    uploadPath: '上传目录路径',
    enterUploadPath: '请输入上传目录',
    startUpload: '开始上传',
    uploadProgress: '上传进度',
    uploadResults: '上传结果'
  },
  fileDownload: {
    enterDownloadPath: '请输入下载路径',
    startDownload: '开始下载',
    downloadProgress: '下载进度',
  },
  hostConfig: {
    title: '主机配置',
    addHost: '添加主机',
    exportSelected: '导出选中',
    deleteSelected: '删除选中',
    downloadTemplate: '下载模板',
    uploadTemplate: '上传模板',
    columns: {
      hostName: '主机名',
      ipAddress: 'IP地址',
      sshPort: 'SSH端口',
      loginUser: '登录用户',
      loginPassword: '登录密码',
      switchRootCmd: 'root切换命令',
      switchRootPwd: 'root切换密码'
    },
  },
  repositoryConfig: {
    title: '代码仓配置',
    addRepository: '添加代码仓',
    exportSelected: '导出选中',
    deleteSelected: '删除选中',
    downloadSelected: '下载选中',
    downloadTemplate: '下载模板',
    uploadTemplate: '上传模板',
    selectDownloadPath: '选择下载路径',
    downloadProgress: '下载进度',
    columns: {
      microservice: '微服务',
      repositoryUrl: '仓库地址',
      branchName: '送检代码分支'
    },
    validation: {
      invalidUrl: '无效的代码仓地址',
      unsupportedFormat: '不支持的代码仓格式',
      missingBranch: '缺少分支名称',
      parseError: '解析失败'
    },
    download: {
      selectPath: '请选择下载路径',
      downloading: '正在下载...',
      starting: '正在启动下载...',
      success: '下载成功',
      failed: '下载失败',
      partialSuccess: '部分下载成功',
      cloneError: 'Git克隆失败'
    }
  },
  repositoryDownload: {
    title: '代码仓下载结果',
    total: '总计',
    success: '成功',
    failed: '失败',
    downloading: '下载中',
    pending: '等待中',
    completed: '已完成',
    progress: '进度'
  },
  log: {
    title: '日志查看器',
    viewLogs: '查看日志',
    currentNode: '节点',
    noNodeSelected: '未选择节点',
    selectLevel: '选择级别',
    refresh: '刷新',
    noLogs: '暂无日志',
    fetchError: '获取日志失败',
  },
  testcase: {
    title: '测试用例',
    detail: '用例详情',
    selectLevel: '选择级别',
    searchButton: '搜索',
    resetButton: '重置',
  },
  caseColumn: {
    number: '用例编号',
    name: '用例名称',
    level: '用例级别',
    similarity: '相似度',
    prepareCondition: '前置条件',
    testSteps: '测试步骤',
    expectedResult: '预期结果',
    feature: '用例特性',
  },
  smartOrchestration: {
    title: '智能编排',
    smartAnalysis: '智能测试用例分析',
    startAnalysis: '开始智能分析',
    caseAnalysis: '用例分析',
    naturalLanguageQuery: '自然语言查询',
    queryPlaceholder: '请输入自然语言查询',
    inputRequired: '请输入查询',
    searchFailed: '搜索失败',
    searchError: '搜索错误',
    resultsCleared: '搜索结果清除',
    topK: 'Top K',
    scoreThreshold: '得分阈值',
    searchResults: '搜索结果',
    foundResults: '找到 {count} 个结果',
  },
};