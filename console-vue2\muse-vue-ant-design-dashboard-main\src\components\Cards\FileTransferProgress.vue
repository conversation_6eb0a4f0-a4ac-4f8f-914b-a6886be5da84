<template>
  <a-card
    :title="title"
    style="margin-top: 16px;"
    class="compact-card"
  >
    <template v-if="overallProgress !== undefined" slot="extra">
      <span>Overall Progress: {{ overallProgress }}%</span>
    </template>

    <a-progress
      :percent="overallProgress"
      :status="progressBarStatus"
      style="margin-bottom: 16px;"
    />

    <a-table
      :dataSource="progressData"
      :columns="progressColumns"
      rowKey="ip"
      :pagination="false"
    >
      <template slot="errorDetail" slot-scope="text, record">
        <a-popover v-if="record && record.error_detail" placement="topLeft">
          <template slot="content">
            <p>Time: {{ record.error_detail.time }}</p>
            <p>Type: {{ record.error_detail.type }}</p>
            <p>Message: {{ record.error_detail.message }}</p>
          </template>
          <a-icon type="info-circle" style="color: #ff4d4f" />
        </a-popover>
      </template>
    </a-table>
  </a-card>
</template>

<script>
export default {
  name: 'ProgressDisplay',
  props: {
    title: {
      type: String,
      required: true
    },
    taskType: {
      type: String,
      required: true
    },
    activeTask: {
      type: Object,
      default: null
    },
    formatBytes: {
      type: Function,
      required: true
    }
  },
  computed: {
    progressColumns() {
      const columns = [
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          key: 'ip',
          width: '120px'
        },
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name',
          width: '150px',
          ellipsis: true
        },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          key: 'status',
          width: '100px',
          customRender: (text) => {
            const colorMap = {
              'pending': '#1890ff',
              'in_progress': '#1890ff',
              'paused': '#faad14',
              'success': '#52c41a',
              'completed': '#52c41a',
              'failed': '#f5222d',
              'downloading': '#1890ff'
            };
            const color = colorMap[text] || '#000';
            return <span style={{ color }}>{text}</span>;
          }
        },
        {
          title: this.$t('tool.columns.progress'),
          dataIndex: 'progress',
          key: 'progress',
          width: '200px',
          customRender: (text, record) => (
            <div>
              <a-progress
                percent={text || 0}
                size="small"
                status={record.status === 'failed' ? 'exception' :
                       record.status === 'paused' ? 'normal' : undefined}
              />
              <div style="font-size: 12px; color: #999">
                {this.formatBytes(record.bytes_transferred)} / {this.formatBytes(record.file_size)}
              </div>
            </div>
          )
        },
        {
          title: this.$t('tool.columns.speed'),
          dataIndex: 'speed',
          key: 'speed',
          width: '100px'
        },
        {
          title: this.$t('tool.columns.fileSize'),
          dataIndex: 'file_size',
          key: 'file_size',
          width: '100px',
          customRender: (text) => this.formatBytes(text)
        },
        {
          title: this.$t('tool.columns.errorDetails'),
          dataIndex: 'error_detail',
          key: 'error_detail',
          width: '60px',
          scopedSlots: { customRender: 'errorDetail' }
        }
      ];

      return columns;
    },
    progressData() {
      if (!this.activeTask || !this.activeTask.nodes) return [];
      return Object.keys(this.activeTask.nodes).map(ip => {
        const node = this.activeTask.nodes[ip];
        return {
          ip,
          host_name: node.host_name,
          status: node.status,
          progress: node.progress || 0,
          speed: node.speed || '-',
          bytes_transferred: node.bytes_transferred,
          error_detail: node.error_detail,
          file_size: node.file_size
        };
      });
    },
    overallProgress() {
      if (!this.activeTask || !this.activeTask.nodes) return 0;
      const nodes = Object.values(this.activeTask.nodes);
      if (nodes.length === 0) return 0;

      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);
      return Math.round(totalProgress / nodes.length);
    },
    progressBarStatus() {
      if (!this.activeTask || !this.activeTask.nodes) return 'normal';

      const nodes = Object.values(this.activeTask.nodes || {});
      if (nodes.length === 0) return 'normal';

      if (nodes.some(node => node.status === 'failed')) return 'exception';
      if (nodes.every(node => ['success', 'completed'].includes(node.status))) return 'success';
      return 'active';
    }
  }
};
</script>

<style scoped>
/* You can add specific styles for ProgressDisplay component here if needed */
/* Shared styles will be handled in the parent view components or global styles */
</style> 