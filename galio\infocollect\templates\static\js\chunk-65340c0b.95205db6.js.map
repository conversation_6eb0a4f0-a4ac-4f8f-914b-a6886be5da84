{"version": 3, "sources": ["webpack:///./src/components/Widgets/TestCaseDetailModal.vue", "webpack:///src/components/Widgets/TestCaseDetailModal.vue", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?a55e", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?4c7a", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?d149", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?a2b5", "webpack:///./src/views/SmartOrchestration.vue", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue", "webpack:///src/components/Cards/SmartOrchestrationInfo.vue", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?d925", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?3240", "webpack:///src/views/SmartOrchestration.vue", "webpack:///./src/views/SmartOrchestration.vue?499c", "webpack:///./src/views/SmartOrchestration.vue?cc22"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "visible", "$t", "on", "handleClose", "currentTestcase", "staticClass", "_v", "_s", "Testcase_Number", "Testcase_Name", "getLevelColor", "Testcase_Level", "Testcase_PrepareCondition", "Testcase_TestSteps", "Testcase_ExpectedResult", "_e", "staticRenderFns", "name", "props", "type", "Boolean", "default", "testcase", "Object", "fetchDetails", "data", "loading", "detailedTestcase", "watch", "newVal", "fetchTestcaseDetails", "computed", "methods", "response", "axios", "get", "similarity", "error", "console", "$message", "$emit", "level", "colors", "getSimilarityColor", "component", "scopedSlots", "_u", "key", "fn", "proxy", "analyzing", "hasNodeData", "showAnalysisModal", "searching", "searchTestcases", "model", "value", "smartSearchQuery", "callback", "$$v", "expression", "staticStyle", "smartSearchTopK", "smartSearchThreshold", "count", "smartSearchResults", "length", "clearSearchHistory", "searchResultColumns", "x", "text", "record", "$event", "viewTestcaseDetail", "Math", "round", "toFixed", "availableDataTypes", "join", "analysisResults", "activeKeys", "_l", "result", "index", "info_type", "toUpperCase", "status", "getStatusColor", "getStatusText", "query_text", "matched_testcases", "testcaseColumns", "column", "truncateText", "execution_results", "executionColumns", "expandedRowRender", "testcase_name", "startAnalysis", "analysisModalVisible", "selectedNodeId", "availableNodes", "node", "id", "ip", "selectedAnalysisTypes", "getTypeName", "testcaseDetailVisible", "selectedTestcase", "components", "TestCaseDetailModal", "mapState", "title", "dataIndex", "width", "align", "customRender", "ellipsis", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "lastProjectInfo", "project", "currentProject", "projectName", "currentProjectName", "$axios", "dataTypes", "available", "for<PERSON>ach", "dataKey", "localStorage", "getItem", "push", "message", "warning", "infoType", "collectedData", "getCollectedData", "$http", "post", "node_id", "collected_data", "map", "_", "toString", "success", "JSON", "parse", "typeNames", "texts", "max<PERSON><PERSON><PERSON>", "substring", "outputs", "output", "command", "exit_code", "trim", "query", "top_k", "score_threshold", "$store", "dispatch", "results", "SmartOrchestrationInfo"], "mappings": "kHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIK,QAAQ,MAAQL,EAAIM,GAAG,mBAAmB,MAAQ,QAAQ,OAAS,MAAMC,GAAG,CAAC,OAASP,EAAIQ,cAAc,CAAER,EAAIS,gBAAiBP,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAW,KAAK,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,qBAAqB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBI,iBAAiB,SAASX,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,mBAAmB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBK,eAAe,SAASZ,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,oBAAoB,KAAO,IAAI,CAACJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIe,cAAcf,EAAIS,gBAAgBO,kBAAkB,CAAChB,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBO,gBAAgB,QAAQ,GAAGd,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,+BAA+B,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBQ,2BAA2B,SAASf,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,wBAAwB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBS,oBAAoB,SAAShB,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,6BAA6B,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBU,yBAAyB,UAAU,GAAGnB,EAAIoB,MAAM,IAEn9CC,EAAkB,G,YC4CP,GACfC,KAAA,sBACAC,MAAA,CACAlB,QAAA,CACAmB,KAAAC,QACAC,SAAA,GAEAC,SAAA,CACAH,KAAAI,OACAF,QAAA,MAGAG,aAAA,CACAL,KAAAC,QACAC,SAAA,IAGAI,OACA,OACAC,SAAA,EACAC,iBAAA,OAGAC,MAAA,CACA5B,QAAA6B,GACAA,GAAA,KAAAL,cAAA,KAAAF,WAAA,KAAAK,kBACA,KAAAG,wBAGAR,WACA,KAAAK,iBAAA,OAGAI,SAAA,CACA3B,kBACA,YAAAuB,kBAAA,KAAAL,WAGAU,QAAA,CACA,6BACA,QAAAV,UAAA,KAAAA,SAAAd,gBAAA,CAEA,KAAAkB,SAAA,EACA,IACA,MAAAO,QAAAC,OAAAC,IAAA,sBAAAb,SAAAd,iBACA,KAAAmB,iBAAA,IACAM,EAAAR,KAEAW,WAAA,KAAAd,SAAAc,YAEA,MAAAC,GACAC,QAAAD,MAAA,cAAAA,GACA,KAAAE,SAAAF,MAAA,cACA,QACA,KAAAX,SAAA,KAIAvB,cACA,KAAAqC,MAAA,SACA,KAAAb,iBAAA,MAGAjB,cAAA+B,GACA,MAAAC,EAAA,CACA,gBACA,mBACA,kBACA,iBACA,mBACA,SACA,YACA,UACA,WACA,WAEA,OAAAA,EAAAD,IAAA,WAGAE,mBAAAP,GACA,OAAAA,GAAA,aACAA,GAAA,aACA,aChI2W,I,wBCQvWQ,EAAY,eACd,EACAlD,EACAsB,GACA,EACA,KACA,WACA,MAIa,OAAA4B,E,2CCnBf,W,oCCAA,W,uFCAA,IAAIlD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACQ,YAAY,QAAQN,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,2BAA2B,IAAI,IAAI,IAEnNmB,EAAkB,GCFlBtB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACQ,YAAY,sBAAsBN,MAAM,CAAC,UAAW,GAAO8C,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACnD,EAAG,KAAK,CAACQ,YAAY,qBAAqB,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIM,GAAG,0CAA0CgD,OAAM,GAAM,CAACF,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACnD,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,QAAUJ,EAAIuD,UAAU,UAAYvD,EAAIwD,YAAY,KAAO,YAAYjD,GAAG,CAAC,MAAQP,EAAIyD,oBAAoB,CAACzD,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIM,GAAG,qCAAqC,SAASgD,OAAM,MAAS,CAACpD,EAAG,QAAQ,CAACQ,YAAY,QAAQN,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,SAAS,CAACQ,YAAY,aAAaN,MAAM,CAAC,MAAQJ,EAAIM,GAAG,mCAAmC,KAAO,UAAU,CAACJ,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,aAAa,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,6CAA6C,CAACJ,EAAG,iBAAiB,CAACE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,uCAAuC,eAAeN,EAAIM,GAAG,yBAAyB,KAAO,QAAQ,QAAUN,EAAI0D,WAAWnD,GAAG,CAAC,OAASP,EAAI2D,iBAAiBC,MAAM,CAACC,MAAO7D,EAAI8D,iBAAkBC,SAAS,SAAUC,GAAMhE,EAAI8D,iBAAiBE,GAAKC,WAAW,uBAAuB,GAAG/D,EAAG,cAAc,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,IAAI,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,iBAAiB,CAACgE,YAAY,CAAC,MAAQ,QAAQ9D,MAAM,CAAC,IAAM,EAAE,IAAM,GAAG,YAAcJ,EAAIM,GAAG,4BAA4BsD,MAAM,CAACC,MAAO7D,EAAImE,gBAAiBJ,SAAS,SAAUC,GAAMhE,EAAImE,gBAAgBH,GAAKC,WAAW,qBAAqB/D,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIM,GAAG,gCAAgC,GAAGJ,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,iBAAiB,CAACgE,YAAY,CAAC,MAAQ,QAAQ9D,MAAM,CAAC,IAAM,EAAE,IAAM,EAAE,KAAO,GAAI,YAAcJ,EAAIM,GAAG,sCAAsCsD,MAAM,CAACC,MAAO7D,EAAIoE,qBAAsBL,SAAS,SAAUC,GAAMhE,EAAIoE,qBAAqBJ,GAAKC,WAAW,0BAA0B/D,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIM,GAAG,0CAA0C,GAAGJ,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,QAAUJ,EAAI0D,UAAU,MAAQ,GAAG,KAAO,UAAUnD,GAAG,CAAC,MAAQP,EAAI2D,kBAAkB,CAAC3D,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIM,GAAG,0BAA0B,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAGJ,EAAG,QAAQ,CAACQ,YAAY,QAAQN,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,oCAAoC,KAAO,SAAS4C,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACnD,EAAG,UAAU,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIM,GAAG,kCAAmC,CAAE+D,OAAQrE,EAAIsE,oBAAsB,IAAIC,aAAcrE,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQ,KAAO,SAASG,GAAG,CAAC,MAAQP,EAAIwE,qBAAqB,CAACxE,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIM,GAAG,iBAAiB,QAAQ,KAAKgD,OAAM,MAAS,CAACpD,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIyE,oBAAoB,cAAczE,EAAIsE,mBAAmB,YAAa,EAAM,KAAO,QAAQ,OAAS,CAAEI,EAAG,MAAOxB,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,kBAAkBC,GAAG,SAASsB,EAAMC,GAAQ,MAAO,CAAC1E,EAAG,IAAI,CAACgE,YAAY,CAAC,MAAQ,UAAU,OAAS,WAAW3D,GAAG,CAAC,MAAQ,SAASsE,GAAQ,OAAO7E,EAAI8E,mBAAmBF,MAAW,CAAC5E,EAAIW,GAAG,IAAIX,EAAIY,GAAGgE,EAAO/D,iBAAiB,UAAU,CAACuC,IAAI,iBAAiBC,GAAG,SAASsB,EAAMC,GAAQ,MAAO,CAAC1E,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIe,cAAc6D,EAAO5D,kBAAkB,CAAChB,EAAIW,GAAG,IAAIX,EAAIY,GAAGgE,EAAO5D,gBAAgB,UAAU,CAACoC,IAAI,aAAaC,GAAG,SAASsB,EAAMC,GAAQ,MAAO,CAAC1E,EAAG,aAAa,CAACE,MAAM,CAAC,QAAU2E,KAAKC,MAA0B,IAApBJ,EAAOnC,YAAkB,KAAO,QAAQ,eAAezC,EAAIgD,mBAAmB4B,EAAOnC,eAAevC,EAAG,OAAO,CAACgE,YAAY,CAAC,cAAc,MAAM,YAAY,SAAS,CAAClE,EAAIW,GAAG,IAAIX,EAAIY,IAAwB,IAApBgE,EAAOnC,YAAkBwC,QAAQ,IAAI,gBAAgB,IAAI,IAAI,GAAG/E,EAAG,QAAQ,CAACQ,YAAY,QAAQN,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAAGJ,EAAIwD,YAA8JtD,EAAG,UAAU,CAACQ,YAAY,QAAQN,MAAM,CAAC,QAAU,UAAU,YAAc,SAASJ,EAAIkF,oBAAsB,IAAIX,kBAAkBvE,EAAIkF,oBAAsB,IAAIC,KAAK,OAAO,KAAO,UAAU,YAAY,MAA5VjF,EAAG,UAAU,CAACQ,YAAY,QAAQN,MAAM,CAAC,QAAU,WAAW,YAAc,qCAAqC,KAAO,OAAO,YAAY,OAAwN,IAAI,IAAKJ,EAAIoF,iBAAmB,IAAIb,OAAS,EAAGrE,EAAG,MAAM,CAACA,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,SAAS,CAACJ,EAAIW,GAAG,UAAUT,EAAG,aAAa,CAACQ,YAAY,QAAQkD,MAAM,CAACC,MAAO7D,EAAIqF,WAAYtB,SAAS,SAAUC,GAAMhE,EAAIqF,WAAWrB,GAAKC,WAAW,eAAejE,EAAIsF,GAAItF,EAAIoF,iBAAiB,SAASG,EAAOC,GAAO,OAAOtF,EAAG,mBAAmB,CAACkD,IAAIoC,EAAMpF,MAAM,CAAC,OAAS,GAAGmF,EAAOE,UAAUC,wBAA0C,YAAlBH,EAAOI,OAAuB,KAAyB,YAAlBJ,EAAOI,OAAuB,KAAO,QAAQzC,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACnD,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAI4F,eAAeL,EAAOI,UAAU,CAAC3F,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAI6F,cAAcN,EAAOI,SAAS,SAASrC,OAAM,IAAO,MAAK,IAAO,CAACpD,EAAG,iBAAiB,CAACQ,YAAY,QAAQN,MAAM,CAAC,MAAQ,OAAO,OAAS,EAAE,KAAO,UAAU,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAGX,EAAIY,GAAG2E,EAAOE,cAAcvF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAGX,EAAIY,GAAG2E,EAAOO,eAAe5F,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIW,GAAGX,EAAIY,IAAI2E,EAAOQ,mBAAqB,IAAIxB,YAAY,GAAGrE,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAO,qBAAqB,MAAM,CAACJ,EAAIW,GAAG,aAAaT,EAAG,UAAU,CAACQ,YAAY,QAAQN,MAAM,CAAC,WAAamF,EAAOQ,kBAAkB,QAAU/F,EAAIgG,gBAAgB,YAAa,EAAM,KAAO,SAAS9C,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAE4C,EAAM,OAAErB,IAAU,MAAO,CAAiB,kBAAfqB,EAAO7C,IAAyB,CAAClD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQwE,EAAO9D,gBAAgB,CAACZ,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkG,aAAatB,EAAO9D,cAAe,WAAWd,EAAIoB,KAAqB,uBAAf6E,EAAO7C,IAA8B,CAAClD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQwE,EAAO1D,qBAAqB,CAAChB,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkG,aAAatB,EAAO1D,mBAAoB,WAAWlB,EAAIoB,SAAS,MAAK,KAAQlB,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAO,qBAAqB,MAAM,CAACJ,EAAIW,GAAG,UAAUT,EAAG,UAAU,CAACE,MAAM,CAAC,WAAamF,EAAOY,kBAAkB,QAAUnG,EAAIoG,iBAAiB,YAAa,EAAM,KAAO,QAAQ,WAAa,CAAEC,kBAAmBrG,EAAIqG,oBAAqBnD,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAE4C,EAAM,OAAErB,IAAU,MAAO,CAAiB,WAAfqB,EAAO7C,IAAkB,CAAClD,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAI4F,eAAehB,EAAOe,UAAU,CAAC3F,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAI6F,cAAcjB,EAAOe,SAAS,QAAQ3F,EAAIoB,KAAqB,kBAAf6E,EAAO7C,IAAyB,CAAClD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQwE,EAAO0B,gBAAgB,CAACpG,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkG,aAAatB,EAAO0B,cAAe,WAAWtG,EAAIoB,SAAS,MAAK,MAAS,MAAK,IAAI,GAAGpB,EAAIoB,KAAKlB,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,aAAa,MAAQ,IAAI,eAAiBJ,EAAIuD,WAAWhD,GAAG,CAAC,GAAKP,EAAIuG,eAAe3C,MAAM,CAACC,MAAO7D,EAAIwG,qBAAsBzC,SAAS,SAAUC,GAAMhE,EAAIwG,qBAAqBxC,GAAKC,WAAW,yBAAyB,CAAC/D,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,aAAa,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,SAAW,KAAK,CAACF,EAAG,WAAW,CAACgE,YAAY,CAAC,MAAQ,QAAQ9D,MAAM,CAAC,YAAc,aAAawD,MAAM,CAACC,MAAO7D,EAAIyG,eAAgB1C,SAAS,SAAUC,GAAMhE,EAAIyG,eAAezC,GAAKC,WAAW,mBAAmBjE,EAAIsF,GAAItF,EAAI0G,gBAAgB,SAASC,GAAM,OAAOzG,EAAG,kBAAkB,CAACkD,IAAIuD,EAAKC,GAAGxG,MAAM,CAAC,MAAQuG,EAAKC,KAAK,CAAC5G,EAAIW,GAAG,IAAIX,EAAIY,GAAG+F,EAAKrF,MAAM,KAAKtB,EAAIY,GAAG+F,EAAKE,IAAI,WAAU,IAAI,GAAG3G,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,SAAW,KAAK,CAACF,EAAG,mBAAmB,CAAC0D,MAAM,CAACC,MAAO7D,EAAI8G,sBAAuB/C,SAAS,SAAUC,GAAMhE,EAAI8G,sBAAsB9C,GAAKC,WAAW,0BAA0B,CAAC/D,EAAG,QAAQF,EAAIsF,GAAItF,EAAIkF,oBAAoB,SAAS1D,GAAM,OAAOtB,EAAG,QAAQ,CAACkD,IAAI5B,EAAKpB,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQoB,IAAO,CAACxB,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI+G,YAAYvF,QAAW,MAAK,IAAI,IAAI,IAAI,IAAI,GAAGtB,EAAG,sBAAsB,CAACE,MAAM,CAAC,QAAUJ,EAAIgH,sBAAsB,SAAWhH,EAAIiH,kBAAkB1G,GAAG,CAAC,MAAQ,SAASsE,GAAQ7E,EAAIgH,uBAAwB,OAAW,IAE93P3F,EAAkB,G,0DCuQP,GACfC,KAAA,0BACA4F,WAAA,CACAC,4BAEArF,OACA,OACAyB,WAAA,EACAiD,sBAAA,EACAC,eAAA,KACAK,sBAAA,GACA1B,gBAAA,GACAC,WAAA,MACAqB,eAAA,GACAxB,mBAAA,GAGAxB,WAAA,EACAsD,uBAAA,EACAC,iBAAA,KAGAnD,iBAAA,GACAK,gBAAA,GACAC,qBAAA,KAKAhC,SAAA,IACAgF,eAAA,8DACA5D,cACA,YAAAkD,eAAAnC,OAAA,QAAAW,mBAAAX,OAAA,GAEAE,sBACA,OACA,CACA4C,MAAA,IACAC,UAAA,QACAlE,IAAA,QACAmE,MAAA,GACAC,MAAA,SACAC,cAAA9C,EAAAC,EAAAY,IACAA,EAAA,GAGA,CACA6B,MAAA,KAAA/G,GAAA,qBACAgH,UAAA,kBACAlE,IAAA,kBACAmE,MAAA,IACArE,YAAA,CAAAuE,aAAA,oBAEA,CACAJ,MAAA,KAAA/G,GAAA,mBACAgH,UAAA,gBACAlE,IAAA,gBACAsE,UAAA,EACAH,MAAA,KAEA,CACAF,MAAA,KAAA/G,GAAA,oBACAgH,UAAA,iBACAlE,IAAA,iBACAmE,MAAA,IACArE,YAAA,CAAAuE,aAAA,mBAEA,CACAJ,MAAA,KAAA/G,GAAA,yBACAgH,UAAA,aACAlE,IAAA,aACAmE,MAAA,IACArE,YAAA,CAAAuE,aAAA,iBAIAzB,kBACA,OACA,CACAqB,MAAA,KAAA/G,GAAA,qBACAgH,UAAA,kBACAlE,IAAA,kBACAmE,MAAA,KAEA,CACAF,MAAA,KAAA/G,GAAA,mBACAgH,UAAA,gBACAlE,IAAA,gBACAsE,UAAA,GAEA,CACAL,MAAA,KAAA/G,GAAA,oBACAgH,UAAA,iBACAlE,IAAA,iBACAmE,MAAA,IAEA,CACAF,MAAA,KAAA/G,GAAA,wBACAgH,UAAA,qBACAlE,IAAA,qBACAsE,UAAA,KAIAtB,mBACA,OACA,CACAiB,MAAA,KAAA/G,GAAA,qBACAgH,UAAA,kBACAlE,IAAA,kBACAmE,MAAA,KAEA,CACAF,MAAA,KAAA/G,GAAA,mBACAgH,UAAA,gBACAlE,IAAA,gBACAsE,UAAA,GAEA,CACAL,MAAA,KAAA/G,GAAA,sCACAgH,UAAA,SACAlE,IAAA,SACAmE,MAAA,KAEA,CACAF,MAAA,KAAA/G,GAAA,uCACAgH,UAAA,UACAlE,IAAA,UACAsE,UAAA,MAMAC,UACA,KAAAC,qBACA,KAAAC,2BAEA,KAAAC,gBAAA,CACAC,QAAA,KAAAC,eACAC,YAAA,KAAAC,qBAMA7F,QAAA,CACA,2BACA,IACA,MAAAC,QAAA,KAAA6F,OAAA3F,IAAA,mBACAF,EAAAR,MAAAQ,EAAAR,KAAAyC,OAAA,IACA,KAAAmC,eAAApE,EAAAR,MAEA,MAAAY,GACAC,QAAAD,MAAA,YAAAA,KAIAmF,2BAEA,MAAAO,EAAA,2EACAC,EAAA,GAEAD,EAAAE,QAAA9G,IACA,MAAA+G,EAAA/G,EAAA,QACAgH,aAAAC,QAAAF,IACAF,EAAAK,KAAAlH,KAIA,KAAA0D,mBAAAmD,GAGA5E,oBACA,KAAAD,aAKA,KAAAsD,sBAAA,SAAA5B,oBACA,KAAAsB,sBAAA,EAEA,SAAAE,eAAAnC,SACA,KAAAkC,eAAA,KAAAC,eAAA,GAAAE,KARA+B,OAAAC,QAAA,aAYA,sBACA,QAAAnC,eAKA,YAAAK,sBAAAvC,OAAA,CAKA,KAAAhB,WAAA,EACA,KAAA6B,gBAAA,GAEA,IAEA,UAAAyD,KAAA,KAAA/B,sBAAA,CACA,MAAAgC,EAAA,KAAAC,iBAAAF,GAEA,GAAAC,EAAA,CACA,MAAAxG,QAAA,KAAA0G,MAAAC,KAAA,wCACAC,QAAA,KAAAzC,eACAhB,UAAAoD,EACAM,eAAAL,IAGA,KAAA1D,gBAAAsD,KAAApG,EAAAR,OAIA,KAAA0E,sBAAA,EACA,KAAAnB,WAAA,KAAAD,gBAAAgE,IAAA,CAAAC,EAAA7D,MAAA8D,YAEAX,OAAAY,QAAA,YAAAnE,gBAAAb,qBAEA,MAAA7B,GACAC,QAAAD,MAAA,QAAAA,GACAiG,OAAAjG,MAAA,aACA,QACA,KAAAa,WAAA,QAhCAoF,OAAAjG,MAAA,gBALAiG,OAAAjG,MAAA,UAyCAqG,iBAAAF,GACA,MAAAN,EAAAM,EAAA,QACA/G,EAAA0G,aAAAC,QAAAF,GACA,OAAAzG,EAAA0H,KAAAC,MAAA3H,GAAA,MAGAiF,YAAAvF,GACA,MAAAkI,EAAA,CACA,eACA,gBACA,gBACA,oBACA,YACA,kBACA,2BAEA,OAAAA,EAAAlI,OAGAoE,eAAAD,GACA,MAAA5C,EAAA,CACA,gBACA,iBACA,iBACA,aACA,YACA,aAEA,OAAAA,EAAA4C,IAAA,WAGAE,cAAAF,GACA,MAAAgE,EAAA,CACA,aACA,eACA,aACA,YACA,WACA,WAEA,OAAAA,EAAAhE,OAGAO,aAAAvB,EAAAiF,GACA,OAAAjF,EACAA,EAAAJ,OAAAqF,EAAAjF,EAAAkF,UAAA,EAAAD,GAAA,MAAAjF,EADA,IAIA0B,kBAAAzB,GACA,OAAAA,EAAAkF,SAAA,IAAAlF,EAAAkF,QAAAvF,OAIA,kFAGAK,EAAAkF,QAAAV,IAAA,CAAAW,EAAAvE,IAAA,8IAEAA,EAAA,qBAAAuE,EAAAC,kFACA,IAAAD,EAAAE,UAAA,kBAAAF,EAAAE,uCACAF,SAAA,yHAAAA,qBAAA,qBACAA,EAAArH,MAAA,qIAAAqH,EAAArH,kBAAA,sCAEAyC,KAAA,8BAbA,SAmBA,wBACA,QAAArB,iBAAAoG,OAAA,CAIA,KAAAxG,WAAA,EACA,IACA,MAAApB,QAAA,KAAA6F,OAAAc,KAAA,4CACAkB,MAAA,KAAArG,iBACAsG,MAAA,KAAAjG,gBACAkG,gBAAA,KAAAjG,uBAGA,YAAA9B,EAAAR,KAAA6D,QACA,KAAA2E,OAAAC,SAAA,2BAAAjI,EAAAR,KAAA0I,SACA7B,OAAAY,QAAA,KAAAjJ,GAAA,mCAAA+D,OAAA/B,EAAAR,KAAA0I,SAAA,IAAAjG,YAEAoE,OAAAjG,MAAAJ,EAAAR,KAAA6G,SAAA,KAAArI,GAAA,oCACA,KAAAgK,OAAAC,SAAA,qBAEA,MAAA7H,GACAC,QAAAD,MAAA,YAAAA,GACAiG,OAAAjG,MAAA,KAAApC,GAAA,mCACA,KAAAgK,OAAAC,SAAA,oBACA,QACA,KAAA7G,WAAA,QAvBAiF,OAAAC,QAAA,YA4BA9D,mBAAAF,GACA,KAAAqC,iBAAArC,EACA,KAAAoC,uBAAA,GAIAxC,qBACA,KAAA8F,OAAAC,SAAA,oBACA5B,OAAAY,QAAA,KAAAjJ,GAAA,uCAIA0C,mBAAAP,GACA,OAAAA,GAAA,aACAA,GAAA,aACA,WAIA1B,cAAA+B,GACA,MAAAC,EAAA,CACA,gBACA,mBACA,kBACA,iBACA,mBACA,SACA,YACA,UACA,WACA,WAEA,OAAAA,EAAAD,IAAA,aClnB8W,I,wBCQ1WG,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCLA,GACfiE,WAAA,CACAuD,2BChB2V,ICOvV,EAAY,eACd,EACA1K,EACAsB,GACA,EACA,KACA,KACA,MAIa,e", "file": "static/js/chunk-65340c0b.95205db6.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-modal',{attrs:{\"visible\":_vm.visible,\"title\":_vm.$t('testcase.detail'),\"width\":\"800px\",\"footer\":null},on:{\"cancel\":_vm.handleClose}},[(_vm.currentTestcase)?_c('a-descriptions',{attrs:{\"bordered\":\"\"}},[_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.number'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Number)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.name'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Name)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.level'),\"span\":3}},[_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(_vm.currentTestcase.Testcase_Level)}},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Level)+\" \")])],1),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.prepareCondition'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_PrepareCondition)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.testSteps'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_TestSteps)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.expectedResult'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_ExpectedResult)+\" \")])])],1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <a-modal\n    :visible=\"visible\"\n    :title=\"$t('testcase.detail')\"\n    width=\"800px\"\n    :footer=\"null\"\n    @cancel=\"handleClose\"\n  >\n    <a-descriptions v-if=\"currentTestcase\" bordered>\n      <a-descriptions-item :label=\"$t('caseColumn.number')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_Number }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.name')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_Name }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.level')\" :span=\"3\">\n        <a-tag :color=\"getLevelColor(currentTestcase.Testcase_Level)\">\n          {{ currentTestcase.Testcase_Level }}\n        </a-tag>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.prepareCondition')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_PrepareCondition }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.testSteps')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_TestSteps }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.expectedResult')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_ExpectedResult }}\n        </div>\n      </a-descriptions-item>\n    </a-descriptions>\n  </a-modal>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\n\nexport default {\n  name: 'TestCaseDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    testcase: {\n      type: Object,\n      default: null\n    },\n    // 是否需要通过API获取详细信息（当传入的testcase只有基本信息时）\n    fetchDetails: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      detailedTestcase: null\n    };\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal && this.fetchDetails && this.testcase && !this.detailedTestcase) {\n        this.fetchTestcaseDetails();\n      }\n    },\n    testcase() {\n      this.detailedTestcase = null;\n    }\n  },\n  computed: {\n    currentTestcase() {\n      return this.detailedTestcase || this.testcase;\n    }\n  },\n  methods: {\n    async fetchTestcaseDetails() {\n      if (!this.testcase || !this.testcase.Testcase_Number) return;\n      \n      this.loading = true;\n      try {\n        const response = await axios.get(`/api/testcase/${this.testcase.Testcase_Number}`);\n        this.detailedTestcase = {\n          ...response.data,\n          // 保留原有的相似度信息（如果有的话）\n          similarity: this.testcase.similarity\n        };\n      } catch (error) {\n        console.error('获取测试用例详情失败:', error);\n        this.$message.error('获取测试用例详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    handleClose() {\n      this.$emit('close');\n      this.detailedTestcase = null;\n    },\n    \n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange', \n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n        'P0': 'red',\n        'P1': 'orange',\n        'P2': 'blue', \n        'P3': 'green',\n        'P4': 'gray'\n      };\n      return colors[level] || 'default';\n    },\n    \n    getSimilarityColor(similarity) {\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\n      if (similarity >= 0.6) return '#faad14'; // 橙色\n      return '#f5222d'; // 红色\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.testcase-content {\n  white-space: pre-wrap;\n  word-break: break-word;\n  max-height: 300px;\n  overflow-y: auto;\n  padding: 12px;\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.75);\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCaseDetailModal.vue?vue&type=template&id=59bfc3d1&scoped=true\"\nimport script from \"./TestCaseDetailModal.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseDetailModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./TestCaseDetailModal.vue?vue&type=style&index=0&id=59bfc3d1&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59bfc3d1\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=style&index=0&id=1c34ca2e&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=style&index=0&id=59bfc3d1&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('SmartOrchestrationInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('smartOrchestration.smartAnalysis')))])]},proxy:true},{key:\"extra\",fn:function(){return [_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.analyzing,\"disabled\":!_vm.hasNodeData,\"icon\":\"branches\"},on:{\"click\":_vm.showAnalysisModal}},[_vm._v(\" \"+_vm._s(_vm.$t('smartOrchestration.startAnalysis'))+\" \")])]},proxy:true}])},[_c('a-row',{staticClass:\"mb-16\",attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":24}},[_c('a-card',{staticClass:\"query-card\",attrs:{\"title\":_vm.$t('smartOrchestration.caseAnalysis'),\"size\":\"small\"}},[_c('a-form',{attrs:{\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('smartOrchestration.naturalLanguageQuery')}},[_c('a-input-search',{attrs:{\"placeholder\":_vm.$t('smartOrchestration.queryPlaceholder'),\"enter-button\":_vm.$t('testcase.searchButton'),\"size\":\"large\",\"loading\":_vm.searching},on:{\"search\":_vm.searchTestcases},model:{value:(_vm.smartSearchQuery),callback:function ($$v) {_vm.smartSearchQuery=$$v},expression:\"smartSearchQuery\"}})],1),_c('a-form-item',[_c('a-row',{attrs:{\"gutter\":8}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":1,\"max\":50,\"placeholder\":_vm.$t('smartOrchestration.topK')},model:{value:(_vm.smartSearchTopK),callback:function ($$v) {_vm.smartSearchTopK=$$v},expression:\"smartSearchTopK\"}}),_c('div',{staticClass:\"param-label\"},[_vm._v(_vm._s(_vm.$t('smartOrchestration.topK')))])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"max\":1,\"step\":0.1,\"placeholder\":_vm.$t('smartOrchestration.scoreThreshold')},model:{value:(_vm.smartSearchThreshold),callback:function ($$v) {_vm.smartSearchThreshold=$$v},expression:\"smartSearchThreshold\"}}),_c('div',{staticClass:\"param-label\"},[_vm._v(_vm._s(_vm.$t('smartOrchestration.scoreThreshold')))])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.searching,\"block\":\"\",\"icon\":\"search\"},on:{\"click\":_vm.searchTestcases}},[_vm._v(\" \"+_vm._s(_vm.$t('testcase.searchButton'))+\" \")])],1)],1)],1)],1)],1)],1)],1),_c('a-row',{staticClass:\"mb-16\",attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":24}},[_c('a-card',{attrs:{\"title\":_vm.$t('smartOrchestration.searchResults'),\"size\":\"small\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('a-space',[_c('a-tag',{attrs:{\"color\":\"blue\"}},[_vm._v(_vm._s(_vm.$t('smartOrchestration.foundResults', { count: (_vm.smartSearchResults || []).length })))]),_c('a-button',{attrs:{\"type\":\"link\",\"size\":\"small\",\"icon\":\"close\"},on:{\"click\":_vm.clearSearchHistory}},[_vm._v(\" \"+_vm._s(_vm.$t('common.clear'))+\" \")])],1)]},proxy:true}])},[_c('a-table',{attrs:{\"columns\":_vm.searchResultColumns,\"data-source\":_vm.smartSearchResults,\"pagination\":false,\"size\":\"small\",\"scroll\":{ x: 800 }},scopedSlots:_vm._u([{key:\"Testcase_Number\",fn:function(text, record){return [_c('a',{staticStyle:{\"color\":\"#1890ff\",\"cursor\":\"pointer\"},on:{\"click\":function($event){return _vm.viewTestcaseDetail(record)}}},[_vm._v(\" \"+_vm._s(record.Testcase_Number)+\" \")])]}},{key:\"Testcase_Level\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(record.Testcase_Level)}},[_vm._v(\" \"+_vm._s(record.Testcase_Level)+\" \")])]}},{key:\"similarity\",fn:function(text, record){return [_c('a-progress',{attrs:{\"percent\":Math.round(record.similarity * 100),\"size\":\"small\",\"stroke-color\":_vm.getSimilarityColor(record.similarity)}}),_c('span',{staticStyle:{\"margin-left\":\"8px\",\"font-size\":\"12px\"}},[_vm._v(\" \"+_vm._s((record.similarity * 100).toFixed(1))+\"% \")])]}}])})],1)],1)],1),_c('a-row',{staticClass:\"mb-16\",attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":24}},[(!_vm.hasNodeData)?_c('a-alert',{staticClass:\"mb-16\",attrs:{\"message\":\"未检测到节点数据\",\"description\":\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\",\"type\":\"info\",\"show-icon\":\"\"}}):_c('a-alert',{staticClass:\"mb-16\",attrs:{\"message\":\"节点数据已就绪\",\"description\":`已检测到 ${(_vm.availableDataTypes || []).length} 种类型的数据：${(_vm.availableDataTypes || []).join('、')}`,\"type\":\"success\",\"show-icon\":\"\"}})],1)],1),((_vm.analysisResults || []).length > 0)?_c('div',[_c('a-divider',{attrs:{\"orientation\":\"left\"}},[_vm._v(\"分析结果\")]),_c('a-collapse',{staticClass:\"mb-16\",model:{value:(_vm.activeKeys),callback:function ($$v) {_vm.activeKeys=$$v},expression:\"activeKeys\"}},_vm._l((_vm.analysisResults),function(result,index){return _c('a-collapse-panel',{key:index,attrs:{\"header\":`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(result.status)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(result.status))+\" \")])]},proxy:true}],null,true)},[_c('a-descriptions',{staticClass:\"mb-16\",attrs:{\"title\":\"查询信息\",\"column\":1,\"size\":\"small\"}},[_c('a-descriptions-item',{attrs:{\"label\":\"信息类型\"}},[_vm._v(_vm._s(result.info_type))]),_c('a-descriptions-item',{attrs:{\"label\":\"查询文本\"}},[_vm._v(_vm._s(result.query_text))]),_c('a-descriptions-item',{attrs:{\"label\":\"匹配用例数\"}},[_vm._v(_vm._s((result.matched_testcases || []).length))])],1),_c('a-divider',{attrs:{\"orientation\":\"left\",\"orientation-margin\":\"0\"}},[_vm._v(\"匹配的测试用例\")]),_c('a-table',{staticClass:\"mb-16\",attrs:{\"dataSource\":result.matched_testcases,\"columns\":_vm.testcaseColumns,\"pagination\":false,\"size\":\"small\"},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'Testcase_Name')?[_c('a-tooltip',{attrs:{\"title\":record.Testcase_Name}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.Testcase_Name, 30)))])])]:_vm._e(),(column.key === 'Testcase_TestSteps')?[_c('a-tooltip',{attrs:{\"title\":record.Testcase_TestSteps}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.Testcase_TestSteps, 50)))])])]:_vm._e()]}}],null,true)}),_c('a-divider',{attrs:{\"orientation\":\"left\",\"orientation-margin\":\"0\"}},[_vm._v(\"执行结果\")]),_c('a-table',{attrs:{\"dataSource\":result.execution_results,\"columns\":_vm.executionColumns,\"pagination\":false,\"size\":\"small\",\"expandable\":{ expandedRowRender: _vm.expandedRowRender }},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'status')?[_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.status)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.status))+\" \")])]:_vm._e(),(column.key === 'testcase_name')?[_c('a-tooltip',{attrs:{\"title\":record.testcase_name}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.testcase_name, 30)))])])]:_vm._e()]}}],null,true)})],1)}),1)],1):_vm._e(),_c('a-modal',{attrs:{\"title\":\"智能测试用例分析配置\",\"width\":800,\"confirmLoading\":_vm.analyzing},on:{\"ok\":_vm.startAnalysis},model:{value:(_vm.analysisModalVisible),callback:function ($$v) {_vm.analysisModalVisible=$$v},expression:\"analysisModalVisible\"}},[_c('a-form',{attrs:{\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":\"选择节点\",\"required\":\"\"}},[_c('a-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择要分析的节点\"},model:{value:(_vm.selectedNodeId),callback:function ($$v) {_vm.selectedNodeId=$$v},expression:\"selectedNodeId\"}},_vm._l((_vm.availableNodes),function(node){return _c('a-select-option',{key:node.id,attrs:{\"value\":node.id}},[_vm._v(\" \"+_vm._s(node.name)+\" (\"+_vm._s(node.ip)+\") \")])}),1)],1),_c('a-form-item',{attrs:{\"label\":\"选择分析类型\",\"required\":\"\"}},[_c('a-checkbox-group',{model:{value:(_vm.selectedAnalysisTypes),callback:function ($$v) {_vm.selectedAnalysisTypes=$$v},expression:\"selectedAnalysisTypes\"}},[_c('a-row',_vm._l((_vm.availableDataTypes),function(type){return _c('a-col',{key:type,attrs:{\"span\":8}},[_c('a-checkbox',{attrs:{\"value\":type}},[_vm._v(_vm._s(_vm.getTypeName(type)))])],1)}),1)],1)],1)],1)],1),_c('TestCaseDetailModal',{attrs:{\"visible\":_vm.testcaseDetailVisible,\"testcase\":_vm.selectedTestcase},on:{\"close\":function($event){_vm.testcaseDetailVisible = false}}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">{{ $t('smartOrchestration.smartAnalysis') }}</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n        icon=\"branches\"\r\n      >\r\n        {{ $t('smartOrchestration.startAnalysis') }}\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.caseAnalysis')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model=\"smartSearchQuery\"\r\n                :placeholder=\"$t('smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item>\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchTopK\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchThreshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block icon=\"search\">\r\n                    {{ $t('testcase.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('smartOrchestration.foundResults', { count: (smartSearchResults || []).length }) }}</a-tag>\r\n              <a-button \r\n              type=\"link\"\r\n              size=\"small\" \r\n              @click=\"clearSearchHistory\"\r\n              icon=\"close\"\r\n              >\r\n              {{ $t('common.clear') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"smartSearchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${(availableDataTypes || []).length} 种类型的数据：${(availableDataTypes || []).join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"(analysisResults || []).length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ (result.matched_testcases || []).length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      searching: false,\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      // 本地查询参数状态\r\n      smartSearchQuery: '',\r\n      smartSearchTopK: 10,\r\n      smartSearchThreshold: 0.5,\r\n      \r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    ...mapState(['currentProject', 'currentProjectName', 'smartSearchResults']),\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    },\r\n    searchResultColumns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 60,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ];\r\n    },\r\n    testcaseColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n    executionColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionStatus'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionMessage'),\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    // 初始化时记录当前项目信息\r\n    this.lastProjectInfo = {\r\n      project: this.currentProject,\r\n      projectName: this.currentProjectName\r\n    };\r\n  },\r\n  \r\n  // 移除watch，因为现在项目状态是自动隔离的\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$axios.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.smartSearchQuery.trim()) {\r\n        message.warning('请输入查询内容');\r\n        return;\r\n      }\r\n      this.searching = true;\r\n      try {\r\n        const response = await this.$axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.smartSearchQuery,\r\n          top_k: this.smartSearchTopK,\r\n          score_threshold: this.smartSearchThreshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.$store.dispatch('updateSmartSearchResults', response.data.results);\r\n          message.success(this.$t('smartOrchestration.foundResults', { count: (response.data.results || []).length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('smartOrchestration.searchFailed'));\r\n          this.$store.dispatch('clearSmartSearch');\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('smartOrchestration.searchError'));\r\n        this.$store.dispatch('clearSmartSearch');\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.$store.dispatch('clearSmartSearch');\r\n      message.success(this.$t('smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SmartOrchestrationInfo.vue?vue&type=template&id=1c34ca2e&scoped=true\"\nimport script from \"./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"\nexport * from \"./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./SmartOrchestrationInfo.vue?vue&type=style&index=0&id=1c34ca2e&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1c34ca2e\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<!-- 智能测试用例分析 -->\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<SmartOrchestrationInfo></SmartOrchestrationInfo>\r\n\t\t\t</a-col>\r\n\t\t</a-row>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport SmartOrchestrationInfo from \"@/components/Cards/SmartOrchestrationInfo\";\r\n\r\nexport default {\r\n    components: {\r\n        SmartOrchestrationInfo,\r\n    },\r\n};\r\n</script> ", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestration.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestration.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SmartOrchestration.vue?vue&type=template&id=57b6b0c4\"\nimport script from \"./SmartOrchestration.vue?vue&type=script&lang=js\"\nexport * from \"./SmartOrchestration.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}