{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue?vue&type=template&id=16fde50e&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue", "mtime": 1753169785200}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgW19jKCJhLWNhcmQiLCB7CiAgICBhdHRyczogewogICAgICB0aXRsZTogIumhueebruWIl+ihqCIKICAgIH0sCiAgICBzY29wZWRTbG90czogX3ZtLl91KFt7CiAgICAgIGtleTogImV4dHJhIiwKICAgICAgZm46IGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gW19jKCJhLWJ1dHRvbiIsIHsKICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgIHR5cGU6ICJwcmltYXJ5IgogICAgICAgICAgfSwKICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgIGNsaWNrOiBfdm0uY3JlYXRlTmV3UHJvamVjdAogICAgICAgICAgfQogICAgICAgIH0sIFtfdm0uX3YoIiDliJvlu7rmlrDpobnnm64gIildKV07CiAgICAgIH0sCiAgICAgIHByb3h5OiB0cnVlCiAgICB9XSkKICB9LCBbX2MoImEtdGFibGUiLCB7CiAgICBzdGF0aWNDbGFzczogInByb2plY3QtdGFibGUiLAogICAgYXR0cnM6IHsKICAgICAgY29sdW1uczogX3ZtLmNvbHVtbnMsCiAgICAgICJkYXRhLXNvdXJjZSI6IF92bS5wcm9qZWN0cywKICAgICAgInJvdy1rZXkiOiByZWNvcmQgPT4gcmVjb3JkLmRiRmlsZSwKICAgICAgY3VzdG9tUm93OiBfdm0ub25DdXN0b21Sb3cKICAgIH0KICB9KV0sIDEpXSwgMSk7Cn07CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXTsKcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlOwpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9Ow=="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "title", "scopedSlots", "_u", "key", "fn", "type", "on", "click", "createNewProject", "_v", "proxy", "staticClass", "columns", "projects", "record", "dbFile", "customRow", "onCustomRow", "staticRenderFns", "_withStripped"], "sources": ["D:/_Projects_python/Tools/console-vue2/muse-vue-ant-design-dashboard-main/src/views/ProjectManager.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"a-card\",\n        {\n          attrs: { title: \"项目列表\" },\n          scopedSlots: _vm._u([\n            {\n              key: \"extra\",\n              fn: function() {\n                return [\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.createNewProject }\n                    },\n                    [_vm._v(\" 创建新项目 \")]\n                  )\n                ]\n              },\n              proxy: true\n            }\n          ])\n        },\n        [\n          _c(\"a-table\", {\n            staticClass: \"project-table\",\n            attrs: {\n              columns: _vm.columns,\n              \"data-source\": _vm.projects,\n              \"row-key\": record => record.dbFile,\n              customRow: _vm.onCustomRow\n            }\n          })\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,QAAQ,EACR;IACEE,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBC,WAAW,EAAEL,GAAG,CAACM,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,OAAO;MACZC,EAAE,EAAE,SAAAA,CAAA,EAAW;QACb,OAAO,CACLP,EAAE,CACA,UAAU,EACV;UACEE,KAAK,EAAE;YAAEM,IAAI,EAAE;UAAU,CAAC;UAC1BC,EAAE,EAAE;YAAEC,KAAK,EAAEX,GAAG,CAACY;UAAiB;QACpC,CAAC,EACD,CAACZ,GAAG,CAACa,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,CACF;MACH,CAAC;MACDC,KAAK,EAAE;IACT,CAAC,CACF;EACH,CAAC,EACD,CACEb,EAAE,CAAC,SAAS,EAAE;IACZc,WAAW,EAAE,eAAe;IAC5BZ,KAAK,EAAE;MACLa,OAAO,EAAEhB,GAAG,CAACgB,OAAO;MACpB,aAAa,EAAEhB,GAAG,CAACiB,QAAQ;MAC3B,SAAS,EAAEC,MAAM,IAAIA,MAAM,CAACC,MAAM;MAClCC,SAAS,EAAEpB,GAAG,CAACqB;IACjB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBvB,MAAM,CAACwB,aAAa,GAAG,IAAI;AAE3B,SAASxB,MAAM,EAAEuB,eAAe", "ignoreList": []}]}