{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue", "mtime": 1753170222127}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gJ3Z1ZXgnOw0KaW1wb3J0IGF4aW9zIGZyb20gJ0AvYXBpL2F4aW9zSW5zdGFuY2UnOw0KaW1wb3J0IFJlZnJlc2hCdXR0b24gZnJvbSAnLi4vV2lkZ2V0cy9SZWZyZXNoQnV0dG9uLnZ1ZSc7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIFJlZnJlc2hCdXR0b24NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFja2FnZXM6IFtdLA0KICAgICAgY29sdW1uczogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICdQYWNrYWdlIE5hbWUnLA0KICAgICAgICAgIGRhdGFJbmRleDogJ3BhY2thZ2VfbmFtZScsDQogICAgICAgICAga2V5OiAncGFja2FnZV9uYW1lJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAnUGFja2FnZSBUeXBlJywNCiAgICAgICAgICBkYXRhSW5kZXg6ICdwYWNrYWdlX3R5cGUnLA0KICAgICAgICAgIGtleTogJ3BhY2thZ2VfdHlwZScsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgcGFnaW5hdGlvbjogew0KICAgICAgICBwYWdlU2l6ZTogMTAwLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC4uLm1hcFN0YXRlKFsnc2VsZWN0ZWROb2RlSXAnLCAnY3VycmVudFByb2plY3QnLCAnc2lkZWJhckNvbG9yJ10pLA0KICB9LA0KICB3YXRjaDogew0KICAgIHNlbGVjdGVkTm9kZUlwKG5ld0lwKSB7DQogICAgICAvLyDlvZMgc2VsZWN0ZWROb2RlSXAg5Y+Y5YyW5pe26YeN5paw6I635Y+W5YyF5pWw5o2uDQogICAgICB0aGlzLmZldGNoUGFja2FnZXMoKTsNCiAgICB9LA0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZmV0Y2hQYWNrYWdlcygpOyAvLyDliJ3lp4vliqDovb3ml7bosIPnlKgNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGZldGNoUGFja2FnZXMoKSB7DQogICAgICBjb25zb2xlLmxvZygnU2VsZWN0ZWQgTm9kZSBJUDonLCB0aGlzLnNlbGVjdGVkTm9kZUlwKTsNCiAgICAgIGlmICghdGhpcy5zZWxlY3RlZE5vZGVJcCkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdOb2RlIElQIGlzIG5vdCBkZWZpbmVkJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAvYXBpL3BhY2thZ2VzLyR7dGhpcy5zZWxlY3RlZE5vZGVJcH1gLCB7DQogICAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgICBkYkZpbGU6IHRoaXMuY3VycmVudFByb2plY3QgLy8g5re75YqgIGRiRmlsZSDlj4LmlbANCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLnBhY2thZ2VzID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBhY2thZ2VzOicsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["PackageInfo.vue"], "names": [], "mappings": ";AAkDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PackageInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <!-- Packages Table Card -->\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n                <path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\" clip-rule=\"evenodd\"/>\r\n              </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.package') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchPackages\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"packages\"\r\n      :rowKey=\"(record) => record.package_name\"\r\n      :pagination=\"pagination\"\r\n    >\r\n       <template #bodyCell=\"{ column, record }\">\r\n        <template v-if=\"column.key === 'package_name'\">\r\n          <div class=\"table-package-info\">\r\n            <span>{{ record.package_name }}</span>\r\n            <span>{{ record.package_type }}</span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"column.key === 'action'\">\r\n          <a-button type=\"link\" class=\"btn-edit\">Edit</a-button>\r\n        </template>\r\n      </template>\r\n    </a-table>\r\n  </a-card>\r\n  <!-- / Packages Table Card -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      packages: [],\r\n      columns: [\r\n        {\r\n          title: 'Package Name',\r\n          dataIndex: 'package_name',\r\n          key: 'package_name',\r\n        },\r\n        {\r\n          title: 'Package Type',\r\n          dataIndex: 'package_type',\r\n          key: 'package_type',\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      // 当 selectedNodeIp 变化时重新获取包数据\r\n      this.fetchPackages();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchPackages(); // 初始加载时调用\r\n  },\r\n  methods: {\r\n    async fetchPackages() {\r\n      console.log('Selected Node IP:', this.selectedNodeIp);\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/packages/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject // 添加 dbFile 参数\r\n          }\r\n        });\r\n        this.packages = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching packages:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"]}]}