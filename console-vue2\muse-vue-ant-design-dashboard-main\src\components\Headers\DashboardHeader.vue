<template>
	<!-- Layout Header's Conditionally Fixed Wrapper -->
	<component :is="navbarFixed ? 'a-affix' : 'div'" :offset-top="top">
		<!-- Layout Header -->
		<a-layout-header>
			<a-row type="flex">
				<!-- Header Breadcrumbs & Title Column -->
				<a-col :span="24" :md="12">
						<!-- Header Navigation Buttons -->
						<div class="header-nav-buttons">

							<!-- Home Button -->
							<router-link to="/" class="header-nav-button" :class="[{ 'active': $route.path === '/' }, $route.path === '/' ? `bg-${sidebarColor} nav-btn-transparent` : '']">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" width="17" height="17" style="margin-right: 7px">
                                    <path d="M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z" fill="currentColor"/>
                                </svg>
                                <span class="label">{{ $t('common.home') }}</span>
							</router-link>
              <!-- Project Name Display (when project is selected) -->
							<div v-if="currentProject" class="project-name-display">
								<span class="project-name-text">{{ getDisplayProjectName() }}</span>
							</div>
							<!-- Task Panel Button -->
							<router-link to="/task" class="header-nav-button" :class="[{ 'active': $route.path.includes('/task') }, $route.path.includes('/task') ? `bg-${sidebarColor} nav-btn-transparent` : '']">
								<span class="label">{{ $t('sidebar.taskPanel') }}</span>
							</router-link>

							<!-- Configuration Button -->
							<router-link :to="{ path: '/config', hash: '#host' }" class="header-nav-button" :class="[{ 'active': $route.path.includes('/config') }, $route.path.includes('/config') ? `bg-${sidebarColor} nav-btn-transparent` : '']">
								<span class="label">{{ $t('sidebar.hostConfig') }}</span>
							</router-link>

							<!-- Repository Configuration Button -->
							<router-link to="/repository" class="header-nav-button" :class="[{ 'active': $route.path.includes('/repository') }, $route.path.includes('/repository') ? `bg-${sidebarColor} nav-btn-transparent` : '']">
								<span class="label">{{ $t('sidebar.repositoryConfig') }}</span>
							</router-link>

							<!-- Tools Button -->
							<router-link to="/tools" class="header-nav-button" :class="[{ 'active': $route.path.includes('/tools') }, $route.path.includes('/tools') ? `bg-${sidebarColor} nav-btn-transparent` : '']">
								<span class="label">{{ $t('sidebar.toolPanel') }}</span>
							</router-link>

						</div>
				</a-col>
				<!-- / Header Breadcrumbs & Title Column -->
				<!-- Header Control Column -->
				<a-col :span="24" :md="12" class="header-control">
					<!-- Language Switcher -->
          <log-viewer />
					<a-dropdown placement="bottomRight">
						<a class="language-switcher" @click="e => e.preventDefault()">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
								<circle cx="12" cy="12" r="10"></circle>
								<line x1="2" y1="12" x2="22" y2="12"></line>
								<path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
							</svg>
							<span class="language-text">{{ currentLanguageLabel }}</span>
						</a>
						<a-menu slot="overlay">
							<a-menu-item key="en-US" @click="changeLanguage('en-US')">
								<span :class="{'active-language': language === 'en-US'}">English</span>
							</a-menu-item>
							<a-menu-item key="zh-CN" @click="changeLanguage('zh-CN')">
								<span :class="{'active-language': language === 'zh-CN'}">中文</span>
							</a-menu-item>
						</a-menu>
					</a-dropdown>

					<!-- Header Control Buttons -->
					<notification-button />
					<theme-toggle-button />
					<a-button type="link" ref="secondarySidebarTriggerBtn" @click="$emit('toggleSettingsDrawer', true)">
						<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M11.4892 3.17094C11.1102 1.60969 8.8898 1.60969 8.51078 3.17094C8.26594 4.17949 7.11045 4.65811 6.22416 4.11809C4.85218 3.28212 3.28212 4.85218 4.11809 6.22416C4.65811 7.11045 4.17949 8.26593 3.17094 8.51078C1.60969 8.8898 1.60969 11.1102 3.17094 11.4892C4.17949 11.7341 4.65811 12.8896 4.11809 13.7758C3.28212 15.1478 4.85218 16.7179 6.22417 15.8819C7.11045 15.3419 8.26594 15.8205 8.51078 16.8291C8.8898 18.3903 11.1102 18.3903 11.4892 16.8291C11.7341 15.8205 12.8896 15.3419 13.7758 15.8819C15.1478 16.7179 16.7179 15.1478 15.8819 13.7758C15.3419 12.8896 15.8205 11.7341 16.8291 11.4892C18.3903 11.1102 18.3903 8.8898 16.8291 8.51078C15.8205 8.26593 15.3419 7.11045 15.8819 6.22416C16.7179 4.85218 15.1478 3.28212 13.7758 4.11809C12.8896 4.65811 11.7341 4.17949 11.4892 3.17094ZM10 13C11.6569 13 13 11.6569 13 10C13 8.34315 11.6569 7 10 7C8.34315 7 7 8.34315 7 10C7 11.6569 8.34315 13 10 13Z" fill="#111827"/>
						</svg>
					</a-button>
					<a-button type="link" class="sidebar-toggler" @click="$emit('toggleSidebar', ! sidebarCollapsed) , resizeEventHandler()">
						<svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"/></svg>
					</a-button>
					<!-- / Header Control Buttons -->

					<!-- Header Search Input -->
          <a-col :span="24" :md="6" class="header-control">
            <div class="node-selector">
              <template v-if="currentProject">

                <a-dropdown :trigger="['click']">
                  <a class="ant-dropdown-link node-selector-link" @click="e => e.preventDefault()">

                    <span class="node-name">
                      {{ selectedNode ? selectedNode.ip : $t('common.selectNode') }}
                    </span>
                    <a-icon type="down" />
                  </a>
                  <template #overlay>
                    <a-menu class="node-menu">
                      <a-menu-item v-for="node in nodes" :key="node.ip" @click="selectNode(node)">
                        <div class="node-menu-item">
                          <div class="ip-address">{{ node.ip }}</div>
                          <div class="host-name" :title="node.host_name">{{ node.host_name }}</div>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>

              </template>
              <template v-else>
                <a class="ant-dropdown-link node-selector-link" @click="goToProjects">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 7H7V5h6v2zm0 4H7V9h6v2zm0 4H7v-2h6v2z" fill="#111827"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2 4a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0h12v12H4V4z" fill="#111827"/>
                  </svg>
                  <span>{{ $t('common.selectProject') }}</span>
                </a>
              </template>
            </div>
          </a-col>
				</a-col>
				<!-- / Header Control Column -->
			</a-row>
		</a-layout-header>
		<!--  /Layout Header -->
	</component>
	<!-- / Main Sidebar -->
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
import NotificationButton from '@/components/Widgets/NotificationButton.vue';
import LogViewer from '@/components/Widgets/LogViewer.vue';
import ThemeToggleButton from '@/components/Widgets/ThemeToggleButton.vue';
import axios from '@/api/axiosInstance';

export default {
    components: {
        NotificationButton,
        LogViewer,
        ThemeToggleButton,
    },
    props: {
        navbarFixed: { type: Boolean, default: false },
        sidebarCollapsed: { type: Boolean, default: false },
        sidebarColor: { type: String, default: 'primary' },
    },
    data() {
        return {
            top: 0,
            searchLoading: false,
            wrapper: document.body,
            selectedNode: null,
        };
    },
    computed: {
        ...mapState(['nodes', 'currentProject', 'currentProjectName', 'selectedNodeIp', 'language']),
        currentLanguageLabel() {
            return this.language === 'zh-CN' ? '中文' : 'English';
        }
    },
    methods: {
        ...mapMutations(['setSelectedNodeIp']),
        ...mapActions(['updateLanguage']),

        changeLanguage(lang) {
            this.updateLanguage(lang);
            this.$i18n.locale = lang;
        },
        selectNode(node) {
            this.selectedNode = node;
            this.setSelectedNodeIp(node.ip);
            this.goToProcessInfo(node.ip);
        },
        goToProcessInfo() {
            const targetRoute = '/task';
            if (this.$route.path !== targetRoute) {
                this.$router.push(targetRoute).catch(err => {
                    if (err.name !== 'NavigationDuplicated') {
                        console.error(err);
                    }
                });
            }
        },
        goToProjects() {
            this.$router.push('/projects');
        },
        updateSelectedNode() {
            if (this.selectedNodeIp && this.nodes?.length) {
                this.selectedNode = this.nodes.find(node => node.ip === this.selectedNodeIp);
            } else if (this.nodes?.length) {
                // If no node is selected but nodes are available, select the first one
                this.selectNode(this.nodes[0]);
            } else {
                this.selectedNode = null;
            }
        },
        async initializeNodesData() {
            if (this.currentProject) {
                await this.$store.dispatch('fetchNodes');
            }
        },
        // 获取项目显示名称
        getDisplayProjectName() {
            if (this.currentProjectName) {
                return this.currentProjectName;
            }
            return '';
        },
    },
    watch: {
        currentProject: {
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    this.initializeNodesData();
                } else {
                    // Clear nodes when no project is selected
                    this.$store.commit('setNodes', []);
                    this.selectedNode = null;
                    this.setSelectedNodeIp(null);
                }
            }
        },
        nodes: {
            immediate: true,
            handler(newVal) {
                if (newVal?.length) {
                    this.updateSelectedNode();
                } else {
                    this.selectedNode = null;
                    this.setSelectedNodeIp(null);
                }
            }
        }
    },
    mounted() {
        this.wrapper = document.getElementById('layout-dashboard');
        if (this.currentProject) {
            this.initializeNodesData();
        }
    },
    activated() {
        if (this.currentProject) {
            this.initializeNodesData();
        }
    }
};
</script>


