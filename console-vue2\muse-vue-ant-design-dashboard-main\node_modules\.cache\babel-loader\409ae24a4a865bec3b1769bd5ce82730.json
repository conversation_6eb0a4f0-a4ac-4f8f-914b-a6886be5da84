{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue", "mtime": 1753170781740}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "axios", "RefreshButton", "components", "data", "h", "$createElement", "processes", "columns", "title", "dataIndex", "key", "width", "ellipsis", "align", "customRender", "_", "record", "sidebarColor", "e", "stopPropagation", "showAIAnalysis", "pagination", "pageSize", "current", "onChange", "page", "$store", "dispatch", "aiAnalysisVisible", "aiAnalysisLoading", "aiAnalysisResult", "aiAnalysisError", "selectedProcess", "computed", "watch", "selectedNodeIp", "handler", "fetchProcesses", "immediate", "currentPage", "newPage", "mounted", "$nextTick", "scrollPosition", "setTimeout", "window", "scrollTo", "top", "behavior", "lastViewedPid", "applyHighlight", "updated", "length", "beforeRouteLeave", "to", "_from", "next", "name", "methods", "console", "error", "response", "get", "params", "fields", "dbFile", "currentProject", "rowClick", "_index", "isLastViewed", "pid", "on", "click", "viewProcessDetails", "class", "style", "cursor", "process", "pageYOffset", "document", "documentElement", "scrollTop", "body", "$router", "push", "requestAIAnalysis", "rows", "querySelectorAll", "i", "row", "cells", "textContent", "includes", "classList", "add", "j", "cell", "backgroundColor", "borderTop", "borderBottom", "borderLeft", "borderRight", "handleAIAnalysisClose", "processData", "aiResponse", "post", "process_data", "process_list", "timeout", "success", "analysis", "_error$response", "message", "formatMarkdown", "markdown", "formatted", "replace", "match", "trim"], "sources": ["src/components/Cards/ProcessInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full process-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.process') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchProcesses\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"processes\"\r\n      :rowKey=\"record => record.pid\"\r\n      :customRow=\"rowClick\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #emptyText>\r\n        <a-empty description=\"No processes found\" />\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- AI分析模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"aiAnalysisVisible\"\r\n      title=\"AI Security Analysis\"\r\n      width=\"800px\"\r\n      @cancel=\"handleAIAnalysisClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleAIAnalysisClose\">Close</a-button>\r\n      </template>\r\n\r\n      <div class=\"ai-analysis-container\">\r\n        <div v-if=\"selectedProcess\" class=\"process-info\">\r\n          <p><strong>PID:</strong> {{ selectedProcess.pid }}</p>\r\n        </div>\r\n\r\n        <a-skeleton :loading=\"aiAnalysisLoading\" active v-if=\"aiAnalysisLoading\" />\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisResult\" class=\"analysis-results\">\r\n          <div v-html=\"formatMarkdown(aiAnalysisResult)\" class=\"markdown-content\"></div>\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && !aiAnalysisResult && !aiAnalysisError\" class=\"no-analysis\">\r\n          <a-empty description=\"Analyzing process...\" />\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisError\" class=\"analysis-error\">\r\n          <a-alert\r\n            message=\"Analysis Error\"\r\n            :description=\"aiAnalysisError\"\r\n            type=\"error\"\r\n            show-icon\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      processes: [],\r\n      columns: [\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'Process Name',\r\n          dataIndex: 'cmdline',\r\n          key: 'cmdline',\r\n          width: '95%',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'AI Analysis',\r\n          key: 'ai_analysis',\r\n          width: '15%',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <a-button\r\n              class={`bg-${this.sidebarColor}`}\r\n              style=\"color: white\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                this.showAIAnalysis(record);\r\n              }}\r\n            >\r\n              Analyze\r\n            </a-button>\r\n          ),\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n        // 使用计算属性来绑定当前页码\r\n        current: 1,\r\n        // 添加分页变化事件处理\r\n        onChange: (page) => {\r\n          // 当页码变化时，更新Vuex中的页码状态\r\n          this.$store.dispatch('processList/updateCurrentPage', page);\r\n        }\r\n      },\r\n      // AI分析相关\r\n      aiAnalysisVisible: false,\r\n      aiAnalysisLoading: false,\r\n      aiAnalysisResult: null,\r\n      aiAnalysisError: null,\r\n      selectedProcess: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    ...mapState('processList', ['currentPage', 'scrollPosition', 'lastViewedPid']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp: {\r\n      handler() {\r\n        this.fetchProcesses();\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听Vuex中的currentPage变化，同步到分页组件\r\n    currentPage: {\r\n      handler(newPage) {\r\n        if (newPage && this.pagination.current !== newPage) {\r\n          this.pagination.current = newPage;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchProcesses();\r\n\r\n    // 恢复之前保存的滚动位置和应用高亮效果\r\n    this.$nextTick(() => {\r\n      // 恢复滚动位置\r\n      if (this.scrollPosition > 0) {\r\n        setTimeout(() => {\r\n          window.scrollTo({\r\n            top: this.scrollPosition,\r\n            behavior: 'auto'\r\n          });\r\n        }, 100);\r\n      }\r\n\r\n      // 如果有lastViewedPid，尝试应用高亮效果\r\n      if (this.lastViewedPid) {\r\n        setTimeout(this.applyHighlight, 500); // 延迟确保表格已渲染\r\n      }\r\n    });\r\n  },\r\n\r\n  updated() {\r\n    // 只有当processes数组有内容且有lastViewedPid时才应用高亮\r\n    if (this.lastViewedPid && this.processes.length > 0) {\r\n      this.applyHighlight();\r\n    }\r\n  },\r\n  // 当用户离开进程列表页面但不是通过点击进程详情时，清除保存的滚动位置和分页信息\r\n  beforeRouteLeave(to, _from, next) {\r\n    // 如果不是导航到进程详情页面，则清除滚动位置和分页信息\r\n    if (to.name !== 'ProcessDetail') {\r\n      this.$store.dispatch('processList/resetState');\r\n      // 清除最后查看的进程ID\r\n      this.$store.dispatch('processList/clearLastViewedPid');\r\n    }\r\n    next();\r\n  },\r\n  methods: {\r\n    async fetchProcesses() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            fields: 'pid,cmdline',\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.processes = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching processes:', error);\r\n      }\r\n    },\r\n    rowClick(record, _index) {\r\n      // 检查是否是最后查看的进程\r\n      const isLastViewed = this.lastViewedPid && record.pid === this.lastViewedPid;\r\n\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            this.viewProcessDetails(record);\r\n          },\r\n        },\r\n        class: {\r\n          // 如果是最后查看的进程，添加高亮类\r\n          'last-viewed-row': isLastViewed\r\n        },\r\n        style: {\r\n          cursor: 'pointer'\r\n        }\r\n      };\r\n    },\r\n    viewProcessDetails(process) {\r\n      // 保存当前滚动位置到Vuex store\r\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n      this.$store.dispatch('processList/updateScrollPosition', scrollPosition);\r\n\r\n      // 保存当前查看的进程ID\r\n      this.$store.dispatch('processList/updateLastViewedPid', process.pid);\r\n\r\n      // 导航到进程详情页面\r\n      this.$router.push({ name: 'ProcessDetail', params: { pid: process.pid } });\r\n    },\r\n\r\n    // AI分析相关方法\r\n    showAIAnalysis(process) {\r\n      this.selectedProcess = process;\r\n      this.aiAnalysisVisible = true;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n\r\n      // 自动开始分析\r\n      this.requestAIAnalysis();\r\n    },\r\n\r\n    // 应用高亮效果到最后查看的进程行\r\n    applyHighlight() {\r\n      if (!this.lastViewedPid) return;\r\n\r\n      // 尝试查找包含lastViewedPid的行\r\n      const rows = document.querySelectorAll('.ant-table-row');\r\n      for (let i = 0; i < rows.length; i++) {\r\n        const row = rows[i];\r\n        const cells = row.querySelectorAll('td');\r\n\r\n        // 检查第一个单元格（PID列）是否包含lastViewedPid\r\n        if (cells.length > 0 && cells[0].textContent.includes(this.lastViewedPid)) {\r\n          // 添加高亮类\r\n          row.classList.add('last-viewed-row');\r\n\r\n          // 直接设置样式以确保高亮效果生效\r\n          for (let j = 0; j < cells.length; j++) {\r\n            const cell = cells[j];\r\n            cell.style.backgroundColor = '#f5f5f5';\r\n            cell.style.borderTop = '1px solid #1890ff';\r\n            cell.style.borderBottom = '1px solid #1890ff';\r\n\r\n            // 为第一个单元格添加左侧边框\r\n            if (j === 0) {\r\n              cell.style.borderLeft = '3px solid #1890ff';\r\n            }\r\n\r\n            // 为最后一个单元格添加右侧边框\r\n            if (j === cells.length - 1) {\r\n              cell.style.borderRight = '3px solid #1890ff';\r\n            }\r\n          }\r\n\r\n          // 找到后退出循环\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    handleAIAnalysisClose() {\r\n      this.aiAnalysisVisible = false;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n    },\r\n\r\n    async requestAIAnalysis() {\r\n      if (!this.selectedProcess) {\r\n        this.aiAnalysisError = \"No process selected for analysis\";\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedNodeIp) {\r\n        this.aiAnalysisError = \"No node selected\";\r\n        return;\r\n      }\r\n\r\n      this.aiAnalysisLoading = true;\r\n      this.aiAnalysisError = null;\r\n\r\n      try {\r\n        // 获取完整的进程信息\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            pid: this.selectedProcess.pid,\r\n            fields: 'pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps',\r\n            dbFile: this.currentProject || ''\r\n          }\r\n        });\r\n\r\n        const processData = response.data;\r\n\r\n        // 调用AI分析API，设置更长的超时时间\r\n        const aiResponse = await axios.post('/api/ai/analyze/process', {\r\n          process_data: {\r\n            process_list: [processData]\r\n          }\r\n        }, {\r\n          timeout: 600000 // 10分钟超时\r\n        });\r\n\r\n        if (aiResponse.data.success) {\r\n          this.aiAnalysisResult = aiResponse.data.analysis;\r\n        } else {\r\n          this.aiAnalysisError = aiResponse.data.error || \"Failed to analyze process data\";\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI analysis error:\", error);\r\n        this.aiAnalysisError = error.response?.data?.error || error.message || \"An error occurred during analysis\";\r\n      } finally {\r\n        this.aiAnalysisLoading = false;\r\n      }\r\n    },\r\n\r\n    formatMarkdown(markdown) {\r\n      // 简单的Markdown格式化，可以使用更复杂的库如marked.js\r\n      if (!markdown) return '';\r\n\r\n      // 替换标题\r\n      let formatted = markdown\r\n        .replace(/^# (.*$)/gm, '<h1>$1</h1>')\r\n        .replace(/^## (.*$)/gm, '<h2>$1</h2>')\r\n        .replace(/^### (.*$)/gm, '<h3>$1</h3>')\r\n        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')\r\n        .replace(/^##### (.*$)/gm, '<h5>$1</h5>')\r\n        .replace(/^###### (.*$)/gm, '<h6>$1</h6>');\r\n\r\n      // 替换粗体和斜体\r\n      formatted = formatted\r\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n        .replace(/__(.*?)__/g, '<strong>$1</strong>')\r\n        .replace(/_(.*?)_/g, '<em>$1</em>');\r\n\r\n      // 替换代码块\r\n      formatted = formatted.replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>');\r\n\r\n      // 替换行内代码\r\n      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');\r\n\r\n      // 替换列表\r\n      formatted = formatted\r\n        .replace(/^\\s*\\d+\\.\\s+(.*$)/gm, '<li>$1</li>')\r\n        .replace(/^\\s*[-*]\\s+(.*$)/gm, '<li>$1</li>');\r\n\r\n      // 替换段落\r\n      formatted = formatted.replace(/^(?!<[a-z])(.*$)/gm, function(match) {\r\n        return match.trim() ? '<p>' + match + '</p>' : '';\r\n      });\r\n\r\n      // 替换链接\r\n      formatted = formatted.replace(/\\[(.*?)\\]\\((.*?)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>');\r\n\r\n      return formatted;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* AI分析相关样式 */\r\n.ai-analysis-container {\r\n  padding: 16px;\r\n}\r\n\r\n.process-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #1890ff;\r\n\r\n  p {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.analysis-results {\r\n  margin-top: 10px;\r\n}\r\n\r\n.markdown-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.markdown-content h1,\r\n.markdown-content h2,\r\n.markdown-content h3,\r\n.markdown-content h4,\r\n.markdown-content h5,\r\n.markdown-content h6 {\r\n  margin-top: 24px;\r\n  margin-bottom: 16px;\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n}\r\n\r\n.markdown-content h1 {\r\n  font-size: 2em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h2 {\r\n  font-size: 1.5em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h3 {\r\n  font-size: 1.25em;\r\n}\r\n\r\n.markdown-content h4 {\r\n  font-size: 1em;\r\n}\r\n\r\n.markdown-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content code {\r\n  padding: 0.2em 0.4em;\r\n  margin: 0;\r\n  font-size: 85%;\r\n  background-color: rgba(27, 31, 35, 0.05);\r\n  border-radius: 3px;\r\n  font-family: \"SFMono-Regular\", Consolas, \"Liberation Mono\", Menlo, monospace;\r\n}\r\n\r\n.markdown-content pre {\r\n  padding: 16px;\r\n  overflow: auto;\r\n  font-size: 85%;\r\n  line-height: 1.45;\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content pre code {\r\n  padding: 0;\r\n  margin: 0;\r\n  background-color: transparent;\r\n  border: 0;\r\n  word-break: normal;\r\n  white-space: pre;\r\n}\r\n\r\n.markdown-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n\r\n\r\n.no-analysis {\r\n  margin-top: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.analysis-error {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 最后查看的行样式 - 浅灰色背景和轮廓线 */\r\n.last-viewed-row td {\r\n  background-color: #f5f5f5 !important;\r\n  position: relative;\r\n  border-top: 1px solid #1890ff !important;\r\n  border-bottom: 1px solid #1890ff !important;\r\n}\r\n\r\n/* 为第一个单元格添加左侧边框 */\r\n.last-viewed-row td:first-child {\r\n  border-left: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 为最后一个单元格添加右侧边框 */\r\n.last-viewed-row td:last-child {\r\n  border-right: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 悬停时加强边框效果 */\r\n.last-viewed-row:hover td {\r\n  border-top: 2px solid #1890ff !important;\r\n  border-bottom: 2px solid #1890ff !important;\r\n}\r\n</style>\r\n"], "mappings": "AAyEA,SAAAA,QAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,KAAA;IAAA,MAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,SAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAJ,KAAA;QACAE,GAAA;QACAC,KAAA;QACAE,KAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA,KAAAZ,CAAA;UAAA,SAEA,WAAAa,YAAA;UAAA,SACA;UAAA;YAAA,QACA;UAAA;UAAA;YAAA,SACAC,CAAA;cACAA,CAAA,CAAAC,eAAA;cACA,KAAAC,cAAA,CAAAJ,MAAA;YACA;UAAA;QAAA;MAKA,EACA;MACAK,UAAA;QACAC,QAAA;QACA;QACAC,OAAA;QACA;QACAC,QAAA,EAAAC,IAAA;UACA;UACA,KAAAC,MAAA,CAAAC,QAAA,kCAAAF,IAAA;QACA;MACA;MACA;MACAG,iBAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAlC,QAAA;IACA,GAAAA,QAAA;EACA;EACAmC,KAAA;IACAC,cAAA;MACAC,QAAA;QACA,KAAAC,cAAA;MACA;MACAC,SAAA;IACA;IACA;IACAC,WAAA;MACAH,QAAAI,OAAA;QACA,IAAAA,OAAA,SAAAnB,UAAA,CAAAE,OAAA,KAAAiB,OAAA;UACA,KAAAnB,UAAA,CAAAE,OAAA,GAAAiB,OAAA;QACA;MACA;MACAF,SAAA;IACA;EACA;EACAG,QAAA;IACA,KAAAJ,cAAA;;IAEA;IACA,KAAAK,SAAA;MACA;MACA,SAAAC,cAAA;QACAC,UAAA;UACAC,MAAA,CAAAC,QAAA;YACAC,GAAA,OAAAJ,cAAA;YACAK,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAC,aAAA;QACAL,UAAA,MAAAM,cAAA;MACA;IACA;EACA;EAEAC,QAAA;IACA;IACA,SAAAF,aAAA,SAAA3C,SAAA,CAAA8C,MAAA;MACA,KAAAF,cAAA;IACA;EACA;EACA;EACAG,iBAAAC,EAAA,EAAAC,KAAA,EAAAC,IAAA;IACA;IACA,IAAAF,EAAA,CAAAG,IAAA;MACA,KAAA/B,MAAA,CAAAC,QAAA;MACA;MACA,KAAAD,MAAA,CAAAC,QAAA;IACA;IACA6B,IAAA;EACA;EACAE,OAAA;IACA,MAAArB,eAAA;MACA,UAAAF,cAAA;QACAwB,OAAA,CAAAC,KAAA;QACA;MACA;MACA;QACA,MAAAC,QAAA,SAAA7D,KAAA,CAAA8D,GAAA,wBAAA3B,cAAA;UACA4B,MAAA;YACAC,MAAA;YACAC,MAAA,OAAAC;UACA;QACA;QACA,KAAA5D,SAAA,GAAAuD,QAAA,CAAA1D,IAAA;MACA,SAAAyD,KAAA;QACAD,OAAA,CAAAC,KAAA,8BAAAA,KAAA;MACA;IACA;IACAO,SAAAnD,MAAA,EAAAoD,MAAA;MACA;MACA,MAAAC,YAAA,QAAApB,aAAA,IAAAjC,MAAA,CAAAsD,GAAA,UAAArB,aAAA;MAEA;QACAsB,EAAA;UACAC,KAAA,EAAAA,CAAA;YACA,KAAAC,kBAAA,CAAAzD,MAAA;UACA;QACA;QACA0D,KAAA;UACA;UACA,mBAAAL;QACA;QACAM,KAAA;UACAC,MAAA;QACA;MACA;IACA;IACAH,mBAAAI,OAAA;MACA;MACA,MAAAlC,cAAA,GAAAE,MAAA,CAAAiC,WAAA,IAAAC,QAAA,CAAAC,eAAA,CAAAC,SAAA,IAAAF,QAAA,CAAAG,IAAA,CAAAD,SAAA;MACA,KAAAvD,MAAA,CAAAC,QAAA,qCAAAgB,cAAA;;MAEA;MACA,KAAAjB,MAAA,CAAAC,QAAA,oCAAAkD,OAAA,CAAAP,GAAA;;MAEA;MACA,KAAAa,OAAA,CAAAC,IAAA;QAAA3B,IAAA;QAAAM,MAAA;UAAAO,GAAA,EAAAO,OAAA,CAAAP;QAAA;MAAA;IACA;IAEA;IACAlD,eAAAyD,OAAA;MACA,KAAA7C,eAAA,GAAA6C,OAAA;MACA,KAAAjD,iBAAA;MACA,KAAAE,gBAAA;MACA,KAAAC,eAAA;;MAEA;MACA,KAAAsD,iBAAA;IACA;IAEA;IACAnC,eAAA;MACA,UAAAD,aAAA;;MAEA;MACA,MAAAqC,IAAA,GAAAP,QAAA,CAAAQ,gBAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAlC,MAAA,EAAAoC,CAAA;QACA,MAAAC,GAAA,GAAAH,IAAA,CAAAE,CAAA;QACA,MAAAE,KAAA,GAAAD,GAAA,CAAAF,gBAAA;;QAEA;QACA,IAAAG,KAAA,CAAAtC,MAAA,QAAAsC,KAAA,IAAAC,WAAA,CAAAC,QAAA,MAAA3C,aAAA;UACA;UACAwC,GAAA,CAAAI,SAAA,CAAAC,GAAA;;UAEA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAtC,MAAA,EAAA2C,CAAA;YACA,MAAAC,IAAA,GAAAN,KAAA,CAAAK,CAAA;YACAC,IAAA,CAAArB,KAAA,CAAAsB,eAAA;YACAD,IAAA,CAAArB,KAAA,CAAAuB,SAAA;YACAF,IAAA,CAAArB,KAAA,CAAAwB,YAAA;;YAEA;YACA,IAAAJ,CAAA;cACAC,IAAA,CAAArB,KAAA,CAAAyB,UAAA;YACA;;YAEA;YACA,IAAAL,CAAA,KAAAL,KAAA,CAAAtC,MAAA;cACA4C,IAAA,CAAArB,KAAA,CAAA0B,WAAA;YACA;UACA;;UAEA;UACA;QACA;MACA;IACA;IAEAC,sBAAA;MACA,KAAA1E,iBAAA;MACA,KAAAE,gBAAA;MACA,KAAAC,eAAA;IACA;IAEA,MAAAsD,kBAAA;MACA,UAAArD,eAAA;QACA,KAAAD,eAAA;QACA;MACA;MAEA,UAAAI,cAAA;QACA,KAAAJ,eAAA;QACA;MACA;MAEA,KAAAF,iBAAA;MACA,KAAAE,eAAA;MAEA;QACA;QACA,MAAA8B,QAAA,SAAA7D,KAAA,CAAA8D,GAAA,wBAAA3B,cAAA;UACA4B,MAAA;YACAO,GAAA,OAAAtC,eAAA,CAAAsC,GAAA;YACAN,MAAA;YACAC,MAAA,OAAAC,cAAA;UACA;QACA;QAEA,MAAAqC,WAAA,GAAA1C,QAAA,CAAA1D,IAAA;;QAEA;QACA,MAAAqG,UAAA,SAAAxG,KAAA,CAAAyG,IAAA;UACAC,YAAA;YACAC,YAAA,GAAAJ,WAAA;UACA;QACA;UACAK,OAAA;QACA;QAEA,IAAAJ,UAAA,CAAArG,IAAA,CAAA0G,OAAA;UACA,KAAA/E,gBAAA,GAAA0E,UAAA,CAAArG,IAAA,CAAA2G,QAAA;QACA;UACA,KAAA/E,eAAA,GAAAyE,UAAA,CAAArG,IAAA,CAAAyD,KAAA;QACA;MACA,SAAAA,KAAA;QAAA,IAAAmD,eAAA;QACApD,OAAA,CAAAC,KAAA,uBAAAA,KAAA;QACA,KAAA7B,eAAA,KAAAgF,eAAA,GAAAnD,KAAA,CAAAC,QAAA,cAAAkD,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAA5G,IAAA,cAAA4G,eAAA,uBAAAA,eAAA,CAAAnD,KAAA,KAAAA,KAAA,CAAAoD,OAAA;MACA;QACA,KAAAnF,iBAAA;MACA;IACA;IAEAoF,eAAAC,QAAA;MACA;MACA,KAAAA,QAAA;;MAEA;MACA,IAAAC,SAAA,GAAAD,QAAA,CACAE,OAAA,8BACAA,OAAA,+BACAA,OAAA,gCACAA,OAAA,iCACAA,OAAA,kCACAA,OAAA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CACAC,OAAA,0CACAA,OAAA,8BACAA,OAAA,sCACAA,OAAA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CAAAC,OAAA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CAAAC,OAAA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CACAC,OAAA,uCACAA,OAAA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CAAAC,OAAA,iCAAAC,KAAA;QACA,OAAAA,KAAA,CAAAC,IAAA,aAAAD,KAAA;MACA;;MAEA;MACAF,SAAA,GAAAA,SAAA,CAAAC,OAAA;MAEA,OAAAD,SAAA;IACA;EACA;AACA", "ignoreList": []}]}