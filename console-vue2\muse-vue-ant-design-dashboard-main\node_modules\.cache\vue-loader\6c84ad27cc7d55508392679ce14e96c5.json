{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1753170815177}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SmartOrchestrationInfo.vue"], "names": [], "mappings": ";;AAqQA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SmartOrchestrationInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">{{ $t('smartOrchestration.smartAnalysis') }}</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n        icon=\"branches\"\r\n      >\r\n        {{ $t('smartOrchestration.startAnalysis') }}\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.caseAnalysis')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model=\"smartSearchQuery\"\r\n                :placeholder=\"$t('smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item>\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchTopK\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchThreshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block icon=\"search\">\r\n                    {{ $t('testcase.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('smartOrchestration.foundResults', { count: (smartSearchResults || []).length }) }}</a-tag>\r\n              <a-button \r\n              type=\"link\"\r\n              size=\"small\" \r\n              @click=\"clearSearchHistory\"\r\n              icon=\"close\"\r\n              >\r\n              {{ $t('common.clear') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"smartSearchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${(availableDataTypes || []).length} 种类型的数据：${(availableDataTypes || []).join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"(analysisResults || []).length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ (result.matched_testcases || []).length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      searching: false,\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      // 本地查询参数状态\r\n      smartSearchQuery: '',\r\n      smartSearchTopK: 10,\r\n      smartSearchThreshold: 0.5,\r\n      \r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    ...mapState(['currentProject', 'currentProjectName', 'smartSearchResults']),\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    },\r\n    searchResultColumns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 60,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ];\r\n    },\r\n    testcaseColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n    executionColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionStatus'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionMessage'),\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    // 初始化时记录当前项目信息\r\n    this.lastProjectInfo = {\r\n      project: this.currentProject,\r\n      projectName: this.currentProjectName\r\n    };\r\n  },\r\n  \r\n  // 移除watch，因为现在项目状态是自动隔离的\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$axios.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.smartSearchQuery.trim()) {\r\n        message.warning('请输入查询内容');\r\n        return;\r\n      }\r\n      this.searching = true;\r\n      try {\r\n        const response = await this.$axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.smartSearchQuery,\r\n          top_k: this.smartSearchTopK,\r\n          score_threshold: this.smartSearchThreshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.$store.dispatch('updateSmartSearchResults', response.data.results);\r\n          message.success(this.$t('smartOrchestration.foundResults', { count: (response.data.results || []).length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('smartOrchestration.searchFailed'));\r\n          this.$store.dispatch('clearSmartSearch');\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('smartOrchestration.searchError'));\r\n        this.$store.dispatch('clearSmartSearch');\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.$store.dispatch('clearSmartSearch');\r\n      message.success(this.$t('smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "]}]}