<template>
  <a-table
    v-bind="$attrs"
    :columns="resizableColumns"
    :components="tableComponents"
    bordered
    v-on="$listeners"
  >
    <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope">
      <slot :name="slot" v-bind="scope"></slot>
    </template>
  </a-table>
</template>

<script>
export default {
  name: 'ResizableTable',
  props: {
    columns: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      resizableColumns: []
    };
  },
  watch: {
    columns: {
      handler(newColumns) {
        this.resizableColumns = [...newColumns];
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    tableComponents() {
      return {
        header: {
          cell: (h, props, children) => {
            const { key, ...restProps } = props;
            const col = this.resizableColumns.find(col => {
              const k = col.dataIndex || col.key;
              return k === key;
            });
            
            if (!col || !col.width) {
              return h('th', { ...restProps }, children);
            }

            return h(
              'th',
              {
                ...restProps,
                style: { position: 'relative' }
              },
              [
                ...children,
                h('div', {
                  class: 'resize-handle',
                  on: {
                    mousedown: (e) => {
                      e.preventDefault();
                      const startX = e.clientX;
                      const startWidth = col.width;
                      
                      const onMouseMove = (e) => {
                        const newWidth = startWidth + (e.clientX - startX);
                        if (newWidth > 50) {
                          this.updateColumnWidth(key, newWidth);
                        }
                      };
                      
                      const onMouseUp = () => {
                        document.removeEventListener('mousemove', onMouseMove);
                        document.removeEventListener('mouseup', onMouseUp);
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                      };
                      
                      document.body.style.cursor = 'col-resize';
                      document.body.style.userSelect = 'none';
                      document.addEventListener('mousemove', onMouseMove);
                      document.addEventListener('mouseup', onMouseUp);
                    }
                  }
                })
              ]
            );
          }
        }
      };
    }
  },
  methods: {
    updateColumnWidth(key, width) {
      this.resizableColumns = this.resizableColumns.map(col => {
        const k = col.dataIndex || col.key;
        if (k === key) {
          return { ...col, width };
        }
        return col;
      });
      
      // 触发父组件的列宽变化事件
      this.$emit('columns-change', this.resizableColumns);
    }
  }
};
</script>

<style lang="scss" scoped>

:deep(.resize-handle) {
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  width: 10px;
  cursor: col-resize;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
    background-color: rgba(24, 144, 255, 0.2);
  }
  
  &:active {
    background-color: rgba(24, 144, 255, 0.4);
  }
}
</style>
