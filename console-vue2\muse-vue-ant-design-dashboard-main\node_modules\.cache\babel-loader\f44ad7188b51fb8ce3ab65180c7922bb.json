{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\App.vue", "mtime": 1753175872855}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IERpZnlDaGF0Qm90IGZyb20gJ0AvY29tcG9uZW50cy9jb21tb24vRGlmeUNoYXRCb3QudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdBcHAnLAogIGNvbXBvbmVudHM6IHsKICAgIERpZnlDaGF0Qm90CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNSZWFkeTogZmFsc2UKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLy8gU2V0cyBjb21wb25lbnRzIG5hbWUgYmFzZWQgb24gY3VycmVudCByb3V0ZSdzIHNwZWNpZmllZCBsYXlvdXQsIGRlZmF1bHRzIHRvCiAgICAvLyA8bGF5b3V0LWRlZmF1bHQ+PC9sYXlvdXQtZGVmYXVsdD4gY29tcG9uZW50LgogICAgbGF5b3V0KCkgewogICAgICByZXR1cm4gImxheW91dC0iICsgKHRoaXMuJHJvdXRlLm1ldGEubGF5b3V0IHx8ICJkYXNoYm9hcmQiKTsKICAgIH0sCiAgICBpc0RhcmtNb2RlKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuZGFya01vZGU7CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5pc1JlYWR5ID0gdHJ1ZTsKICAgIC8vIOWIneWni+WMlua3seiJsuaooeW8jwogICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhcmtNb2RlKSB7CiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QuYWRkKCdkYXJrLW1vZGUnKTsKICAgIH0gZWxzZSB7CiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKCdkYXJrLW1vZGUnKTsKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzmt7HoibLmqKHlvI/lj5jljJYKICAgIGlzRGFya01vZGUobmV3VmFsdWUpIHsKICAgICAgaWYgKG5ld1ZhbHVlKSB7CiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmstbW9kZScpOwogICAgICB9IGVsc2UgewogICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKCdkYXJrLW1vZGUnKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["DifyChatBot", "name", "components", "data", "isReady", "computed", "layout", "$route", "meta", "isDarkMode", "$store", "state", "darkMode", "created", "document", "documentElement", "classList", "add", "remove", "watch", "newValue"], "sources": ["src/App.vue"], "sourcesContent": ["<!--\r\n\tThis is the main page of the application, the layout component is used here,\r\n\tand the router-view is passed to it.\r\n\tLayout component is dynamically declared based on the layout for each route,\r\n\tspecified in routes list router/index.js .\r\n -->\r\n\r\n<template>\r\n\t<div id=\"app\">\r\n\t\t<component :is=\"layout\" v-if=\"isReady\">\r\n\t\t\t<router-view />\r\n\t\t</component>\r\n\t\t<div v-else>Loading...</div>\r\n\t\t\r\n\t\t<!-- 添加Dify聊天机器人 -->\r\n\t\t<DifyChatBot />\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport DifyChatBot from '@/components/common/DifyChatBot.vue';\r\n\r\nexport default {\r\n\tname: 'App',\r\n\tcomponents: {\r\n\t\tDifyChatBot\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisReady: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// Sets components name based on current route's specified layout, defaults to\r\n\t\t// <layout-default></layout-default> component.\r\n\t\tlayout() {\r\n\t\t\treturn \"layout-\" + (this.$route.meta.layout || \"dashboard\");\r\n\t\t},\r\n\t\tisDarkMode() {\r\n\t\t\treturn this.$store.state.darkMode;\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.isReady = true;\r\n\t\t// 初始化深色模式\r\n\t\tif (this.$store.state.darkMode) {\r\n\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t} else {\r\n\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听深色模式变化\r\n\t\tisDarkMode(newValue) {\r\n\t\t\tif (newValue) {\r\n\t\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t\t} else {\r\n\t\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n</style>\r\n"], "mappings": "AAoBA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA;IACA;IACAC,OAAA;MACA,yBAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA;IACA;IACAG,WAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAT,OAAA;IACA;IACA,SAAAM,MAAA,CAAAC,KAAA,CAAAC,QAAA;MACAE,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,GAAA;IACA;MACAH,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAE,MAAA;IACA;EACA;EACAC,KAAA;IACA;IACAV,WAAAW,QAAA;MACA,IAAAA,QAAA;QACAN,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,GAAA;MACA;QACAH,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAE,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}