{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue", "mtime": 1753170087083}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "axios", "RefreshButton", "components", "name", "data", "filesystemItems", "loading", "columns", "title", "dataIndex", "key", "width", "ellipsis", "pagination", "pageSize", "computed", "watch", "selectedNodeIp", "newIp", "fetchFilesystem", "mounted", "methods", "console", "error", "response", "get", "params", "dbFile", "currentProject", "map", "item", "index", "device", "mount_point"], "sources": ["src/components/Cards/FilesystemInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" fill-rule=\"evenodd\" d=\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\" clip-rule=\"evenodd\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.mount') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchFilesystem\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :dataSource=\"filesystemItems\"\r\n      :rowKey=\"(record) => record.key || record.device\"\r\n      :pagination=\"pagination\"\r\n      :loading=\"loading\"\r\n    >\r\n    </a-table>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  name: 'FilesystemInfo',\r\n  data() {\r\n    return {\r\n      filesystemItems: [],\r\n      loading: false,\r\n      columns: [\r\n        {\r\n          title: 'Device',\r\n          dataIndex: 'device',\r\n          key: 'device',\r\n          width: '20%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Point',\r\n          dataIndex: 'mount_point',\r\n          key: 'mount_point',\r\n          width: '25%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'File System Type',\r\n          dataIndex: 'fs_type',\r\n          key: 'fs_type',\r\n          width: '15%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Options',\r\n          dataIndex: 'mount_options',\r\n          key: 'mount_options',\r\n          width: '40%',\r\n          ellipsis: true,\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchFilesystem();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchFilesystem();\r\n  },\r\n  methods: {\r\n    async fetchFilesystem() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.filesystemItems = [];\r\n        return;\r\n      }\r\n      try {\r\n        // 显示加载状态\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/filesystem/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n\r\n        // 处理数据，确保每条记录有唯一的key\r\n        this.filesystemItems = response.data.map((item, index) => ({\r\n          ...item,\r\n          // 使用组合键作为唯一标识，防止device重复导致的渲染问题\r\n          key: `${item.device}_${item.mount_point}_${index}`\r\n        }));\r\n      } catch (error) {\r\n        console.error('Error fetching filesystem:', error);\r\n        this.filesystemItems = [];\r\n      } finally {\r\n        // 无论成功失败，都关闭加载状态\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n"], "mappings": ";;AAmCA,SAAAA,QAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,eAAA;MACAC,OAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,EACA;MACAC,UAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAhB,QAAA;EACA;EACAiB,KAAA;IACAC,eAAAC,KAAA;MACA,KAAAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,eAAA;EACA;EACAE,OAAA;IACA,MAAAF,gBAAA;MACA,UAAAF,cAAA;QACAK,OAAA,CAAAC,KAAA;QACA,KAAAlB,eAAA;QACA;MACA;MACA;QACA;QACA,KAAAC,OAAA;QACA,MAAAkB,QAAA,SAAAxB,KAAA,CAAAyB,GAAA,yBAAAR,cAAA;UACAS,MAAA;YACAC,MAAA,OAAAC;UACA;QACA;;QAEA;QACA,KAAAvB,eAAA,GAAAmB,QAAA,CAAApB,IAAA,CAAAyB,GAAA,EAAAC,IAAA,EAAAC,KAAA;UACA,GAAAD,IAAA;UACA;UACApB,GAAA,KAAAoB,IAAA,CAAAE,MAAA,IAAAF,IAAA,CAAAG,WAAA,IAAAF,KAAA;QACA;MACA,SAAAR,KAAA;QACAD,OAAA,CAAAC,KAAA,+BAAAA,KAAA;QACA,KAAAlB,eAAA;MACA;QACA;QACA,KAAAC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}