{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1753170815177}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["message", "mapState", "mapActions", "mapGetters", "TestCaseDetailModal", "name", "components", "data", "analyzing", "analysisModalVisible", "selectedNodeId", "selectedAnalysisTypes", "analysisResults", "activeKeys", "availableNodes", "availableDataTypes", "searching", "testcaseDetailVisible", "selectedTestcase", "smartSearchQuery", "smartSearchTopK", "smartSearchThreshold", "computed", "hasNodeData", "length", "searchResultColumns", "title", "dataIndex", "key", "width", "align", "customRender", "text", "record", "index", "$t", "scopedSlots", "ellipsis", "testcaseColumns", "executionColumns", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "lastProjectInfo", "project", "currentProject", "projectName", "currentProjectName", "methods", "response", "$axios", "get", "error", "console", "dataTypes", "available", "for<PERSON>ach", "type", "dataKey", "localStorage", "getItem", "push", "showAnalysisModal", "warning", "id", "startAnalysis", "infoType", "collectedData", "getCollectedData", "$http", "post", "node_id", "info_type", "collected_data", "map", "_", "toString", "success", "JSON", "parse", "getTypeName", "typeNames", "getStatusColor", "status", "colors", "getStatusText", "texts", "truncateText", "max<PERSON><PERSON><PERSON>", "substring", "expandedRowRender", "outputs", "output", "command", "exit_code", "join", "searchTestcases", "trim", "query", "top_k", "score_threshold", "$store", "dispatch", "results", "count", "viewTestcaseDetail", "clearSearchHistory", "getSimilarityColor", "similarity", "getLevelColor", "level"], "sources": ["src/components/Cards/SmartOrchestrationInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">{{ $t('smartOrchestration.smartAnalysis') }}</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n        icon=\"branches\"\r\n      >\r\n        {{ $t('smartOrchestration.startAnalysis') }}\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.caseAnalysis')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model=\"smartSearchQuery\"\r\n                :placeholder=\"$t('smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item>\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchTopK\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"smartSearchThreshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block icon=\"search\">\r\n                    {{ $t('testcase.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('smartOrchestration.foundResults', { count: (smartSearchResults || []).length }) }}</a-tag>\r\n              <a-button \r\n              type=\"link\"\r\n              size=\"small\" \r\n              @click=\"clearSearchHistory\"\r\n              icon=\"close\"\r\n              >\r\n              {{ $t('common.clear') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"smartSearchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${(availableDataTypes || []).length} 种类型的数据：${(availableDataTypes || []).join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"(analysisResults || []).length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ (result.matched_testcases || []).length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      searching: false,\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      // 本地查询参数状态\r\n      smartSearchQuery: '',\r\n      smartSearchTopK: 10,\r\n      smartSearchThreshold: 0.5,\r\n      \r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    ...mapState(['currentProject', 'currentProjectName', 'smartSearchResults']),\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    },\r\n    searchResultColumns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 60,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ];\r\n    },\r\n    testcaseColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n    executionColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionStatus'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: this.$t('smartOrchestration.executionMessage'),\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ];\r\n    },\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    // 初始化时记录当前项目信息\r\n    this.lastProjectInfo = {\r\n      project: this.currentProject,\r\n      projectName: this.currentProjectName\r\n    };\r\n  },\r\n  \r\n  // 移除watch，因为现在项目状态是自动隔离的\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$axios.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.smartSearchQuery.trim()) {\r\n        message.warning('请输入查询内容');\r\n        return;\r\n      }\r\n      this.searching = true;\r\n      try {\r\n        const response = await this.$axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.smartSearchQuery,\r\n          top_k: this.smartSearchTopK,\r\n          score_threshold: this.smartSearchThreshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.$store.dispatch('updateSmartSearchResults', response.data.results);\r\n          message.success(this.$t('smartOrchestration.foundResults', { count: (response.data.results || []).length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('smartOrchestration.searchFailed'));\r\n          this.$store.dispatch('clearSmartSearch');\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('smartOrchestration.searchError'));\r\n        this.$store.dispatch('clearSmartSearch');\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.$store.dispatch('clearSmartSearch');\r\n      message.success(this.$t('smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "], "mappings": ";;AAqQA,SAAAA,OAAA;AACA,SAAAC,QAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,qBAAA;MACAC,gBAAA;MAEA;MACAC,gBAAA;MACAC,eAAA;MACAC,oBAAA;IAEA;EACA;EAEAC,QAAA;IACA,GAAArB,QAAA;IACAsB,YAAA;MACA,YAAAT,cAAA,CAAAU,MAAA,aAAAT,kBAAA,CAAAS,MAAA;IACA;IACAC,oBAAA;MACA,QACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,IAAA,EAAAC,MAAA,EAAAC,KAAA;UACA,OAAAA,KAAA;QACA;MACA,GACA;QACAR,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;MACA,GACA;QACAL,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAS,QAAA;QACAR,KAAA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;MACA,GACA;QACAL,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;MACA,EACA;IACA;IACAO,gBAAA;MACA,QACA;QACAZ,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAS,QAAA;MACA,GACA;QACAX,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAS,QAAA;MACA,EACA;IACA;IACAE,iBAAA;MACA,QACA;QACAb,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAS,QAAA;MACA,GACA;QACAX,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAS,QAAA;MACA,EACA;IACA;EACA;EAEAG,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;IACA;IACA,KAAAC,eAAA;MACAC,OAAA,OAAAC,cAAA;MACAC,WAAA,OAAAC;IACA;EACA;EAEA;;EAEAC,OAAA;IACA,MAAAP,mBAAA;MACA;QACA,MAAAQ,QAAA,cAAAC,MAAA,CAAAC,GAAA;QACA,IAAAF,QAAA,CAAA1C,IAAA,IAAA0C,QAAA,CAAA1C,IAAA,CAAAiB,MAAA;UACA,KAAAV,cAAA,GAAAmC,QAAA,CAAA1C,IAAA;QACA;MACA,SAAA6C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEAV,yBAAA;MACA;MACA,MAAAY,SAAA;MACA,MAAAC,SAAA;MAEAD,SAAA,CAAAE,OAAA,CAAAC,IAAA;QACA,MAAAC,OAAA,MAAAD,IAAA;QACA,IAAAE,YAAA,CAAAC,OAAA,CAAAF,OAAA;UACAH,SAAA,CAAAM,IAAA,CAAAJ,IAAA;QACA;MACA;MAEA,KAAA1C,kBAAA,GAAAwC,SAAA;IACA;IAEAO,kBAAA;MACA,UAAAvC,WAAA;QACAvB,OAAA,CAAA+D,OAAA;QACA;MACA;MAEA,KAAApD,qBAAA,YAAAI,kBAAA;MACA,KAAAN,oBAAA;MAEA,SAAAK,cAAA,CAAAU,MAAA;QACA,KAAAd,cAAA,QAAAI,cAAA,IAAAkD,EAAA;MACA;IACA;IAEA,MAAAC,cAAA;MACA,UAAAvD,cAAA;QACAV,OAAA,CAAAoD,KAAA;QACA;MACA;MAEA,SAAAzC,qBAAA,CAAAa,MAAA;QACAxB,OAAA,CAAAoD,KAAA;QACA;MACA;MAEA,KAAA5C,SAAA;MACA,KAAAI,eAAA;MAEA;QACA;QACA,WAAAsD,QAAA,SAAAvD,qBAAA;UACA,MAAAwD,aAAA,QAAAC,gBAAA,CAAAF,QAAA;UAEA,IAAAC,aAAA;YACA,MAAAlB,QAAA,cAAAoB,KAAA,CAAAC,IAAA;cACAC,OAAA,OAAA7D,cAAA;cACA8D,SAAA,EAAAN,QAAA;cACAO,cAAA,EAAAN;YACA;YAEA,KAAAvD,eAAA,CAAAiD,IAAA,CAAAZ,QAAA,CAAA1C,IAAA;UACA;QACA;QAEA,KAAAE,oBAAA;QACA,KAAAI,UAAA,QAAAD,eAAA,CAAA8D,GAAA,EAAAC,CAAA,EAAAzC,KAAA,KAAAA,KAAA,CAAA0C,QAAA;QAEA5E,OAAA,CAAA6E,OAAA,aAAAjE,eAAA,CAAAY,MAAA;MAEA,SAAA4B,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACApD,OAAA,CAAAoD,KAAA;MACA;QACA,KAAA5C,SAAA;MACA;IACA;IAEA4D,iBAAAF,QAAA;MACA,MAAAR,OAAA,MAAAQ,QAAA;MACA,MAAA3D,IAAA,GAAAoD,YAAA,CAAAC,OAAA,CAAAF,OAAA;MACA,OAAAnD,IAAA,GAAAuE,IAAA,CAAAC,KAAA,CAAAxE,IAAA;IACA;IAEAyE,YAAAvB,IAAA;MACA,MAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,IAAA,KAAAA,IAAA;IACA;IAEAyB,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IAEAE,cAAAF,MAAA;MACA,MAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEAI,aAAAvD,IAAA,EAAAwD,SAAA;MACA,KAAAxD,IAAA;MACA,OAAAA,IAAA,CAAAR,MAAA,GAAAgE,SAAA,GAAAxD,IAAA,CAAAyD,SAAA,IAAAD,SAAA,YAAAxD,IAAA;IACA;IAEA0D,kBAAAzD,MAAA;MACA,KAAAA,MAAA,CAAA0D,OAAA,IAAA1D,MAAA,CAAA0D,OAAA,CAAAnE,MAAA;QACA;MACA;MAEA;AACA;AACA;AACA,YAAAS,MAAA,CAAA0D,OAAA,CAAAjB,GAAA,EAAAkB,MAAA,EAAA1D,KAAA;AACA;AACA,8BAAAA,KAAA,wBAAA0D,MAAA,CAAAC,OAAA;AACA,6DAAAD,MAAA,CAAAE,SAAA,6BAAAF,MAAA,CAAAE,SAAA;AACA,gBAAAF,MAAA,CAAAA,MAAA,4HAAAA,MAAA,CAAAA,MAAA;AACA,gBAAAA,MAAA,CAAAxC,KAAA,wIAAAwC,MAAA,CAAAxC,KAAA;AACA;AACA,aAAA2C,IAAA;AACA;AACA;IACA;IAEA;IACA,MAAAC,gBAAA;MACA,UAAA7E,gBAAA,CAAA8E,IAAA;QACAjG,OAAA,CAAA+D,OAAA;QACA;MACA;MACA,KAAA/C,SAAA;MACA;QACA,MAAAiC,QAAA,cAAAC,MAAA,CAAAoB,IAAA;UACA4B,KAAA,OAAA/E,gBAAA;UACAgF,KAAA,OAAA/E,eAAA;UACAgF,eAAA,OAAA/E;QACA;QAEA,IAAA4B,QAAA,CAAA1C,IAAA,CAAA4E,MAAA;UACA,KAAAkB,MAAA,CAAAC,QAAA,6BAAArD,QAAA,CAAA1C,IAAA,CAAAgG,OAAA;UACAvG,OAAA,CAAA6E,OAAA,MAAA1C,EAAA;YAAAqE,KAAA,GAAAvD,QAAA,CAAA1C,IAAA,CAAAgG,OAAA,QAAA/E;UAAA;QACA;UACAxB,OAAA,CAAAoD,KAAA,CAAAH,QAAA,CAAA1C,IAAA,CAAAP,OAAA,SAAAmC,EAAA;UACA,KAAAkE,MAAA,CAAAC,QAAA;QACA;MACA,SAAAlD,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACApD,OAAA,CAAAoD,KAAA,MAAAjB,EAAA;QACA,KAAAkE,MAAA,CAAAC,QAAA;MACA;QACA,KAAAtF,SAAA;MACA;IACA;IAEA;IACAyF,mBAAAxE,MAAA;MACA,KAAAf,gBAAA,GAAAe,MAAA;MACA,KAAAhB,qBAAA;IACA;IAEA;IACAyF,mBAAA;MACA,KAAAL,MAAA,CAAAC,QAAA;MACAtG,OAAA,CAAA6E,OAAA,MAAA1C,EAAA;IACA;IAEA;IACAwE,mBAAAC,UAAA;MACA,IAAAA,UAAA;MACA,IAAAA,UAAA;MACA;IACA;IAEA;IACAC,cAAAC,KAAA;MACA,MAAA1B,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAA0B,KAAA;IACA;EACA;AACA", "ignoreList": []}]}