{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue", "mtime": 1753170781740}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProcessInfo.vue"], "names": [], "mappings": ";AAyEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA", "file": "ProcessInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full process-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.process') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchProcesses\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"processes\"\r\n      :rowKey=\"record => record.pid\"\r\n      :customRow=\"rowClick\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #emptyText>\r\n        <a-empty description=\"No processes found\" />\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- AI分析模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"aiAnalysisVisible\"\r\n      title=\"AI Security Analysis\"\r\n      width=\"800px\"\r\n      @cancel=\"handleAIAnalysisClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleAIAnalysisClose\">Close</a-button>\r\n      </template>\r\n\r\n      <div class=\"ai-analysis-container\">\r\n        <div v-if=\"selectedProcess\" class=\"process-info\">\r\n          <p><strong>PID:</strong> {{ selectedProcess.pid }}</p>\r\n        </div>\r\n\r\n        <a-skeleton :loading=\"aiAnalysisLoading\" active v-if=\"aiAnalysisLoading\" />\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisResult\" class=\"analysis-results\">\r\n          <div v-html=\"formatMarkdown(aiAnalysisResult)\" class=\"markdown-content\"></div>\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && !aiAnalysisResult && !aiAnalysisError\" class=\"no-analysis\">\r\n          <a-empty description=\"Analyzing process...\" />\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisError\" class=\"analysis-error\">\r\n          <a-alert\r\n            message=\"Analysis Error\"\r\n            :description=\"aiAnalysisError\"\r\n            type=\"error\"\r\n            show-icon\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      processes: [],\r\n      columns: [\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'Process Name',\r\n          dataIndex: 'cmdline',\r\n          key: 'cmdline',\r\n          width: '95%',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'AI Analysis',\r\n          key: 'ai_analysis',\r\n          width: '15%',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <a-button\r\n              class={`bg-${this.sidebarColor}`}\r\n              style=\"color: white\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                this.showAIAnalysis(record);\r\n              }}\r\n            >\r\n              Analyze\r\n            </a-button>\r\n          ),\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n        // 使用计算属性来绑定当前页码\r\n        current: 1,\r\n        // 添加分页变化事件处理\r\n        onChange: (page) => {\r\n          // 当页码变化时，更新Vuex中的页码状态\r\n          this.$store.dispatch('processList/updateCurrentPage', page);\r\n        }\r\n      },\r\n      // AI分析相关\r\n      aiAnalysisVisible: false,\r\n      aiAnalysisLoading: false,\r\n      aiAnalysisResult: null,\r\n      aiAnalysisError: null,\r\n      selectedProcess: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    ...mapState('processList', ['currentPage', 'scrollPosition', 'lastViewedPid']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp: {\r\n      handler() {\r\n        this.fetchProcesses();\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听Vuex中的currentPage变化，同步到分页组件\r\n    currentPage: {\r\n      handler(newPage) {\r\n        if (newPage && this.pagination.current !== newPage) {\r\n          this.pagination.current = newPage;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchProcesses();\r\n\r\n    // 恢复之前保存的滚动位置和应用高亮效果\r\n    this.$nextTick(() => {\r\n      // 恢复滚动位置\r\n      if (this.scrollPosition > 0) {\r\n        setTimeout(() => {\r\n          window.scrollTo({\r\n            top: this.scrollPosition,\r\n            behavior: 'auto'\r\n          });\r\n        }, 100);\r\n      }\r\n\r\n      // 如果有lastViewedPid，尝试应用高亮效果\r\n      if (this.lastViewedPid) {\r\n        setTimeout(this.applyHighlight, 500); // 延迟确保表格已渲染\r\n      }\r\n    });\r\n  },\r\n\r\n  updated() {\r\n    // 只有当processes数组有内容且有lastViewedPid时才应用高亮\r\n    if (this.lastViewedPid && this.processes.length > 0) {\r\n      this.applyHighlight();\r\n    }\r\n  },\r\n  // 当用户离开进程列表页面但不是通过点击进程详情时，清除保存的滚动位置和分页信息\r\n  beforeRouteLeave(to, _from, next) {\r\n    // 如果不是导航到进程详情页面，则清除滚动位置和分页信息\r\n    if (to.name !== 'ProcessDetail') {\r\n      this.$store.dispatch('processList/resetState');\r\n      // 清除最后查看的进程ID\r\n      this.$store.dispatch('processList/clearLastViewedPid');\r\n    }\r\n    next();\r\n  },\r\n  methods: {\r\n    async fetchProcesses() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            fields: 'pid,cmdline',\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.processes = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching processes:', error);\r\n      }\r\n    },\r\n    rowClick(record, _index) {\r\n      // 检查是否是最后查看的进程\r\n      const isLastViewed = this.lastViewedPid && record.pid === this.lastViewedPid;\r\n\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            this.viewProcessDetails(record);\r\n          },\r\n        },\r\n        class: {\r\n          // 如果是最后查看的进程，添加高亮类\r\n          'last-viewed-row': isLastViewed\r\n        },\r\n        style: {\r\n          cursor: 'pointer'\r\n        }\r\n      };\r\n    },\r\n    viewProcessDetails(process) {\r\n      // 保存当前滚动位置到Vuex store\r\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n      this.$store.dispatch('processList/updateScrollPosition', scrollPosition);\r\n\r\n      // 保存当前查看的进程ID\r\n      this.$store.dispatch('processList/updateLastViewedPid', process.pid);\r\n\r\n      // 导航到进程详情页面\r\n      this.$router.push({ name: 'ProcessDetail', params: { pid: process.pid } });\r\n    },\r\n\r\n    // AI分析相关方法\r\n    showAIAnalysis(process) {\r\n      this.selectedProcess = process;\r\n      this.aiAnalysisVisible = true;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n\r\n      // 自动开始分析\r\n      this.requestAIAnalysis();\r\n    },\r\n\r\n    // 应用高亮效果到最后查看的进程行\r\n    applyHighlight() {\r\n      if (!this.lastViewedPid) return;\r\n\r\n      // 尝试查找包含lastViewedPid的行\r\n      const rows = document.querySelectorAll('.ant-table-row');\r\n      for (let i = 0; i < rows.length; i++) {\r\n        const row = rows[i];\r\n        const cells = row.querySelectorAll('td');\r\n\r\n        // 检查第一个单元格（PID列）是否包含lastViewedPid\r\n        if (cells.length > 0 && cells[0].textContent.includes(this.lastViewedPid)) {\r\n          // 添加高亮类\r\n          row.classList.add('last-viewed-row');\r\n\r\n          // 直接设置样式以确保高亮效果生效\r\n          for (let j = 0; j < cells.length; j++) {\r\n            const cell = cells[j];\r\n            cell.style.backgroundColor = '#f5f5f5';\r\n            cell.style.borderTop = '1px solid #1890ff';\r\n            cell.style.borderBottom = '1px solid #1890ff';\r\n\r\n            // 为第一个单元格添加左侧边框\r\n            if (j === 0) {\r\n              cell.style.borderLeft = '3px solid #1890ff';\r\n            }\r\n\r\n            // 为最后一个单元格添加右侧边框\r\n            if (j === cells.length - 1) {\r\n              cell.style.borderRight = '3px solid #1890ff';\r\n            }\r\n          }\r\n\r\n          // 找到后退出循环\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    handleAIAnalysisClose() {\r\n      this.aiAnalysisVisible = false;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n    },\r\n\r\n    async requestAIAnalysis() {\r\n      if (!this.selectedProcess) {\r\n        this.aiAnalysisError = \"No process selected for analysis\";\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedNodeIp) {\r\n        this.aiAnalysisError = \"No node selected\";\r\n        return;\r\n      }\r\n\r\n      this.aiAnalysisLoading = true;\r\n      this.aiAnalysisError = null;\r\n\r\n      try {\r\n        // 获取完整的进程信息\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            pid: this.selectedProcess.pid,\r\n            fields: 'pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps',\r\n            dbFile: this.currentProject || ''\r\n          }\r\n        });\r\n\r\n        const processData = response.data;\r\n\r\n        // 调用AI分析API，设置更长的超时时间\r\n        const aiResponse = await axios.post('/api/ai/analyze/process', {\r\n          process_data: {\r\n            process_list: [processData]\r\n          }\r\n        }, {\r\n          timeout: 600000 // 10分钟超时\r\n        });\r\n\r\n        if (aiResponse.data.success) {\r\n          this.aiAnalysisResult = aiResponse.data.analysis;\r\n        } else {\r\n          this.aiAnalysisError = aiResponse.data.error || \"Failed to analyze process data\";\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI analysis error:\", error);\r\n        this.aiAnalysisError = error.response?.data?.error || error.message || \"An error occurred during analysis\";\r\n      } finally {\r\n        this.aiAnalysisLoading = false;\r\n      }\r\n    },\r\n\r\n    formatMarkdown(markdown) {\r\n      // 简单的Markdown格式化，可以使用更复杂的库如marked.js\r\n      if (!markdown) return '';\r\n\r\n      // 替换标题\r\n      let formatted = markdown\r\n        .replace(/^# (.*$)/gm, '<h1>$1</h1>')\r\n        .replace(/^## (.*$)/gm, '<h2>$1</h2>')\r\n        .replace(/^### (.*$)/gm, '<h3>$1</h3>')\r\n        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')\r\n        .replace(/^##### (.*$)/gm, '<h5>$1</h5>')\r\n        .replace(/^###### (.*$)/gm, '<h6>$1</h6>');\r\n\r\n      // 替换粗体和斜体\r\n      formatted = formatted\r\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n        .replace(/__(.*?)__/g, '<strong>$1</strong>')\r\n        .replace(/_(.*?)_/g, '<em>$1</em>');\r\n\r\n      // 替换代码块\r\n      formatted = formatted.replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>');\r\n\r\n      // 替换行内代码\r\n      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');\r\n\r\n      // 替换列表\r\n      formatted = formatted\r\n        .replace(/^\\s*\\d+\\.\\s+(.*$)/gm, '<li>$1</li>')\r\n        .replace(/^\\s*[-*]\\s+(.*$)/gm, '<li>$1</li>');\r\n\r\n      // 替换段落\r\n      formatted = formatted.replace(/^(?!<[a-z])(.*$)/gm, function(match) {\r\n        return match.trim() ? '<p>' + match + '</p>' : '';\r\n      });\r\n\r\n      // 替换链接\r\n      formatted = formatted.replace(/\\[(.*?)\\]\\((.*?)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>');\r\n\r\n      return formatted;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* AI分析相关样式 */\r\n.ai-analysis-container {\r\n  padding: 16px;\r\n}\r\n\r\n.process-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #1890ff;\r\n\r\n  p {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.analysis-results {\r\n  margin-top: 10px;\r\n}\r\n\r\n.markdown-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.markdown-content h1,\r\n.markdown-content h2,\r\n.markdown-content h3,\r\n.markdown-content h4,\r\n.markdown-content h5,\r\n.markdown-content h6 {\r\n  margin-top: 24px;\r\n  margin-bottom: 16px;\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n}\r\n\r\n.markdown-content h1 {\r\n  font-size: 2em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h2 {\r\n  font-size: 1.5em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h3 {\r\n  font-size: 1.25em;\r\n}\r\n\r\n.markdown-content h4 {\r\n  font-size: 1em;\r\n}\r\n\r\n.markdown-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content code {\r\n  padding: 0.2em 0.4em;\r\n  margin: 0;\r\n  font-size: 85%;\r\n  background-color: rgba(27, 31, 35, 0.05);\r\n  border-radius: 3px;\r\n  font-family: \"SFMono-Regular\", Consolas, \"Liberation Mono\", Menlo, monospace;\r\n}\r\n\r\n.markdown-content pre {\r\n  padding: 16px;\r\n  overflow: auto;\r\n  font-size: 85%;\r\n  line-height: 1.45;\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content pre code {\r\n  padding: 0;\r\n  margin: 0;\r\n  background-color: transparent;\r\n  border: 0;\r\n  word-break: normal;\r\n  white-space: pre;\r\n}\r\n\r\n.markdown-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n\r\n\r\n.no-analysis {\r\n  margin-top: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.analysis-error {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 最后查看的行样式 - 浅灰色背景和轮廓线 */\r\n.last-viewed-row td {\r\n  background-color: #f5f5f5 !important;\r\n  position: relative;\r\n  border-top: 1px solid #1890ff !important;\r\n  border-bottom: 1px solid #1890ff !important;\r\n}\r\n\r\n/* 为第一个单元格添加左侧边框 */\r\n.last-viewed-row td:first-child {\r\n  border-left: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 为最后一个单元格添加右侧边框 */\r\n.last-viewed-row td:last-child {\r\n  border-right: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 悬停时加强边框效果 */\r\n.last-viewed-row:hover td {\r\n  border-top: 2px solid #1890ff !important;\r\n  border-bottom: 2px solid #1890ff !important;\r\n}\r\n</style>\r\n"]}]}