{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue?vue&type=style&index=0&id=50a24671&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\ProcessInfo.vue", "mtime": 1753170781740}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProcessInfo.vue"], "names": [], "mappings": ";AA0YA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ProcessInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full process-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.process') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchProcesses\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"processes\"\r\n      :rowKey=\"record => record.pid\"\r\n      :customRow=\"rowClick\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #emptyText>\r\n        <a-empty description=\"No processes found\" />\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- AI分析模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"aiAnalysisVisible\"\r\n      title=\"AI Security Analysis\"\r\n      width=\"800px\"\r\n      @cancel=\"handleAIAnalysisClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleAIAnalysisClose\">Close</a-button>\r\n      </template>\r\n\r\n      <div class=\"ai-analysis-container\">\r\n        <div v-if=\"selectedProcess\" class=\"process-info\">\r\n          <p><strong>PID:</strong> {{ selectedProcess.pid }}</p>\r\n        </div>\r\n\r\n        <a-skeleton :loading=\"aiAnalysisLoading\" active v-if=\"aiAnalysisLoading\" />\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisResult\" class=\"analysis-results\">\r\n          <div v-html=\"formatMarkdown(aiAnalysisResult)\" class=\"markdown-content\"></div>\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && !aiAnalysisResult && !aiAnalysisError\" class=\"no-analysis\">\r\n          <a-empty description=\"Analyzing process...\" />\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisError\" class=\"analysis-error\">\r\n          <a-alert\r\n            message=\"Analysis Error\"\r\n            :description=\"aiAnalysisError\"\r\n            type=\"error\"\r\n            show-icon\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      processes: [],\r\n      columns: [\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'Process Name',\r\n          dataIndex: 'cmdline',\r\n          key: 'cmdline',\r\n          width: '95%',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'AI Analysis',\r\n          key: 'ai_analysis',\r\n          width: '15%',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <a-button\r\n              class={`bg-${this.sidebarColor}`}\r\n              style=\"color: white\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                this.showAIAnalysis(record);\r\n              }}\r\n            >\r\n              Analyze\r\n            </a-button>\r\n          ),\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n        // 使用计算属性来绑定当前页码\r\n        current: 1,\r\n        // 添加分页变化事件处理\r\n        onChange: (page) => {\r\n          // 当页码变化时，更新Vuex中的页码状态\r\n          this.$store.dispatch('processList/updateCurrentPage', page);\r\n        }\r\n      },\r\n      // AI分析相关\r\n      aiAnalysisVisible: false,\r\n      aiAnalysisLoading: false,\r\n      aiAnalysisResult: null,\r\n      aiAnalysisError: null,\r\n      selectedProcess: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    ...mapState('processList', ['currentPage', 'scrollPosition', 'lastViewedPid']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp: {\r\n      handler() {\r\n        this.fetchProcesses();\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听Vuex中的currentPage变化，同步到分页组件\r\n    currentPage: {\r\n      handler(newPage) {\r\n        if (newPage && this.pagination.current !== newPage) {\r\n          this.pagination.current = newPage;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchProcesses();\r\n\r\n    // 恢复之前保存的滚动位置和应用高亮效果\r\n    this.$nextTick(() => {\r\n      // 恢复滚动位置\r\n      if (this.scrollPosition > 0) {\r\n        setTimeout(() => {\r\n          window.scrollTo({\r\n            top: this.scrollPosition,\r\n            behavior: 'auto'\r\n          });\r\n        }, 100);\r\n      }\r\n\r\n      // 如果有lastViewedPid，尝试应用高亮效果\r\n      if (this.lastViewedPid) {\r\n        setTimeout(this.applyHighlight, 500); // 延迟确保表格已渲染\r\n      }\r\n    });\r\n  },\r\n\r\n  updated() {\r\n    // 只有当processes数组有内容且有lastViewedPid时才应用高亮\r\n    if (this.lastViewedPid && this.processes.length > 0) {\r\n      this.applyHighlight();\r\n    }\r\n  },\r\n  // 当用户离开进程列表页面但不是通过点击进程详情时，清除保存的滚动位置和分页信息\r\n  beforeRouteLeave(to, _from, next) {\r\n    // 如果不是导航到进程详情页面，则清除滚动位置和分页信息\r\n    if (to.name !== 'ProcessDetail') {\r\n      this.$store.dispatch('processList/resetState');\r\n      // 清除最后查看的进程ID\r\n      this.$store.dispatch('processList/clearLastViewedPid');\r\n    }\r\n    next();\r\n  },\r\n  methods: {\r\n    async fetchProcesses() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            fields: 'pid,cmdline',\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.processes = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching processes:', error);\r\n      }\r\n    },\r\n    rowClick(record, _index) {\r\n      // 检查是否是最后查看的进程\r\n      const isLastViewed = this.lastViewedPid && record.pid === this.lastViewedPid;\r\n\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            this.viewProcessDetails(record);\r\n          },\r\n        },\r\n        class: {\r\n          // 如果是最后查看的进程，添加高亮类\r\n          'last-viewed-row': isLastViewed\r\n        },\r\n        style: {\r\n          cursor: 'pointer'\r\n        }\r\n      };\r\n    },\r\n    viewProcessDetails(process) {\r\n      // 保存当前滚动位置到Vuex store\r\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n      this.$store.dispatch('processList/updateScrollPosition', scrollPosition);\r\n\r\n      // 保存当前查看的进程ID\r\n      this.$store.dispatch('processList/updateLastViewedPid', process.pid);\r\n\r\n      // 导航到进程详情页面\r\n      this.$router.push({ name: 'ProcessDetail', params: { pid: process.pid } });\r\n    },\r\n\r\n    // AI分析相关方法\r\n    showAIAnalysis(process) {\r\n      this.selectedProcess = process;\r\n      this.aiAnalysisVisible = true;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n\r\n      // 自动开始分析\r\n      this.requestAIAnalysis();\r\n    },\r\n\r\n    // 应用高亮效果到最后查看的进程行\r\n    applyHighlight() {\r\n      if (!this.lastViewedPid) return;\r\n\r\n      // 尝试查找包含lastViewedPid的行\r\n      const rows = document.querySelectorAll('.ant-table-row');\r\n      for (let i = 0; i < rows.length; i++) {\r\n        const row = rows[i];\r\n        const cells = row.querySelectorAll('td');\r\n\r\n        // 检查第一个单元格（PID列）是否包含lastViewedPid\r\n        if (cells.length > 0 && cells[0].textContent.includes(this.lastViewedPid)) {\r\n          // 添加高亮类\r\n          row.classList.add('last-viewed-row');\r\n\r\n          // 直接设置样式以确保高亮效果生效\r\n          for (let j = 0; j < cells.length; j++) {\r\n            const cell = cells[j];\r\n            cell.style.backgroundColor = '#f5f5f5';\r\n            cell.style.borderTop = '1px solid #1890ff';\r\n            cell.style.borderBottom = '1px solid #1890ff';\r\n\r\n            // 为第一个单元格添加左侧边框\r\n            if (j === 0) {\r\n              cell.style.borderLeft = '3px solid #1890ff';\r\n            }\r\n\r\n            // 为最后一个单元格添加右侧边框\r\n            if (j === cells.length - 1) {\r\n              cell.style.borderRight = '3px solid #1890ff';\r\n            }\r\n          }\r\n\r\n          // 找到后退出循环\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    handleAIAnalysisClose() {\r\n      this.aiAnalysisVisible = false;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n    },\r\n\r\n    async requestAIAnalysis() {\r\n      if (!this.selectedProcess) {\r\n        this.aiAnalysisError = \"No process selected for analysis\";\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedNodeIp) {\r\n        this.aiAnalysisError = \"No node selected\";\r\n        return;\r\n      }\r\n\r\n      this.aiAnalysisLoading = true;\r\n      this.aiAnalysisError = null;\r\n\r\n      try {\r\n        // 获取完整的进程信息\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            pid: this.selectedProcess.pid,\r\n            fields: 'pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps',\r\n            dbFile: this.currentProject || ''\r\n          }\r\n        });\r\n\r\n        const processData = response.data;\r\n\r\n        // 调用AI分析API，设置更长的超时时间\r\n        const aiResponse = await axios.post('/api/ai/analyze/process', {\r\n          process_data: {\r\n            process_list: [processData]\r\n          }\r\n        }, {\r\n          timeout: 600000 // 10分钟超时\r\n        });\r\n\r\n        if (aiResponse.data.success) {\r\n          this.aiAnalysisResult = aiResponse.data.analysis;\r\n        } else {\r\n          this.aiAnalysisError = aiResponse.data.error || \"Failed to analyze process data\";\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI analysis error:\", error);\r\n        this.aiAnalysisError = error.response?.data?.error || error.message || \"An error occurred during analysis\";\r\n      } finally {\r\n        this.aiAnalysisLoading = false;\r\n      }\r\n    },\r\n\r\n    formatMarkdown(markdown) {\r\n      // 简单的Markdown格式化，可以使用更复杂的库如marked.js\r\n      if (!markdown) return '';\r\n\r\n      // 替换标题\r\n      let formatted = markdown\r\n        .replace(/^# (.*$)/gm, '<h1>$1</h1>')\r\n        .replace(/^## (.*$)/gm, '<h2>$1</h2>')\r\n        .replace(/^### (.*$)/gm, '<h3>$1</h3>')\r\n        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')\r\n        .replace(/^##### (.*$)/gm, '<h5>$1</h5>')\r\n        .replace(/^###### (.*$)/gm, '<h6>$1</h6>');\r\n\r\n      // 替换粗体和斜体\r\n      formatted = formatted\r\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n        .replace(/__(.*?)__/g, '<strong>$1</strong>')\r\n        .replace(/_(.*?)_/g, '<em>$1</em>');\r\n\r\n      // 替换代码块\r\n      formatted = formatted.replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>');\r\n\r\n      // 替换行内代码\r\n      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');\r\n\r\n      // 替换列表\r\n      formatted = formatted\r\n        .replace(/^\\s*\\d+\\.\\s+(.*$)/gm, '<li>$1</li>')\r\n        .replace(/^\\s*[-*]\\s+(.*$)/gm, '<li>$1</li>');\r\n\r\n      // 替换段落\r\n      formatted = formatted.replace(/^(?!<[a-z])(.*$)/gm, function(match) {\r\n        return match.trim() ? '<p>' + match + '</p>' : '';\r\n      });\r\n\r\n      // 替换链接\r\n      formatted = formatted.replace(/\\[(.*?)\\]\\((.*?)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>');\r\n\r\n      return formatted;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* AI分析相关样式 */\r\n.ai-analysis-container {\r\n  padding: 16px;\r\n}\r\n\r\n.process-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #1890ff;\r\n\r\n  p {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.analysis-results {\r\n  margin-top: 10px;\r\n}\r\n\r\n.markdown-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.markdown-content h1,\r\n.markdown-content h2,\r\n.markdown-content h3,\r\n.markdown-content h4,\r\n.markdown-content h5,\r\n.markdown-content h6 {\r\n  margin-top: 24px;\r\n  margin-bottom: 16px;\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n}\r\n\r\n.markdown-content h1 {\r\n  font-size: 2em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h2 {\r\n  font-size: 1.5em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h3 {\r\n  font-size: 1.25em;\r\n}\r\n\r\n.markdown-content h4 {\r\n  font-size: 1em;\r\n}\r\n\r\n.markdown-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content code {\r\n  padding: 0.2em 0.4em;\r\n  margin: 0;\r\n  font-size: 85%;\r\n  background-color: rgba(27, 31, 35, 0.05);\r\n  border-radius: 3px;\r\n  font-family: \"SFMono-Regular\", Consolas, \"Liberation Mono\", Menlo, monospace;\r\n}\r\n\r\n.markdown-content pre {\r\n  padding: 16px;\r\n  overflow: auto;\r\n  font-size: 85%;\r\n  line-height: 1.45;\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content pre code {\r\n  padding: 0;\r\n  margin: 0;\r\n  background-color: transparent;\r\n  border: 0;\r\n  word-break: normal;\r\n  white-space: pre;\r\n}\r\n\r\n.markdown-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n\r\n\r\n.no-analysis {\r\n  margin-top: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.analysis-error {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 最后查看的行样式 - 浅灰色背景和轮廓线 */\r\n.last-viewed-row td {\r\n  background-color: #f5f5f5 !important;\r\n  position: relative;\r\n  border-top: 1px solid #1890ff !important;\r\n  border-bottom: 1px solid #1890ff !important;\r\n}\r\n\r\n/* 为第一个单元格添加左侧边框 */\r\n.last-viewed-row td:first-child {\r\n  border-left: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 为最后一个单元格添加右侧边框 */\r\n.last-viewed-row td:last-child {\r\n  border-right: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 悬停时加强边框效果 */\r\n.last-viewed-row:hover td {\r\n  border-top: 2px solid #1890ff !important;\r\n  border-bottom: 2px solid #1890ff !important;\r\n}\r\n</style>\r\n"]}]}