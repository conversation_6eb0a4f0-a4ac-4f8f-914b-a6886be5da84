{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue", "mtime": 1753175269721}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TaskPanel.vue"], "names": [], "mappings": ";AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TaskPanel.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full task-card\"\r\n    :bodyStyle=\"{ padding: '8px 16px' }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <!-- 流程图 -->\r\n    <div class=\"steps-container\">\r\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"apartment\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"global\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-tooltip :title=\"getPlayIconTooltip\">\r\n              <a-icon\r\n                type=\"play-circle\"\r\n                class=\"step-icon\"\r\n                :class=\"{\r\n                  'clickable': selectedIp && selectedRowKeys.length > 0 && !isProcessing,\r\n                  'ready-to-start': selectedIp && selectedRowKeys.length > 0 && !isProcessing\r\n                }\"\r\n                @click=\"selectedIp && selectedRowKeys.length > 0 && !isProcessing && handleStart()\"\r\n                :style=\"{\r\n                  color: (selectedIp && selectedRowKeys.length > 0 && !isProcessing)\r\n                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\r\n                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\r\n                }\"\r\n              />\r\n            </a-tooltip>\r\n          </template>\r\n        </a-step>\r\n      </a-steps>\r\n    </div>\r\n\r\n    <!-- 节点选择区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\r\n      <node-selector\r\n        v-model=\"selectedRowKeys\"\r\n        :project-file=\"currentProject\"\r\n        :disabled=\"isProcessing\"\r\n        @input=\"onNodesSelected\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 代理配置区域 -->\r\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\r\n      <proxy-selector\r\n        v-model=\"selectedIp\"\r\n        :disabled=\"isProcessing\"\r\n        @change=\"handleProxyChange\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 任务状态 -->\r\n    <task-progress-card :task-type=\"'task'\" :is-processing=\"isProcessing\" />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\r\nimport NodeSelector from '@/components/common/NodeSelector.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin, TaskPollingMixin],\r\n  components: {\r\n    ProxySelector,\r\n    TaskProgressCard,\r\n    NodeSelector\r\n  },\r\n  data() {\r\n    return {\r\n      selectedRowKeys: [],\r\n      selectedIp: null,\r\n      currentStep: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['activeTask', 'currentProject', 'sidebarColor']),\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n\r\n    taskId: {\r\n      get() {\r\n        return this.activeTask?.task_id;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateTask', value ? { task_id: value } : null);\r\n      }\r\n    },\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n    currentStepComputed() {\r\n      if (this.isProcessing) {\r\n        return 1;  // 运行中时，只点亮前两个图标\r\n      }\r\n      if (this.selectedRowKeys.length === 0) {\r\n        return -1;  // 没有选择任何节点，所有图标不点亮\r\n      }\r\n      if (this.selectedRowKeys.length > 0 && !this.selectedIp) {\r\n        return 0;   // 选择了节点但未选择IP，点亮第一步图标和连接线\r\n      }\r\n      return 2;     // 选择了节点和IP，且未在运行时，点亮所有三个图标和连接线\r\n    },\r\n    getPlayIconTooltip() {\r\n      if (this.isProcessing) {\r\n        return 'Task is in progress...';\r\n      }\r\n      if (!this.selectedRowKeys.length) {\r\n        return 'Please select nodes first';\r\n      }\r\n      if (!this.selectedIp) {\r\n        return 'Please select a proxy IP';\r\n      }\r\n      return 'Click to start collection!'; // 当都选择完成时显示这个提示\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    // 只检查当前项目的活动任务\r\n    const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveTask();\r\n      } else {\r\n        // 清除任务信息如果属于不同项目\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 处理节点选择变化\r\n    onNodesSelected(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n      if (this.selectedRowKeys.length) {\r\n        this.currentStep = 1;\r\n      } else {\r\n        this.currentStep = 0;\r\n      }\r\n    },\r\n\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      this.selectedIp = ip;\r\n    },\r\n\r\n    async handleStart() {\r\n      if (!this.checkDatabaseStatus()) return;\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: 'No Nodes or Proxy Selected',\r\n          message: 'Please select one or more nodes and a reachable IP to collect the data.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isProcessing = true;\r\n      this.notificationSent = false; // 重置通知标志位\r\n\r\n      // 清除之前的任务通知记录\r\n      const previousTaskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n      if (previousTaskInfo) {\r\n        try {\r\n          const { taskId } = JSON.parse(previousTaskInfo);\r\n          if (taskId) {\r\n            this.clearTaskNotificationMark(taskId, 'task', this.currentProject);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error clearing previous task notification:', e);\r\n        }\r\n      }\r\n\r\n      try {\r\n        const { data } = await axios.post('/api/task/collect', {\r\n          targets: this.selectedRowKeys,\r\n          proxy_ip: this.selectedIp,\r\n          dbFile: this.currentProject\r\n        });\r\n\r\n        if (data && data.task_id) {\r\n          localStorage.setItem(`taskInfo_${this.currentProject}`, JSON.stringify({\r\n            taskId: data.task_id,\r\n            projectFile: this.currentProject\r\n          }));\r\n          localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n\r\n          this.taskId = data.task_id;\r\n          this.startPolling(data.task_id, 'task', 'task');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error starting task:', error);\r\n        this.$notify.error({\r\n          title: 'Task Start Failed',\r\n          message: error.message || 'Server connection error.',\r\n        });\r\n        this.isProcessing = false;\r\n      }\r\n    },\r\n\r\n    // 重写 checkActiveTask 方法，调用混入中的方法\r\n    async checkActiveTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`taskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/task/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, 'task', 'task');\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(`taskCompleted_${this.currentProject}`, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n\r\n\r\n    activated() {\r\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\r\n      this.checkActiveTask();\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听 currentProject 变化\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除之前项目的任务状态\r\n          this.$store.dispatch('updateTask', null);\r\n          this.stopPolling();\r\n          // 检查新项目的活动任务\r\n          this.checkActiveTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 基础卡片样式\r\n.task-card {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n// 步骤容器\r\n.steps-container {\r\n  width: 50%;\r\n  margin: 0 auto 24px;\r\n  padding: 12px 0;\r\n}\r\n\r\n// 深度选择器样式集中管理\r\n::v-deep {\r\n  .ant-card {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    .ant-card-head {\r\n      background: #f0f2f5;\r\n    }\r\n  }\r\n\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n  .ant-table-tbody > tr:last-child > td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  .steps-flow {\r\n    .ant-steps-item {\r\n      &-process,\r\n      &-finish {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #3b4149 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      &-wait {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #d9d9d9 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n      }\r\n\r\n      &-icon {\r\n        width: 88px;\r\n        height: 88px;\r\n        line-height: 80px;\r\n        padding: 4px;\r\n        font-size: 40px;\r\n        border-width: 2px;\r\n        margin-top: -20px;\r\n        color: #3b4149;\r\n\r\n        .step-icon {\r\n          font-size: 40px;\r\n          color: #3b4149;\r\n        }\r\n      }\r\n\r\n      &-tail::after {\r\n        height: 2px;\r\n      }\r\n\r\n      &:last-child {\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n\r\n        &.ant-steps-item-process,\r\n        &.ant-steps-item-finish {\r\n          .step-icon {\r\n            color: #3b4149 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ready-to-start {\r\n    animation: pulse 1.2s infinite;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n      opacity: 0.8;\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .clickable {\r\n    cursor: pointer;\r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}