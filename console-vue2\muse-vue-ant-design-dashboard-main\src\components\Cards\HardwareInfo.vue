<template>
  <!-- Hardware Table Card -->
  <a-card
    :bordered="false"
    class="header-solid h-full hardware-card"
    :bodyStyle="{ padding: 0 }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >
    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 512 512" :class="`text-${sidebarColor}`">
              <path :fill="'currentColor'" d="M160 160h192v192H160z"/>
              <path :fill="'currentColor'" d="M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.hardware') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchHardware" />
        </div>
      </div>
    </template>

    <a-table
      :columns="columns"
      :data-source="hardwareItems"
      :rowKey="(record) => record.device_info"
      :pagination="pagination"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'device_info'">
          <div class="table-hardware-info">
            <span>{{ record.device_info }}</span>
            <span>{{ record.device_type }}</span>
          </div>
        </template>
      </template>
    </a-table>
  </a-card>
  <!-- / Hardware Table Card -->
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';

export default {
  components: {
    RefreshButton
  },
  data() {
    return {
      hardwareItems: [],
      columns: [
        {
          title: 'Device Info',
          dataIndex: 'device_info',
          key: 'device_info',
        },
        {
          title: 'Device Type',
          dataIndex: 'device_type',
          key: 'device_type',
        },
      ],
      pagination: {
        pageSize: 100,
      },
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp(newIp) {
      this.fetchHardware();
    },
  },
  mounted() {
    this.fetchHardware();
  },
  methods: {
    async fetchHardware() {
      console.log('Selected Node IP:', this.selectedNodeIp);
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        return;
      }
      try {
        const response = await axios.get(`/api/hardware/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject
          }
        });
        this.hardwareItems = response.data;
      } catch (error) {
        console.error('Error fetching hardware:', error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.hardware-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.table-hardware-info {
  display: flex;
  justify-content: space-between;
}

.card-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
