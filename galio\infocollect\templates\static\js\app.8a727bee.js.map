{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/layouts/Simple.vue?56a0", "webpack:///./node_modules/moment/locale sync ^\\.\\/.*$", "webpack:///./src/components/Widgets/NotificationButton.vue?dad1", "webpack:///./src/App.vue", "webpack:///./src/components/common/DifyChatBot.vue", "webpack:///src/components/common/DifyChatBot.vue", "webpack:///./src/components/common/DifyChatBot.vue?54be", "webpack:///./src/components/common/DifyChatBot.vue?94f0", "webpack:///src/App.vue", "webpack:///./src/App.vue?8f16", "webpack:///./src/App.vue?a938", "webpack:///./src/views/ProjectManager.vue", "webpack:///src/views/ProjectManager.vue", "webpack:///./src/views/ProjectManager.vue?a866", "webpack:///./src/views/ProjectManager.vue?e3fa", "webpack:///./src/router/index.js", "webpack:///./src/store/index.js", "webpack:///./src/layouts/Simple.vue", "webpack:///src/layouts/Simple.vue", "webpack:///./src/layouts/Simple.vue?4d01", "webpack:///./src/layouts/Simple.vue?fa7c", "webpack:///./src/layouts/Dashboard.vue", "webpack:///./src/components/Sidebars/DashboardSidebar.vue", "webpack:///./src/components/Widgets/FooterAnimation.vue", "webpack:///src/components/Widgets/FooterAnimation.vue", "webpack:///./src/components/Widgets/FooterAnimation.vue?77a7", "webpack:///./src/components/Widgets/FooterAnimation.vue?bdf5", "webpack:///src/components/Sidebars/DashboardSidebar.vue", "webpack:///./src/components/Sidebars/DashboardSidebar.vue?31af", "webpack:///./src/components/Sidebars/DashboardSidebar.vue?a194", "webpack:///./src/components/Headers/DashboardHeader.vue", "webpack:///./src/components/Widgets/NotificationButton.vue", "webpack:///src/components/Widgets/NotificationButton.vue", "webpack:///./src/components/Widgets/NotificationButton.vue?c0d8", "webpack:///./src/components/Widgets/NotificationButton.vue?56a7", "webpack:///./src/components/Widgets/LogViewer.vue", "webpack:///src/components/Widgets/LogViewer.vue", "webpack:///./src/components/Widgets/LogViewer.vue?133e", "webpack:///./src/components/Widgets/LogViewer.vue?e033", "webpack:///./src/components/Widgets/ThemeToggleButton.vue", "webpack:///src/components/Widgets/ThemeToggleButton.vue", "webpack:///./src/components/Widgets/ThemeToggleButton.vue?08dc", "webpack:///./src/components/Widgets/ThemeToggleButton.vue?507e", "webpack:///src/components/Headers/DashboardHeader.vue", "webpack:///./src/components/Headers/DashboardHeader.vue?74d9", "webpack:///./src/components/Headers/DashboardHeader.vue?3458", "webpack:///./src/components/Footers/DashboardFooter.vue", "webpack:///src/components/Footers/DashboardFooter.vue", "webpack:///./src/components/Footers/DashboardFooter.vue?0d73", "webpack:///./src/components/Footers/DashboardFooter.vue?5b5a", "webpack:///./src/components/Sidebars/DashboardSettingsDrawer.vue", "webpack:///src/components/Sidebars/DashboardSettingsDrawer.vue", "webpack:///./src/components/Sidebars/DashboardSettingsDrawer.vue?b513", "webpack:///./src/components/Sidebars/DashboardSettingsDrawer.vue?abba", "webpack:///src/layouts/Dashboard.vue", "webpack:///./src/layouts/Dashboard.vue?d6b3", "webpack:///./src/layouts/Dashboard.vue?4b50", "webpack:///./src/i18n/locales/en-US.js", "webpack:///./src/i18n/locales/zh-CN.js", "webpack:///./src/i18n/index.js", "webpack:///./src/main.js", "webpack:///./src/components/Sidebars/DashboardSidebar.vue?9d4a", "webpack:///./src/components/common/DifyChatBot.vue?e1ae", "webpack:///./src/components/Widgets/FooterAnimation.vue?8647", "webpack:///./src/components/Widgets/ThemeToggleButton.vue?8c49", "webpack:///./src/views/ProjectManager.vue?ceed", "webpack:///./src/components/Widgets/LogViewer.vue?036a", "webpack:///./src/api/axiosInstance.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "jsonpScriptSrc", "p", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "code", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "error", "clearTimeout", "chunk", "errorType", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "map", "webpackContext", "req", "id", "webpackContextResolve", "keys", "render", "_vm", "this", "_c", "_self", "attrs", "isReady", "layout", "_v", "staticRenderFns", "mounted", "difyChatbotConfig", "token", "baseUrl", "systemVariables", "userVariables", "containerProps", "draggable", "dragAxis", "defer", "style", "textContent", "component", "components", "DifyChatBot", "computed", "$route", "meta", "isDarkMode", "$store", "state", "darkMode", "created", "documentElement", "classList", "add", "remove", "watch", "newValue", "scopedSlots", "_u", "fn", "on", "createNewProject", "proxy", "staticClass", "columns", "projects", "record", "dbFile", "onCustomRow", "h", "$createElement", "title", "dataIndex", "width", "ellipsis", "customRender", "text", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "align", "fixed", "click", "selectProject", "confirm", "deleteProject", "tempProjectName", "methods", "response", "axios", "Array", "isArray", "project", "createdAt", "now", "toString", "$message", "encodedDbFile", "encodeURIComponent", "validationUrl", "valid", "dispatch", "projectName", "localStorage", "removeItem", "$router", "success", "_error$response", "delete", "fetchProjects", "$confirm", "content", "replace", "okText", "cancelText", "onOk", "test", "warning", "onCancel", "_response$data", "post", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirect", "ProjectManager", "props", "route", "defaultTab", "hash", "addLayoutToRoute", "parentLayout", "children", "childRoute", "router", "base", "process", "scroll<PERSON>eh<PERSON>or", "to", "from", "savedPosition", "selector", "behavior", "x", "y", "onReady", "currentPath", "currentRoute", "catch", "beforeEach", "next", "validTabs", "currentTab", "includes", "isSamePath", "isSameHash", "is<PERSON>ame<PERSON><PERSON>y", "JSON", "stringify", "query", "after<PERSON>ach", "Vuex", "processListModule", "namespaced", "currentPage", "scrollPosition", "lastViewedPid", "mutations", "setCurrentPage", "page", "setScrollPosition", "position", "setLastViewedPid", "pid", "resetState", "clearLastViewedPid", "actions", "updateCurrentPage", "commit", "updateScrollPosition", "updateLastViewedPid", "Store", "processList", "nodes", "selectedNodeIp", "activeUploadTask", "activeDownloadTask", "activeTask", "activeToolTask", "repositoryDownloadResults", "currentProject", "getItem", "currentProjectName", "sidebarColor", "notifications", "language", "smartSearchResults", "getters", "unreadNotifications", "filter", "read", "setNodes", "setSelectedNodeIp", "ip", "setActiveUploadTask", "task", "setActiveDownloadTask", "setActiveTask", "clearActiveTask", "setActiveToolTask", "clearActiveToolTask", "setRepositoryDownloadResults", "results", "clearRepositoryDownloadResults", "setCurrentProject", "setItem", "setSidebarColor", "color", "setLanguage", "addNotification", "notification", "unshift", "markNotificationsAsRead", "for<PERSON>ach", "clearNotifications", "setDarkMode", "isDark", "setSmartSearchResults", "clearSmartSearch", "params", "detail", "updateUploadTask", "updateDownloadTask", "updateTask", "updateToolTask", "updateRepositoryDownloadResults", "updateSidebarColor", "updateLanguage", "time", "toLocaleTimeString", "toggleDarkMode", "updateSmartSearchResults", "clearProjectStates", "root", "switchProject", "isRouterViewMounted", "$nextTick", "class", "navbarFixed", "sidebarCollapsed", "layoutClass", "sidebarTheme", "toggleSidebar", "toggleSettingsDrawer", "directives", "rawName", "expression", "$event", "showSettingsDrawer", "toggleNavbarPosition", "updateSidebarTheme", "backgroundColor", "open", "_s", "$t", "ref", "currentColor", "direction", "animationFrame", "containerWidth", "animalWidth", "speed", "jumpHeight", "isJumping", "jumpDirection", "maxJumpHeight", "jumpSpeed", "jumpProbability", "colorIndex", "colors", "frameCount", "frameSkip", "$el", "offsetWidth", "updateAnimalColor", "initAnimation", "addEventListener", "handleResize", "beforeUnmount", "removeEventListener", "cancelAnimationFrame", "animate", "$refs", "animal", "Math", "random", "left", "bottom", "requestAnimationFrame", "FooterAnimation", "mapState", "Boolean", "default", "String", "top", "staticStyle", "getDisplayProjectName", "_e", "preventDefault", "currentLanguageLabel", "slot", "changeLanguage", "$emit", "resizeEventHandler", "_l", "node", "selectNode", "host_name", "selectedNode", "goToProjects", "getPopupContainer", "wrapper", "onPopoverVisibleChange", "clearAllNotifications", "model", "notificationVisible", "callback", "$$v", "toggleNotifications", "body", "mapGetters", "mapActions", "stopPropagation", "visible", "$forceUpdate", "getElementById", "showLogModal", "padding", "handleCancel", "currentNodeInfo", "selectedLogLevel", "logLevelOptions", "level", "toLowerCase", "loading", "fetchLogs", "logs", "filteredLogs", "log", "index", "_log$log_level", "getLogLevelClass", "log_level", "formatTime", "timestamp", "log_content", "total", "pageSize", "handlePageChange", "currentNode", "find", "levels", "Set", "sort", "page_size", "a", "b", "timeA", "getTime", "timeB", "logContent", "scrollTop", "date", "toggleTheme", "NotificationButton", "LogViewer", "ThemeToggleButton", "searchLoading", "mapMutations", "lang", "$i18n", "locale", "goToProcessInfo", "targetRoute", "updateSelectedNode", "_this$nodes", "_this$nodes2", "immediate", "handler", "newVal", "initializeNodesData", "activated", "rtl", "getContainer", "handleSidebarColorChange", "sidebarColorModel", "sidebarThemeModel", "navbarFixedModel", "VueGitHubButtons", "useCache", "newColor", "DashboardSidebar", "DashboardHeader", "DashboardFooter", "DashboardSettingsDrawer", "theme", "common", "home", "settings", "clearAll", "noNotifications", "configureNodes", "configureProxy", "detectReachableIps", "taskProgress", "refresh", "lightMode", "selectedNodes", "copiedToClipboard", "copyFailed", "clear", "configuratorset", "sidenavType", "configurator", "headTop<PERSON>", "package", "hardware", "mount", "port", "docker", "k8s", "fileUpload", "fileDownload", "aiBash", "testcase", "tool", "configureTool", "spiderTool", "generalTool", "uploadToolPackage", "selectToolPackage", "editScript", "start", "editShellScript", "confirmScript", "scriptReady", "localSaveDirectory", "viewResult", "selectReachableIp", "hostName", "status", "progress", "errorDetails", "fileSize", "failed", "sidebar", "taskPanel", "envAwareness", "processInfo", "packageInfo", "hardwareInfo", "filesystemInfo", "portInfo", "dockerInfo", "k8sInfo", "codeInfo", "materialInfo", "securityTool", "fileDown", "testCases", "llmAutoTesting", "aiTaintAnalysis", "smartOrchestration", "toolPanel", "hostConfig", "cbhConfig", "repositoryConfig", "executeCase", "selectFile", "clickToSelect", "uploadPath", "enterUploadPath", "startUpload", "uploadProgress", "uploadResults", "enterDownloadPath", "startDownload", "downloadProgress", "addHost", "exportSelected", "deleteSelected", "downloadTemplate", "uploadTemplate", "save", "edit", "copy", "cancel", "ip<PERSON><PERSON><PERSON>", "sshPort", "loginUser", "loginPassword", "switchRootCmd", "switchRootPwd", "addRepository", "downloadSelected", "selectDownloadPath", "microservice", "repositoryUrl", "branchName", "validation", "invalidUrl", "unsupportedFormat", "missing<PERSON><PERSON><PERSON>", "parseError", "download", "selectPath", "downloading", "starting", "partialSuccess", "cloneError", "repositoryDownload", "pending", "completed", "viewLogs", "noNodeSelected", "selectLevel", "noLogs", "fetchError", "management", "searchButton", "resetButton", "clearResults", "caseColumn", "number", "similarity", "prepareCondition", "testSteps", "expectedResult", "smartAnalysis", "startAnalysis", "caseAnalysis", "naturalLanguageQuery", "queryPlaceholder", "inputRequired", "searchFailed", "searchError", "resultsCleared", "topK", "scoreThreshold", "searchResults", "foundResults", "VueI18n", "messages", "enUS", "zhCN", "getDefaultLanguage", "savedLanguage", "browserLang", "navigator", "userLanguage", "startsWith", "i18n", "fallback<PERSON><PERSON><PERSON>", "silentTranslationWarn", "Antd", "config", "productionTip", "SimpleLayout", "DashboardLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$axios", "store", "App", "$mount", "axiosInstance", "baseURL", "VUE_APP_BASE_URL"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAGnBC,EAAqB,CACxB,IAAO,GAMJjB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASa,EAAe7B,GACvB,OAAOyB,EAAoBK,EAAI,cAAgB,GAAG9B,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,MAInsB,SAASyB,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAUgC,QAGnC,IAAIC,EAASL,EAAiB5B,GAAY,CACzCK,EAAGL,EACHkC,GAAG,EACHF,QAAS,IAUV,OANAlB,EAAQd,GAAUW,KAAKsB,EAAOD,QAASC,EAAQA,EAAOD,QAASN,GAG/DO,EAAOC,GAAI,EAGJD,EAAOD,QAKfN,EAAoBS,EAAI,SAAuBlC,GAC9C,IAAImC,EAAW,GAIXC,EAAY,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,GAC3ZR,EAAmB5B,GAAUmC,EAASvB,KAAKgB,EAAmB5B,IACzB,IAAhC4B,EAAmB5B,IAAkBoC,EAAUpC,IACtDmC,EAASvB,KAAKgB,EAAmB5B,GAAW,IAAIqC,SAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,eAAiB,GAAGxC,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,OAC3qByC,EAAWhB,EAAoBK,EAAIU,EACnCE,EAAmBC,SAASC,qBAAqB,QAC7CxC,EAAI,EAAGA,EAAIsC,EAAiBpC,OAAQF,IAAK,CAChD,IAAIyC,EAAMH,EAAiBtC,GACvB0C,EAAWD,EAAIE,aAAa,cAAgBF,EAAIE,aAAa,QACjE,GAAe,eAAZF,EAAIG,MAAyBF,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIW,EAAoBN,SAASC,qBAAqB,SACtD,IAAQxC,EAAI,EAAGA,EAAI6C,EAAkB3C,OAAQF,IAAK,CAC7CyC,EAAMI,EAAkB7C,GACxB0C,EAAWD,EAAIE,aAAa,aAChC,GAAGD,IAAaN,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIY,EAAUP,SAASQ,cAAc,QACrCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WACfF,EAAQG,OAASf,EACjBY,EAAQI,QAAU,SAASC,GAC1B,IAAIC,EAAUD,GAASA,EAAME,QAAUF,EAAME,OAAOC,KAAOjB,EACvDkB,EAAM,IAAIC,MAAM,qBAAuB5D,EAAU,cAAgBwD,EAAU,KAC/EG,EAAIE,KAAO,wBACXF,EAAIH,QAAUA,SACP5B,EAAmB5B,GAC1BkD,EAAQY,WAAWC,YAAYb,GAC/BX,EAAOoB,IAERT,EAAQV,KAAOC,EAEf,IAAIuB,EAAOrB,SAASC,qBAAqB,QAAQ,GACjDoB,EAAKC,YAAYf,MACfgB,MAAK,WACPtC,EAAmB5B,GAAW,MAMhC,IAAImE,EAAqBxD,EAAgBX,GACzC,GAA0B,IAAvBmE,EAGF,GAAGA,EACFhC,EAASvB,KAAKuD,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAI/B,SAAQ,SAASC,EAASC,GAC3C4B,EAAqBxD,EAAgBX,GAAW,CAACsC,EAASC,MAE3DJ,EAASvB,KAAKuD,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS3B,SAASQ,cAAc,UAGpCmB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb/C,EAAoBgD,IACvBH,EAAOI,aAAa,QAASjD,EAAoBgD,IAElDH,EAAOZ,IAAM7B,EAAe7B,GAG5B,IAAI2E,EAAQ,IAAIf,MAChBS,EAAmB,SAAUd,GAE5Be,EAAOhB,QAAUgB,EAAOjB,OAAS,KACjCuB,aAAaJ,GACb,IAAIK,EAAQlE,EAAgBX,GAC5B,GAAa,IAAV6E,EAAa,CACf,GAAGA,EAAO,CACT,IAAIC,EAAYvB,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE2B,EAAUxB,GAASA,EAAME,QAAUF,EAAME,OAAOC,IACpDiB,EAAMK,QAAU,iBAAmBhF,EAAU,cAAgB8E,EAAY,KAAOC,EAAU,IAC1FJ,EAAMM,KAAO,iBACbN,EAAMvB,KAAO0B,EACbH,EAAMnB,QAAUuB,EAChBF,EAAM,GAAGF,GAEVhE,EAAgBX,QAAWkF,IAG7B,IAAIV,EAAUW,YAAW,WACxBd,EAAiB,CAAEjB,KAAM,UAAWK,OAAQa,MAC1C,MACHA,EAAOhB,QAAUgB,EAAOjB,OAASgB,EACjC1B,SAASqB,KAAKC,YAAYK,GAG5B,OAAOjC,QAAQ+C,IAAIjD,IAIpBV,EAAoB4D,EAAIxE,EAGxBY,EAAoB6D,EAAI3D,EAGxBF,EAAoB8D,EAAI,SAASxD,EAASkD,EAAMO,GAC3C/D,EAAoBgE,EAAE1D,EAASkD,IAClC1E,OAAOmF,eAAe3D,EAASkD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE/D,EAAoBoE,EAAI,SAAS9D,GACX,qBAAX+D,QAA0BA,OAAOC,aAC1CxF,OAAOmF,eAAe3D,EAAS+D,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAOmF,eAAe3D,EAAS,aAAc,CAAEiE,OAAO,KAQvDvE,EAAoBwE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQvE,EAAoBuE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAK7F,OAAO8F,OAAO,MAGvB,GAFA5E,EAAoBoE,EAAEO,GACtB7F,OAAOmF,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOvE,EAAoB8D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIR3E,EAAoB+E,EAAI,SAASxE,GAChC,IAAIwD,EAASxD,GAAUA,EAAOmE,WAC7B,WAAwB,OAAOnE,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAP,EAAoB8D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR/D,EAAoBgE,EAAI,SAASgB,EAAQC,GAAY,OAAOnG,OAAOC,UAAUC,eAAeC,KAAK+F,EAAQC,IAGzGjF,EAAoBK,EAAI,IAGxBL,EAAoBkF,GAAK,SAAShD,GAA2B,MAApBiD,QAAQjC,MAAMhB,GAAYA,GAEnE,IAAIkD,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAWjG,KAAK2F,KAAKM,GAC5CA,EAAWjG,KAAOf,EAClBgH,EAAaA,EAAWG,QACxB,IAAI,IAAI5G,EAAI,EAAGA,EAAIyG,EAAWvG,OAAQF,IAAKP,EAAqBgH,EAAWzG,IAC3E,IAAIU,EAAsBiG,EAI1B/F,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,wJC1QT,W,uECAA,IAAI+F,EAAM,CACT,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,gBAAiB,OACjB,aAAc,OACd,gBAAiB,OACjB,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,WAAY,OACZ,cAAe,OACf,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,WAAY,OACZ,cAAe,OACf,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,YAAa,OACb,eAAgB,OAChB,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,QAAS,OACT,aAAc,OACd,gBAAiB,OACjB,WAAY,OACZ,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,YAAa,OACb,eAAgB,OAChB,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,gBAAiB,OACjB,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,QAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAO1F,EAAoB2F,GAE5B,SAASC,EAAsBF,GAC9B,IAAI1F,EAAoBgE,EAAEwB,EAAKE,GAAM,CACpC,IAAIjF,EAAI,IAAI0B,MAAM,uBAAyBuD,EAAM,KAEjD,MADAjF,EAAE2B,KAAO,mBACH3B,EAEP,OAAO+E,EAAIE,GAEZD,EAAeI,KAAO,WACrB,OAAO/G,OAAO+G,KAAKL,IAEpBC,EAAe5E,QAAU+E,EACzBrF,EAAOD,QAAUmF,EACjBA,EAAeE,GAAK,Q,6DCvSpB,W,2DCAIG,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAAEJ,EAAIK,QAASH,EAAGF,EAAIM,OAAO,CAACjF,IAAI,aAAa,CAAC6E,EAAG,gBAAgB,GAAGA,EAAG,MAAM,CAACF,EAAIO,GAAG,gBAAgBL,EAAG,gBAAgB,IAEvNM,EAAkB,GCFlBT,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAElEM,EAAkB,GCKP,GACf/C,KAAA,cACAgD,UAEAnB,OAAAoB,kBAAA,CACAC,MAAA,mBACAC,QAAA,wBACAC,gBAAA,GAIAC,cAAA,GAIAC,eAAA,GAEAC,WAAA,EAEAC,SAAA,QAIA,MAAAnE,EAAA3B,SAAAQ,cAAA,UACAmB,EAAAZ,IAAA,qCACAY,EAAA8C,GAAA,mBACA9C,EAAAoE,OAAA,EACA/F,SAAAqB,KAAAC,YAAAK,GAGA,MAAAqE,EAAAhG,SAAAQ,cAAA,SACAwF,EAAAC,YAAA,yNASAjG,SAAAqB,KAAAC,YAAA0E,KC/CmW,I,wBCQ/VE,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCGA,GACf5D,KAAA,MACA6D,WAAA,CACAC,eAEAjJ,OACA,OACA+H,SAAA,IAGAmB,SAAA,CAGAlB,SACA,sBAAAmB,OAAAC,KAAApB,QAAA,cAEAqB,aACA,YAAAC,OAAAC,MAAAC,WAGAC,UACA,KAAA1B,SAAA,EAEA,KAAAuB,OAAAC,MAAAC,SACA3G,SAAA6G,gBAAAC,UAAAC,IAAA,aAEA/G,SAAA6G,gBAAAC,UAAAE,OAAA,cAGAC,MAAA,CAEAT,WAAAU,GACAA,EACAlH,SAAA6G,gBAAAC,UAAAC,IAAA,aAEA/G,SAAA6G,gBAAAC,UAAAE,OAAA,gBCzD6T,ICOzT,EAAY,eACd,EACApC,EACAS,GACA,EACA,KACA,KACA,MAIa,I,0CClBXT,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,QAAQkC,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,QAAQ0D,GAAG,WAAW,MAAO,CAACtC,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWqC,GAAG,CAAC,MAAQzC,EAAI0C,mBAAmB,CAAC1C,EAAIO,GAAG,eAAeoC,OAAM,MAAS,CAACzC,EAAG,UAAU,CAAC0C,YAAY,gBAAgBxC,MAAM,CAAC,QAAUJ,EAAI6C,QAAQ,cAAc7C,EAAI8C,SAAS,UAAUC,GAAUA,EAAOC,OAAO,UAAYhD,EAAIiD,gBAAgB,IAAI,IAE9bzC,EAAkB,G,YCoBP,GACf/C,KAAA,iBACAnF,OAAA,MAAA4K,EAAA,KAAAC,eACA,OACAN,QAAA,CACA,CACAO,MAAA,OACAC,UAAA,OACAvE,IAAA,OACAwE,MAAA,QACAC,UAAA,EACAC,aAAAC,GACAP,EAAA,QAAAA,EAAA,sBAEA,uCACAO,KAKA,CACAL,MAAA,QACAC,UAAA,SACAvE,IAAA,SACAwE,MAAA,QACAC,UAAA,GAEA,CACAH,MAAA,OACAC,UAAA,YACAvE,IAAA,YACAwE,MAAA,QACAE,aAAAC,GACA,IAAAC,KAAAD,GAAAE,eAAA,SACAC,KAAA,UACAC,MAAA,UACAC,IAAA,UACAC,KAAA,UACAC,OAAA,aAIA,CACAZ,MAAA,KACAtE,IAAA,SACAwE,MAAA,QACAW,MAAA,SACAC,MAAA,QACAV,cAAAC,EAAAV,IACAG,EAAA,uBACA,KAAAA,EAAA,wBAEA,qBACAiB,IAAA,KAAAC,cAAArB,KAAA,UAAAG,EAAA,6BAKA,qBAEA,gBACA,kBAFAmB,IAAA,KAAAC,cAAAvB,KAAA,CAAAG,EAAA,wBAIA,yBAOAJ,SAAA,GACAyB,gBAAA,KAGAC,QAAA,CACAvB,YAAAF,GACA,OACAN,GAAA,CACA0B,gBAMA,sBACA,IACA,MAAAM,QAAAC,OAAAtG,IAAA,iBACAuG,MAAAC,QAAAH,EAAAnM,MACA,KAAAwK,SAAA2B,EAAAnM,KAAAmH,IAAAoF,IAAA,CACApH,KAAAoH,EAAApH,MAAA,GACAuF,OAAA6B,EAAA7B,QAAA,GACA8B,UAAAD,EAAAC,WAAA,GACAhG,IAAA+F,EAAA7B,QAAAU,KAAAqB,MAAAC,eAGA,KAAAlC,SAAA,GACA1D,QAAAjC,MAAA,YAAAsH,EAAAnM,OAEA,MAAA6E,GACAiC,QAAAjC,MAAA,YAAAA,GACA,KAAA8H,SAAA9H,MAAA,YACA,KAAA2F,SAAA,KAIA,oBAAA+B,GACA,UAAAA,QAAA,IAAAA,MAAA7B,OAGA,OAFA5D,QAAAjC,MAAA,UAAA0H,QACA,KAAAI,SAAA9H,MAAA,WAIA,IACA,MAAA+H,EAAAC,mBAAAN,EAAA7B,QACAoC,EAAA,0BAAAF,EAEAT,QAAAC,OAAAtG,IAAAgH,GACAX,EAAAnM,KAAA+M,aACA,KAAAzD,OAAA0D,SAAA,iBACAtC,OAAA6B,EAAA7B,OACAuC,YAAAV,EAAApH,aAEA,KAAAmE,OAAA0D,SAAA,cAGAE,aAAAC,WAAA,gBACAD,aAAAC,WAAA,iBACAD,aAAAC,WAAA,sBACAD,aAAAC,WAAA,wBACAD,aAAAC,WAAA,0BAEA,KAAAC,QAAAtM,KAAA,SACA,KAAA6L,SAAAU,QAAA,YAEAvG,QAAAjC,MAAA,QAAAsH,EAAAnM,KAAA6E,OACA,KAAA8H,SAAA9H,MAAAsH,EAAAnM,KAAA6E,OAAA,gBAEA,MAAAA,GAAA,IAAAyI,EACAxG,QAAAjC,MAAA,UAAAA,GACA,KAAA8H,SAAA9H,OAAA,QAAAyI,EAAAzI,EAAAsH,gBAAA,IAAAmB,GAAA,QAAAA,IAAAtN,YAAA,IAAAsN,OAAA,EAAAA,EAAAzI,QAAA,YAIA,oBAAA0H,GACA,UAAAA,QAAA,IAAAA,KAAA7B,OAKA,UACA0B,OAAAmB,OAAA,iBAAAV,mBAAAN,EAAA7B,SACA,KAAAiC,SAAAU,QAAA,gBACA,KAAAG,gBACA,MAAA3I,GACAiC,QAAAjC,MAAA,0BAAAA,GACA,KAAA8H,SAAA9H,MAAA,eAVA,KAAA8H,SAAA9H,MAAA,YAcA,8BAAAgG,eACA,IACA,MAAAoC,QAAA,IAAA1K,QAAA,CAAAC,EAAAC,KACA,KAAAgL,SAAA,CACA3C,MAAA,QACA4C,QAAA9C,KAAA,OAAAA,EAAA,8BAGA,sBACAxI,IACA,MAAA8D,EAAA9D,EAAAuB,OAAAuC,MAAAyH,QAAA,sBACA,KAAA1B,gBAAA/F,EACA9D,EAAAuB,OAAAuC,YACA0E,EAAA,aAEA,4FAKAgD,OAAA,KACAC,WAAA,KACAC,SACA,KAAA7B,gBAKA,mBAAA8B,KAAA,KAAA9B,sBAIAzJ,EAAA,KAAAyJ,kBAHA,KAAAU,SAAAqB,QAAA,4BACAzL,QAAAE,WANA,KAAAkK,SAAAqB,QAAA,WACAzL,QAAAE,UASAwL,cACAxL,SAKA,GAAAwK,EAAA,KAAAiB,EACA,MAAA/B,QAAAC,OAAA+B,KAAA,qBACAhJ,KAAA8H,UAGA,KAAAO,gBACA,KAAAb,SAAAU,QAAA,WACA,QAAAa,EAAA/B,EAAAnM,YAAA,IAAAkO,KAAAxD,cACA,KAAAoB,cAAAK,EAAAnM,OAGA,MAAA6E,GACAA,IACAiC,QAAAjC,MAAA,8BAAAA,GACA,KAAA8H,SAAA9H,MAAA,eAKA4E,UACA,KAAA+D,kBCjPuV,ICQnV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCffY,OAAIC,IAAIC,QAER,IAAIC,EAAS,CACZ,CACCC,KAAM,IACNC,SAAU,aAEX,CACCD,KAAM,YACNrJ,KAAM,iBACN6C,OAAQ,SACRe,UAAW2F,GAEZ,CACCF,KAAM,QACNrJ,KAAM,OACN6C,OAAQ,YACRe,UAAWA,IAAM,sFAElB,CACCyF,KAAM,IACNzF,UAAWA,IAAM,iDAElB,CACCyF,KAAM,WACNrJ,KAAM,UACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,gBACNrJ,KAAM,gBACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,WACNrJ,KAAM,UACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,YACNrJ,KAAM,WACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,cACNrJ,KAAM,aACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,QACNrJ,KAAM,OACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,UACNrJ,KAAM,SACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,cACNrJ,KAAM,aACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,aACNrJ,KAAM,WACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,iBACNrJ,KAAM,eACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,UACNrJ,KAAM,SACN6C,OAAQ,YACRe,UAAWA,IAAM,gDACjB4F,MAAQC,IAAK,CACZC,WAAYD,EAAME,KAAKnB,QAAQ,IAAM,KAAO,UAG9C,CACCa,KAAM,cACNrJ,KAAM,aACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,UACNrJ,KAAM,SACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,YACNrJ,KAAM,WACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,UACNrJ,KAAM,SACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,YACNrJ,KAAM,WACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,gBACNrJ,KAAM,cACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,uBACNrJ,KAAM,qBACN6C,OAAQ,YACRe,UAAWA,IAAM,iDAElB,CACCyF,KAAM,SACNrJ,KAAM,QACN6C,OAAQ,YACRe,UAAWA,IAAM,uFAMnB,SAASgG,EAAkBH,EAAOI,EAAe,WAShD,OAPAJ,EAAMxF,KAAOwF,EAAMxF,MAAQ,GAC3BwF,EAAMxF,KAAKpB,OAAS4G,EAAM5G,QAAUgH,EAEhCJ,EAAMK,WAETL,EAAMK,SAAWL,EAAMK,SAAS9H,IAAO+H,GAAgBH,EAAkBG,EAAYN,EAAMxF,KAAKpB,UAE1F4G,EAGRL,EAASA,EAAOpH,IAAOyH,GAAWG,EAAkBH,IAEpD,MAAMO,EAAS,IAAIb,OAAU,CAC5BlI,KAAM,OACNgJ,KAAMC,IACNd,SACAe,eAAgBC,EAAIC,EAAMC,GACzB,OAAKF,EAAGT,KACA,CACNY,SAAUH,EAAGT,KACba,SAAU,UAGL,CACNC,EAAG,EACHC,EAAG,EACHF,SAAU,aAMbR,EAAOW,QAAQ,KACd,MAAMC,EAAcZ,EAAOa,aAAaxB,KACpB,MAAhBuB,GACHZ,EAAOrO,KAAK,aAAamP,MAAMpM,IAC9B,GAAiB,yBAAbA,EAAIsB,KACP,MAAMtB,MAOVsL,EAAOe,WAAW,CAACX,EAAIC,EAAMW,KAC5B,GAAiB,YAAbZ,EAAGf,KAAqB,CAC3B,MAAM4B,EAAY,CAAC,OAAQ,OACrBC,EAAad,EAAGT,KAAKnB,QAAQ,IAAM,IAEzC,IAAKyC,EAAUE,SAASD,GACvB,OAAOF,EAAK,CACX3B,KAAM,UACNM,KAAM,QACNnB,SAAS,IAKZ,MAAM4C,EAAahB,EAAGf,OAAUgB,EAAKhB,KAC/BgC,EAAajB,EAAGT,OAAUU,EAAKV,KAC/B2B,EAAcC,KAAKC,UAAUpB,EAAGqB,SAAYF,KAAKC,UAAUnB,EAAKoB,OAGlEL,GAAcC,GAAcC,EAC/BN,GAAK,GAELA,MAKFhB,EAAO0B,UAAU,CAACtB,EAAIC,QAGPL,Q,kCC3Nff,OAAIC,IAAIyC,QAGR,MAAMC,EAAoB,CACtBC,YAAY,EACZzH,MAAO,CACH0H,YAAa,EACbC,eAAgB,EAChBC,cAAe,MAEnBC,UAAW,CACPC,eAAe9H,EAAO+H,GAClB/H,EAAM0H,YAAcK,GAExBC,kBAAkBhI,EAAOiI,GACrBjI,EAAM2H,eAAiBM,GAE3BC,iBAAiBlI,EAAOmI,GACpBnI,EAAM4H,cAAgBO,GAE1BC,WAAWpI,GACPA,EAAM0H,YAAc,EACpB1H,EAAM2H,eAAiB,GAG3BU,mBAAmBrI,GACfA,EAAM4H,cAAgB,OAG9BU,QAAS,CACLC,mBAAkB,OAAEC,GAAUT,GAC1BS,EAAO,iBAAkBT,IAE7BU,sBAAqB,OAAED,GAAUP,GAC7BO,EAAO,oBAAqBP,IAEhCS,qBAAoB,OAAEF,GAAUL,GAC5BK,EAAO,mBAAoBL,IAE/BC,YAAW,OAAEI,IACTA,EAAO,eAEXH,oBAAmB,OAAEG,IACjBA,EAAO,yBAKJ,UAAIjB,OAAKoB,MAAM,CAC1BnR,QAAS,CACLoR,YAAapB,GAEjBxH,MAAO,CACH6I,MAAO,GACPC,eAAgB,KAChBC,iBAAkB,KAClBC,mBAAoB,KACpBC,WAAY,KACZC,eAAgB,KAChBC,0BAA2B,KAC3BC,eAAgBzF,aAAa0F,QAAQ,kBACrCC,mBAAoB3F,aAAa0F,QAAQ,sBACzCE,aAAc5F,aAAa0F,QAAQ,iBAAmB,UACtDG,cAAe,GACfC,SAAU9F,aAAa0F,QAAQ,aAAe,QAC9CpJ,SAA+C,UAArC0D,aAAa0F,QAAQ,YAC/BK,mBAAoB,IAExBC,QAAS,CACLC,oBAAqB5J,GAASA,EAAMwJ,cAAcK,OAAO1M,IAAMA,EAAE2M,MAAM7S,QAE3E4Q,UAAW,CACPkC,SAAS/J,EAAO6I,GACZ7I,EAAM6I,MAAQA,GAElBmB,kBAAkBhK,EAAOiK,GACrBjK,EAAM8I,eAAiBmB,GAE3BC,oBAAoBlK,EAAOmK,GACvBnK,EAAM+I,iBAAmBoB,GAE7BC,sBAAsBpK,EAAOmK,GACzBnK,EAAMgJ,mBAAqBmB,GAE/BE,cAAcrK,EAAOmK,GACjBnK,EAAMiJ,WAAakB,GAEvBG,gBAAgBtK,GACZA,EAAMiJ,WAAa,MAEvBsB,kBAAkBvK,EAAOmK,GACrBnK,EAAMkJ,eAAiBiB,GAE3BK,oBAAoBxK,GAChBA,EAAMkJ,eAAiB,MAE3BuB,6BAA6BzK,EAAO0K,GAChC1K,EAAMmJ,0BAA4BuB,GAEtCC,+BAA+B3K,GAC3BA,EAAMmJ,0BAA4B,MAEtCyB,kBAAkB5K,GAAO,OAAEmB,EAAM,YAAEuC,IAC/B1D,EAAMoJ,eAAiBjI,EACvBnB,EAAMsJ,mBAAqB5F,EAC3BC,aAAakH,QAAQ,iBAAkB1J,GACvCwC,aAAakH,QAAQ,qBAAsBnH,GAAe,KAE9DoH,gBAAgB9K,EAAO+K,GACnB/K,EAAMuJ,aAAewB,EACrBpH,aAAakH,QAAQ,eAAgBE,IAEzCC,YAAYhL,EAAOyJ,GACfzJ,EAAMyJ,SAAWA,EACjB9F,aAAakH,QAAQ,WAAYpB,IAErCwB,gBAAgBjL,EAAOkL,GACnBlL,EAAMwJ,cAAc2B,QAAQD,IAEhCE,wBAAwBpL,GACpBA,EAAMwJ,cAAc6B,QAAQlO,GAAKA,EAAE2M,MAAO,IAE9CwB,mBAAmBtL,GACfA,EAAMwJ,cAAgB,IAE1B+B,YAAYvL,EAAOwL,GACfxL,EAAMC,SAAWuL,EACjB7H,aAAakH,QAAQ,WAAYW,GAE7BA,EACAlS,SAAS6G,gBAAgBC,UAAUC,IAAI,aAEvC/G,SAAS6G,gBAAgBC,UAAUE,OAAO,cAIlDmL,sBAAsBzL,EAAO0K,GACzB1K,EAAM0J,mBAAqBgB,GAE/BgB,iBAAiB1L,GACbA,EAAM0J,mBAAqB,KAGnCpB,QAAS,CACL,kBAAiB,MAAEtI,EAAK,OAAEwI,IACtB,IACI,IAAKxI,EAAMoJ,eAEP,YADAZ,EAAO,WAAY,IAIvB,MAAM5F,QAAiBC,OAAMtG,IAAI,cAAe,CAC5CoP,OAAQ,CACJC,QAAQ,EACRzK,OAAQnB,EAAMoJ,kBAIlBtG,MAAMC,QAAQH,EAASnM,MACvB+R,EAAO,WAAY5F,EAASnM,OAE5B8G,QAAQjC,MAAM,6BAA8BsH,EAASnM,MACrD+R,EAAO,WAAY,KAEzB,MAAOlN,GACLiC,QAAQjC,MAAM,wBAAyBA,GACvCkN,EAAO,WAAY,MAG3BqD,kBAAiB,OAAErD,GAAU2B,GACzB3B,EAAO,sBAAuB2B,IAElC2B,oBAAmB,OAAEtD,GAAU2B,GAC3B3B,EAAO,wBAAyB2B,IAEpC4B,YAAW,OAAEvD,GAAU2B,GACnB3B,EAAO,gBAAiB2B,IAE5BG,iBAAgB,OAAE9B,IACdA,EAAO,oBAEXwD,gBAAe,OAAExD,GAAU2B,GACvB3B,EAAO,oBAAqB2B,IAEhCK,qBAAoB,OAAEhC,IAClBA,EAAO,wBAEXyD,iCAAgC,OAAEzD,GAAUkC,GACxClC,EAAO,+BAAgCkC,IAE3CC,gCAA+B,OAAEnC,IAC7BA,EAAO,mCAEX0D,oBAAmB,OAAE1D,GAAUuC,GAC3BvC,EAAO,kBAAmBuC,IAE9BoB,gBAAe,OAAE3D,GAAUiB,GACvBjB,EAAO,cAAeiB,IAE1BwB,iBAAgB,OAAEzC,GAAU0C,GACxB1C,EAAO,kBAAmB,IACnB0C,EACHnN,GAAI8D,KAAKqB,MACTkJ,MAAM,IAAIvK,MAAOwK,qBACjBvC,MAAM,KAGdsB,yBAAwB,OAAE5C,IACtBA,EAAO,4BAEX8C,oBAAmB,OAAE9C,IACjBA,EAAO,uBAEX8D,gBAAe,OAAE9D,EAAM,MAAExI,IACrBwI,EAAO,eAAgBxI,EAAMC,WAEjCsM,0BAAyB,OAAE/D,GAAUkC,GACjClC,EAAO,wBAAyBkC,IAEpCgB,kBAAiB,OAAElD,IACfA,EAAO,qBAEXgE,oBAAmB,OAAEhE,EAAM,SAAE/E,IAEzB+E,EAAO,sBAAuB,MAC9BA,EAAO,wBAAyB,MAChCA,EAAO,mBACPA,EAAO,uBACPA,EAAO,kCACPA,EAAO,oBACPA,EAAO,WAAY,IACnBA,EAAO,oBAAqB,MAC5B/E,EAAS,yBAA0B,KAAM,CAAEgJ,MAAM,IACjDhJ,EAAS,iCAAkC,KAAM,CAAEgJ,MAAM,KAG7DC,eAAc,OAAElE,EAAM,SAAE/E,IAAY,OAAEtC,EAAM,YAAEuC,IAE1CD,EAAS,sBAET+E,EAAO,oBAAqB,CAAErH,SAAQuC,oB,YCrP9CxF,G,UAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC0C,YAAY,iBAAiB,CAAC1C,EAAG,mBAAmB,CAACA,EAAG,MAAM,CAAC0C,YAAY,mBAAmB,CAAE5C,EAAIwO,oBAAqBtO,EAAG,eAAeA,EAAG,MAAM,CAACF,EAAIO,GAAG,iBAAiB,MAAM,IAAI,KAE/QC,EAAkB,GCYP,GACf/C,KAAA,eACAnF,OACA,OACAkW,qBAAA,IAGA/N,UACA,KAAAgO,UAAA,KACA,KAAAD,qBAAA,MCvB+U,ICQ3U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXzO,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC0C,YAAY,mBAAmB8L,MAAM,CAAC1O,EAAI2O,YAAc,eAAiB,GAAM3O,EAAI4O,iBAAmC,GAAhB,cAAoB5O,EAAI6O,aAAazO,MAAM,CAAC,GAAK,qBAAqB,CAACF,EAAG,mBAAmB,CAACE,MAAM,CAAC,iBAAmBJ,EAAI4O,iBAAiB,aAAe5O,EAAIoL,aAAa,aAAepL,EAAI8O,cAAcrM,GAAG,CAAC,cAAgBzC,EAAI+O,iBAAiB7O,EAAG,WAAW,CAACA,EAAG,kBAAkB,CAACE,MAAM,CAAC,iBAAmBJ,EAAI4O,iBAAiB,YAAc5O,EAAI2O,YAAY,aAAe3O,EAAIoL,cAAc3I,GAAG,CAAC,qBAAuBzC,EAAIgP,qBAAqB,cAAgBhP,EAAI+O,iBAAiB7O,EAAG,mBAAmB,CAACA,EAAG,gBAAgB,GAAGA,EAAG,mBAAmBA,EAAG,MAAM,CAAC+O,WAAW,CAAC,CAACxR,KAAK,OAAOyR,QAAQ,SAAS1Q,OAASwB,EAAI4O,iBAAkBO,WAAW,uBAAuBvM,YAAY,kBAAkBH,GAAG,CAAC,MAAQ,SAAS2M,GAAQpP,EAAI4O,kBAAmB,OAAU,GAAG1O,EAAG,0BAA0B,CAACE,MAAM,CAAC,mBAAqBJ,EAAIqP,mBAAmB,YAAcrP,EAAI2O,YAAY,aAAe3O,EAAI8O,cAAcrM,GAAG,CAAC,qBAAuBzC,EAAIgP,qBAAqB,qBAAuBhP,EAAIsP,qBAAqB,mBAAqBtP,EAAIuP,mBAAmB,mBAAqBvP,EAAI+N,uBAAuB,IAAI,IAE5wCvN,EAAkB,GCFlBT,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,iBAAiB,CAAC+O,WAAW,CAAC,CAACxR,KAAK,OAAOyR,QAAQ,SAAS1Q,OAAQwB,EAAI4O,iBAAkBO,WAAW,sBAAsBvM,YAAY,gBAAgB8L,MAAM,CAC3N,oBAAsB1O,EAAIoL,aAC1B,oBAAsBpL,EAAI8O,cAC5B3N,MAAO,CAAEqO,gBAAiB,eAAiBpP,MAAM,CAAC,QAAU,KAAK,YAAc,GAAG,kBAAkB,EAAE,MAAQ,MAAM,MAAQ,UAAU,CAACF,EAAG,MAAM,CAAC0C,YAAY,qBAAqB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,iBAAiB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,gBAAgB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,iBAAiB8L,MAAM,QAAQ1O,EAAIoL,cAAgB,CAAClL,EAAG,MAAM,CAAC0C,YAAY,WAAWxC,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,OAAO,OAAS,OAAO,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,GAAK,KAAK,KAAO,eAAe,QAAU,SAASF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,eAAe,eAAe,MAAM,iBAAiB,QAAQ,kBAAkB,QAAQ,EAAI,wSAAwS,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,cAAgB,mBAAmB,OAAS,oBAAoB,IAAM,KAAK,SAAW,SAAS,YAAc,kBAAkBF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,eAAe,eAAe,MAAM,iBAAiB,QAAQ,kBAAkB,QAAQ,QAAU,MAAM,EAAI,mDAAmDF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,eAAe,eAAe,MAAM,iBAAiB,QAAQ,kBAAkB,QAAQ,QAAU,MAAM,EAAI,qDAAqDF,EAAG,SAAS,CAACE,MAAM,CAAC,GAAK,KAAK,GAAK,SAAS,EAAI,MAAM,KAAO,OAAO,OAAS,eAAe,eAAe,IAAI,iBAAiB,QAAQ,kBAAkB,WAAWF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,eAAe,eAAe,IAAI,iBAAiB,QAAQ,kBAAkB,QAAQ,EAAI,+CAA+CF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,eAAe,eAAe,MAAM,iBAAiB,QAAQ,EAAI,sBAAsBF,EAAG,MAAM,CAAC0C,YAAY,wBAAwB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,cAAc,CAAC5C,EAAIO,GAAG,yBAAyBL,EAAG,MAAM,CAAC0C,YAAY,oBAAoB1C,EAAG,MAAM,CAAC0C,YAAY,sBAAsB,CAAC1C,EAAG,SAAS,CAAC0C,YAAY,eAAexC,MAAM,CAAC,MAAQ,QAAQ,KAAO,SAAS,oBAAoB,CAAC,eAAgB,iBAAkB,mBAAmB,mBAAmBJ,EAAI4O,mBAAmB,CAAC1O,EAAG,aAAa,CAACpB,IAAI,eAAewD,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,QAAQ0D,GAAG,UAAS,KAAEiN,IAAQ,MAAO,CAACvP,EAAG,OAAO,CAAC0C,YAAY,kBAAkB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,kCAAkC,CAACzP,EAAG,cAAc,CAACpB,IAAI,WAAW,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,aAAa,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,MAAQ,6BAA6B,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,8ZAA8ZF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,8BAA8B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,WAAW,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,aAAa,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,YAAY,UAAU,EAAI,qNAAqN,YAAY,iBAAiBF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,8BAA8B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,YAAY,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,cAAc,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,UAAU,EAAI,2BAA2BF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,UAAU,EAAI,uTAAuTF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,+BAA+B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,cAAc,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,gBAAgB,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,UAAU,EAAI,0SAA0S,YAAY,iBAAiBF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,iCAAiC,GAAGzP,EAAG,cAAc,CAACpB,IAAI,kBAAkB,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,UAAU,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,MAAQ,KAAK,OAAS,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,uWAAuWF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,2BAA2B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,UAAU,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,YAAY,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,EAAI,MAAM,EAAI,MAAM,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,kkFAAkkFF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,6BAA6B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,cAAc,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,gBAAgB,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,UAAU,EAAI,wgCAAwgCF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,0BAA0B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,aAAa,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,eAAe,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,wdAAwdF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,2BAA2B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,iBAAiB,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,mBAAmB,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,wvBAAwvBF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,+BAA+B,IAAI,GAAGzP,EAAG,aAAa,CAACpB,IAAI,eAAewD,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,QAAQ0D,GAAG,UAAS,KAAEiN,IAAQ,MAAO,CAACvP,EAAG,OAAO,CAAC0C,YAAY,kBAAkB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,kCAAkC,CAACzP,EAAG,cAAc,CAACpB,IAAI,MAAM,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,YAAY,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,4QAA4QF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,yBAAyB,GAAGzP,EAAG,cAAc,CAACpB,IAAI,eAAe,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,YAAY,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,8cAA8cF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,6BAA6B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,iBAAiB,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,cAAc,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,+cAA+cF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,2BAA2B,IAAI,GAAGzP,EAAG,aAAa,CAACpB,IAAI,iBAAiBwD,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,QAAQ0D,GAAG,UAAS,KAAEiN,IAAQ,MAAO,CAACvP,EAAG,OAAO,CAAC0C,YAAY,kBAAkB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,oCAAoC,CAACzP,EAAG,cAAc,CAACpB,IAAI,aAAa,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,cAAc,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,8PAA8PF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,4BAA4B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,gBAAgB,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,kBAAkB,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,iuBAAiuBF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,8BAA8B,GAAGzP,EAAG,cAAc,CAACpB,IAAI,uBAAuB,CAACoB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAK,yBAAyB,CAACF,EAAG,OAAO,CAAC0C,YAAY,QAAQ,CAAC1C,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,mUAAmUF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,qCAAqC,IAAI,GAAGzP,EAAG,aAAa,CAACpB,IAAI,kBAAkBwD,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,QAAQ0D,GAAG,UAAS,KAAEiN,IAAQ,MAAO,CAACvP,EAAG,OAAO,CAAC0C,YAAY,kBAAkB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,uCAAuC,IAAI,GAAGzP,EAAG,MAAM,CAAC0C,YAAY,kBAAkB,CAAC1C,EAAG,mBAAmB,CAAC0C,YAAY,oBAAoB,QAEhoiBpC,EAAkB,GCLlBT,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC0C,YAAY,8BAA8B,CAAC1C,EAAG,MAAM,CAAC0P,IAAI,SAAShN,YAAY,UAAU,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,aAAaxC,MAAM,CAAC,MAAQ,6BAA6B,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAOJ,EAAI6P,aAAa,EAAI,+qBAE1TrP,EAAkB,GCSP,GACflI,OACA,OACAwR,SAAA,EACAgG,UAAA,EACAC,eAAA,KACAC,eAAA,EACAC,YAAA,GACAC,MAAA,GACAC,WAAA,EACAC,WAAA,EACAC,cAAA,EACAC,cAAA,GACAC,UAAA,GACAC,gBAAA,KACAC,WAAA,EACAC,OAAA,oDACAC,WAAA,EACAC,UAAA,EACAf,aAAA,YAGApP,UACA,KAAAuP,eAAA,KAAAa,IAAAC,YACA,KAAAC,oBACA,KAAAC,gBACA1R,OAAA2R,iBAAA,cAAAC,eAEAC,gBACA7R,OAAA8R,oBAAA,cAAAF,cACAG,qBAAA,KAAAtB,iBAEAvL,QAAA,CACAwM,gBAEA,KAAAlH,SAAA,EACA,KAAAwH,WAEAA,UACA,KAAAX,aAGA,KAAAA,YAAA,KAAAC,UAAA,SAEA,KAAA9G,UAAA,KAAAoG,MAAA,KAAAJ,UAGA,KAAAhG,SAAA,KAAAkG,eAAA,KAAAC,aACA,KAAAH,WAAA,EACA,KAAAyB,MAAAC,SACA,KAAAD,MAAAC,OAAAvP,UAAAE,OAAA,gBACA,KAAAoP,MAAAC,OAAAvP,UAAAC,IAAA,iBAEA,KAAA4H,SAAA,IACA,KAAAgG,UAAA,EACA,KAAAyB,MAAAC,SACA,KAAAD,MAAAC,OAAAvP,UAAAE,OAAA,eACA,KAAAoP,MAAAC,OAAAvP,UAAAC,IAAA,mBAKA,KAAAkO,WAAAqB,KAAAC,SAAA,KAAAlB,kBACA,KAAAJ,WAAA,EACA,KAAAC,cAAA,EACA,KAAAF,WAAA,EAEA,KAAAM,YAAA,KAAAA,WAAA,QAAAC,OAAA5X,OACA,KAAAiY,qBAIA,KAAAX,YACA,KAAAD,YAAA,KAAAI,UAAA,KAAAF,cAEA,KAAAF,YAAA,KAAAG,cACA,KAAAD,eAAA,EACA,KAAAF,YAAA,aAAAE,gBACA,KAAAD,WAAA,EACA,KAAAD,WAAA,KAMA,KAAAoB,MAAAC,SACA,KAAAD,MAAAC,OAAArQ,MAAAwQ,KAAA,KAAA7H,SAAA,KACA,KAAAyH,MAAAC,OAAArQ,MAAAyQ,OAAA,OAAAzB,WAAA,MAIA,KAAAJ,eAAA8B,sBAAA,KAAAP,UAEAJ,eACA,KAAAlB,eAAA,KAAAa,IAAAC,YAEA,KAAAhH,SAAA,KAAAkG,eAAA,KAAAC,cACA,KAAAnG,SAAA,KAAAkG,eAAA,KAAAC,cAGAc,oBAEA,KAAAlB,aAAA,KAAAa,OAAA,KAAAD,eCjHuW,ICQnW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QC6OA,GACfnP,WAAA,CACAwQ,mBAEAtQ,SAAA,IACAuQ,eAAA,sCAEA9K,MAAA,CAEA2H,iBAAA,CACAhT,KAAAoW,QACAC,SAAA,GAIA7G,aAAA,CACAxP,KAAAsW,OACAD,QAAA,WAIAnD,aAAA,CACAlT,KAAAsW,OACAD,QAAA,UAGA3Z,OACA,WC3RwW,ICQpW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXyH,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAGF,EAAI2O,YAAc,UAAY,MAAM,CAACtT,IAAI,YAAY+E,MAAM,CAAC,aAAaJ,EAAImS,MAAM,CAACjS,EAAG,kBAAkB,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,SAAS,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,GAAG,GAAK,KAAK,CAACF,EAAG,MAAM,CAAC0C,YAAY,sBAAsB,CAAC1C,EAAG,cAAc,CAAC0C,YAAY,oBAAoB8L,MAAM,CAAC,CAAE,OAA8B,MAApB1O,EAAIyB,OAAOqF,MAAoC,MAApB9G,EAAIyB,OAAOqF,KAAe,MAAM9G,EAAIoL,mCAAqC,IAAIhL,MAAM,CAAC,GAAK,MAAM,CAACF,EAAG,MAAM,CAACkS,YAAY,CAAC,eAAe,OAAOhS,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,MAAQ,KAAK,OAAS,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,ueAAue,KAAO,oBAAoBF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,qBAAsB3P,EAAIiL,eAAgB/K,EAAG,MAAM,CAAC0C,YAAY,wBAAwB,CAAC1C,EAAG,OAAO,CAAC0C,YAAY,qBAAqB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAIqS,8BAA8BrS,EAAIsS,KAAKpS,EAAG,cAAc,CAAC0C,YAAY,oBAAoB8L,MAAM,CAAC,CAAE,OAAU1O,EAAIyB,OAAOqF,KAAK8B,SAAS,UAAY5I,EAAIyB,OAAOqF,KAAK8B,SAAS,SAAW,MAAM5I,EAAIoL,mCAAqC,IAAIhL,MAAM,CAAC,GAAK,UAAU,CAACF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,2BAA2BzP,EAAG,cAAc,CAAC0C,YAAY,oBAAoB8L,MAAM,CAAC,CAAE,OAAU1O,EAAIyB,OAAOqF,KAAK8B,SAAS,YAAc5I,EAAIyB,OAAOqF,KAAK8B,SAAS,WAAa,MAAM5I,EAAIoL,mCAAqC,IAAIhL,MAAM,CAAC,GAAK,CAAE0G,KAAM,UAAWM,KAAM,WAAY,CAAClH,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,4BAA4BzP,EAAG,cAAc,CAAC0C,YAAY,oBAAoB8L,MAAM,CAAC,CAAE,OAAU1O,EAAIyB,OAAOqF,KAAK8B,SAAS,gBAAkB5I,EAAIyB,OAAOqF,KAAK8B,SAAS,eAAiB,MAAM5I,EAAIoL,mCAAqC,IAAIhL,MAAM,CAAC,GAAK,gBAAgB,CAACF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,kCAAkCzP,EAAG,cAAc,CAAC0C,YAAY,oBAAoB8L,MAAM,CAAC,CAAE,OAAU1O,EAAIyB,OAAOqF,KAAK8B,SAAS,WAAa5I,EAAIyB,OAAOqF,KAAK8B,SAAS,UAAY,MAAM5I,EAAIoL,mCAAqC,IAAIhL,MAAM,CAAC,GAAK,WAAW,CAACF,EAAG,OAAO,CAAC0C,YAAY,SAAS,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,4BAA4B,KAAKzP,EAAG,QAAQ,CAAC0C,YAAY,iBAAiBxC,MAAM,CAAC,KAAO,GAAG,GAAK,KAAK,CAACF,EAAG,cAAcA,EAAG,aAAa,CAACE,MAAM,CAAC,UAAY,gBAAgB,CAACF,EAAG,IAAI,CAAC0C,YAAY,oBAAoBH,GAAG,CAAC,MAAQ/H,GAAKA,EAAE6X,mBAAmB,CAACrS,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,OAAS,eAAe,eAAe,IAAI,iBAAiB,QAAQ,kBAAkB,UAAU,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,GAAK,KAAK,GAAK,KAAK,EAAI,QAAQF,EAAG,OAAO,CAACE,MAAM,CAAC,GAAK,IAAI,GAAK,KAAK,GAAK,KAAK,GAAK,QAAQF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,kGAAkGF,EAAG,OAAO,CAAC0C,YAAY,iBAAiB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAIwS,2BAA2BtS,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,WAAWqS,KAAK,WAAW,CAACvS,EAAG,cAAc,CAACpB,IAAI,QAAQ2D,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI0S,eAAe,YAAY,CAACxS,EAAG,OAAO,CAACwO,MAAM,CAAC,kBAAoC,UAAjB1O,EAAIsL,WAAuB,CAACtL,EAAIO,GAAG,eAAeL,EAAG,cAAc,CAACpB,IAAI,QAAQ2D,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI0S,eAAe,YAAY,CAACxS,EAAG,OAAO,CAACwO,MAAM,CAAC,kBAAoC,UAAjB1O,EAAIsL,WAAuB,CAACtL,EAAIO,GAAG,WAAW,IAAI,GAAGL,EAAG,uBAAuBA,EAAG,uBAAuBA,EAAG,WAAW,CAAC0P,IAAI,6BAA6BxP,MAAM,CAAC,KAAO,QAAQqC,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI2S,MAAM,wBAAwB,MAAS,CAACzS,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,YAAY,UAAU,YAAY,UAAU,EAAI,i4BAAi4B,KAAO,iBAAiBF,EAAG,WAAW,CAAC0C,YAAY,kBAAkBxC,MAAM,CAAC,KAAO,QAAQqC,GAAG,CAAC,MAAQ,SAAS2M,GAAQpP,EAAI2S,MAAM,iBAAmB3S,EAAI4O,kBAAoB5O,EAAI4S,wBAAwB,CAAC1S,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,MAAQ,6BAA6B,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,+VAA+VF,EAAG,QAAQ,CAAC0C,YAAY,iBAAiBxC,MAAM,CAAC,KAAO,GAAG,GAAK,IAAI,CAACF,EAAG,MAAM,CAAC0C,YAAY,iBAAiB,CAAE5C,EAAIiL,eAAgB,CAAC/K,EAAG,aAAa,CAACE,MAAM,CAAC,QAAU,CAAC,UAAUkC,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,UAAU0D,GAAG,WAAW,MAAO,CAACtC,EAAG,SAAS,CAAC0C,YAAY,aAAa5C,EAAI6S,GAAI7S,EAAI0K,OAAO,SAASoI,GAAM,OAAO5S,EAAG,cAAc,CAACpB,IAAIgU,EAAKhH,GAAGrJ,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI+S,WAAWD,MAAS,CAAC5S,EAAG,MAAM,CAAC0C,YAAY,kBAAkB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,cAAc,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAGoD,EAAKhH,OAAO5L,EAAG,MAAM,CAAC0C,YAAY,YAAYxC,MAAM,CAAC,MAAQ0S,EAAKE,YAAY,CAAChT,EAAIO,GAAGP,EAAI0P,GAAGoD,EAAKE,qBAAoB,KAAKrQ,OAAM,IAAO,MAAK,EAAM,YAAY,CAACzC,EAAG,IAAI,CAAC0C,YAAY,uCAAuCH,GAAG,CAAC,MAAQ/H,GAAKA,EAAE6X,mBAAmB,CAACrS,EAAG,OAAO,CAAC0C,YAAY,aAAa,CAAC5C,EAAIO,GAAG,IAAIP,EAAI0P,GAAG1P,EAAIiT,aAAejT,EAAIiT,aAAanH,GAAK9L,EAAI2P,GAAG,sBAAsB,OAAOzP,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,WAAW,MAAM,CAACF,EAAG,IAAI,CAAC0C,YAAY,uCAAuCH,GAAG,CAAC,MAAQzC,EAAIkT,eAAe,CAAChT,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,4CAA4C,KAAO,aAAaF,EAAG,OAAO,CAACE,MAAM,CAAC,YAAY,UAAU,YAAY,UAAU,EAAI,mFAAmF,KAAO,eAAeF,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,gCAAgC,MAAM,IAAI,IAAI,IAAI,IAExnOnP,GAAkB,GCFlBT,I,UAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,QAAU,QAAQ,UAAY,cAAc,iBAAmB,uBAAuB,kBAAoB+S,IAAMnT,EAAIoT,SAAS3Q,GAAG,CAAC,cAAgBzC,EAAIqT,wBAAwB/Q,YAAYtC,EAAIuC,GAAG,CAAC,CAACzD,IAAI,UAAU0D,GAAG,WAAW,MAAO,CAACtC,EAAG,MAAM,CAAC0C,YAAY,0BAA0B,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,uBAAuB,CAAC1C,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,4BAA6B3P,EAAIqL,cAAcvS,OAAQoH,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASqC,GAAG,CAAC,MAAQzC,EAAIsT,wBAAwB,CAACtT,EAAIO,GAAG,IAAIP,EAAI0P,GAAG1P,EAAI2P,GAAG,oBAAoB,OAAO3P,EAAIsS,MAAM,GAAGpS,EAAG,MAAM,CAAC0C,YAAY,qBAAqB,CAAC5C,EAAI6S,GAAI7S,EAAIqL,eAAe,SAAS0B,GAAc,OAAO7M,EAAG,MAAM,CAACpB,IAAIiO,EAAanN,GAAG8O,MAAM,CAAC,oBAAqB,gBAAgB3B,EAAanR,OAAS,CAACsE,EAAG,MAAM,CAAC0C,YAAY,sBAAsB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG3C,EAAa3J,UAAUlD,EAAG,MAAM,CAAC0C,YAAY,wBAAwB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG3C,EAAavP,YAAY0C,EAAG,MAAM,CAAC0C,YAAY,qBAAqB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG3C,EAAakB,cAAejO,EAAIqL,cAAcvS,OAAkJkH,EAAIsS,KAA9IpS,EAAG,MAAM,CAAC0C,YAAY,sBAAsB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,iBAAiB,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,iCAA0C,OAAOhN,OAAM,KAAQ4Q,MAAM,CAAC/U,MAAOwB,EAAIwT,oBAAqBC,SAAS,SAAUC,GAAM1T,EAAIwT,oBAAoBE,GAAKvE,WAAW,wBAAwB,CAACjP,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQJ,EAAIyL,oBAAoB,UAAW,EAAM,cAAgB,GAAG,YAAc,CAAE+D,gBAAiB,aAAc,CAACtP,EAAG,IAAI,CAAC0C,YAAY,uBAAuBH,GAAG,CAAC,MAAQzC,EAAI2T,sBAAsB,CAACzT,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,8RAA8R,KAAO,aAAaF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,mEAAmE,KAAO,oBAAoB,KAEvqEI,GAAkB,GCyCP,IACf/C,KAAA,qBACAnF,OACA,OACAkb,qBAAA,EACAJ,QAAAjY,SAAAyY,OAGApS,SAAA,IACAuQ,eAAA,sBACA8B,eAAA,0BAEArP,QAAA,IACAsP,eAAA,kDAEAH,oBAAAjZ,GAEAA,EAAAqZ,kBAEA,KAAAP,qBAAA,KAAAA,oBAGA,KAAAA,qBAAA,KAAA/H,oBAAA,GACA,KAAAwB,2BAIAoG,uBAAAW,GAEAA,GAAA,KAAAvI,oBAAA,IACA,KAAAwB,0BAEA,KAAAgH,iBAIAX,sBAAA5Y,GAEAA,EAAAqZ,kBAEA,KAAA5G,uBAGA1M,UACA,KAAA2S,QAAAjY,SAAA+Y,eAAA,sBCvF0W,MCQtW,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXnU,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC0C,YAAY,cAAc,CAAC1C,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAI2P,GAAG,kBAAkB,CAACzP,EAAG,IAAI,CAAC0C,YAAY,aAAaH,GAAG,CAAC,MAAQzC,EAAImU,eAAe,CAACjU,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,uWAAuWF,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQJ,EAAI2P,GAAG,aAAa,QAAU3P,EAAIgU,QAAQ,MAAQ,KAAK,OAAS,KAAK,UAAY,CAAEI,QAAS,MAAO3R,GAAG,CAAC,OAASzC,EAAIqU,eAAe,CAACnU,EAAG,MAAM,CAAC0C,YAAY,qBAAqB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,gBAAgB,CAAC1C,EAAG,MAAM,CAAC0C,YAAY,qBAAqB,CAAC1C,EAAG,OAAO,CAAC0C,YAAY,qBAAqB,CAAC5C,EAAIO,GAAG,IAAIP,EAAI0P,GAAG1P,EAAI2P,GAAG,oBAAoB,KAAK3P,EAAI0P,GAAG1P,EAAIsU,iBAAiB,OAAOpU,EAAG,WAAW,CAACkS,YAAY,CAAC,MAAQ,QAAQ,cAAc,QAAQhS,MAAM,CAAC,YAAcJ,EAAI2P,GAAG,mBAAmB,cAAc,IAAI4D,MAAM,CAAC/U,MAAOwB,EAAIuU,iBAAkBd,SAAS,SAAUC,GAAM1T,EAAIuU,iBAAiBb,GAAKvE,WAAW,qBAAqBnP,EAAI6S,GAAI7S,EAAIwU,iBAAiB,SAASC,GAAO,OAAOvU,EAAG,kBAAkB,CAACpB,IAAI2V,EAAMrU,MAAM,CAAC,MAAQqU,IAAQ,CAACvU,EAAG,OAAO,CAAC0C,YAAY,mBAAmB8L,MAAM,UAAc,OAAL+F,QAAK,IAALA,OAAK,EAALA,EAAOC,gBAAiB,CAAC1U,EAAIO,GAAG,IAAIP,EAAI0P,GAAG+E,GAAO,YAAW,IAAI,GAAGvU,EAAG,MAAM,CAAC0C,YAAY,sBAAsB,CAAC1C,EAAG,WAAW,CAACE,MAAM,CAAC,QAAUJ,EAAI2U,QAAQ,KAAO,UAAU,KAAO,SAASlS,GAAG,CAAC,MAAQzC,EAAI4U,YAAY,CAAC1U,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIO,GAAG,IAAIP,EAAI0P,GAAG1P,EAAI2P,GAAG,gBAAgB,MAAM,IAAI,KAAKzP,EAAG,MAAM,CAAC0P,IAAI,aAAahN,YAAY,eAAe,CAAC1C,EAAG,SAAS,CAACE,MAAM,CAAC,SAAWJ,EAAI2U,UAAU,CAAsB,IAApB3U,EAAI6U,KAAK/b,QAAiBkH,EAAI2U,QAA0GzU,EAAG,MAAM,CAAC0C,YAAY,YAAY5C,EAAI6S,GAAI7S,EAAI8U,cAAc,SAASC,EAAIC,GAAM,IAAAC,EAAC,OAAO/U,EAAG,MAAM,CAACpB,IAAIkW,EAAMpS,YAAY,WAAW8L,MAAM1O,EAAIkV,iBAAiBH,EAAII,YAAY,CAACjV,EAAG,MAAM,CAAC0C,YAAY,cAAc,CAAC1C,EAAG,OAAO,CAAC0C,YAAY,YAAY,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAIoV,WAAWL,EAAIM,eAAenV,EAAG,OAAO,CAAC0C,YAAY,YAAY8L,MAAM,UAAsB,QAAtBuG,EAASF,EAAII,iBAAS,IAAAF,OAAA,EAAbA,EAAeP,gBAAiB,CAAC1U,EAAIO,GAAG,IAAIP,EAAI0P,GAAGqF,EAAII,WAAW,OAAQJ,EAAIva,OAAQ0F,EAAG,OAAO,CAAC0C,YAAY,cAAc,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAGqF,EAAIva,WAAWwF,EAAIsS,OAAOpS,EAAG,MAAM,CAAC0C,YAAY,eAAe,CAAC5C,EAAIO,GAAGP,EAAI0P,GAAGqF,EAAIO,qBAAoB,GAA1qBpV,EAAG,MAAM,CAAC0C,YAAY,WAAW,CAAC1C,EAAG,UAAU,CAACE,MAAM,CAAC,YAAcJ,EAAI2P,GAAG,kBAAkB,MAAklB,GAAGzP,EAAG,MAAM,CAACkS,YAAY,CAAC,aAAa,QAAQ,aAAa,OAAO,cAAc,OAAO,eAAe,OAAO,gBAAgB,SAAS,CAAClS,EAAG,eAAe,CAACE,MAAM,CAAC,QAAUJ,EAAIuJ,YAAY,MAAQvJ,EAAIuV,MAAM,YAAYvV,EAAIwV,SAAS,KAAO,SAAS/S,GAAG,CAAC,OAASzC,EAAIyV,qBAAqB,QAAQ,IAEp6FjV,GAAkB,GC2FP,IACf/C,KAAA,YACAnF,OACA,OACA0b,SAAA,EACAW,SAAA,EACAE,KAAA,GACAW,SAAA,IACAjM,YAAA,EACAgM,MAAA,EACAhB,iBAAA,OAGA/S,SAAA,IACAuQ,eAAA,6CAGAuC,kBACA,SAAA3J,eACA,YAAAgF,GAAA,sBAEA,MAAA+F,EAAA,KAAAhL,MAAAiL,KAAA7C,KAAAhH,KAAA,KAAAnB,gBACA,OAAA+K,EAAA,GAAAA,EAAA5J,GAAA,KAAAnB,gBAIAmK,eACA,YAAAP,iBAGA,KAAAM,KAAAnJ,OAAAqJ,KAAAI,YAAA,KAAAZ,kBAFA,KAAAM,MAMAL,kBACA,MAAAoB,EAAA,IAAAC,IAAA,KAAAhB,KAAApV,IAAAsV,KAAAI,WAAAzJ,OAAAsG,UACA,OAAArN,MAAAmD,KAAA8N,GAAAE,SAGAtR,QAAA,CACA2P,eACA,KAAAH,SAAA,EAEA,KAAAzK,YAAA,EACA,KAAAgL,iBAAA,KACA,KAAA5J,gBACA,KAAAiK,aAIAP,eACA,KAAAL,SAAA,GAGA,kBACA,QAAArJ,eAKA,QAAAM,eAAA,CAKA,KAAA0J,SAAA,EACA,IACA,MAAAlQ,QAAAC,OAAAtG,IAAA,uBAAAuM,eAAA,CACA6C,OAAA,CACA5D,KAAA,KAAAL,YACAwM,UAAA,KAAAP,SACAxS,OAAA,KAAAiI,kBAKAxG,EAAAnM,MAAA,kBAAAmM,EAAAnM,MAAAmM,EAAAnM,WACA,KAAAuc,KAAApQ,EAAAnM,WAAA,GACA,KAAAid,MAAA9Q,EAAAnM,KAAAid,OAAA,IAGA,KAAAV,MAAApQ,EAAAnM,MAAA,IAAAwd,KAAA,CAAAE,EAAAC,KACA,MAAAC,EAAA,IAAAxS,KAAAsS,EAAAX,WAAAc,UACAC,EAAA,IAAA1S,KAAAuS,EAAAZ,WAAAc,UACA,OAAAD,EAAAE,IAEA,KAAAb,MAAA,KAAAV,KAAA/b,QAIA,KAAAyb,iBAAA,KAGA,KAAA9F,UAAA,KACA,MAAA4H,EAAA,KAAA9E,MAAA8E,WACAA,IACAA,EAAAC,UAAA,KAIA,MAAAnZ,GACAiC,QAAAjC,MAAA,wBAAAA,GACA,KAAA8H,SAAA9H,MAAA,KAAAwS,GAAA,mBACA,KAAAkF,KAAA,GACA,KAAAU,MAAA,EACA,QACA,KAAAZ,SAAA,QA7CA,KAAA1P,SAAAqB,QAAA,KAAAqJ,GAAA,mCALA,KAAA1K,SAAAqB,QAAA,KAAAqJ,GAAA,wBAuDA8F,iBAAA7L,GACA,KAAAL,YAAAK,EACA,KAAAgL,aAGAQ,WAAAC,GACA,IAAAA,EAAA,SACA,MAAAkB,EAAA,IAAA7S,KAAA2R,GACA,OAAAkB,EAAA5S,kBAGAuR,iBAAAT,GACA,OAAAA,EACA,aAAAA,EAAAC,cADA,MCxNiW,MCQ7V,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX3U,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAI2B,WAAa3B,EAAI2P,GAAG,oBAAsB3P,EAAI2P,GAAG,qBAAqB,CAACzP,EAAG,IAAI,CAAC0C,YAAY,sBAAsBH,GAAG,CAAC,MAAQzC,EAAIwW,cAAc,CAAExW,EAAI2B,WAAYzB,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,oHAAoH,KAAO,aAAaF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,2HAA2H,OAAS,UAAU,eAAe,IAAI,iBAAiB,QAAQ,kBAAkB,aAAaF,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,kDAAkD,KAAO,UAAU,OAAS,UAAU,eAAe,IAAI,iBAAiB,QAAQ,kBAAkB,kBAE1iCI,GAAkB,GCiBP,IACf/C,KAAA,oBACA+D,SAAA,IACAuQ,eAAA,cACApQ,aACA,YAAAG,WAGA0C,QAAA,IACAsP,eAAA,oBACA0C,cACA,KAAArI,oBC9ByW,MCQrW,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCmHA,IACf7M,WAAA,CACAmV,sBACAC,aACAC,sBAEA1P,MAAA,CACA0H,YAAA,CAAA/S,KAAAoW,QAAAC,SAAA,GACArD,iBAAA,CAAAhT,KAAAoW,QAAAC,SAAA,GACA7G,aAAA,CAAAxP,KAAAsW,OAAAD,QAAA,YAEA3Z,OACA,OACA6Z,IAAA,EACAyE,eAAA,EACAxD,QAAAjY,SAAAyY,KACAX,aAAA,OAGAzR,SAAA,IACAuQ,eAAA,6EACAS,uBACA,qBAAAlH,SAAA,iBAGA9G,QAAA,IACAqS,eAAA,0BACA/C,eAAA,oBAEApB,eAAAoE,GACA,KAAA9I,eAAA8I,GACA,KAAAC,MAAAC,OAAAF,GAEA/D,WAAAD,GACA,KAAAG,aAAAH,EACA,KAAAjH,kBAAAiH,EAAAhH,IACA,KAAAmL,gBAAAnE,EAAAhH,KAEAmL,kBACA,MAAAC,EAAA,QACA,KAAAzV,OAAAqF,OAAAoQ,GACA,KAAAxR,QAAAtM,KAAA8d,GAAA3O,MAAApM,IACA,yBAAAA,EAAAsB,MACA2B,QAAAjC,MAAAhB,MAKA+W,eACA,KAAAxN,QAAAtM,KAAA,cAEA+d,qBAAA,IAAAC,EAAAC,EACA,KAAA1M,gBAAA,QAAAyM,EAAA,KAAA1M,aAAA,IAAA0M,KAAAte,OACA,KAAAma,aAAA,KAAAvI,MAAAiL,KAAA7C,KAAAhH,KAAA,KAAAnB,gBACA,QAAA0M,EAAA,KAAA3M,aAAA,IAAA2M,KAAAve,OAEA,KAAAia,WAAA,KAAArI,MAAA,IAEA,KAAAuI,aAAA,MAGA,4BACA,KAAAhI,sBACA,KAAArJ,OAAA0D,SAAA,eAIA+M,wBACA,YAAAlH,mBACA,KAAAA,mBAEA,KAGA/I,MAAA,CACA6I,eAAA,CACAqM,WAAA,EACAC,QAAAC,GACAA,EACA,KAAAC,uBAGA,KAAA7V,OAAAyI,OAAA,eACA,KAAA4I,aAAA,KACA,KAAApH,kBAAA,SAIAnB,MAAA,CACA4M,WAAA,EACAC,QAAAC,GACA,OAAAA,QAAA,IAAAA,KAAA1e,OACA,KAAAqe,sBAEA,KAAAlE,aAAA,KACA,KAAApH,kBAAA,UAKApL,UACA,KAAA2S,QAAAjY,SAAA+Y,eAAA,oBACA,KAAAjJ,gBACA,KAAAwM,uBAGAC,YACA,KAAAzM,gBACA,KAAAwM,wBClPuW,MCOnW,GAAY,eACd,GACA,EACA,IACA,EACA,KACA,KACA,MAIa,M,QClBX1X,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,kBAAkB,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,SAAS,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,GAAG,GAAK,KAAK,CAACF,EAAG,IAAI,CAAC0C,YAAY,aAAa,CAAC5C,EAAIO,GAAG,uBAAuBL,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,+BAA+B,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,YAAY,UAAU,YAAY,UAAU,EAAI,0QAA0Q,KAAO,oBAAoB,IAAI,IAEpsBI,GAAkB,GCkBtB,IACAlI,OACA,WCtBuW,MCOnW,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QClBXyH,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAAC0C,YAAY,kBAAkB8L,MAAM,CAAE1O,EAAI2X,IAAM,sBAAwB,IAAKvX,MAAM,CAAC,UAAYJ,EAAI2X,IAAM,OAAS,QAAQ,UAAW,EAAM,QAAU3X,EAAIqP,mBAAmB,MAAQ,MAAM,aAAeuI,IAAM5X,EAAIoT,SAAS3Q,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI2S,MAAM,wBAAwB,MAAU,CAACzS,EAAG,WAAW,CAAC0C,YAAY,YAAYxC,MAAM,CAAC,KAAO,QAAQqC,GAAG,CAAC,MAAQ,SAAS2M,GAAQ,OAAOpP,EAAI2S,MAAM,wBAAwB,MAAU,CAACzS,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,IAAI,OAAS,IAAI,QAAU,YAAY,CAACF,EAAG,IAAI,CAACE,MAAM,CAAC,GAAK,QAAQ,UAAY,yBAAyB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,GAAK,OAAO,EAAI,eAAe,KAAO,OAAO,OAAS,eAAe,iBAAiB,QAAQ,kBAAkB,QAAQ,oBAAoB,KAAK,eAAe,SAASF,EAAG,OAAO,CAACE,MAAM,CAAC,GAAK,SAAS,YAAY,OAAO,EAAI,eAAe,KAAO,OAAO,OAAS,eAAe,iBAAiB,QAAQ,kBAAkB,QAAQ,oBAAoB,KAAK,eAAe,eAAeF,EAAG,MAAM,CAAC0C,YAAY,kBAAkB,CAAC1C,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,oCAAoCzP,EAAG,MAAMA,EAAG,MAAM,CAAC0C,YAAY,iBAAiB,CAAC1C,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,oCAAoCzP,EAAG,gBAAgB,CAACE,MAAM,CAAC,aAAe,WAAWqC,GAAG,CAAC,OAASzC,EAAI6X,0BAA0BtE,MAAM,CAAC/U,MAAOwB,EAAI8X,kBAAmBrE,SAAS,SAAUC,GAAM1T,EAAI8X,kBAAkBpE,GAAKvE,WAAW,sBAAsB,CAACjP,EAAG,iBAAiB,CAAC0C,YAAY,aAAaxC,MAAM,CAAC,MAAQ,aAAaF,EAAG,iBAAiB,CAAC0C,YAAY,YAAYxC,MAAM,CAAC,MAAQ,YAAYF,EAAG,iBAAiB,CAAC0C,YAAY,WAAWxC,MAAM,CAAC,MAAQ,WAAWF,EAAG,iBAAiB,CAAC0C,YAAY,UAAUxC,MAAM,CAAC,MAAQ,WAAW,IAAI,GAAGF,EAAG,MAAM,CAAC0C,YAAY,gBAAgB,CAAC1C,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,mCAAmCzP,EAAG,IAAI,CAACF,EAAIO,GAAG,+CAA+CL,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAe,QAAQ,aAAe,WAAWqC,GAAG,CAAC,OAAS,SAAS2M,GAAQ,OAAOpP,EAAI2S,MAAM,qBAAsBvD,EAAOnT,OAAOuC,SAAS+U,MAAM,CAAC/U,MAAOwB,EAAI+X,kBAAmBtE,SAAS,SAAUC,GAAM1T,EAAI+X,kBAAkBrE,GAAKvE,WAAW,sBAAsB,CAACjP,EAAG,iBAAiB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIO,GAAG,iBAAiBL,EAAG,iBAAiB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIO,GAAG,YAAY,IAAI,GAAGL,EAAG,MAAM,CAAC0C,YAAY,gBAAgB,CAAC1C,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAI0P,GAAG1P,EAAI2P,GAAG,mCAAmCzP,EAAG,WAAW,CAACE,MAAM,CAAC,kBAAkB,IAAIqC,GAAG,CAAC,OAAS,SAAS2M,GAAQ,OAAOpP,EAAI2S,MAAM,uBAAwB3S,EAAIgY,oBAAoBzE,MAAM,CAAC/U,MAAOwB,EAAIgY,iBAAkBvE,SAAS,SAAUC,GAAM1T,EAAIgY,iBAAiBtE,GAAKvE,WAAW,uBAAuB,MAAM,IAE3uF3O,GAAkB,G,yBC2DtBkG,OAAAC,IAAAsR,QAAA,CAAAC,UAAA,IAEA,QACAjR,MAAA,CAEAoI,mBAAA,CACAzT,KAAAoW,QACAC,SAAA,GAIA7G,aAAA,CACAxP,KAAAsW,OACAD,QAAA,WAIAnD,aAAA,CACAlT,KAAAsW,OACAD,QAAA,SAIAtD,YAAA,CACA/S,KAAAoW,QACAC,SAAA,IAIA3Z,OACA,OAEA8a,QAAAjY,SAAAyY,KAGAkE,kBAAA,KAAA1M,aAGA2M,kBAAA,KAAAjJ,aAGAkJ,iBAAA,KAAArJ,YAEAgJ,KAAA,IAGAnW,SAAA,GAGAgD,QAAA,IACAsP,eAAA,wBACA+D,yBAAA9b,GACA,MAAA6Q,EAAA7Q,EAAAE,OAAAuC,MACA,KAAAuP,mBAAAnB,GACA,KAAA+F,MAAA,qBAAA/F,KAGA7K,UACA,KAAA+V,kBAAA,KAAA1M,cAEAhJ,MAAA,CACAgJ,aAAA+M,GACA,KAAAL,kBAAAK,GAEA,4BAAAA,GACA,KAAAL,kBAAAK,IAGA1X,QAAA,WAEA,KAAA2S,QAAAjY,SAAA+Y,eAAA,sBCnI+W,MCO3W,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QC6DA,IACfzW,KAAA,YACA6D,WAAA,CACA8W,mBACAC,mBACAC,mBACAC,4BAEAjgB,OACA,OACAsW,kBAAA,EACAS,oBAAA,EACAV,aAAA,EACAG,aAAA,QACAD,YAAA,KAGArN,SAAA,CACA4J,eACA,YAAAxJ,OAAAC,MAAAuJ,eAGA5G,QAAA,CACAuK,gBACA,KAAAH,kBAAA,KAAAA,kBAEAI,uBACA,KAAAK,oBAAA,KAAAA,oBAEAC,uBACA,KAAAX,aAAA,KAAAA,aAEAY,mBAAAiJ,GACA,KAAA1J,aAAA0J,GAEAzK,mBAAAnB,GACA,KAAAhL,OAAA0D,SAAA,qBAAAsH,MCnHkV,MCO9U,GAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,MAIa,M,iCClBA,IACb6L,OAAQ,CACNC,KAAM,OACNtV,MAAO,qBACP2P,WAAY,cACZ3O,cAAe,iBACfuU,SAAU,WACVtN,cAAe,gBACfuN,SAAU,YACVC,gBAAiB,mBACjBvN,SAAU,WACVwN,eAAgB,qBAChBC,eAAgB,sBAChBC,mBAAoB,uBACpBC,aAAc,gBACdC,QAAS,UACTpX,SAAU,YACVqX,UAAW,aACXC,cAAe,yBACfC,kBAAmB,sBACnBC,WAAY,cACZC,MAAO,SAETC,gBAAgB,CACdpO,aAAc,gBACdqO,YAAa,eACb9K,YAAa,eACb+K,aAAc,gBAEhBC,UAAU,CACRhS,QAAS,gBACTiS,QAAS,gBACTC,SAAU,iBACVC,MAAO,cACPC,KAAM,0BACNC,OAAQ,eACRC,IAAK,mBACLC,WAAY,cACZC,aAAc,gBACdC,OAAQ,eACRC,SAAU,wBAEZC,KAAM,CACJC,cAAe,iBACfC,WAAY,MACZC,YAAa,cACbC,kBAAmB,4BACnBC,kBAAmB,SACnBC,WAAY,cACZC,MAAO,QACPC,gBAAiB,aACjBC,cAAe,UACfC,YAAa,eACbC,mBAAoB,uBACpBC,WAAY,cACZC,kBAAmB,wBACnBtY,QAAS,CACPuY,SAAU,YACVtP,GAAI,KACJuP,OAAQ,SACRC,SAAU,WACV3hB,OAAQ,SACR4hB,aAAc,gBACdrL,MAAO,QACPsL,SAAU,aAEZH,OAAQ,CACNI,OAAQ,WAGZC,QAAS,CACPC,UAAW,aACXC,aAAc,gBACdC,YAAa,eACbC,YAAa,eACbC,aAAc,gBACdC,eAAgB,eAChBC,SAAU,YACVC,WAAY,cACZC,QAAS,WACTC,SAAU,OACVC,aAAc,OACdC,aAAc,mBACdpC,WAAY,cACZqC,SAAU,YACVnC,OAAQ,UACRoC,UAAW,aACXC,eAAgB,mBAChBC,gBAAiB,oBACjBC,mBAAoB,KACpBC,UAAW,aACXC,WAAY,cACZC,UAAW,aACXC,iBAAkB,cAClBC,YAAa,gBAEf9C,WAAY,CACV+C,WAAY,cACZC,cAAe,cACfC,WAAY,cACZC,gBAAiB,yBACjBC,YAAa,eACbC,eAAgB,kBAChBC,cAAe,kBAEjBpD,aAAc,CACZqD,kBAAmB,yBACnBC,cAAe,iBACfC,iBAAkB,qBAEpBb,WAAY,CACVzZ,MAAO,qBACPua,QAAS,WACTC,eAAgB,kBAChBC,eAAgB,kBAChBC,iBAAkB,eAClBC,eAAgB,aAChB5T,QAAS,UACT6T,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,OAAQ,SACRtY,OAAQ,SACRhD,QAAS,CACPuY,SAAU,YACVgD,UAAW,aACXC,QAAS,WACTC,UAAW,aACXC,cAAe,iBACfC,cAAe,kBACfC,cAAe,oBAGnB1B,iBAAkB,CAChB3Z,MAAO,2BACPsb,cAAe,iBACfd,eAAgB,kBAChBC,eAAgB,kBAChBc,iBAAkB,oBAClBb,iBAAkB,eAClBC,eAAgB,aAChBa,mBAAoB,uBACpBlB,iBAAkB,oBAClBvT,QAAS,UACT6T,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,OAAQ,SACRtY,OAAQ,SACRhD,QAAS,CACPgc,aAAc,eACdC,cAAe,iBACfC,WAAY,eAEdC,WAAY,CACVC,WAAY,yBACZC,kBAAmB,gCACnBC,cAAe,sBACfC,WAAY,gBAEdC,SAAU,CACRC,WAAY,8BACZC,YAAa,iBACbC,SAAU,uBACV7Z,QAAS,sBACT8V,OAAQ,kBACRgE,eAAgB,8BAChBC,WAAY,qBAGhBC,mBAAoB,CAClBvc,MAAO,8BACPmW,MAAO,QACPhE,MAAO,QACP5P,QAAS,UACT8V,OAAQ,SACR8D,YAAa,cACbK,QAAS,UACTC,UAAW,YACXvE,SAAU,YAEZvG,IAAK,CACH3R,MAAO,aACP0c,SAAU,YACVpK,YAAa,OACbqK,eAAgB,mBAChBC,YAAa,eACb9G,QAAS,UACT+G,OAAQ,oBACRC,WAAY,wBAEd7F,SAAU,CACRjX,MAAO,YACP+c,WAAY,kBACZ1S,OAAQ,eACR2S,aAAc,SACdC,YAAa,QACbC,aAAc,iBAEhBC,WAAY,CACVC,OAAQ,cACR/iB,KAAM,YACNgX,MAAO,aACPgM,WAAY,aACZC,iBAAkB,eAClBC,UAAW,aACXC,eAAgB,mBAElBjE,mBAAoB,CAClBvZ,MAAO,sBACPyd,cAAe,2BACfC,cAAe,iBACfC,aAAc,gBACdC,qBAAsB,yBACtBC,iBAAkB,sCAClBC,cAAe,uBACfC,aAAc,gBACdC,YAAa,eACbC,eAAgB,yBAChBC,KAAM,QACNC,eAAgB,kBAChBC,cAAe,iBACfC,aAAc,0BC9NH,IACbhJ,OAAQ,CACNC,KAAM,KACNtV,MAAO,SACP2P,WAAY,OACZ3O,cAAe,OACfuU,SAAU,KACVtN,cAAe,KACfuN,SAAU,OACVC,gBAAiB,OACjBvN,SAAU,KACVwN,eAAgB,OAChBC,eAAgB,OAChBC,mBAAoB,SACpBC,aAAc,OACdC,QAAS,KACTpX,SAAU,OACVqX,UAAW,OACXC,cAAe,kBACfC,kBAAmB,UACnBC,WAAY,OACZC,MAAO,MAETC,gBAAgB,CACdpO,aAAc,QACdqO,YAAa,QACb9K,YAAa,QACb+K,aAAc,OAEhBC,UAAU,CACRhS,QAAS,OACTiS,QAAS,QACTC,SAAU,OACVC,MAAO,OACPC,KAAM,OACNC,OAAQ,OACRC,IAAK,OACLC,WAAY,OACZC,aAAc,OACdC,OAAQ,MACRC,SAAU,QAEZC,KAAM,CACJC,cAAe,OACfC,WAAY,QACZC,YAAa,OACbC,kBAAmB,cACnBC,kBAAmB,MACnBC,WAAY,OACZC,MAAO,KACPC,gBAAiB,UACjBC,cAAe,KACfC,YAAa,QACbC,mBAAoB,SACpBC,WAAY,OACZC,kBAAmB,WACnBtY,QAAS,CACPuY,SAAU,MACVtP,GAAI,OACJuP,OAAQ,KACRC,SAAU,KACV3hB,OAAQ,KACR4hB,aAAc,OACdrL,MAAO,KACPsL,SAAU,QAEZH,OAAQ,CACNI,OAAQ,OAGZC,QAAS,CACPC,UAAW,OACXC,aAAc,OACdC,YAAa,OACbC,YAAa,QACbC,aAAc,OACdC,eAAgB,SAChBC,SAAU,OACVC,WAAY,OACZC,QAAS,OACTC,SAAU,OACVC,aAAc,OACdC,aAAc,SACdpC,WAAY,OACZqC,SAAU,OACVnC,OAAQ,UACRoC,UAAW,OACXC,eAAgB,WAChBC,gBAAiB,SACjBC,mBAAoB,KACpBC,UAAW,OACXC,WAAY,OACZC,UAAW,QACXC,iBAAkB,QAClBC,YAAa,QAEf9C,WAAY,CACV+C,WAAY,OACZC,cAAe,OACfC,WAAY,SACZC,gBAAiB,UACjBC,YAAa,OACbC,eAAgB,OAChBC,cAAe,QAEjBpD,aAAc,CACZqD,kBAAmB,UACnBC,cAAe,OACfC,iBAAkB,QAEpBb,WAAY,CACVzZ,MAAO,OACPua,QAAS,OACTC,eAAgB,OAChBC,eAAgB,OAChBC,iBAAkB,OAClBC,eAAgB,OAChB5T,QAAS,KACT6T,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,OAAQ,KACRtY,OAAQ,KACRhD,QAAS,CACPuY,SAAU,MACVgD,UAAW,OACXC,QAAS,QACTC,UAAW,OACXC,cAAe,OACfC,cAAe,WACfC,cAAe,aAGnB1B,iBAAkB,CAChB3Z,MAAO,QACPsb,cAAe,QACfd,eAAgB,OAChBC,eAAgB,OAChBc,iBAAkB,OAClBb,iBAAkB,OAClBC,eAAgB,OAChBa,mBAAoB,SACpBlB,iBAAkB,OAClBvT,QAAS,KACT6T,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,OAAQ,KACRtY,OAAQ,KACRhD,QAAS,CACPgc,aAAc,MACdC,cAAe,OACfC,WAAY,UAEdC,WAAY,CACVC,WAAY,WACZC,kBAAmB,YACnBC,cAAe,SACfC,WAAY,QAEdC,SAAU,CACRC,WAAY,UACZC,YAAa,UACbC,SAAU,YACV7Z,QAAS,OACT8V,OAAQ,OACRgE,eAAgB,SAChBC,WAAY,YAGhBC,mBAAoB,CAClBvc,MAAO,UACPmW,MAAO,KACPhE,MAAO,KACP5P,QAAS,KACT8V,OAAQ,KACR8D,YAAa,MACbK,QAAS,MACTC,UAAW,MACXvE,SAAU,MAEZvG,IAAK,CACH3R,MAAO,QACP0c,SAAU,OACVpK,YAAa,KACbqK,eAAgB,QAChBC,YAAa,OACb9G,QAAS,KACT+G,OAAQ,OACRC,WAAY,UAEd7F,SAAU,CACRjX,MAAO,OACP+c,WAAY,OACZ1S,OAAQ,OACRuS,YAAa,OACbI,aAAc,KACdC,YAAa,MAEfE,WAAY,CACVC,OAAQ,OACR/iB,KAAM,OACNgX,MAAO,OACPgM,WAAY,MACZC,iBAAkB,OAClBC,UAAW,OACXC,eAAgB,QAElBjE,mBAAoB,CAClBvZ,MAAO,OACPyd,cAAe,WACfC,cAAe,SACfC,aAAc,OACdC,qBAAsB,SACtBC,iBAAkB,YAClBC,cAAe,QACfC,aAAc,OACdC,YAAa,OACbC,eAAgB,SAChBC,KAAM,QACNC,eAAgB,OAChBC,cAAe,OACfC,aAAc,mBCzNlB/a,OAAIC,IAAI+a,SAER,MAAMC,GAAW,CACf,QAASC,GACT,QAASC,IAILC,GAAqBA,KACzB,MAAMC,EAAgBvc,aAAa0F,QAAQ,YAC3C,GAAI6W,GAAiBJ,GAASI,GAC5B,OAAOA,EAIT,MAAMC,EAAcC,UAAU3W,UAAY2W,UAAUC,aAC9CpL,EAAOkL,EAAYG,WAAW,MAAQ,QAAU,QAEtD,OAAOrL,GAGHsL,GAAO,IAAIV,QAAQ,CACvB1K,OAAQ8K,KACRO,eAAgB,QAChBV,YACAW,uBAAuB,IAGVF,U,4CClB0B,OAArC5c,aAAa0F,QAAQ,aACvB1F,aAAakH,QAAQ,WAAY,QAKnChG,OAAIC,IAAI4b,QAER7b,OAAI8b,OAAOC,eAAgB,EAG3B/b,OAAIrF,UAAU,gBAAiBqhB,GAC/Bhc,OAAIrF,UAAU,mBAAoBshB,IAClCjc,OAAIrF,UAAU,gBAAiBuhB,MAE/Blc,OAAI1N,UAAU6pB,OAASne,OAEvB,IAAIgC,OAAI,CACNoc,QACArb,SACA2a,QACAriB,OAAQmD,GAAKA,EAAE6f,KACdC,OAAO,S,oFCrCV,W,2DCAA,W,kCCAA,W,kCCAA,W,kCCAA,W,kCCAA,W,kCCAA,gBAEA,MAAMC,EAAgBve,OAAM7F,OAAO,CACjCqkB,QAASvb,6CAAYwb,kBAAoB,wBACzCnmB,QAAS,OAGIimB", "file": "static/js/app.8a727bee.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"app\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"static/js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-08a0de52\":\"e5253694\",\"chunk-1a22db4a\":\"bbd2dd14\",\"chunk-29a53b76\":\"583083ec\",\"chunk-129ecfa6\":\"d536c141\",\"chunk-60b6d59a\":\"301947dd\",\"chunk-2d0e95df\":\"9e796ba1\",\"chunk-364a09a0\":\"ff89fb38\",\"chunk-3a5d12e0\":\"df8d3418\",\"chunk-4821dd0f\":\"193b2915\",\"chunk-49d38e76\":\"b910ab97\",\"chunk-5d9c9d94\":\"2d32b357\",\"chunk-5e8ae900\":\"4462f1b5\",\"chunk-65340c0b\":\"95205db6\",\"chunk-6fdea86c\":\"fcc82cd1\",\"chunk-7155ac4e\":\"c5071787\",\"chunk-72e4fea3\":\"e154e834\",\"chunk-791bd3a8\":\"eef7974c\",\"chunk-7dbc10af\":\"8df0027e\",\"chunk-8a9b96a2\":\"5297d144\",\"chunk-cfa15118\":\"8a0cb356\",\"chunk-d588eafa\":\"339ed414\",\"chunk-ddd725e6\":\"119c50e2\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-08a0de52\":1,\"chunk-1a22db4a\":1,\"chunk-29a53b76\":1,\"chunk-129ecfa6\":1,\"chunk-60b6d59a\":1,\"chunk-364a09a0\":1,\"chunk-3a5d12e0\":1,\"chunk-4821dd0f\":1,\"chunk-49d38e76\":1,\"chunk-5d9c9d94\":1,\"chunk-5e8ae900\":1,\"chunk-65340c0b\":1,\"chunk-6fdea86c\":1,\"chunk-7155ac4e\":1,\"chunk-72e4fea3\":1,\"chunk-791bd3a8\":1,\"chunk-7dbc10af\":1,\"chunk-8a9b96a2\":1,\"chunk-cfa15118\":1,\"chunk-d588eafa\":1,\"chunk-ddd725e6\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"static/css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-08a0de52\":\"fa3cff66\",\"chunk-1a22db4a\":\"5af5a0b5\",\"chunk-29a53b76\":\"8bb04b2d\",\"chunk-129ecfa6\":\"a943d34a\",\"chunk-60b6d59a\":\"fcd3ef06\",\"chunk-2d0e95df\":\"31d6cfe0\",\"chunk-364a09a0\":\"cf0f0211\",\"chunk-3a5d12e0\":\"38432d17\",\"chunk-4821dd0f\":\"7fc11cc4\",\"chunk-49d38e76\":\"ea20b8c8\",\"chunk-5d9c9d94\":\"0e433876\",\"chunk-5e8ae900\":\"0e433876\",\"chunk-65340c0b\":\"ea22d9f3\",\"chunk-6fdea86c\":\"c99882b2\",\"chunk-7155ac4e\":\"763ddbd2\",\"chunk-72e4fea3\":\"0e433876\",\"chunk-791bd3a8\":\"add1274d\",\"chunk-7dbc10af\":\"0ecfc768\",\"chunk-8a9b96a2\":\"f4e6171b\",\"chunk-cfa15118\":\"4567523e\",\"chunk-d588eafa\":\"7c04c7b7\",\"chunk-ddd725e6\":\"91e8af76\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Simple.vue?vue&type=style&index=0&id=5b12b09b&prod&lang=scss&scoped=true\"", "var map = {\n\t\"./af\": \"2bfb\",\n\t\"./af.js\": \"2bfb\",\n\t\"./ar\": \"8e73\",\n\t\"./ar-dz\": \"a356\",\n\t\"./ar-dz.js\": \"a356\",\n\t\"./ar-kw\": \"423e\",\n\t\"./ar-kw.js\": \"423e\",\n\t\"./ar-ly\": \"1cfd\",\n\t\"./ar-ly.js\": \"1cfd\",\n\t\"./ar-ma\": \"0a84\",\n\t\"./ar-ma.js\": \"0a84\",\n\t\"./ar-ps\": \"4c98\",\n\t\"./ar-ps.js\": \"4c98\",\n\t\"./ar-sa\": \"8230\",\n\t\"./ar-sa.js\": \"8230\",\n\t\"./ar-tn\": \"6d83\",\n\t\"./ar-tn.js\": \"6d83\",\n\t\"./ar.js\": \"8e73\",\n\t\"./az\": \"485c\",\n\t\"./az.js\": \"485c\",\n\t\"./be\": \"1fc1\",\n\t\"./be.js\": \"1fc1\",\n\t\"./bg\": \"84aa\",\n\t\"./bg.js\": \"84aa\",\n\t\"./bm\": \"a7fa\",\n\t\"./bm.js\": \"a7fa\",\n\t\"./bn\": \"9043\",\n\t\"./bn-bd\": \"9686\",\n\t\"./bn-bd.js\": \"9686\",\n\t\"./bn.js\": \"9043\",\n\t\"./bo\": \"d26a\",\n\t\"./bo.js\": \"d26a\",\n\t\"./br\": \"6887\",\n\t\"./br.js\": \"6887\",\n\t\"./bs\": \"2554\",\n\t\"./bs.js\": \"2554\",\n\t\"./ca\": \"d716\",\n\t\"./ca.js\": \"d716\",\n\t\"./cs\": \"3c0d\",\n\t\"./cs.js\": \"3c0d\",\n\t\"./cv\": \"03ec\",\n\t\"./cv.js\": \"03ec\",\n\t\"./cy\": \"9797\",\n\t\"./cy.js\": \"9797\",\n\t\"./da\": \"0f14\",\n\t\"./da.js\": \"0f14\",\n\t\"./de\": \"b469\",\n\t\"./de-at\": \"b3eb\",\n\t\"./de-at.js\": \"b3eb\",\n\t\"./de-ch\": \"bb71\",\n\t\"./de-ch.js\": \"bb71\",\n\t\"./de.js\": \"b469\",\n\t\"./dv\": \"598a\",\n\t\"./dv.js\": \"598a\",\n\t\"./el\": \"8d47\",\n\t\"./el.js\": \"8d47\",\n\t\"./en-au\": \"0e6b\",\n\t\"./en-au.js\": \"0e6b\",\n\t\"./en-ca\": \"3886\",\n\t\"./en-ca.js\": \"3886\",\n\t\"./en-gb\": \"39a6\",\n\t\"./en-gb.js\": \"39a6\",\n\t\"./en-ie\": \"e1d3\",\n\t\"./en-ie.js\": \"e1d3\",\n\t\"./en-il\": \"7333\",\n\t\"./en-il.js\": \"7333\",\n\t\"./en-in\": \"ec2e\",\n\t\"./en-in.js\": \"ec2e\",\n\t\"./en-nz\": \"6f50\",\n\t\"./en-nz.js\": \"6f50\",\n\t\"./en-sg\": \"b7e9\",\n\t\"./en-sg.js\": \"b7e9\",\n\t\"./eo\": \"65db\",\n\t\"./eo.js\": \"65db\",\n\t\"./es\": \"898b\",\n\t\"./es-do\": \"0a3c\",\n\t\"./es-do.js\": \"0a3c\",\n\t\"./es-mx\": \"b5b7\",\n\t\"./es-mx.js\": \"b5b7\",\n\t\"./es-us\": \"55c9\",\n\t\"./es-us.js\": \"55c9\",\n\t\"./es.js\": \"898b\",\n\t\"./et\": \"ec18\",\n\t\"./et.js\": \"ec18\",\n\t\"./eu\": \"0ff2\",\n\t\"./eu.js\": \"0ff2\",\n\t\"./fa\": \"8df4\",\n\t\"./fa.js\": \"8df4\",\n\t\"./fi\": \"81e9\",\n\t\"./fi.js\": \"81e9\",\n\t\"./fil\": \"d69a\",\n\t\"./fil.js\": \"d69a\",\n\t\"./fo\": \"0721\",\n\t\"./fo.js\": \"0721\",\n\t\"./fr\": \"9f26\",\n\t\"./fr-ca\": \"d9f8\",\n\t\"./fr-ca.js\": \"d9f8\",\n\t\"./fr-ch\": \"0e49\",\n\t\"./fr-ch.js\": \"0e49\",\n\t\"./fr.js\": \"9f26\",\n\t\"./fy\": \"7118\",\n\t\"./fy.js\": \"7118\",\n\t\"./ga\": \"5120\",\n\t\"./ga.js\": \"5120\",\n\t\"./gd\": \"f6b4\",\n\t\"./gd.js\": \"f6b4\",\n\t\"./gl\": \"8840\",\n\t\"./gl.js\": \"8840\",\n\t\"./gom-deva\": \"aaf2\",\n\t\"./gom-deva.js\": \"aaf2\",\n\t\"./gom-latn\": \"0caa\",\n\t\"./gom-latn.js\": \"0caa\",\n\t\"./gu\": \"e0c5\",\n\t\"./gu.js\": \"e0c5\",\n\t\"./he\": \"c7aa\",\n\t\"./he.js\": \"c7aa\",\n\t\"./hi\": \"dc4d\",\n\t\"./hi.js\": \"dc4d\",\n\t\"./hr\": \"4ba9\",\n\t\"./hr.js\": \"4ba9\",\n\t\"./hu\": \"5b14\",\n\t\"./hu.js\": \"5b14\",\n\t\"./hy-am\": \"d6b6\",\n\t\"./hy-am.js\": \"d6b6\",\n\t\"./id\": \"5038\",\n\t\"./id.js\": \"5038\",\n\t\"./is\": \"0558\",\n\t\"./is.js\": \"0558\",\n\t\"./it\": \"6e98\",\n\t\"./it-ch\": \"6f12\",\n\t\"./it-ch.js\": \"6f12\",\n\t\"./it.js\": \"6e98\",\n\t\"./ja\": \"079e\",\n\t\"./ja.js\": \"079e\",\n\t\"./jv\": \"b540\",\n\t\"./jv.js\": \"b540\",\n\t\"./ka\": \"201b\",\n\t\"./ka.js\": \"201b\",\n\t\"./kk\": \"6d79\",\n\t\"./kk.js\": \"6d79\",\n\t\"./km\": \"e81d\",\n\t\"./km.js\": \"e81d\",\n\t\"./kn\": \"3e92\",\n\t\"./kn.js\": \"3e92\",\n\t\"./ko\": \"22f8\",\n\t\"./ko.js\": \"22f8\",\n\t\"./ku\": \"2421\",\n\t\"./ku-kmr\": \"7558\",\n\t\"./ku-kmr.js\": \"7558\",\n\t\"./ku.js\": \"2421\",\n\t\"./ky\": \"9609\",\n\t\"./ky.js\": \"9609\",\n\t\"./lb\": \"440c\",\n\t\"./lb.js\": \"440c\",\n\t\"./lo\": \"b29d\",\n\t\"./lo.js\": \"b29d\",\n\t\"./lt\": \"26f9\",\n\t\"./lt.js\": \"26f9\",\n\t\"./lv\": \"b97c\",\n\t\"./lv.js\": \"b97c\",\n\t\"./me\": \"293c\",\n\t\"./me.js\": \"293c\",\n\t\"./mi\": \"688b\",\n\t\"./mi.js\": \"688b\",\n\t\"./mk\": \"6909\",\n\t\"./mk.js\": \"6909\",\n\t\"./ml\": \"02fb\",\n\t\"./ml.js\": \"02fb\",\n\t\"./mn\": \"958b\",\n\t\"./mn.js\": \"958b\",\n\t\"./mr\": \"39bd\",\n\t\"./mr.js\": \"39bd\",\n\t\"./ms\": \"ebe4\",\n\t\"./ms-my\": \"6403\",\n\t\"./ms-my.js\": \"6403\",\n\t\"./ms.js\": \"ebe4\",\n\t\"./mt\": \"1b45\",\n\t\"./mt.js\": \"1b45\",\n\t\"./my\": \"8689\",\n\t\"./my.js\": \"8689\",\n\t\"./nb\": \"6ce3\",\n\t\"./nb.js\": \"6ce3\",\n\t\"./ne\": \"3a39\",\n\t\"./ne.js\": \"3a39\",\n\t\"./nl\": \"facd\",\n\t\"./nl-be\": \"db29\",\n\t\"./nl-be.js\": \"db29\",\n\t\"./nl.js\": \"facd\",\n\t\"./nn\": \"b84c\",\n\t\"./nn.js\": \"b84c\",\n\t\"./oc-lnc\": \"167b\",\n\t\"./oc-lnc.js\": \"167b\",\n\t\"./pa-in\": \"f3ff\",\n\t\"./pa-in.js\": \"f3ff\",\n\t\"./pl\": \"8d57\",\n\t\"./pl.js\": \"8d57\",\n\t\"./pt\": \"f260\",\n\t\"./pt-br\": \"d2d4\",\n\t\"./pt-br.js\": \"d2d4\",\n\t\"./pt.js\": \"f260\",\n\t\"./ro\": \"972c\",\n\t\"./ro.js\": \"972c\",\n\t\"./ru\": \"957c\",\n\t\"./ru.js\": \"957c\",\n\t\"./sd\": \"6784\",\n\t\"./sd.js\": \"6784\",\n\t\"./se\": \"ffff\",\n\t\"./se.js\": \"ffff\",\n\t\"./si\": \"eda5\",\n\t\"./si.js\": \"eda5\",\n\t\"./sk\": \"7be6\",\n\t\"./sk.js\": \"7be6\",\n\t\"./sl\": \"8155\",\n\t\"./sl.js\": \"8155\",\n\t\"./sq\": \"c8f3\",\n\t\"./sq.js\": \"c8f3\",\n\t\"./sr\": \"cf1e\",\n\t\"./sr-cyrl\": \"13e9\",\n\t\"./sr-cyrl.js\": \"13e9\",\n\t\"./sr.js\": \"cf1e\",\n\t\"./ss\": \"52bd\",\n\t\"./ss.js\": \"52bd\",\n\t\"./sv\": \"5fbd\",\n\t\"./sv.js\": \"5fbd\",\n\t\"./sw\": \"74dc\",\n\t\"./sw.js\": \"74dc\",\n\t\"./ta\": \"3de5\",\n\t\"./ta.js\": \"3de5\",\n\t\"./te\": \"5cbb\",\n\t\"./te.js\": \"5cbb\",\n\t\"./tet\": \"576c\",\n\t\"./tet.js\": \"576c\",\n\t\"./tg\": \"3b1b\",\n\t\"./tg.js\": \"3b1b\",\n\t\"./th\": \"10e8\",\n\t\"./th.js\": \"10e8\",\n\t\"./tk\": \"5aff\",\n\t\"./tk.js\": \"5aff\",\n\t\"./tl-ph\": \"0f38\",\n\t\"./tl-ph.js\": \"0f38\",\n\t\"./tlh\": \"cf75\",\n\t\"./tlh.js\": \"cf75\",\n\t\"./tr\": \"0e81\",\n\t\"./tr.js\": \"0e81\",\n\t\"./tzl\": \"cf51\",\n\t\"./tzl.js\": \"cf51\",\n\t\"./tzm\": \"c109\",\n\t\"./tzm-latn\": \"b53d\",\n\t\"./tzm-latn.js\": \"b53d\",\n\t\"./tzm.js\": \"c109\",\n\t\"./ug-cn\": \"6117\",\n\t\"./ug-cn.js\": \"6117\",\n\t\"./uk\": \"ada2\",\n\t\"./uk.js\": \"ada2\",\n\t\"./ur\": \"5294\",\n\t\"./ur.js\": \"5294\",\n\t\"./uz\": \"2e8c\",\n\t\"./uz-latn\": \"010e\",\n\t\"./uz-latn.js\": \"010e\",\n\t\"./uz.js\": \"2e8c\",\n\t\"./vi\": \"2921\",\n\t\"./vi.js\": \"2921\",\n\t\"./x-pseudo\": \"fd7e\",\n\t\"./x-pseudo.js\": \"fd7e\",\n\t\"./yo\": \"7f33\",\n\t\"./yo.js\": \"7f33\",\n\t\"./zh-cn\": \"5c3a\",\n\t\"./zh-cn.js\": \"5c3a\",\n\t\"./zh-hk\": \"49ab\",\n\t\"./zh-hk.js\": \"49ab\",\n\t\"./zh-mo\": \"3a6c\",\n\t\"./zh-mo.js\": \"3a6c\",\n\t\"./zh-tw\": \"90ea\",\n\t\"./zh-tw.js\": \"90ea\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"4678\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NotificationButton.vue?vue&type=style&index=0&id=20888547&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[(_vm.isReady)?_c(_vm.layout,{tag:\"component\"},[_c('router-view')],1):_c('div',[_vm._v(\"Loading...\")]),_c('DifyChatBot')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div')\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <!-- Dify Chatbot 配置和脚本 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'DifyChatBot',\r\n  mounted() {\r\n    // 添加 Dify 配置\r\n    window.difyChatbotConfig = {\r\n      token: 'GAi2PqkyQkz1L6jm',\r\n      baseUrl: 'http://**************',\r\n      systemVariables: {\r\n        // user_id: 'YOU CAN DEFINE USER ID HERE',\r\n        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',\r\n      },\r\n      userVariables: {\r\n        // avatar_url: 'YOU CAN DEFINE USER AVATAR URL HERE',\r\n        // name: 'YOU CAN DEFINE USER NAME HERE',\r\n      },\r\n      containerProps: {},\r\n      // 启用拖拽功能\r\n      draggable: true,\r\n      // 允许在 x 和 y 轴上拖动\r\n      dragAxis: 'both',\r\n    };\r\n\r\n    // 添加 Dify 脚本\r\n    const script = document.createElement('script');\r\n    script.src = 'http://**************/embed.min.js';\r\n    script.id = 'GAi2PqkyQkz1L6jm';\r\n    script.defer = true;\r\n    document.head.appendChild(script);\r\n\r\n    // 添加样式\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      #dify-chatbot-bubble-button {\r\n        background-color: #1C64F2 !important;\r\n      }\r\n      #dify-chatbot-bubble-window {\r\n        width: 32rem !important;\r\n        height: 48rem !important;\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 组件样式 */\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DifyChatBot.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DifyChatBot.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DifyChatBot.vue?vue&type=template&id=67100c59&scoped=true\"\nimport script from \"./DifyChatBot.vue?vue&type=script&lang=js\"\nexport * from \"./DifyChatBot.vue?vue&type=script&lang=js\"\nimport style0 from \"./DifyChatBot.vue?vue&type=style&index=0&id=67100c59&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"67100c59\",\n  null\n  \n)\n\nexport default component.exports", "<!--\r\n\tThis is the main page of the application, the layout component is used here,\r\n\tand the router-view is passed to it.\r\n\tLayout component is dynamically declared based on the layout for each route,\r\n\tspecified in routes list router/index.js .\r\n -->\r\n\r\n<template>\r\n\t<div id=\"app\">\r\n\t\t<component :is=\"layout\" v-if=\"isReady\">\r\n\t\t\t<router-view />\r\n\t\t</component>\r\n\t\t<div v-else>Loading...</div>\r\n\t\t\r\n\t\t<!-- 添加Dify聊天机器人 -->\r\n\t\t<DifyChatBot />\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport DifyChatBot from '@/components/common/DifyChatBot.vue';\r\n\r\nexport default {\r\n\tname: 'App',\r\n\tcomponents: {\r\n\t\tDifyChatBot\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisReady: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// Sets components name based on current route's specified layout, defaults to\r\n\t\t// <layout-default></layout-default> component.\r\n\t\tlayout() {\r\n\t\t\treturn \"layout-\" + (this.$route.meta.layout || \"dashboard\");\r\n\t\t},\r\n\t\tisDarkMode() {\r\n\t\t\treturn this.$store.state.darkMode;\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.isReady = true;\r\n\t\t// 初始化深色模式\r\n\t\tif (this.$store.state.darkMode) {\r\n\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t} else {\r\n\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听深色模式变化\r\n\t\tisDarkMode(newValue) {\r\n\t\t\tif (newValue) {\r\n\t\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t\t} else {\r\n\t\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=7cee83ae\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-card',{attrs:{\"title\":\"项目列表\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.createNewProject}},[_vm._v(\" 创建新项目 \")])]},proxy:true}])},[_c('a-table',{staticClass:\"project-table\",attrs:{\"columns\":_vm.columns,\"data-source\":_vm.projects,\"row-key\":record => record.dbFile,\"customRow\":_vm.onCustomRow}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-card title=\"项目列表\">\r\n      <template #extra>\r\n        <a-button type=\"primary\" @click=\"createNewProject\">\r\n          创建新项目\r\n        </a-button>\r\n      </template>\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"projects\"\r\n        :row-key=\"record => record.dbFile\"\r\n        :customRow=\"onCustomRow\"\r\n        class=\"project-table\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProjectManager',\r\n  data() {\r\n    return {\r\n      columns: [\r\n        {\r\n          title: '项目名称',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '250px',\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            return (\r\n            <span>\r\n            <a-icon type=\"folder\" style=\"margin-right: 8px;\" />\r\n            {text}\r\n            </span>\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: '数据库文件',\r\n          dataIndex: 'dbFile',\r\n          key: 'dbFile',\r\n          width: '280px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          dataIndex: 'createdAt',\r\n          key: 'createdAt',\r\n          width: '160px',\r\n          customRender: (text) => {\r\n            return new Date(text).toLocaleString('zh-CN', {\r\n              year: 'numeric',\r\n              month: '2-digit',\r\n              day: '2-digit',\r\n              hour: '2-digit',\r\n              minute: '2-digit'\r\n            });\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: '180px',\r\n          align: 'center',\r\n          fixed: 'right',\r\n          customRender: (text, record) => {\r\n            return (\r\n              <a-space size={8}>\r\n                <a-button\r\n                  type=\"primary\"\r\n                  onClick={() => this.selectProject(record)}\r\n                >\r\n                  进入项目\r\n                </a-button>\r\n                <a-popconfirm\r\n                  title=\"确定要删除这个项目吗？\"\r\n                  onConfirm={() => this.deleteProject(record)}\r\n                  okText=\"确定\"\r\n                  cancelText=\"取消\"\r\n                >\r\n                  <a-button type=\"danger\">删除</a-button>\r\n                </a-popconfirm>\r\n              </a-space>\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      projects: [],\r\n      tempProjectName: ''\r\n    };\r\n  },\r\n  methods: {\r\n    onCustomRow(record) {\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            // 如果需要行点击事件的话，在这里处理\r\n          }\r\n        }\r\n      };\r\n    },\r\n    async fetchProjects() {\r\n      try {\r\n        const response = await axios.get('/api/projects');\r\n        if (Array.isArray(response.data)) {\r\n          this.projects = response.data.map(project => ({\r\n            name: project.name || '',\r\n            dbFile: project.dbFile || '',\r\n            createdAt: project.createdAt || '',\r\n            key: project.dbFile || Date.now().toString()\r\n          }));\r\n        } else {\r\n          this.projects = [];\r\n          console.error('项目数据格式无效：', response.data);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败：', error);\r\n        this.$message.error('获取项目列表失败');\r\n        this.projects = [];\r\n      }\r\n    },\r\n\r\n    async selectProject(project) {\r\n      if (!project?.dbFile) {\r\n        console.error('项目数据无效：', project);\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const encodedDbFile = encodeURIComponent(project.dbFile);\r\n        const validationUrl = `/api/projects/validate/${encodedDbFile}`;\r\n\r\n        const response = await axios.get(validationUrl);\r\n        if (response.data.valid) {\r\n          await this.$store.dispatch('switchProject', { \r\n            dbFile: project.dbFile, \r\n            projectName: project.name \r\n          });\r\n          await this.$store.dispatch('fetchNodes');\r\n\r\n          // 清除所有任务相关的localStorage\r\n          localStorage.removeItem('activeTaskId');\r\n          localStorage.removeItem('taskCompleted');\r\n          localStorage.removeItem('activeUploadTaskId');\r\n          localStorage.removeItem('activeDownloadTaskId');\r\n          localStorage.removeItem('activeToolTaskId');\r\n\r\n          await this.$router.push('/task');\r\n          this.$message.success('成功进入项目');\r\n        } else {\r\n          console.error('验证失败：', response.data.error);\r\n          this.$message.error(response.data.error || '数据库文件无效或已损坏');\r\n        }\r\n      } catch (error) {\r\n        console.error('项目验证出错：', error);\r\n        this.$message.error(error.response?.data?.error || '验证项目失败');\r\n      }\r\n    },\r\n\r\n    async deleteProject(project) {\r\n      if (!project?.dbFile) {\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await axios.delete(`/api/projects/${encodeURIComponent(project.dbFile)}`);\r\n        this.$message.success('项目删除成功');\r\n        await this.fetchProjects();\r\n      } catch (error) {\r\n        console.error('Error deleting project:', error);\r\n        this.$message.error('删除项目失败');\r\n      }\r\n    },\r\n\r\n    async createNewProject() {\r\n      try {\r\n        const projectName = await new Promise((resolve, reject) => {\r\n          this.$confirm({\r\n            title: '创建新项目',\r\n            content: h => (\r\n              <div>\r\n                <a-input\r\n                  placeholder=\"请输入项目名称\"\r\n                  onChange={(e) => {\r\n                    const value = e.target.value.replace(/[^a-zA-Z0-9_-]/g, '');\r\n                    this.tempProjectName = value;\r\n                    e.target.value = value;\r\n                  }}\r\n                />\r\n                <div class=\"project-hint-text\" style=\"font-size: 12px; margin-top: 8px;\">\r\n                  只允许输入大小写字母、数字、下划线和连字符\r\n                </div>\r\n              </div>\r\n            ),\r\n            okText: '确定',\r\n            cancelText: '取消',\r\n            onOk: () => {\r\n              if (!this.tempProjectName) {\r\n                this.$message.warning('请输入项目名称');\r\n                return Promise.reject();\r\n              }\r\n              // 验证项目名称格式\r\n              if (!/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)) {\r\n                this.$message.warning('项目名称只能包含大小写字母、数字、下划线和连字符');\r\n                return Promise.reject();\r\n              }\r\n              resolve(this.tempProjectName);\r\n            },\r\n            onCancel: () => {\r\n              reject();\r\n            }\r\n          });\r\n        });\r\n\r\n        if (projectName) {\r\n          const response = await axios.post('/api/projects/new', {\r\n            name: projectName\r\n          });\r\n\r\n          await this.fetchProjects();\r\n          this.$message.success('新项目创建成功');\r\n          if (response.data?.dbFile) {\r\n            await this.selectProject(response.data);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error) { // 用户取消操作时不显示错误\r\n          console.error('Error creating new project:', error);\r\n          this.$message.error('创建新项目失败');\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjects();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ant-card {\r\n  margin: 24px;\r\n}\r\n.ant-table {\r\n  margin-top: 16px;\r\n}\r\n.ant-space {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n.project-table >>> .ant-table-thead > tr > th {\r\n  font-weight: 600;\r\n  padding: 12px 16px !important;\r\n}\r\n\r\n.project-table >>> .ant-table-tbody > tr > td {\r\n  padding: 12px 16px !important;\r\n  vertical-align: middle;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProjectManager.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProjectManager.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProjectManager.vue?vue&type=template&id=368921a4&scoped=true\"\nimport script from \"./ProjectManager.vue?vue&type=script&lang=js\"\nexport * from \"./ProjectManager.vue?vue&type=script&lang=js\"\nimport style0 from \"./ProjectManager.vue?vue&type=style&index=0&id=368921a4&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"368921a4\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\nimport ProjectManager from '../views/ProjectManager.vue'\r\n\r\nVue.use(VueRouter)\r\n\r\nlet routes = [\r\n\t{\r\n\t\tpath: '/',\r\n\t\tredirect: '/projects'\r\n\t},\r\n\t{\r\n\t\tpath: '/projects',\r\n\t\tname: 'ProjectManager',\r\n\t\tlayout: \"simple\",\r\n\t\tcomponent: ProjectManager,\r\n\t},\r\n\t{\r\n\t\tpath: '/task',\r\n\t\tname: 'Task',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Task.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '*',\r\n\t\tcomponent: () => import('../views/404.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/process',\r\n\t\tname: 'Process',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Process.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/process/:pid',\r\n\t\tname: 'ProcessDetail',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../components/Cards/ProcessDetail.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/package',\r\n\t\tname: 'Package',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/PackageInfo.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/hardware',\r\n\t\tname: 'Hardware',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Hardware.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/filesystem',\r\n\t\tname: 'Filesystem',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Filesystem.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/port',\r\n\t\tname: 'Port',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Port.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/docker',\r\n\t\tname: 'Docker',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Docker.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/kubernetes',\r\n\t\tname: 'Kubernetes',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Kubernetes.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/code-info',\r\n\t\tname: 'CodeInfo',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/CodeInfo.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/material-info',\r\n\t\tname: 'MaterialInfo',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/MaterialInfo.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/config',\r\n\t\tname: 'Config',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Config.vue'),\r\n\t\tprops: (route) => ({\r\n\t\t\tdefaultTab: route.hash.replace('#',  '') || 'host'\r\n\t\t})\r\n\t},\r\n\t{\r\n\t\tpath: '/repository',\r\n\t\tname: 'Repository',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/Repository.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/upload',\r\n\t\tname: 'Upload',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/FileUpload.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/download',\r\n\t\tname: 'Download',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/FileDownload.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/aibash',\r\n\t\tname: 'AIBash',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/AIBash.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/testcase',\r\n\t\tname: 'TestCase',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/TestCase.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/execute-case',\r\n\t\tname: 'ExecuteCase',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/ExecuteCase.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/smart-orchestration',\r\n\t\tname: 'SmartOrchestration',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/SmartOrchestration.vue'),\r\n\t},\r\n\t{\r\n\t\tpath: '/tools',\r\n\t\tname: 'Tools',\r\n\t\tlayout: \"dashboard\",\r\n\t\tcomponent: () => import('../views/GenerateScript.vue'),\r\n\t},\r\n]\r\n\r\n// Adding layout property from each route to the meta\r\n// object so it can be accessed later.\r\nfunction addLayoutToRoute( route, parentLayout = \"default\" )\r\n{\r\n\troute.meta = route.meta || {} ;\r\n\troute.meta.layout = route.layout || parentLayout ;\r\n\r\n\tif( route.children )\r\n\t{\r\n\t\troute.children = route.children.map( ( childRoute ) => addLayoutToRoute( childRoute, route.meta.layout ) ) ;\r\n\t}\r\n\treturn route ;\r\n}\r\n\r\nroutes = routes.map( ( route ) => addLayoutToRoute( route ) ) ;\r\n\r\nconst router = new VueRouter({\r\n\tmode: 'hash',\r\n\tbase: process.env.BASE_URL,\r\n\troutes,\r\n\tscrollBehavior (to, from, savedPosition) {\r\n\t\tif ( to.hash ) {\r\n\t\t\treturn {\r\n\t\t\t\tselector: to.hash,\r\n\t\t\t\tbehavior: 'smooth',\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tx: 0,\r\n\t\t\ty: 0,\r\n\t\t\tbehavior: 'smooth',\r\n\t\t}\r\n\t}\r\n})\r\n\r\n// 强制第一次导航\r\nrouter.onReady(() => {\r\n\tconst currentPath = router.currentRoute.path;\r\n\tif (currentPath === '/') {\r\n\t\trouter.push('/projects').catch(err => {\r\n\t\t\tif (err.name !== 'NavigationDuplicated') {\r\n\t\t\t\tthrow err;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n});\r\n\r\n// 添加导航守卫\r\nrouter.beforeEach((to, from, next) => {\r\n\tif (to.path  === '/config') {\r\n\t\tconst validTabs = ['host', 'cbh']\r\n\t\tconst currentTab = to.hash.replace('#',  '')\r\n\r\n\t\tif (!validTabs.includes(currentTab))  {\r\n\t\t\treturn next({\r\n\t\t\t\tpath: '/config',\r\n\t\t\t\thash: '#host',\r\n\t\t\t\treplace: true\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\tconst isSamePath = to.path  === from.path\r\n\tconst isSameHash = to.hash  === from.hash\r\n\tconst isSameQuery = JSON.stringify(to.query)  === JSON.stringify(from.query)\r\n\r\n\t// 仅当路径、hash、查询参数完全相同时阻止导航\r\n\tif (isSamePath && isSameHash && isSameQuery) {\r\n\t\tnext(false)\r\n\t} else {\r\n\t\tnext()\r\n\t}\r\n})\r\n\r\n// 添加导航后的回调\r\nrouter.afterEach((to, from) => {\r\n})\r\n\r\nexport default router\r\n", "// store/index.js\r\nimport Vue from 'vue';\r\nimport Vuex from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nVue.use(Vuex);\r\n\r\n// 进程列表状态模块\r\nconst processListModule = {\r\n    namespaced: true,\r\n    state: {\r\n        currentPage: 1,\r\n        scrollPosition: 0,\r\n        lastViewedPid: null // 添加最后查看的进程ID\r\n    },\r\n    mutations: {\r\n        setCurrentPage(state, page) {\r\n            state.currentPage = page;\r\n        },\r\n        setScrollPosition(state, position) {\r\n            state.scrollPosition = position;\r\n        },\r\n        setLastViewedPid(state, pid) {\r\n            state.lastViewedPid = pid;\r\n        },\r\n        resetState(state) {\r\n            state.currentPage = 1;\r\n            state.scrollPosition = 0;\r\n            // 不重置lastViewedPid，因为我们希望在返回时仍然能够高亮显示\r\n        },\r\n        clearLastViewedPid(state) {\r\n            state.lastViewedPid = null;\r\n        }\r\n    },\r\n    actions: {\r\n        updateCurrentPage({ commit }, page) {\r\n            commit('setCurrentPage', page);\r\n        },\r\n        updateScrollPosition({ commit }, position) {\r\n            commit('setScrollPosition', position);\r\n        },\r\n        updateLastViewedPid({ commit }, pid) {\r\n            commit('setLastViewedPid', pid);\r\n        },\r\n        resetState({ commit }) {\r\n            commit('resetState');\r\n        },\r\n        clearLastViewedPid({ commit }) {\r\n            commit('clearLastViewedPid');\r\n        }\r\n    }\r\n};\r\n\r\nexport default new Vuex.Store({\r\n    modules: {\r\n        processList: processListModule\r\n    },\r\n    state: {\r\n        nodes: [], // 用于存储节点信息\r\n        selectedNodeIp: null, // 用于存储选中的节点 IP\r\n        activeUploadTask: null,\r\n        activeDownloadTask: null,\r\n        activeTask: null,\r\n        activeToolTask: null,\r\n        repositoryDownloadResults: null, // 存储代码仓下载结果  // 添加 activeTask 状态\r\n        currentProject: localStorage.getItem('currentProject'),\r\n        currentProjectName: localStorage.getItem('currentProjectName'),\r\n        sidebarColor: localStorage.getItem('sidebarColor') || 'primary',\r\n        notifications: [], // 用于存储通知信息\r\n        language: localStorage.getItem('language') || 'en-US', // 当前语言设置\r\n        darkMode: localStorage.getItem('darkMode') !== 'false', // 深色模式状态，默认为true\r\n        smartSearchResults: []\r\n    },\r\n    getters: {\r\n        unreadNotifications: state => state.notifications.filter(n => !n.read).length\r\n    },\r\n    mutations: {\r\n        setNodes(state, nodes) {\r\n            state.nodes = nodes;\r\n        },\r\n        setSelectedNodeIp(state, ip) {\r\n            state.selectedNodeIp = ip;\r\n        },\r\n        setActiveUploadTask(state, task) {\r\n            state.activeUploadTask = task;\r\n        },\r\n        setActiveDownloadTask(state, task) {\r\n            state.activeDownloadTask = task;\r\n        },\r\n        setActiveTask(state, task) {\r\n            state.activeTask = task;\r\n        },\r\n        clearActiveTask(state) {\r\n            state.activeTask = null;\r\n        },\r\n        setActiveToolTask(state, task) {\r\n            state.activeToolTask = task;\r\n        },\r\n        clearActiveToolTask(state) {\r\n            state.activeToolTask = null;\r\n        },\r\n        setRepositoryDownloadResults(state, results) {\r\n            state.repositoryDownloadResults = results;\r\n        },\r\n        clearRepositoryDownloadResults(state) {\r\n            state.repositoryDownloadResults = null;\r\n        },\r\n        setCurrentProject(state, { dbFile, projectName }) {\r\n            state.currentProject = dbFile;\r\n            state.currentProjectName = projectName;\r\n            localStorage.setItem('currentProject', dbFile);\r\n            localStorage.setItem('currentProjectName', projectName || '');\r\n        },\r\n        setSidebarColor(state, color) {\r\n            state.sidebarColor = color;\r\n            localStorage.setItem('sidebarColor', color);\r\n        },\r\n        setLanguage(state, language) {\r\n            state.language = language;\r\n            localStorage.setItem('language', language);\r\n        },\r\n        addNotification(state, notification) {\r\n            state.notifications.unshift(notification); // 添加到数组开头\r\n        },\r\n        markNotificationsAsRead(state) {\r\n            state.notifications.forEach(n => n.read = true);\r\n        },\r\n        clearNotifications(state) {\r\n            state.notifications = [];\r\n        },\r\n        setDarkMode(state, isDark) {\r\n            state.darkMode = isDark;\r\n            localStorage.setItem('darkMode', isDark);\r\n            // 应用深色模式类到根元素\r\n            if (isDark) {\r\n                document.documentElement.classList.add('dark-mode');\r\n            } else {\r\n                document.documentElement.classList.remove('dark-mode');\r\n            }\r\n        },\r\n        // 智能编排搜索相关mutations\r\n        setSmartSearchResults(state, results) {\r\n            state.smartSearchResults = results;\r\n        },\r\n        clearSmartSearch(state) {\r\n            state.smartSearchResults = [];\r\n        }\r\n    },\r\n    actions: {\r\n        async fetchNodes({ state, commit }) {\r\n            try {\r\n                if (!state.currentProject) {\r\n                    commit('setNodes', []);\r\n                    return;\r\n                }\r\n\r\n                const response = await axios.get('/api/config', {\r\n                    params: {\r\n                        detail: true,\r\n                        dbFile: state.currentProject\r\n                    }\r\n                });\r\n\r\n                if (Array.isArray(response.data)) {\r\n                    commit('setNodes', response.data);\r\n                } else {\r\n                    console.error('Invalid nodes data format:', response.data);\r\n                    commit('setNodes', []);\r\n                }\r\n            } catch (error) {\r\n                console.error('Error fetching nodes:', error);\r\n                commit('setNodes', []);\r\n            }\r\n        },\r\n        updateUploadTask({ commit }, task) {\r\n            commit('setActiveUploadTask', task);\r\n        },\r\n        updateDownloadTask({ commit }, task) {\r\n            commit('setActiveDownloadTask', task);\r\n        },\r\n        updateTask({ commit }, task) {\r\n            commit('setActiveTask', task);\r\n        },\r\n        clearActiveTask({ commit }) {\r\n            commit('clearActiveTask');\r\n        },\r\n        updateToolTask({ commit }, task) {\r\n            commit('setActiveToolTask', task);\r\n        },\r\n        clearActiveToolTask({ commit }) {\r\n            commit('clearActiveToolTask');\r\n        },\r\n        updateRepositoryDownloadResults({ commit }, results) {\r\n            commit('setRepositoryDownloadResults', results);\r\n        },\r\n        clearRepositoryDownloadResults({ commit }) {\r\n            commit('clearRepositoryDownloadResults');\r\n        },\r\n        updateSidebarColor({ commit }, color) {\r\n            commit('setSidebarColor', color);\r\n        },\r\n        updateLanguage({ commit }, language) {\r\n            commit('setLanguage', language);\r\n        },\r\n        addNotification({ commit }, notification) {\r\n            commit('addNotification', {\r\n                ...notification,\r\n                id: Date.now(),\r\n                time: new Date().toLocaleTimeString(),\r\n                read: false\r\n            });\r\n        },\r\n        markNotificationsAsRead({ commit }) {\r\n            commit('markNotificationsAsRead');\r\n        },\r\n        clearNotifications({ commit }) {\r\n            commit('clearNotifications');\r\n        },\r\n        toggleDarkMode({ commit, state }) {\r\n            commit('setDarkMode', !state.darkMode);\r\n        },\r\n        updateSmartSearchResults({ commit }, results) {\r\n            commit('setSmartSearchResults', results);\r\n        },\r\n        clearSmartSearch({ commit }) {\r\n            commit('clearSmartSearch');\r\n        },\r\n        clearProjectStates({ commit, dispatch }) {\r\n            // 清除主状态\r\n            commit('setActiveUploadTask', null);\r\n            commit('setActiveDownloadTask', null);\r\n            commit('clearActiveTask');\r\n            commit('clearActiveToolTask');\r\n            commit('clearRepositoryDownloadResults');\r\n            commit('clearSmartSearch');\r\n            commit('setNodes', []);\r\n            commit('setSelectedNodeIp', null);\r\n            dispatch('processList/resetState', null, { root: true });\r\n            dispatch('processList/clearLastViewedPid', null, { root: true });\r\n        },\r\n        // 切换项目\r\n        switchProject({ commit, dispatch }, { dbFile, projectName }) {\r\n            // 先清除所有状态\r\n            dispatch('clearProjectStates');\r\n            // 然后设置新项目\r\n            commit('setCurrentProject', { dbFile, projectName });\r\n        }\r\n    },\r\n});\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-layout',{staticClass:\"layout-simple\"},[_c('a-layout-content',[_c('div',{staticClass:\"content-wrapper\"},[(_vm.isRouterViewMounted)?_c('router-view'):_c('div',[_vm._v(\"Loading...\")])],1)])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-layout class=\"layout-simple\">\r\n      <a-layout-content>\r\n        <div class=\"content-wrapper\">\r\n          <router-view v-if=\"isRouterViewMounted\" />\r\n          <div v-else>Loading...</div>\r\n        </div>\r\n      </a-layout-content>\r\n    </a-layout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SimpleLayout',\r\n  data() {\r\n    return {\r\n      isRouterViewMounted: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.isRouterViewMounted = true;\r\n    });\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout-simple {\r\n  min-height: 100vh;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Simple.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Simple.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Simple.vue?vue&type=template&id=5b12b09b&scoped=true\"\nimport script from \"./Simple.vue?vue&type=script&lang=js\"\nexport * from \"./Simple.vue?vue&type=script&lang=js\"\nimport style0 from \"./Simple.vue?vue&type=style&index=0&id=5b12b09b&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b12b09b\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-layout',{staticClass:\"layout-dashboard\",class:[_vm.navbarFixed ? 'navbar-fixed' : '', ! _vm.sidebarCollapsed ? 'has-sidebar' : '', _vm.layoutClass],attrs:{\"id\":\"layout-dashboard\"}},[_c('DashboardSidebar',{attrs:{\"sidebarCollapsed\":_vm.sidebarCollapsed,\"sidebarColor\":_vm.sidebarColor,\"sidebarTheme\":_vm.sidebarTheme},on:{\"toggleSidebar\":_vm.toggleSidebar}}),_c('a-layout',[_c('DashboardHeader',{attrs:{\"sidebarCollapsed\":_vm.sidebarCollapsed,\"navbarFixed\":_vm.navbarFixed,\"sidebarColor\":_vm.sidebarColor},on:{\"toggleSettingsDrawer\":_vm.toggleSettingsDrawer,\"toggleSidebar\":_vm.toggleSidebar}}),_c('a-layout-content',[_c('router-view')],1),_c('DashboardFooter'),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(! _vm.sidebarCollapsed),expression:\"! sidebarCollapsed\"}],staticClass:\"sidebar-overlay\",on:{\"click\":function($event){_vm.sidebarCollapsed = true}}})],1),_c('DashboardSettingsDrawer',{attrs:{\"showSettingsDrawer\":_vm.showSettingsDrawer,\"navbarFixed\":_vm.navbarFixed,\"sidebarTheme\":_vm.sidebarTheme},on:{\"toggleSettingsDrawer\":_vm.toggleSettingsDrawer,\"toggleNavbarPosition\":_vm.toggleNavbarPosition,\"updateSidebarTheme\":_vm.updateSidebarTheme,\"updateSidebarColor\":_vm.updateSidebarColor}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-layout-sider',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.sidebarCollapsed),expression:\"!sidebarCollapsed\"}],staticClass:\"sider-primary\",class:[\n        'ant-layout-sider-' + _vm.sidebarColor,\n        'ant-layout-sider-' + _vm.sidebarTheme\n    ],style:({ backgroundColor: 'transparent' }),attrs:{\"trigger\":null,\"collapsible\":\"\",\"collapsed-width\":0,\"width\":\"220\",\"theme\":\"light\"}},[_c('div',{staticClass:\"sidebar-container\"},[_c('div',{staticClass:\"brand-section\"},[_c('div',{staticClass:\"brand-header\"},[_c('div',{staticClass:\"logo-container\",class:`text-${_vm.sidebarColor}`},[_c('svg',{staticClass:\"ios-logo\",attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"36px\",\"height\":\"36px\",\"viewBox\":\"0 0 48 48\"}},[_c('rect',{attrs:{\"width\":\"48\",\"height\":\"48\",\"rx\":\"10\",\"fill\":\"currentColor\",\"opacity\":\"0.1\"}}),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"2.2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"d\":\"M24 43.5c9.043-3.117 15.488-10.363 16.5-19.589c.28-4.005.256-8.025-.072-12.027a2.54 2.54 0 0 0-2.467-2.366c-4.091-.126-8.846-.808-12.52-4.427a2.05 2.05 0 0 0-2.881 0c-3.675 3.619-8.43 4.301-12.52 4.427a2.54 2.54 0 0 0-2.468 2.366A79.4 79.4 0 0 0 7.5 23.911C8.51 33.137 14.957 40.383 24 43.5z\"}},[_c('animate',{attrs:{\"attributeName\":\"stroke-dasharray\",\"values\":\"0 150;150 0;0 150\",\"dur\":\"5s\",\"calcMode\":\"linear\",\"repeatCount\":\"indefinite\"}})]),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"0.8\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"opacity\":\"0.6\",\"d\":\"M24 39c7.5-2.5 12.5-8.5 13.5-16c.2-3 .2-6 0-9\"}}),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"0.8\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"opacity\":\"0.6\",\"d\":\"M24 39c-7.5-2.5-12.5-8.5-13.5-16c-.2-3-.2-6 0-9\"}}),_c('circle',{attrs:{\"cx\":\"24\",\"cy\":\"20.206\",\"r\":\"4.5\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\"}}),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"d\":\"M31.589 32.093a7.589 7.589 0 1 0-15.178 0\"}}),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"1.5\",\"stroke-linecap\":\"round\",\"d\":\"M24 20.2v2.5\"}})])]),_c('div',{staticClass:\"brand-text-container\"},[_c('div',{staticClass:\"brand-name\"},[_vm._v(\"SecTest Copilot\")])])]),_c('div',{staticClass:\"brand-divider\"})]),_c('div',{staticClass:\"navigation-section\"},[_c('a-menu',{staticClass:\"sidebar-menu\",attrs:{\"theme\":\"light\",\"mode\":\"inline\",\"default-open-keys\":['securityTool', 'llmAutoTesting', 'aiTaintAnalysis'],\"inline-collapsed\":_vm.sidebarCollapsed}},[_c('a-sub-menu',{key:\"envAwareness\",scopedSlots:_vm._u([{key:\"title\",fn:function({ open }){return [_c('span',{staticClass:\"enhanced-title\"},[_vm._v(_vm._s(_vm.$t('sidebar.envAwareness')))])]}}])},[_c('a-menu-item',{key:\"process\"},[_c('router-link',{attrs:{\"to\":\"/process\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 512 512\"}},[_c('path',{attrs:{\"d\":\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.processInfo')))])])],1),_c('a-menu-item',{key:\"package\"},[_c('router-link',{attrs:{\"to\":\"/package\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 16 16\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"fill-rule\":\"evenodd\",\"d\":\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\",\"clip-rule\":\"evenodd\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.packageInfo')))])])],1),_c('a-menu-item',{key:\"hardware\"},[_c('router-link',{attrs:{\"to\":\"/hardware\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 512 512\"}},[_c('path',{attrs:{\"fill\":\"#ffffff\",\"d\":\"M160 160h192v192H160z\"}}),_c('path',{attrs:{\"fill\":\"#ffffff\",\"d\":\"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.hardwareInfo')))])])],1),_c('a-menu-item',{key:\"filesystem\"},[_c('router-link',{attrs:{\"to\":\"/filesystem\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 16 16\"}},[_c('path',{attrs:{\"fill\":\"#ffffff\",\"fill-rule\":\"evenodd\",\"d\":\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\",\"clip-rule\":\"evenodd\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.filesystemInfo')))])])],1),_c('a-menu-item',{key:\"high-port-info\"},[_c('router-link',{attrs:{\"to\":\"/port\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 384 512\",\"width\":\"20\",\"height\":\"20\"}},[_c('path',{attrs:{\"d\":\"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.portInfo')))])])],1),_c('a-menu-item',{key:\"docker\"},[_c('router-link',{attrs:{\"to\":\"/docker\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"x\":\"0px\",\"y\":\"0px\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 48 48\"}},[_c('path',{attrs:{\"d\":\"M 22.5 6 C 22.224 6 22 6.224 22 6.5 L 22 9.5 C 22 9.776 22.224 10 22.5 10 L 25.5 10 C 25.776 10 26 9.776 26 9.5 L 26 6.5 C 26 6.224 25.776 6 25.5 6 L 22.5 6 z M 10.5 12 C 10.224 12 10 12.224 10 12.5 L 10 15.5 C 10 15.776 10.224 16 10.5 16 L 13.5 16 C 13.776 16 14 15.776 14 15.5 L 14 12.5 C 14 12.224 13.776 12 13.5 12 L 10.5 12 z M 16.5 12 C 16.224 12 16 12.224 16 12.5 L 16 15.5 C 16 15.776 16.224 16 16.5 16 L 19.5 16 C 19.776 16 20 15.776 20 15.5 L 20 12.5 C 20 12.224 19.776 12 19.5 12 L 16.5 12 z M 22.5 12 C 22.224 12 22 12.224 22 12.5 L 22 15.5 C 22 15.776 22.224 16 22.5 16 L 25.5 16 C 25.776 16 26 15.776 26 15.5 L 26 12.5 C 26 12.224 25.776 12 25.5 12 L 22.5 12 z M 37.478516 14.300781 L 37.025391 14.951172 C 36.458391 15.825172 36.045734 16.787828 35.802734 17.798828 C 35.343734 19.731828 35.621422 21.546656 36.607422 23.097656 C 35.416422 23.758656 33.386 23.986 33 24 L 2 24 C 0.895 24 0 24.895 0 26 C 0 28 0.43371875 30.924625 1.3867188 33.515625 C 2.4757187 36.359625 4.0970781 38.454328 6.2050781 39.736328 C 8.5670781 41.177328 12.404859 42 16.755859 42 C 18.720859 42.006 20.683234 41.828703 22.615234 41.470703 C 25.301234 40.979703 27.885719 40.045078 30.261719 38.705078 C 32.219719 37.576078 33.981469 36.139172 35.480469 34.451172 C 37.985469 31.627172 39.477891 28.4815 40.587891 25.6875 C 40.592891 25.6845 40.596562 25.683688 40.601562 25.679688 C 43.598562 25.800687 45.412625\\n                            24.642688 46.390625 23.679688 C 47.008625 23.095688 47.491688 22.38275 47.804688 21.59375 L 48 21.021484 L 47.527344 20.650391 C 47.397344 20.547391 46.182141 19.632812 43.619141 19.632812 C 42.942141 19.635813 42.266609 19.694641 41.599609 19.806641 C 41.103609 16.421641 38.293969 14.769313 38.167969 14.695312 L 37.478516 14.300781 z M 4.5 18 C 4.224 18 4 18.224 4 18.5 L 4 21.5 C 4 21.776 4.224 22 4.5 22 L 7.5 22 C 7.776 22 8 21.776 8 21.5 L 8 18.5 C 8 18.224 7.776 18 7.5 18 L 4.5 18 z M 10.5 18 C 10.224 18 10 18.224 10 18.5 L 10 21.5 C 10 21.776 10.224 22 10.5 22 L 13.5 22 C 13.776 22 14 21.776 14 21.5 L 14 18.5 C 14 18.224 13.776 18 13.5 18 L 10.5 18 z M 16.5 18 C 16.224 18 16 18.224 16 18.5 L 16 21.5 C 16 21.776 16.224 22 16.5 22 L 19.5 22 C 19.776 22 20 21.776 20 21.5 L 20 18.5 C 20 18.224 19.776 18 19.5 18 L 16.5 18 z M 22.5 18 C 22.224 18 22 18.224 22 18.5 L 22 21.5 C 22 21.776 22.224 22 22.5 22 L 25.5 22 C 25.776 22 26 21.776 26 21.5 L 26 18.5 C 26 18.224 25.776 18 25.5 18 L 22.5 18 z M 28.5 18 C 28.224 18 28 18.224 28 18.5 L 28 21.5 C 28 21.776 28.224 22 28.5 22 L 31.5 22 C 31.776 22 32 21.776 32 21.5 L 32 18.5 C 32 18.224 31.776 18 31.5 18 L 28.5 18 z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.dockerInfo')))])])],1),_c('a-menu-item',{key:\"kubernetes\"},[_c('router-link',{attrs:{\"to\":\"/kubernetes\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"80\",\"height\":\"80\",\"viewBox\":\"0 0 32 32\"}},[_c('path',{attrs:{\"fill\":\"#ffffff\",\"d\":\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.k8sInfo')))])])],1),_c('a-menu-item',{key:\"code-info\"},[_c('router-link',{attrs:{\"to\":\"/code-info\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 640 512\"}},[_c('path',{attrs:{\"d\":\"M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.codeInfo')))])])],1),_c('a-menu-item',{key:\"material-info\"},[_c('router-link',{attrs:{\"to\":\"/material-info\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"19\",\"viewBox\":\"0 0 384 512\"}},[_c('path',{attrs:{\"d\":\"M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM80 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L80 96c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm54.2 253.8c-6.1 20.3-24.8 34.2-46 34.2L80 416c-8.8 0-16-7.2-16-16s7.2-16 16-16l8.2 0c7.1 0 13.3-4.6 15.3-11.4l14.9-49.5c3.4-11.3 13.8-19.1 25.6-19.1s22.2 7.7 25.6 19.1l11.6 38.6c7.4-6.2 16.8-9.7 26.8-9.7c15.9 0 30.4 9 37.5 23.2l4.4 8.8 54.1 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-6.1 0-11.6-3.4-14.3-8.8l-8.8-17.7c-1.7-3.4-5.1-5.5-8.8-5.5s-7.2 2.1-8.8 5.5l-8.8 17.7c-2.9 5.9-9.2 9.4-15.7 8.8s-12.1-5.1-13.9-11.3L144 349l-9.8 32.8z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.materialInfo')))])])],1)],1),_c('a-sub-menu',{key:\"securityTool\",scopedSlots:_vm._u([{key:\"title\",fn:function({ open }){return [_c('span',{staticClass:\"enhanced-title\"},[_vm._v(_vm._s(_vm.$t('sidebar.securityTool')))])]}}])},[_c('a-menu-item',{key:\"ai\"},[_c('router-link',{attrs:{\"to\":\"/aiBash\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 576 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M9.4 86.6C-3.1 74.1-3.1 53.9 9.4 41.4s32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L178.7 256 9.4 86.6zM256 416l288 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-288 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.aiBash')))])])],1),_c('a-menu-item',{key:\"file-upload\"},[_c('router-link',{attrs:{\"to\":\"/upload\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.fileUpload')))])])],1),_c('a-menu-item',{key:\"file-download\"},[_c('router-link',{attrs:{\"to\":\"/download\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.fileDown')))])])],1)],1),_c('a-sub-menu',{key:\"llmAutoTesting\",scopedSlots:_vm._u([{key:\"title\",fn:function({ open }){return [_c('span',{staticClass:\"enhanced-title\"},[_vm._v(_vm._s(_vm.$t('sidebar.llmAutoTesting')))])]}}])},[_c('a-menu-item',{key:\"case-list\"},[_c('router-link',{attrs:{\"to\":\"/testcase\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 448 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.testCases')))])])],1),_c('a-menu-item',{key:\"execute-case\"},[_c('router-link',{attrs:{\"to\":\"/execute-case\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-464 0c-39.8 0-72-32.2-72-72L-64 168c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32l160 0zM208 128c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.executeCase')))])])],1),_c('a-menu-item',{key:\"smart-orchestration\"},[_c('router-link',{attrs:{\"to\":\"/smart-orchestration\"}},[_c('span',{staticClass:\"icon\"},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 448 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"d\":\"M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 64 0 368 0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-128 64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48l0-16z\"}})])]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.smartOrchestration')))])])],1)],1),_c('a-sub-menu',{key:\"aiTaintAnalysis\",scopedSlots:_vm._u([{key:\"title\",fn:function({ open }){return [_c('span',{staticClass:\"enhanced-title\"},[_vm._v(_vm._s(_vm.$t('sidebar.aiTaintAnalysis')))])]}}])})],1)],1),_c('div',{staticClass:\"footer-section\"},[_c('footer-animation',{staticClass:\"sidebar-footer\"})],1)])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-animation-container\"},[_c('div',{ref:\"animal\",staticClass:\"animal\"},[_c('svg',{staticClass:\"animal-svg\",attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 512 512\"}},[_c('path',{attrs:{\"fill\":_vm.currentColor,\"d\":\"M226.5 92.9c14.3 42.9-.3 86.2-32.6 96.8s-70.1-15.6-84.4-58.5s.3-86.2 32.6-96.8s70.1 15.6 84.4 58.5zM100.4 198.6c18.9 32.4 14.3 70.1-10.2 84.1s-59.7-.9-78.5-33.3S-2.7 179.3 21.8 165.3s59.7 .9 78.5 33.3zM69.2 401.2C121.6 259.9 214.7 224 256 224s134.4 35.9 186.8 177.2c3.6 9.7 5.2 20.1 5.2 30.5v1.6c0 25.8-20.9 46.7-46.7 46.7c-11.5 0-22.9-1.4-34-4.2l-88-22c-15.3-3.8-31.3-3.8-46.6 0l-88 22c-11.1 2.8-22.5 4.2-34 4.2C84.9 480 64 459.1 64 433.3v-1.6c0-10.4 1.6-20.8 5.2-30.5zM421.8 282.7c-24.5-14-29.1-51.7-10.2-84.1s54-47.3 78.5-33.3s29.1 51.7 10.2 84.1s-54 47.3-78.5 33.3zM310.1 189.7c-32.3-10.6-46.9-53.9-32.6-96.8s52.1-69.1 84.4-58.5s46.9 53.9 32.6 96.8s-52.1 69.1-84.4 58.5z\"}})])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"footer-animation-container\">\r\n    <div class=\"animal\" ref=\"animal\">\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" class=\"animal-svg\">\r\n        <path :fill=\"currentColor\" d=\"M226.5 92.9c14.3 42.9-.3 86.2-32.6 96.8s-70.1-15.6-84.4-58.5s.3-86.2 32.6-96.8s70.1 15.6 84.4 58.5zM100.4 198.6c18.9 32.4 14.3 70.1-10.2 84.1s-59.7-.9-78.5-33.3S-2.7 179.3 21.8 165.3s59.7 .9 78.5 33.3zM69.2 401.2C121.6 259.9 214.7 224 256 224s134.4 35.9 186.8 177.2c3.6 9.7 5.2 20.1 5.2 30.5v1.6c0 25.8-20.9 46.7-46.7 46.7c-11.5 0-22.9-1.4-34-4.2l-88-22c-15.3-3.8-31.3-3.8-46.6 0l-88 22c-11.1 2.8-22.5 4.2-34 4.2C84.9 480 64 459.1 64 433.3v-1.6c0-10.4 1.6-20.8 5.2-30.5zM421.8 282.7c-24.5-14-29.1-51.7-10.2-84.1s54-47.3 78.5-33.3s29.1 51.7 10.2 84.1s-54 47.3-78.5 33.3zM310.1 189.7c-32.3-10.6-46.9-53.9-32.6-96.8s52.1-69.1 84.4-58.5s46.9 53.9 32.6 96.8s-52.1 69.1-84.4 58.5z\"/>\r\n      </svg>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      position: 0,\r\n      direction: 1, // 1 表示向右, -1 表示向左\r\n      animationFrame: null,\r\n      containerWidth: 0,\r\n      animalWidth: 40,\r\n      speed: 0.6, // 降低一半速度\r\n      jumpHeight: 0,\r\n      isJumping: false,\r\n      jumpDirection: 1, // 1 表示上升, -1 表示下降\r\n      maxJumpHeight: 15,\r\n      jumpSpeed: 0.3, // 降低一半跳跃速度\r\n      jumpProbability: 0.008, // 降低跳跃概率\r\n      colorIndex: 0,\r\n      colors: ['#818080', '#4096ff', '#ff7875', '#52c41a', '#faad14'],\r\n      frameCount: 0,\r\n      frameSkip: 1, // 每隔一帧才更新一次位置，这样可以降低动画速度\r\n      currentColor: '#818080' // 当前颜色，初始为第一个颜色\r\n    };\r\n  },\r\n  mounted() {\r\n    this.containerWidth = this.$el.offsetWidth;\r\n    this.updateAnimalColor(); // 初始化颜色\r\n    this.initAnimation();\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeUnmount() {\r\n    window.removeEventListener('resize', this.handleResize);\r\n    cancelAnimationFrame(this.animationFrame);\r\n  },\r\n  methods: {\r\n    initAnimation() {\r\n      // 初始化动画\r\n      this.position = 0;\r\n      this.animate();\r\n    },\r\n    animate() {\r\n      this.frameCount++;\r\n\r\n      // 每隔指定帧数才更新位置，这样可以降低动画速度\r\n      if (this.frameCount % (this.frameSkip + 1) === 0) {\r\n        // 更新水平位置\r\n        this.position += this.speed * this.direction;\r\n\r\n        // 检查边界并改变方向\r\n        if (this.position > this.containerWidth - this.animalWidth) {\r\n          this.direction = -1;\r\n          if (this.$refs.animal) {\r\n            this.$refs.animal.classList.remove('facing-right');\r\n            this.$refs.animal.classList.add('facing-left');\r\n          }\r\n        } else if (this.position < 0) {\r\n          this.direction = 1;\r\n          if (this.$refs.animal) {\r\n            this.$refs.animal.classList.remove('facing-left');\r\n            this.$refs.animal.classList.add('facing-right');\r\n          }\r\n        }\r\n\r\n        // 随机跳跃\r\n        if (!this.isJumping && Math.random() < this.jumpProbability) {\r\n          this.isJumping = true;\r\n          this.jumpDirection = 1;\r\n          this.jumpHeight = 0;\r\n          // 改变颜色\r\n          this.colorIndex = (this.colorIndex + 1) % this.colors.length;\r\n          this.updateAnimalColor();\r\n        }\r\n\r\n        // 处理跳跃动画\r\n        if (this.isJumping) {\r\n          this.jumpHeight += this.jumpSpeed * this.jumpDirection;\r\n\r\n          if (this.jumpHeight >= this.maxJumpHeight) {\r\n            this.jumpDirection = -1; // 开始下降\r\n          } else if (this.jumpHeight <= 0 && this.jumpDirection === -1) {\r\n            this.isJumping = false;\r\n            this.jumpHeight = 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 应用新位置 - 添加安全检查\r\n      if (this.$refs.animal) {\r\n        this.$refs.animal.style.left = `${this.position}px`;\r\n        this.$refs.animal.style.bottom = `${5 + this.jumpHeight}px`;\r\n      }\r\n\r\n      // 继续动画循环\r\n      this.animationFrame = requestAnimationFrame(this.animate);\r\n    },\r\n    handleResize() {\r\n      this.containerWidth = this.$el.offsetWidth;\r\n      // 如果当前位置超出新的容器宽度，重置位置\r\n      if (this.position > this.containerWidth - this.animalWidth) {\r\n        this.position = this.containerWidth - this.animalWidth;\r\n      }\r\n    },\r\n    updateAnimalColor() {\r\n      // 更新SVG图标的颜色\r\n      this.currentColor = this.colors[this.colorIndex];\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer-animation-container {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 40px;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n}\r\n\r\n.animal {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  width: 30px;\r\n  height: 30px;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.animal-svg {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.facing-right {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.facing-left {\r\n  transform: scaleX(-1);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FooterAnimation.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FooterAnimation.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FooterAnimation.vue?vue&type=template&id=bf170ac4&scoped=true\"\nimport script from \"./FooterAnimation.vue?vue&type=script&lang=js\"\nexport * from \"./FooterAnimation.vue?vue&type=script&lang=js\"\nimport style0 from \"./FooterAnimation.vue?vue&type=style&index=0&id=bf170ac4&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bf170ac4\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n    <!-- Main Sidebar -->\r\n    <a-layout-sider\r\n        v-show=\"!sidebarCollapsed\"\r\n        :trigger=\"null\"\r\n        collapsible\r\n        :collapsed-width=\"0\"\r\n        width=\"220\"\r\n        class=\"sider-primary\"\r\n        :class=\"[\r\n            'ant-layout-sider-' + sidebarColor,\r\n            'ant-layout-sider-' + sidebarTheme\r\n        ]\"\r\n        theme=\"light\"\r\n        :style=\"{ backgroundColor: 'transparent' }\">\r\n        <div class=\"sidebar-container\">\r\n            <!-- Brand Area at Top -->\r\n            <div class=\"brand-section\">\r\n                <div class=\"brand-header\">\r\n                    <div class=\"logo-container\" :class=\"`text-${sidebarColor}`\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"36px\" height=\"36px\" viewBox=\"0 0 48 48\" class=\"ios-logo\">\r\n                        <!-- 背景矩形 -->\r\n                        <rect width=\"48\" height=\"48\" rx=\"10\" fill=\"currentColor\" opacity=\"0.1\"></rect>\r\n                        <!-- 盾牌轮廓-->\r\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2.2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                              d=\"M24 43.5c9.043-3.117 15.488-10.363 16.5-19.589c.28-4.005.256-8.025-.072-12.027a2.54 2.54 0 0 0-2.467-2.366c-4.091-.126-8.846-.808-12.52-4.427a2.05 2.05 0 0 0-2.881 0c-3.675 3.619-8.43 4.301-12.52 4.427a2.54 2.54 0 0 0-2.468 2.366A79.4 79.4 0 0 0 7.5 23.911C8.51 33.137 14.957 40.383 24 43.5z\">\r\n                          <animate attributeName=\"stroke-dasharray\" values=\"0 150;150 0;0 150\" dur=\"5s\" calcMode=\"linear\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        <!-- 盾牌内部装饰线 -->\r\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"0.8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" opacity=\"0.6\"\r\n                              d=\"M24 39c7.5-2.5 12.5-8.5 13.5-16c.2-3 .2-6 0-9\"></path>\r\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"0.8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" opacity=\"0.6\"\r\n                              d=\"M24 39c-7.5-2.5-12.5-8.5-13.5-16c-.2-3-.2-6 0-9\"></path>\r\n                        <!-- 锁图标 -->\r\n                        <circle cx=\"24\" cy=\"20.206\" r=\"4.5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n                        <!-- 锁底部 -->\r\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                              d=\"M31.589 32.093a7.589 7.589 0 1 0-15.178 0\"></path>\r\n\r\n                        <!-- 钥匙孔 -->\r\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\"\r\n                              d=\"M24 20.2v2.5\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div class=\"brand-text-container\">\r\n                        <div class=\"brand-name\">SecTest Copilot</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"brand-divider\"></div>\r\n            </div>\r\n\r\n            <!-- Sidebar Navigation Menu -->\r\n            <div class=\"navigation-section\">\r\n                <a-menu theme=\"light\" mode=\"inline\"\r\n                    :default-open-keys=\"['securityTool', 'llmAutoTesting', 'aiTaintAnalysis']\"\r\n                    :inline-collapsed=\"sidebarCollapsed\"\r\n                    class=\"sidebar-menu\">\r\n\r\n            <!-- Info Collection 分组 -->\r\n            <a-sub-menu key=\"envAwareness\">\r\n                <template slot=\"title\" slot-scope=\"{ open }\">\r\n                    <span class=\"enhanced-title\">{{ $t('sidebar.envAwareness') }}</span>\r\n                </template>\r\n                <a-menu-item key=\"process\">\r\n                    <router-link to=\"/process\">\r\n                        <span class=\"icon\">\r\n                          <svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\r\n                            <path d=\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.processInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <!-- 其余子菜单项保持不变 -->\r\n                <a-menu-item key=\"package\">\r\n                    <router-link to=\"/package\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\">\r\n                            <path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\" clip-rule=\"evenodd\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.packageInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"hardware\">\r\n                    <router-link to=\"/hardware\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 512 512\">\r\n                            <path fill=\"#ffffff\" d=\"M160 160h192v192H160z\"/>\r\n                            <path fill=\"#ffffff\" d=\"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.hardwareInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"filesystem\">\r\n                    <router-link to=\"/filesystem\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\">\r\n                            <path fill=\"#ffffff\" fill-rule=\"evenodd\" d=\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\" clip-rule=\"evenodd\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.filesystemInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"high-port-info\">\r\n                    <router-link to=\"/port\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\" width=\"20\" height=\"20\">\r\n                            <path d=\"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.portInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"docker\">\r\n                    <router-link to=\"/docker\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"20\" height=\"20\" viewBox=\"0 0 48 48\">\r\n                            <path d=\"M 22.5 6 C 22.224 6 22 6.224 22 6.5 L 22 9.5 C 22 9.776 22.224 10 22.5 10 L 25.5 10 C 25.776 10 26 9.776 26 9.5 L 26 6.5 C 26 6.224 25.776 6 25.5 6 L 22.5 6 z M 10.5 12 C 10.224 12 10 12.224 10 12.5 L 10 15.5 C 10 15.776 10.224 16 10.5 16 L 13.5 16 C 13.776 16 14 15.776 14 15.5 L 14 12.5 C 14 12.224 13.776 12 13.5 12 L 10.5 12 z M 16.5 12 C 16.224 12 16 12.224 16 12.5 L 16 15.5 C 16 15.776 16.224 16 16.5 16 L 19.5 16 C 19.776 16 20 15.776 20 15.5 L 20 12.5 C 20 12.224 19.776 12 19.5 12 L 16.5 12 z M 22.5 12 C 22.224 12 22 12.224 22 12.5 L 22 15.5 C 22 15.776 22.224 16 22.5 16 L 25.5 16 C 25.776 16 26 15.776 26 15.5 L 26 12.5 C 26 12.224 25.776 12 25.5 12 L 22.5 12 z M 37.478516 14.300781 L 37.025391 14.951172 C 36.458391 15.825172 36.045734 16.787828 35.802734 17.798828 C 35.343734 19.731828 35.621422 21.546656 36.607422 23.097656 C 35.416422 23.758656 33.386 23.986 33 24 L 2 24 C 0.895 24 0 24.895 0 26 C 0 28 0.43371875 30.924625 1.3867188 33.515625 C 2.4757187 36.359625 4.0970781 38.454328 6.2050781 39.736328 C 8.5670781 41.177328 12.404859 42 16.755859 42 C 18.720859 42.006 20.683234 41.828703 22.615234 41.470703 C 25.301234 40.979703 27.885719 40.045078 30.261719 38.705078 C 32.219719 37.576078 33.981469 36.139172 35.480469 34.451172 C 37.985469 31.627172 39.477891 28.4815 40.587891 25.6875 C 40.592891 25.6845 40.596562 25.683688 40.601562 25.679688 C 43.598562 25.800687 45.412625\r\n                                24.642688 46.390625 23.679688 C 47.008625 23.095688 47.491688 22.38275 47.804688 21.59375 L 48 21.021484 L 47.527344 20.650391 C 47.397344 20.547391 46.182141 19.632812 43.619141 19.632812 C 42.942141 19.635813 42.266609 19.694641 41.599609 19.806641 C 41.103609 16.421641 38.293969 14.769313 38.167969 14.695312 L 37.478516 14.300781 z M 4.5 18 C 4.224 18 4 18.224 4 18.5 L 4 21.5 C 4 21.776 4.224 22 4.5 22 L 7.5 22 C 7.776 22 8 21.776 8 21.5 L 8 18.5 C 8 18.224 7.776 18 7.5 18 L 4.5 18 z M 10.5 18 C 10.224 18 10 18.224 10 18.5 L 10 21.5 C 10 21.776 10.224 22 10.5 22 L 13.5 22 C 13.776 22 14 21.776 14 21.5 L 14 18.5 C 14 18.224 13.776 18 13.5 18 L 10.5 18 z M 16.5 18 C 16.224 18 16 18.224 16 18.5 L 16 21.5 C 16 21.776 16.224 22 16.5 22 L 19.5 22 C 19.776 22 20 21.776 20 21.5 L 20 18.5 C 20 18.224 19.776 18 19.5 18 L 16.5 18 z M 22.5 18 C 22.224 18 22 18.224 22 18.5 L 22 21.5 C 22 21.776 22.224 22 22.5 22 L 25.5 22 C 25.776 22 26 21.776 26 21.5 L 26 18.5 C 26 18.224 25.776 18 25.5 18 L 22.5 18 z M 28.5 18 C 28.224 18 28 18.224 28 18.5 L 28 21.5 C 28 21.776 28.224 22 28.5 22 L 31.5 22 C 31.776 22 32 21.776 32 21.5 L 32 18.5 C 32 18.224 31.776 18 31.5 18 L 28.5 18 z\"></path>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.dockerInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"kubernetes\">\r\n                    <router-link to=\"/kubernetes\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"80\" height=\"80\" viewBox=\"0 0 32 32\">\r\n                            <path fill=\"#ffffff\" d=\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.k8sInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"code-info\">\r\n                    <router-link to=\"/code-info\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 640 512\">\r\n                            <path d=\"M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.codeInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"material-info\">\r\n                    <router-link to=\"/material-info\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"19\" viewBox=\"0 0 384 512\">\r\n                            <path d=\"M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM80 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L80 96c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm54.2 253.8c-6.1 20.3-24.8 34.2-46 34.2L80 416c-8.8 0-16-7.2-16-16s7.2-16 16-16l8.2 0c7.1 0 13.3-4.6 15.3-11.4l14.9-49.5c3.4-11.3 13.8-19.1 25.6-19.1s22.2 7.7 25.6 19.1l11.6 38.6c7.4-6.2 16.8-9.7 26.8-9.7c15.9 0 30.4 9 37.5 23.2l4.4 8.8 54.1 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-6.1 0-11.6-3.4-14.3-8.8l-8.8-17.7c-1.7-3.4-5.1-5.5-8.8-5.5s-7.2 2.1-8.8 5.5l-8.8 17.7c-2.9 5.9-9.2 9.4-15.7 8.8s-12.1-5.1-13.9-11.3L144 349l-9.8 32.8z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.materialInfo') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n            </a-sub-menu>\r\n\r\n            <!-- securityTool分组 -->\r\n            <a-sub-menu key=\"securityTool\">\r\n                <template slot=\"title\" slot-scope=\"{ open }\">\r\n                    <span class=\"enhanced-title\">{{ $t('sidebar.securityTool') }}</span>\r\n                </template>\r\n                <a-menu-item key=\"ai\">\r\n                    <router-link to=\"/aiBash\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M9.4 86.6C-3.1 74.1-3.1 53.9 9.4 41.4s32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L178.7 256 9.4 86.6zM256 416l288 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-288 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.aiBash') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"file-upload\">\r\n                    <router-link to=\"/upload\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.fileUpload') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"file-download\">\r\n                    <router-link to=\"/download\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.fileDown') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n            </a-sub-menu>\r\n\r\n\r\n            <!-- llmAutoTesting 分组 -->\r\n            <a-sub-menu key=\"llmAutoTesting\">\r\n                <template slot=\"title\" slot-scope=\"{ open }\">\r\n                    <span class=\"enhanced-title\">{{ $t('sidebar.llmAutoTesting') }}</span>\r\n                </template>\r\n                <a-menu-item key=\"case-list\">\r\n                    <router-link to=\"/testcase\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.testCases') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"execute-case\">\r\n                    <router-link to=\"/execute-case\">\r\n                        <span class=\"icon\">\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-464 0c-39.8 0-72-32.2-72-72L-64 168c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32l160 0zM208 128c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z\"/>\r\n                          </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.executeCase') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>\r\n                <a-menu-item key=\"smart-orchestration\">\r\n                    <router-link to=\"/smart-orchestration\">\r\n                        <span class=\"icon\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\">\r\n                            <path d=\"M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 64 0 368 0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-128 64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48l0-16z\"/>\r\n                        </svg>\r\n                        </span>\r\n                        <span class=\"label\">{{ $t('sidebar.smartOrchestration') }}</span>\r\n                    </router-link>\r\n                </a-menu-item>                \r\n            </a-sub-menu>\r\n\r\n            <!-- aiTaintAnalysis 分组 -->\r\n            <a-sub-menu key=\"aiTaintAnalysis\">\r\n                <template slot=\"title\" slot-scope=\"{ open }\">\r\n                    <span class=\"enhanced-title\">{{ $t('sidebar.aiTaintAnalysis') }}</span>\r\n                </template>\r\n            </a-sub-menu>\r\n\r\n                </a-menu>\r\n            </div>\r\n\r\n            <!-- 在侧边栏底部添加动画组件 -->\r\n            <div class=\"footer-section\">\r\n                <footer-animation class=\"sidebar-footer\"></footer-animation>\r\n            </div>\r\n        </div>\r\n    </a-layout-sider>\r\n    <!-- / Main Sidebar -->\r\n</template>\r\n\r\n<script>\r\nimport {mapState} from \"vuex\";\r\nimport FooterAnimation from '../Widgets/FooterAnimation.vue';\r\n\r\nexport default {\r\n    components: {\r\n        FooterAnimation\r\n    },\r\n    computed: {\r\n        ...mapState(['selectedNodeIp', 'currentProject']),\r\n    },\r\n    props: {\r\n        // Sidebar collapsed status.\r\n        sidebarCollapsed: {\r\n            type: Boolean,\r\n            default: false,\r\n        },\r\n\r\n        // Main sidebar color.\r\n        sidebarColor: {\r\n            type: String,\r\n            default: \"primary\",\r\n        },\r\n\r\n        // Main sidebar theme : light, white, dark.\r\n        sidebarTheme: {\r\n            type: String,\r\n            default: \"light\",\r\n        },\r\n    },\r\n    data() {\r\n        return {};\r\n    }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.enhanced-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  position: relative;\r\n  padding-left: 2px;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -10px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 3px;\r\n    height: 0;\r\n    border-radius: 3px;\r\n    opacity: 0;\r\n  }\r\n\r\n  &:hover::before {\r\n    height: 60%;\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.ios-logo {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n.logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navigation-section {\r\n  padding-top: 10px;\r\n\r\n  // 添加滚动条美化\r\n  &::-webkit-scrollbar {\r\n    width: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: transparent;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(140, 140, 140, 0.4); /* 使用淡灰色，透明度40% */\r\n    border-radius: 2px; /* 减小圆角半径，与更窄的滚动条匹配 */\r\n\r\n    &:hover {\r\n      background-color: rgba(140, 140, 140, 0.6); /* 悬停时透明度增加到60% */\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardSidebar.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardSidebar.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DashboardSidebar.vue?vue&type=template&id=476db7da&scoped=true\"\nimport script from \"./DashboardSidebar.vue?vue&type=script&lang=js\"\nexport * from \"./DashboardSidebar.vue?vue&type=script&lang=js\"\nimport style0 from \"./DashboardSidebar.vue?vue&type=style&index=0&id=476db7da&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"476db7da\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(_vm.navbarFixed ? 'a-affix' : 'div',{tag:\"component\",attrs:{\"offset-top\":_vm.top}},[_c('a-layout-header',[_c('a-row',{attrs:{\"type\":\"flex\"}},[_c('a-col',{attrs:{\"span\":24,\"md\":12}},[_c('div',{staticClass:\"header-nav-buttons\"},[_c('router-link',{staticClass:\"header-nav-button\",class:[{ 'active': _vm.$route.path === '/' }, _vm.$route.path === '/' ? `bg-${_vm.sidebarColor} nav-btn-transparent` : ''],attrs:{\"to\":\"/\"}},[_c('svg',{staticStyle:{\"margin-right\":\"7px\"},attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 576 512\",\"width\":\"17\",\"height\":\"17\"}},[_c('path',{attrs:{\"d\":\"M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z\",\"fill\":\"currentColor\"}})]),_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('common.home')))])]),(_vm.currentProject)?_c('div',{staticClass:\"project-name-display\"},[_c('span',{staticClass:\"project-name-text\"},[_vm._v(_vm._s(_vm.getDisplayProjectName()))])]):_vm._e(),_c('router-link',{staticClass:\"header-nav-button\",class:[{ 'active': _vm.$route.path.includes('/task') }, _vm.$route.path.includes('/task') ? `bg-${_vm.sidebarColor} nav-btn-transparent` : ''],attrs:{\"to\":\"/task\"}},[_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.taskPanel')))])]),_c('router-link',{staticClass:\"header-nav-button\",class:[{ 'active': _vm.$route.path.includes('/config') }, _vm.$route.path.includes('/config') ? `bg-${_vm.sidebarColor} nav-btn-transparent` : ''],attrs:{\"to\":{ path: '/config', hash: '#host' }}},[_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.hostConfig')))])]),_c('router-link',{staticClass:\"header-nav-button\",class:[{ 'active': _vm.$route.path.includes('/repository') }, _vm.$route.path.includes('/repository') ? `bg-${_vm.sidebarColor} nav-btn-transparent` : ''],attrs:{\"to\":\"/repository\"}},[_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.repositoryConfig')))])]),_c('router-link',{staticClass:\"header-nav-button\",class:[{ 'active': _vm.$route.path.includes('/tools') }, _vm.$route.path.includes('/tools') ? `bg-${_vm.sidebarColor} nav-btn-transparent` : ''],attrs:{\"to\":\"/tools\"}},[_c('span',{staticClass:\"label\"},[_vm._v(_vm._s(_vm.$t('sidebar.toolPanel')))])])],1)]),_c('a-col',{staticClass:\"header-control\",attrs:{\"span\":24,\"md\":12}},[_c('log-viewer'),_c('a-dropdown',{attrs:{\"placement\":\"bottomRight\"}},[_c('a',{staticClass:\"language-switcher\",on:{\"click\":e => e.preventDefault()}},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\"}},[_c('circle',{attrs:{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\"}}),_c('line',{attrs:{\"x1\":\"2\",\"y1\":\"12\",\"x2\":\"22\",\"y2\":\"12\"}}),_c('path',{attrs:{\"d\":\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"}})]),_c('span',{staticClass:\"language-text\"},[_vm._v(_vm._s(_vm.currentLanguageLabel))])]),_c('a-menu',{attrs:{\"slot\":\"overlay\"},slot:\"overlay\"},[_c('a-menu-item',{key:\"en-US\",on:{\"click\":function($event){return _vm.changeLanguage('en-US')}}},[_c('span',{class:{'active-language': _vm.language === 'en-US'}},[_vm._v(\"English\")])]),_c('a-menu-item',{key:\"zh-CN\",on:{\"click\":function($event){return _vm.changeLanguage('zh-CN')}}},[_c('span',{class:{'active-language': _vm.language === 'zh-CN'}},[_vm._v(\"中文\")])])],1)],1),_c('notification-button'),_c('theme-toggle-button'),_c('a-button',{ref:\"secondarySidebarTriggerBtn\",attrs:{\"type\":\"link\"},on:{\"click\":function($event){return _vm.$emit('toggleSettingsDrawer', true)}}},[_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 20 20\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"fill-rule\":\"evenodd\",\"clip-rule\":\"evenodd\",\"d\":\"M11.4892 3.17094C11.1102 1.60969 8.8898 1.60969 8.51078 3.17094C8.26594 4.17949 7.11045 4.65811 6.22416 4.11809C4.85218 3.28212 3.28212 4.85218 4.11809 6.22416C4.65811 7.11045 4.17949 8.26593 3.17094 8.51078C1.60969 8.8898 1.60969 11.1102 3.17094 11.4892C4.17949 11.7341 4.65811 12.8896 4.11809 13.7758C3.28212 15.1478 4.85218 16.7179 6.22417 15.8819C7.11045 15.3419 8.26594 15.8205 8.51078 16.8291C8.8898 18.3903 11.1102 18.3903 11.4892 16.8291C11.7341 15.8205 12.8896 15.3419 13.7758 15.8819C15.1478 16.7179 16.7179 15.1478 15.8819 13.7758C15.3419 12.8896 15.8205 11.7341 16.8291 11.4892C18.3903 11.1102 18.3903 8.8898 16.8291 8.51078C15.8205 8.26593 15.3419 7.11045 15.8819 6.22416C16.7179 4.85218 15.1478 3.28212 13.7758 4.11809C12.8896 4.65811 11.7341 4.17949 11.4892 3.17094ZM10 13C11.6569 13 13 11.6569 13 10C13 8.34315 11.6569 7 10 7C8.34315 7 7 8.34315 7 10C7 11.6569 8.34315 13 10 13Z\",\"fill\":\"#111827\"}})])]),_c('a-button',{staticClass:\"sidebar-toggler\",attrs:{\"type\":\"link\"},on:{\"click\":function($event){_vm.$emit('toggleSidebar', ! _vm.sidebarCollapsed) , _vm.resizeEventHandler()}}},[_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 448 512\"}},[_c('path',{attrs:{\"d\":\"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"}})])]),_c('a-col',{staticClass:\"header-control\",attrs:{\"span\":24,\"md\":6}},[_c('div',{staticClass:\"node-selector\"},[(_vm.currentProject)?[_c('a-dropdown',{attrs:{\"trigger\":['click']},scopedSlots:_vm._u([{key:\"overlay\",fn:function(){return [_c('a-menu',{staticClass:\"node-menu\"},_vm._l((_vm.nodes),function(node){return _c('a-menu-item',{key:node.ip,on:{\"click\":function($event){return _vm.selectNode(node)}}},[_c('div',{staticClass:\"node-menu-item\"},[_c('div',{staticClass:\"ip-address\"},[_vm._v(_vm._s(node.ip))]),_c('div',{staticClass:\"host-name\",attrs:{\"title\":node.host_name}},[_vm._v(_vm._s(node.host_name))])])])}),1)]},proxy:true}],null,false,617815065)},[_c('a',{staticClass:\"ant-dropdown-link node-selector-link\",on:{\"click\":e => e.preventDefault()}},[_c('span',{staticClass:\"node-name\"},[_vm._v(\" \"+_vm._s(_vm.selectedNode ? _vm.selectedNode.ip : _vm.$t('common.selectNode'))+\" \")]),_c('a-icon',{attrs:{\"type\":\"down\"}})],1)])]:[_c('a',{staticClass:\"ant-dropdown-link node-selector-link\",on:{\"click\":_vm.goToProjects}},[_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 20 20\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"d\":\"M13 7H7V5h6v2zm0 4H7V9h6v2zm0 4H7v-2h6v2z\",\"fill\":\"#111827\"}}),_c('path',{attrs:{\"fill-rule\":\"evenodd\",\"clip-rule\":\"evenodd\",\"d\":\"M2 4a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0h12v12H4V4z\",\"fill\":\"#111827\"}})]),_c('span',[_vm._v(_vm._s(_vm.$t('common.selectProject')))])])]],2)])],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-popover',{attrs:{\"trigger\":\"click\",\"placement\":\"bottomRight\",\"overlayClassName\":\"notification-popover\",\"getPopupContainer\":() => _vm.wrapper},on:{\"visibleChange\":_vm.onPopoverVisibleChange},scopedSlots:_vm._u([{key:\"content\",fn:function(){return [_c('div',{staticClass:\"notification-container\"},[_c('div',{staticClass:\"notification-header\"},[_c('span',[_vm._v(_vm._s(_vm.$t('common.notifications')))]),(_vm.notifications.length)?_c('a-button',{attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":_vm.clearAllNotifications}},[_vm._v(\" \"+_vm._s(_vm.$t('common.clearAll'))+\" \")]):_vm._e()],1),_c('div',{staticClass:\"notification-list\"},[_vm._l((_vm.notifications),function(notification){return _c('div',{key:notification.id,class:['notification-item', `notification-${notification.type}`]},[_c('div',{staticClass:\"notification-title\"},[_vm._v(_vm._s(notification.title))]),_c('div',{staticClass:\"notification-message\"},[_vm._v(_vm._s(notification.message))]),_c('div',{staticClass:\"notification-time\"},[_vm._v(_vm._s(notification.time))])])}),(!_vm.notifications.length)?_c('div',{staticClass:\"empty-notification\"},[_c('div',{staticClass:\"empty-message\"},[_vm._v(_vm._s(_vm.$t('common.noNotifications')))])]):_vm._e()],2)])]},proxy:true}]),model:{value:(_vm.notificationVisible),callback:function ($$v) {_vm.notificationVisible=$$v},expression:\"notificationVisible\"}},[_c('a-badge',{attrs:{\"count\":_vm.unreadNotifications,\"showZero\":false,\"overflowCount\":99,\"numberStyle\":{ backgroundColor: '#ff4d4f' }}},[_c('a',{staticClass:\"notification-trigger\",on:{\"click\":_vm.toggleNotifications}},[_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 20 20\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"d\":\"M10 2C6.68632 2 4.00003 4.68629 4.00003 8V11.5858L3.29292 12.2929C3.00692 12.5789 2.92137 13.009 3.07615 13.3827C3.23093 13.7564 3.59557 14 4.00003 14H16C16.4045 14 16.7691 13.7564 16.9239 13.3827C17.0787 13.009 16.9931 12.5789 16.7071 12.2929L16 11.5858V8C16 4.68629 13.3137 2 10 2Z\",\"fill\":\"#111827\"}}),_c('path',{attrs:{\"d\":\"M10 18C8.34315 18 7 16.6569 7 15H13C13 16.6569 11.6569 18 10 18Z\",\"fill\":\"#111827\"}})])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-popover\r\n    trigger=\"click\"\r\n    placement=\"bottomRight\"\r\n    overlayClassName=\"notification-popover\"\r\n    :getPopupContainer=\"() => wrapper\"\r\n    v-model=\"notificationVisible\"\r\n    @visibleChange=\"onPopoverVisibleChange\"\r\n  >\r\n    <template #content>\r\n      <div class=\"notification-container\">\r\n        <div class=\"notification-header\">\r\n          <span>{{ $t('common.notifications') }}</span>\r\n          <a-button type=\"link\" size=\"small\" @click=\"clearAllNotifications\" v-if=\"notifications.length\">\r\n            {{ $t('common.clearAll') }}\r\n          </a-button>\r\n        </div>\r\n        <div class=\"notification-list\">\r\n          <div v-for=\"notification in notifications\" :key=\"notification.id\" :class=\"['notification-item', `notification-${notification.type}`]\">\r\n            <div class=\"notification-title\">{{ notification.title }}</div>\r\n            <div class=\"notification-message\">{{ notification.message }}</div>\r\n            <div class=\"notification-time\">{{ notification.time }}</div>\r\n          </div>\r\n          <div v-if=\"!notifications.length\" class=\"empty-notification\">\r\n            <div class=\"empty-message\">{{ $t('common.noNotifications') }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <a-badge :count=\"unreadNotifications\" :showZero=\"false\" :overflowCount=\"99\" :numberStyle=\"{ backgroundColor: '#ff4d4f' }\">\r\n      <a class=\"notification-trigger\" @click=\"toggleNotifications\">\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M10 2C6.68632 2 4.00003 4.68629 4.00003 8V11.5858L3.29292 12.2929C3.00692 12.5789 2.92137 13.009 3.07615 13.3827C3.23093 13.7564 3.59557 14 4.00003 14H16C16.4045 14 16.7691 13.7564 16.9239 13.3827C17.0787 13.009 16.9931 12.5789 16.7071 12.2929L16 11.5858V8C16 4.68629 13.3137 2 10 2Z\" fill=\"#111827\"/>\r\n          <path d=\"M10 18C8.34315 18 7 16.6569 7 15H13C13 16.6569 11.6569 18 10 18Z\" fill=\"#111827\"/>\r\n        </svg>\r\n      </a>\r\n    </a-badge>\r\n  </a-popover>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters, mapActions } from 'vuex';\r\n\r\nexport default {\r\n  name: 'NotificationButton',\r\n  data() {\r\n    return {\r\n      notificationVisible: false,\r\n      wrapper: document.body,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['notifications']),\r\n    ...mapGetters(['unreadNotifications']),\r\n  },\r\n  methods: {\r\n    ...mapActions(['markNotificationsAsRead', 'clearNotifications']),\r\n\r\n    toggleNotifications(e) {\r\n      // 阻止事件冒泡\r\n      e.stopPropagation();\r\n      // 切换通知弹出框的显示状态\r\n      this.notificationVisible = !this.notificationVisible;\r\n\r\n      // 如果显示通知弹出框，并且有未读通知，则标记为已读\r\n      if (this.notificationVisible && this.unreadNotifications > 0) {\r\n        this.markNotificationsAsRead();\r\n      }\r\n    },\r\n\r\n    onPopoverVisibleChange(visible) {\r\n      // 如果显示通知弹出框，并且有未读通知，则标记为已读\r\n      if (visible && this.unreadNotifications > 0) {\r\n        this.markNotificationsAsRead();\r\n        // 强制更新组件\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n\r\n    clearAllNotifications(e) {\r\n      // 阻止事件冒泡\r\n      e.stopPropagation();\r\n      // 清除所有通知\r\n      this.clearNotifications();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.wrapper = document.getElementById('layout-dashboard');\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 通知样式 */\r\n.notification-trigger {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n}\r\n\r\n/* 通知相关样式 */\r\n/* 数字标记 */\r\n::v-deep .ant-badge-count {\r\n  background-color: #ff4d4f;\r\n  box-shadow: 0 0 0 1px #fff;\r\n  font-size: 12px;\r\n  height: 20px;\r\n  line-height: 20px;\r\n  padding: 0 6px;\r\n  border-radius: 10px;\r\n  min-width: 20px;\r\n  font-weight: normal;\r\n  top: 0;\r\n  right: 0;\r\n}\r\n\r\n/* 弹出框 */\r\n::v-deep .notification-popover {\r\n  width: 320px !important;\r\n  max-width: none !important;\r\n  z-index: 1050 !important;\r\n}\r\n\r\n::v-deep .notification-popover .ant-popover-inner-content {\r\n  padding: 0;\r\n}\r\n\r\n/* 通知容器 */\r\n.notification-container {\r\n  width: 100%;\r\n}\r\n\r\n.notification-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.notification-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  font-weight: 500;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.notification-item {\r\n  padding: 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: default;\r\n}\r\n\r\n.notification-title {\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-message {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-time {\r\n  font-size: 11px;\r\n  color: #999;\r\n  text-align: right;\r\n}\r\n\r\n.notification-success {\r\n  border-left: 3px solid #52c41a;\r\n}\r\n\r\n.notification-error {\r\n  border-left: 3px solid #ff4d4f;\r\n}\r\n\r\n.empty-notification {\r\n  text-align: center;\r\n  padding: 16px;\r\n  color: #999;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NotificationButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NotificationButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./NotificationButton.vue?vue&type=template&id=20888547&scoped=true\"\nimport script from \"./NotificationButton.vue?vue&type=script&lang=js\"\nexport * from \"./NotificationButton.vue?vue&type=script&lang=js\"\nimport style0 from \"./NotificationButton.vue?vue&type=style&index=0&id=20888547&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"20888547\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"log-viewer\"},[_c('a-tooltip',{attrs:{\"title\":_vm.$t('log.viewLogs')}},[_c('a',{staticClass:\"log-button\",on:{\"click\":_vm.showLogModal}},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 448 512\"}},[_c('path',{attrs:{\"d\":\"M160 80c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 352c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-352zM0 272c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 160c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48L0 272zM368 96l32 0c26.5 0 48 21.5 48 48l0 288c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48z\"}})])])]),_c('a-modal',{attrs:{\"title\":_vm.$t('log.title'),\"visible\":_vm.visible,\"width\":1200,\"footer\":null,\"bodyStyle\":{ padding: '0' }},on:{\"cancel\":_vm.handleCancel}},[_c('div',{staticClass:\"log-modal-content\"},[_c('div',{staticClass:\"log-controls\"},[_c('div',{staticClass:\"log-controls-left\"},[_c('span',{staticClass:\"current-node-info\"},[_vm._v(\" \"+_vm._s(_vm.$t('log.currentNode'))+\": \"+_vm._s(_vm.currentNodeInfo)+\" \")]),_c('a-select',{staticStyle:{\"width\":\"120px\",\"margin-left\":\"12px\"},attrs:{\"placeholder\":_vm.$t('log.selectLevel'),\"allow-clear\":\"\"},model:{value:(_vm.selectedLogLevel),callback:function ($$v) {_vm.selectedLogLevel=$$v},expression:\"selectedLogLevel\"}},_vm._l((_vm.logLevelOptions),function(level){return _c('a-select-option',{key:level,attrs:{\"value\":level}},[_c('span',{staticClass:\"log-level-option\",class:`level-${level?.toLowerCase()}`},[_vm._v(\" \"+_vm._s(level)+\" \")])])}),1)],1),_c('div',{staticClass:\"log-controls-right\"},[_c('a-button',{attrs:{\"loading\":_vm.loading,\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.fetchLogs}},[_c('a-icon',{attrs:{\"type\":\"reload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('log.refresh'))+\" \")],1)],1)]),_c('div',{ref:\"logContent\",staticClass:\"log-content\"},[_c('a-spin',{attrs:{\"spinning\":_vm.loading}},[(_vm.logs.length === 0 && !_vm.loading)?_c('div',{staticClass:\"no-logs\"},[_c('a-empty',{attrs:{\"description\":_vm.$t('log.noLogs')}})],1):_c('div',{staticClass:\"log-list\"},_vm._l((_vm.filteredLogs),function(log,index){return _c('div',{key:index,staticClass:\"log-item\",class:_vm.getLogLevelClass(log.log_level)},[_c('div',{staticClass:\"log-header\"},[_c('span',{staticClass:\"log-time\"},[_vm._v(_vm._s(_vm.formatTime(log.timestamp)))]),_c('span',{staticClass:\"log-level\",class:`level-${log.log_level?.toLowerCase()}`},[_vm._v(\" \"+_vm._s(log.log_level)+\" \")]),(log.module)?_c('span',{staticClass:\"log-module\"},[_vm._v(_vm._s(log.module))]):_vm._e()]),_c('div',{staticClass:\"log-message\"},[_vm._v(_vm._s(log.log_content))])])}),0)])],1),_c('div',{staticStyle:{\"text-align\":\"right\",\"margin-top\":\"16px\",\"padding-top\":\"16px\",\"margin-right\":\"16px\",\"margin-bottom\":\"24px\"}},[_c('a-pagination',{attrs:{\"current\":_vm.currentPage,\"total\":_vm.total,\"page-size\":_vm.pageSize,\"size\":\"small\"},on:{\"change\":_vm.handlePageChange}})],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"log-viewer\">\r\n    <!-- Log <PERSON><PERSON> -->\r\n    <a-tooltip :title=\"$t('log.viewLogs')\">\r\n      <a class=\"log-button\" @click=\"showLogModal\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 448 512\">\r\n            <path d=\"M160 80c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 352c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-352zM0 272c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 160c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48L0 272zM368 96l32 0c26.5 0 48 21.5 48 48l0 288c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48z\"/>\r\n        </svg>\r\n      </a>\r\n    </a-tooltip>\r\n\r\n    <!-- Log <PERSON> -->\r\n    <a-modal\r\n      :title=\"$t('log.title')\"\r\n      :visible=\"visible\"\r\n      @cancel=\"handleCancel\"\r\n      :width=\"1200\"\r\n      :footer=\"null\"\r\n      :bodyStyle=\"{ padding: '0' }\"\r\n    >\r\n      <div class=\"log-modal-content\">\r\n        <!-- Header Controls -->\r\n        <div class=\"log-controls\">\r\n          <div class=\"log-controls-left\">\r\n            <span class=\"current-node-info\">\r\n              {{ $t('log.currentNode') }}: {{ currentNodeInfo }}\r\n            </span>\r\n            <a-select \r\n              v-model=\"selectedLogLevel\" \r\n              style=\"width: 120px; margin-left: 12px;\" \r\n              :placeholder=\"$t('log.selectLevel')\"\r\n              allow-clear\r\n            >\r\n              <a-select-option v-for=\"level in logLevelOptions\" :key=\"level\" :value=\"level\">\r\n                <span :class=\"`level-${level?.toLowerCase()}`\" class=\"log-level-option\">\r\n                  {{ level }}\r\n                </span>\r\n              </a-select-option>\r\n            </a-select>\r\n          </div>\r\n          <div class=\"log-controls-right\">\r\n            <a-button @click=\"fetchLogs\" :loading=\"loading\" type=\"primary\" size=\"small\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('log.refresh') }}\r\n            </a-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Log Content -->\r\n        <div class=\"log-content\" ref=\"logContent\">\r\n          <a-spin :spinning=\"loading\">\r\n            <div v-if=\"logs.length === 0 && !loading\" class=\"no-logs\">\r\n              <a-empty :description=\"$t('log.noLogs')\" />\r\n            </div>\r\n            <div v-else class=\"log-list\">\r\n              <div \r\n                v-for=\"(log, index) in filteredLogs\" \r\n                :key=\"index\" \r\n                class=\"log-item\"\r\n                :class=\"getLogLevelClass(log.log_level)\"\r\n              >\r\n                <div class=\"log-header\">\r\n                  <span class=\"log-time\">{{ formatTime(log.timestamp) }}</span>\r\n                  <span class=\"log-level\" :class=\"`level-${log.log_level?.toLowerCase()}`\">\r\n                    {{ log.log_level }}\r\n                  </span>\r\n                  <span v-if=\"log.module\" class=\"log-module\">{{ log.module }}</span>\r\n                </div>\r\n                <div class=\"log-message\">{{ log.log_content }}</div>\r\n              </div>\r\n            </div>\r\n          </a-spin>\r\n        </div>\r\n        \r\n                  <!-- Pagination -->\r\n          <div style=\"text-align: right; margin-top: 16px; padding-top: 16px; margin-right: 16px; margin-bottom: 24px;\">\r\n            <a-pagination \r\n              :current=\"currentPage\" \r\n              :total=\"total\" \r\n              :page-size=\"pageSize\"\r\n              @change=\"handlePageChange\"\r\n              size=\"small\"\r\n            />\r\n          </div>\r\n      </div>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'LogViewer',\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      logs: [],\r\n      pageSize: 100, // 固定每页显示100条\r\n      currentPage: 1, // 当前页数\r\n      total: 0, // 总数据量\r\n      selectedLogLevel: null, // 筛选的日志级别\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['nodes', 'selectedNodeIp', 'currentProject']),\r\n    \r\n    // 当前节点信息显示\r\n    currentNodeInfo() {\r\n      if (!this.selectedNodeIp) {\r\n        return this.$t('log.noNodeSelected');\r\n      }\r\n      const currentNode = this.nodes.find(node => node.ip === this.selectedNodeIp);\r\n      return currentNode ? `${currentNode.ip}` : this.selectedNodeIp;\r\n    },\r\n    \r\n    // 筛选后的日志列表（分页已在后端处理，这里只处理级别筛选）\r\n    filteredLogs() {\r\n      if (!this.selectedLogLevel) {\r\n        return this.logs;\r\n      }\r\n      return this.logs.filter(log => log.log_level === this.selectedLogLevel);\r\n    },\r\n    \r\n    // 可用的日志级别选项\r\n    logLevelOptions() {\r\n      const levels = new Set(this.logs.map(log => log.log_level).filter(Boolean));\r\n      return Array.from(levels).sort();\r\n    }\r\n  },\r\n  methods: {\r\n    showLogModal() {\r\n      this.visible = true;\r\n      // 重置分页状态\r\n      this.currentPage = 1;\r\n      this.selectedLogLevel = null;\r\n      if (this.selectedNodeIp) {\r\n        this.fetchLogs();\r\n      }\r\n    },\r\n    \r\n    handleCancel() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    async fetchLogs() {\r\n      if (!this.selectedNodeIp) {\r\n        this.$message.warning(this.$t('log.noNodeSelected'));\r\n        return;\r\n      }\r\n      \r\n      if (!this.currentProject) {\r\n        this.$message.warning(this.$t('common.selectProjectFirst'));\r\n        return;\r\n      }\r\n      \r\n      this.loading = true;\r\n      try {\r\n        const response = await axios.get(`/api/agent_log/${this.selectedNodeIp}`, {\r\n          params: {\r\n            page: this.currentPage,\r\n            page_size: this.pageSize,\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        \r\n        // 假设后端返回分页数据格式: { data: [], total: number }\r\n        if (response.data && typeof response.data === 'object' && response.data.data) {\r\n          this.logs = response.data.data || [];\r\n          this.total = response.data.total || 0;\r\n        } else {\r\n          // 兼容旧格式，如果后端还没改为分页格式\r\n          this.logs = (response.data || []).sort((a, b) => {\r\n            const timeA = new Date(a.timestamp).getTime();\r\n            const timeB = new Date(b.timestamp).getTime();\r\n            return timeA - timeB;\r\n          });\r\n          this.total = this.logs.length;\r\n        }\r\n        \r\n        // 重置级别筛选\r\n        this.selectedLogLevel = null;\r\n        \r\n        // 滚动到顶部显示当前页日志\r\n        this.$nextTick(() => {\r\n          const logContent = this.$refs.logContent;\r\n          if (logContent) {\r\n            logContent.scrollTop = 0;\r\n          }\r\n        });\r\n        \r\n      } catch (error) {\r\n        console.error('Failed to fetch logs:', error);\r\n        this.$message.error(this.$t('log.fetchError'));\r\n        this.logs = [];\r\n        this.total = 0;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 处理页码变化\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchLogs();\r\n    },\r\n    \r\n    formatTime(timestamp) {\r\n      if (!timestamp) return '';\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleString();\r\n    },\r\n    \r\n    getLogLevelClass(level) {\r\n      if (!level) return '';\r\n      return `log-level-${level.toLowerCase()}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.log-button {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n}\r\n\r\n.log-modal-content {\r\n  height: 900px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.log-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid var(--border-color, #f0f0f0);\r\n  background: var(--modal-bg, #fafafa);\r\n}\r\n\r\n.log-controls-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.current-node-info {\r\n  font-weight: 500;\r\n  color: var(--text-color, #333);\r\n  margin-right: 16px;\r\n}\r\n\r\n.log-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 16px 16px 0 16px;\r\n  background: var(--modal-bg, #fff);\r\n}\r\n\r\n\r\n\r\n.no-logs {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n}\r\n\r\n.log-list {\r\n  font-family: monospace;\r\n  font-size: 13px;\r\n  line-height: 2.0;\r\n}\r\n\r\n.log-item {\r\n  padding: 4px 0;\r\n  color: var(--text-color, #333);\r\n  border-bottom: 1px solid var(--border-color, transparent);\r\n}\r\n\r\n.log-item:hover {\r\n  background-color: var(--hover-bg, rgba(0,0,0,0.02));\r\n}\r\n\r\n.log-header {\r\n  display: inline;\r\n}\r\n\r\n.log-time {\r\n  color: var(--input-text, #666);\r\n  margin-right: 8px;\r\n}\r\n\r\n.log-level {\r\n  margin-right: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.log-module {\r\n  color: var(--input-text, #666);\r\n  margin-right: 8px;\r\n}\r\n\r\n/* 简化的日志级别颜色 */\r\n.level-info {\r\n  color: #1890ff;\r\n}\r\n\r\n.level-warn {\r\n  color: #fa8c16;\r\n}\r\n\r\n.level-error {\r\n  color: #f5222d;\r\n}\r\n\r\n.level-debug {\r\n  color: #52c41a;\r\n}\r\n\r\n.level-warning {\r\n  color: #fa8c16;\r\n}\r\n\r\n.level-critical {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.log-message {\r\n  display: inline;\r\n  word-break: break-word;\r\n}\r\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LogViewer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LogViewer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./LogViewer.vue?vue&type=template&id=2731ff6c&scoped=true\"\nimport script from \"./LogViewer.vue?vue&type=script&lang=js\"\nexport * from \"./LogViewer.vue?vue&type=script&lang=js\"\nimport style0 from \"./LogViewer.vue?vue&type=style&index=0&id=2731ff6c&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2731ff6c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-tooltip',{attrs:{\"title\":_vm.isDarkMode ? _vm.$t('common.lightMode') : _vm.$t('common.darkMode')}},[_c('a',{staticClass:\"theme-toggle-button\",on:{\"click\":_vm.toggleTheme}},[(_vm.isDarkMode)?_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"d\":\"M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z\",\"fill\":\"#111827\"}}),_c('path',{attrs:{\"d\":\"M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22\",\"stroke\":\"#111827\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\"}})]):_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"d\":\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\",\"fill\":\"#111827\",\"stroke\":\"#111827\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\"}})])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-tooltip :title=\"isDarkMode ? $t('common.lightMode') : $t('common.darkMode')\">\r\n    <a class=\"theme-toggle-button\" @click=\"toggleTheme\">\r\n      <svg v-if=\"isDarkMode\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <!-- 太阳图标 -->\r\n        <path d=\"M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z\" fill=\"#111827\"/>\r\n        <path d=\"M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22\" stroke=\"#111827\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n      </svg>\r\n      <svg v-else width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <!-- 月亮图标 -->\r\n        <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" fill=\"#111827\" stroke=\"#111827\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n      </svg>\r\n    </a>\r\n  </a-tooltip>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\n\r\nexport default {\r\n  name: 'ThemeToggleButton',\r\n  computed: {\r\n    ...mapState(['darkMode']),\r\n    isDarkMode() {\r\n      return this.darkMode;\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['toggleDarkMode']),\r\n    toggleTheme() {\r\n      this.toggleDarkMode();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.theme-toggle-button {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.theme-toggle-button:hover {\r\n  transform: rotate(30deg);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ThemeToggleButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ThemeToggleButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ThemeToggleButton.vue?vue&type=template&id=bffad3ac&scoped=true\"\nimport script from \"./ThemeToggleButton.vue?vue&type=script&lang=js\"\nexport * from \"./ThemeToggleButton.vue?vue&type=script&lang=js\"\nimport style0 from \"./ThemeToggleButton.vue?vue&type=style&index=0&id=bffad3ac&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bffad3ac\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<!-- Layout Header's Conditionally Fixed Wrapper -->\r\n\t<component :is=\"navbarFixed ? 'a-affix' : 'div'\" :offset-top=\"top\">\r\n\t\t<!-- Layout Header -->\r\n\t\t<a-layout-header>\r\n\t\t\t<a-row type=\"flex\">\r\n\t\t\t\t<!-- Header Breadcrumbs & Title Column -->\r\n\t\t\t\t<a-col :span=\"24\" :md=\"12\">\r\n\t\t\t\t\t\t<!-- Header Navigation Buttons -->\r\n\t\t\t\t\t\t<div class=\"header-nav-buttons\">\r\n\r\n\t\t\t\t\t\t\t<!-- Home Button -->\r\n\t\t\t\t\t\t\t<router-link to=\"/\" class=\"header-nav-button\" :class=\"[{ 'active': $route.path === '/' }, $route.path === '/' ? `bg-${sidebarColor} nav-btn-transparent` : '']\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\" width=\"17\" height=\"17\" style=\"margin-right: 7px\">\r\n                                    <path d=\"M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z\" fill=\"currentColor\"/>\r\n                                </svg>\r\n                                <span class=\"label\">{{ $t('common.home') }}</span>\r\n\t\t\t\t\t\t\t</router-link>\r\n              <!-- Project Name Display (when project is selected) -->\r\n\t\t\t\t\t\t\t<div v-if=\"currentProject\" class=\"project-name-display\">\r\n\t\t\t\t\t\t\t\t<span class=\"project-name-text\">{{ getDisplayProjectName() }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<!-- Task Panel Button -->\r\n\t\t\t\t\t\t\t<router-link to=\"/task\" class=\"header-nav-button\" :class=\"[{ 'active': $route.path.includes('/task') }, $route.path.includes('/task') ? `bg-${sidebarColor} nav-btn-transparent` : '']\">\r\n\t\t\t\t\t\t\t\t<span class=\"label\">{{ $t('sidebar.taskPanel') }}</span>\r\n\t\t\t\t\t\t\t</router-link>\r\n\r\n\t\t\t\t\t\t\t<!-- Configuration Button -->\r\n\t\t\t\t\t\t\t<router-link :to=\"{ path: '/config', hash: '#host' }\" class=\"header-nav-button\" :class=\"[{ 'active': $route.path.includes('/config') }, $route.path.includes('/config') ? `bg-${sidebarColor} nav-btn-transparent` : '']\">\r\n\t\t\t\t\t\t\t\t<span class=\"label\">{{ $t('sidebar.hostConfig') }}</span>\r\n\t\t\t\t\t\t\t</router-link>\r\n\r\n\t\t\t\t\t\t\t<!-- Repository Configuration Button -->\r\n\t\t\t\t\t\t\t<router-link to=\"/repository\" class=\"header-nav-button\" :class=\"[{ 'active': $route.path.includes('/repository') }, $route.path.includes('/repository') ? `bg-${sidebarColor} nav-btn-transparent` : '']\">\r\n\t\t\t\t\t\t\t\t<span class=\"label\">{{ $t('sidebar.repositoryConfig') }}</span>\r\n\t\t\t\t\t\t\t</router-link>\r\n\r\n\t\t\t\t\t\t\t<!-- Tools Button -->\r\n\t\t\t\t\t\t\t<router-link to=\"/tools\" class=\"header-nav-button\" :class=\"[{ 'active': $route.path.includes('/tools') }, $route.path.includes('/tools') ? `bg-${sidebarColor} nav-btn-transparent` : '']\">\r\n\t\t\t\t\t\t\t\t<span class=\"label\">{{ $t('sidebar.toolPanel') }}</span>\r\n\t\t\t\t\t\t\t</router-link>\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t</a-col>\r\n\t\t\t\t<!-- / Header Breadcrumbs & Title Column -->\r\n\t\t\t\t<!-- Header Control Column -->\r\n\t\t\t\t<a-col :span=\"24\" :md=\"12\" class=\"header-control\">\r\n\t\t\t\t\t<!-- Language Switcher -->\r\n          <log-viewer />\r\n\t\t\t\t\t<a-dropdown placement=\"bottomRight\">\r\n\t\t\t\t\t\t<a class=\"language-switcher\" @click=\"e => e.preventDefault()\">\r\n\t\t\t\t\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n\t\t\t\t\t\t\t\t<circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n\t\t\t\t\t\t\t\t<line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"></line>\r\n\t\t\t\t\t\t\t\t<path d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"></path>\r\n\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t<span class=\"language-text\">{{ currentLanguageLabel }}</span>\r\n\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t<a-menu slot=\"overlay\">\r\n\t\t\t\t\t\t\t<a-menu-item key=\"en-US\" @click=\"changeLanguage('en-US')\">\r\n\t\t\t\t\t\t\t\t<span :class=\"{'active-language': language === 'en-US'}\">English</span>\r\n\t\t\t\t\t\t\t</a-menu-item>\r\n\t\t\t\t\t\t\t<a-menu-item key=\"zh-CN\" @click=\"changeLanguage('zh-CN')\">\r\n\t\t\t\t\t\t\t\t<span :class=\"{'active-language': language === 'zh-CN'}\">中文</span>\r\n\t\t\t\t\t\t\t</a-menu-item>\r\n\t\t\t\t\t\t</a-menu>\r\n\t\t\t\t\t</a-dropdown>\r\n\r\n\t\t\t\t\t<!-- Header Control Buttons -->\r\n\t\t\t\t\t<notification-button />\r\n\t\t\t\t\t<theme-toggle-button />\r\n\t\t\t\t\t<a-button type=\"link\" ref=\"secondarySidebarTriggerBtn\" @click=\"$emit('toggleSettingsDrawer', true)\">\r\n\t\t\t\t\t\t<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n\t\t\t\t\t\t\t<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.4892 3.17094C11.1102 1.60969 8.8898 1.60969 8.51078 3.17094C8.26594 4.17949 7.11045 4.65811 6.22416 4.11809C4.85218 3.28212 3.28212 4.85218 4.11809 6.22416C4.65811 7.11045 4.17949 8.26593 3.17094 8.51078C1.60969 8.8898 1.60969 11.1102 3.17094 11.4892C4.17949 11.7341 4.65811 12.8896 4.11809 13.7758C3.28212 15.1478 4.85218 16.7179 6.22417 15.8819C7.11045 15.3419 8.26594 15.8205 8.51078 16.8291C8.8898 18.3903 11.1102 18.3903 11.4892 16.8291C11.7341 15.8205 12.8896 15.3419 13.7758 15.8819C15.1478 16.7179 16.7179 15.1478 15.8819 13.7758C15.3419 12.8896 15.8205 11.7341 16.8291 11.4892C18.3903 11.1102 18.3903 8.8898 16.8291 8.51078C15.8205 8.26593 15.3419 7.11045 15.8819 6.22416C16.7179 4.85218 15.1478 3.28212 13.7758 4.11809C12.8896 4.65811 11.7341 4.17949 11.4892 3.17094ZM10 13C11.6569 13 13 11.6569 13 10C13 8.34315 11.6569 7 10 7C8.34315 7 7 8.34315 7 10C7 11.6569 8.34315 13 10 13Z\" fill=\"#111827\"/>\r\n\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t</a-button>\r\n\t\t\t\t\t<a-button type=\"link\" class=\"sidebar-toggler\" @click=\"$emit('toggleSidebar', ! sidebarCollapsed) , resizeEventHandler()\">\r\n\t\t\t\t\t\t<svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\"><path d=\"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"/></svg>\r\n\t\t\t\t\t</a-button>\r\n\t\t\t\t\t<!-- / Header Control Buttons -->\r\n\r\n\t\t\t\t\t<!-- Header Search Input -->\r\n          <a-col :span=\"24\" :md=\"6\" class=\"header-control\">\r\n            <div class=\"node-selector\">\r\n              <template v-if=\"currentProject\">\r\n\r\n                <a-dropdown :trigger=\"['click']\">\r\n                  <a class=\"ant-dropdown-link node-selector-link\" @click=\"e => e.preventDefault()\">\r\n\r\n                    <span class=\"node-name\">\r\n                      {{ selectedNode ? selectedNode.ip : $t('common.selectNode') }}\r\n                    </span>\r\n                    <a-icon type=\"down\" />\r\n                  </a>\r\n                  <template #overlay>\r\n                    <a-menu class=\"node-menu\">\r\n                      <a-menu-item v-for=\"node in nodes\" :key=\"node.ip\" @click=\"selectNode(node)\">\r\n                        <div class=\"node-menu-item\">\r\n                          <div class=\"ip-address\">{{ node.ip }}</div>\r\n                          <div class=\"host-name\" :title=\"node.host_name\">{{ node.host_name }}</div>\r\n                        </div>\r\n                      </a-menu-item>\r\n                    </a-menu>\r\n                  </template>\r\n                </a-dropdown>\r\n\r\n              </template>\r\n              <template v-else>\r\n                <a class=\"ant-dropdown-link node-selector-link\" @click=\"goToProjects\">\r\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M13 7H7V5h6v2zm0 4H7V9h6v2zm0 4H7v-2h6v2z\" fill=\"#111827\"/>\r\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 4a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0h12v12H4V4z\" fill=\"#111827\"/>\r\n                  </svg>\r\n                  <span>{{ $t('common.selectProject') }}</span>\r\n                </a>\r\n              </template>\r\n            </div>\r\n          </a-col>\r\n\t\t\t\t</a-col>\r\n\t\t\t\t<!-- / Header Control Column -->\r\n\t\t\t</a-row>\r\n\t\t</a-layout-header>\r\n\t\t<!--  /Layout Header -->\r\n\t</component>\r\n\t<!-- / Main Sidebar -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapMutations, mapActions } from 'vuex';\r\nimport NotificationButton from '@/components/Widgets/NotificationButton.vue';\r\nimport LogViewer from '@/components/Widgets/LogViewer.vue';\r\nimport ThemeToggleButton from '@/components/Widgets/ThemeToggleButton.vue';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n    components: {\r\n        NotificationButton,\r\n        LogViewer,\r\n        ThemeToggleButton,\r\n    },\r\n    props: {\r\n        navbarFixed: { type: Boolean, default: false },\r\n        sidebarCollapsed: { type: Boolean, default: false },\r\n        sidebarColor: { type: String, default: 'primary' },\r\n    },\r\n    data() {\r\n        return {\r\n            top: 0,\r\n            searchLoading: false,\r\n            wrapper: document.body,\r\n            selectedNode: null,\r\n        };\r\n    },\r\n    computed: {\r\n        ...mapState(['nodes', 'currentProject', 'currentProjectName', 'selectedNodeIp', 'language']),\r\n        currentLanguageLabel() {\r\n            return this.language === 'zh-CN' ? '中文' : 'English';\r\n        }\r\n    },\r\n    methods: {\r\n        ...mapMutations(['setSelectedNodeIp']),\r\n        ...mapActions(['updateLanguage']),\r\n\r\n        changeLanguage(lang) {\r\n            this.updateLanguage(lang);\r\n            this.$i18n.locale = lang;\r\n        },\r\n        selectNode(node) {\r\n            this.selectedNode = node;\r\n            this.setSelectedNodeIp(node.ip);\r\n            this.goToProcessInfo(node.ip);\r\n        },\r\n        goToProcessInfo() {\r\n            const targetRoute = '/task';\r\n            if (this.$route.path !== targetRoute) {\r\n                this.$router.push(targetRoute).catch(err => {\r\n                    if (err.name !== 'NavigationDuplicated') {\r\n                        console.error(err);\r\n                    }\r\n                });\r\n            }\r\n        },\r\n        goToProjects() {\r\n            this.$router.push('/projects');\r\n        },\r\n        updateSelectedNode() {\r\n            if (this.selectedNodeIp && this.nodes?.length) {\r\n                this.selectedNode = this.nodes.find(node => node.ip === this.selectedNodeIp);\r\n            } else if (this.nodes?.length) {\r\n                // If no node is selected but nodes are available, select the first one\r\n                this.selectNode(this.nodes[0]);\r\n            } else {\r\n                this.selectedNode = null;\r\n            }\r\n        },\r\n        async initializeNodesData() {\r\n            if (this.currentProject) {\r\n                await this.$store.dispatch('fetchNodes');\r\n            }\r\n        },\r\n        // 获取项目显示名称\r\n        getDisplayProjectName() {\r\n            if (this.currentProjectName) {\r\n                return this.currentProjectName;\r\n            }\r\n            return '';\r\n        },\r\n    },\r\n    watch: {\r\n        currentProject: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal) {\r\n                    this.initializeNodesData();\r\n                } else {\r\n                    // Clear nodes when no project is selected\r\n                    this.$store.commit('setNodes', []);\r\n                    this.selectedNode = null;\r\n                    this.setSelectedNodeIp(null);\r\n                }\r\n            }\r\n        },\r\n        nodes: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal?.length) {\r\n                    this.updateSelectedNode();\r\n                } else {\r\n                    this.selectedNode = null;\r\n                    this.setSelectedNodeIp(null);\r\n                }\r\n            }\r\n        }\r\n    },\r\n    mounted() {\r\n        this.wrapper = document.getElementById('layout-dashboard');\r\n        if (this.currentProject) {\r\n            this.initializeNodesData();\r\n        }\r\n    },\r\n    activated() {\r\n        if (this.currentProject) {\r\n            this.initializeNodesData();\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardHeader.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardHeader.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DashboardHeader.vue?vue&type=template&id=5c0deba0\"\nimport script from \"./DashboardHeader.vue?vue&type=script&lang=js\"\nexport * from \"./DashboardHeader.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-layout-footer',[_c('a-row',{attrs:{\"type\":\"flex\"}},[_c('a-col',{attrs:{\"span\":24,\"md\":12}},[_c('p',{staticClass:\"copyright\"},[_vm._v(\" © 2025, made with \"),_c('svg',{attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 20 20\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\"}},[_c('path',{attrs:{\"fill-rule\":\"evenodd\",\"clip-rule\":\"evenodd\",\"d\":\"M3.17157 5.17157C4.73367 3.60948 7.26633 3.60948 8.82843 5.17157L10 6.34315L11.1716 5.17157C12.7337 3.60948 15.2663 3.60948 16.8284 5.17157C18.3905 6.73367 18.3905 9.26633 16.8284 10.8284L10 17.6569L3.17157 10.8284C1.60948 9.26633 1.60948 6.73367 3.17157 5.17157Z\",\"fill\":\"#111827\"}})])])])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<a-layout-footer>\r\n\t\t<a-row type=\"flex\">\r\n\t\t\t<a-col :span=\"24\" :md=\"12\">\r\n\r\n\t\t\t\t<p class=\"copyright\">\r\n\t\t\t\t\t© 2025, made with\r\n\t\t\t\t\t<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n\t\t\t\t\t\t<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3.17157 5.17157C4.73367 3.60948 7.26633 3.60948 8.82843 5.17157L10 6.34315L11.1716 5.17157C12.7337 3.60948 15.2663 3.60948 16.8284 5.17157C18.3905 6.73367 18.3905 9.26633 16.8284 10.8284L10 17.6569L3.17157 10.8284C1.60948 9.26633 1.60948 6.73367 3.17157 5.17157Z\" fill=\"#111827\"/>\r\n\t\t\t\t\t</svg>\r\n\t\t\t\t</p>\r\n\r\n\t\t\t</a-col>\r\n\t\t</a-row>\r\n\t</a-layout-footer>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\n\texport default ({\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t}\r\n\t\t},\r\n\t})\r\n\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardFooter.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardFooter.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DashboardFooter.vue?vue&type=template&id=b64e1ede\"\nimport script from \"./DashboardFooter.vue?vue&type=script&lang=js\"\nexport * from \"./DashboardFooter.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-drawer',{staticClass:\"settings-drawer\",class:[ _vm.rtl ? 'settings-drawer-rtl' : '' ],attrs:{\"placement\":_vm.rtl ? 'left' : 'right',\"closable\":false,\"visible\":_vm.showSettingsDrawer,\"width\":\"360\",\"getContainer\":() => _vm.wrapper},on:{\"close\":function($event){return _vm.$emit('toggleSettingsDrawer', false)}}},[_c('a-button',{staticClass:\"btn-close\",attrs:{\"type\":\"link\"},on:{\"click\":function($event){return _vm.$emit('toggleSettingsDrawer', false)}}},[_c('svg',{attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"9\",\"height\":\"9\",\"viewBox\":\"0 0 9 9\"}},[_c('g',{attrs:{\"id\":\"close\",\"transform\":\"translate(0.75 0.75)\"}},[_c('path',{attrs:{\"id\":\"Path\",\"d\":\"M7.5,0,0,7.5\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"stroke-miterlimit\":\"10\",\"stroke-width\":\"1.5\"}}),_c('path',{attrs:{\"id\":\"Path-2\",\"data-name\":\"Path\",\"d\":\"M0,0,7.5,7.5\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",\"stroke-miterlimit\":\"10\",\"stroke-width\":\"1.5\"}})])])]),_c('div',{staticClass:\"drawer-content\"},[_c('h6',[_vm._v(_vm._s(_vm.$t('configuratorset.configurator')))]),_c('hr'),_c('div',{staticClass:\"sidebar-color\"},[_c('h6',[_vm._v(_vm._s(_vm.$t('configuratorset.sidebarColor')))]),_c('a-radio-group',{attrs:{\"defaultValue\":\"primary\"},on:{\"change\":_vm.handleSidebarColorChange},model:{value:(_vm.sidebarColorModel),callback:function ($$v) {_vm.sidebarColorModel=$$v},expression:\"sidebarColorModel\"}},[_c('a-radio-button',{staticClass:\"bg-primary\",attrs:{\"value\":\"primary\"}}),_c('a-radio-button',{staticClass:\"bg-purple\",attrs:{\"value\":\"purple\"}}),_c('a-radio-button',{staticClass:\"bg-green\",attrs:{\"value\":\"green\"}}),_c('a-radio-button',{staticClass:\"bg-gray\",attrs:{\"value\":\"gray\"}})],1)],1),_c('div',{staticClass:\"sidenav-type\"},[_c('h6',[_vm._v(_vm._s(_vm.$t('configuratorset.sidenavType')))]),_c('p',[_vm._v(\"Choose between 2 different sidenav types.\")]),_c('a-radio-group',{attrs:{\"button-style\":\"solid\",\"defaultValue\":\"primary\"},on:{\"change\":function($event){return _vm.$emit('updateSidebarTheme', $event.target.value)}},model:{value:(_vm.sidebarThemeModel),callback:function ($$v) {_vm.sidebarThemeModel=$$v},expression:\"sidebarThemeModel\"}},[_c('a-radio-button',{attrs:{\"value\":\"light\"}},[_vm._v(\"TRANSPARENT\")]),_c('a-radio-button',{attrs:{\"value\":\"white\"}},[_vm._v(\"WHITE\")])],1)],1),_c('div',{staticClass:\"navbar-fixed\"},[_c('h6',[_vm._v(_vm._s(_vm.$t('configuratorset.navbarFixed')))]),_c('a-switch',{attrs:{\"default-checked\":\"\"},on:{\"change\":function($event){return _vm.$emit('toggleNavbarPosition', _vm.navbarFixedModel)}},model:{value:(_vm.navbarFixedModel),callback:function ($$v) {_vm.navbarFixedModel=$$v},expression:\"navbarFixedModel\"}})],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\r\n\t<!-- Settings Drawer -->\r\n\t<a-drawer\r\n\t\tclass=\"settings-drawer\"\r\n\t\t:class=\"[ rtl ? 'settings-drawer-rtl' : '' ]\"\r\n\t\t:placement=\"rtl ? 'left' : 'right'\"\r\n\t\t:closable=\"false\"\r\n\t\t:visible=\"showSettingsDrawer\"\r\n\t\twidth=\"360\"\r\n\t\t:getContainer=\"() => wrapper\"\r\n\t\t@close=\"$emit('toggleSettingsDrawer', false)\"\r\n\t>\r\n\r\n\t\t<!-- Settings Drawer Close Button -->\r\n\t\t<a-button type=\"link\" class=\"btn-close\" @click=\"$emit('toggleSettingsDrawer', false)\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"9\" height=\"9\" viewBox=\"0 0 9 9\">\r\n\t\t\t\t<g id=\"close\" transform=\"translate(0.75 0.75)\">\r\n\t\t\t\t\t<path id=\"Path\" d=\"M7.5,0,0,7.5\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" stroke-width=\"1.5\"/>\r\n\t\t\t\t\t<path id=\"Path-2\" data-name=\"Path\" d=\"M0,0,7.5,7.5\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" stroke-width=\"1.5\"/>\r\n\t\t\t\t</g>\r\n\t\t\t</svg>\r\n\t\t</a-button>\r\n\r\n\t\t<!-- Settings Drawer Content -->\r\n\t\t<div class=\"drawer-content\">\r\n\t\t\t<h6>{{ $t('configuratorset.configurator') }}</h6>\r\n\t\t\t<hr>\r\n\t\t\t<div class=\"sidebar-color\">\r\n\t\t\t\t<h6>{{ $t('configuratorset.sidebarColor') }}</h6>\r\n\t\t\t\t<a-radio-group v-model=\"sidebarColorModel\" @change=\"handleSidebarColorChange\" defaultValue=\"primary\">\r\n\t\t\t\t\t<a-radio-button value=\"primary\" class=\"bg-primary\"></a-radio-button>\r\n\t\t\t\t\t<a-radio-button value=\"purple\" class=\"bg-purple\"></a-radio-button>\r\n\t\t\t\t\t<a-radio-button value=\"green\" class=\"bg-green\"></a-radio-button>\r\n\t\t\t\t\t<a-radio-button value=\"gray\" class=\"bg-gray\"></a-radio-button>\r\n\t\t\t\t</a-radio-group>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"sidenav-type\">\r\n\t\t\t\t<h6>{{ $t('configuratorset.sidenavType') }}</h6>\r\n\t\t\t\t<p>Choose between 2 different sidenav types.</p>\r\n\t\t\t\t<a-radio-group button-style=\"solid\" v-model=\"sidebarThemeModel\" @change=\"$emit('updateSidebarTheme', $event.target.value)\" defaultValue=\"primary\">\r\n\t\t\t\t\t<a-radio-button value=\"light\">TRANSPARENT</a-radio-button>\r\n\t\t\t\t\t<a-radio-button value=\"white\">WHITE</a-radio-button>\r\n\t\t\t\t</a-radio-group>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"navbar-fixed\">\r\n\t\t\t\t<h6>{{ $t('configuratorset.navbarFixed') }}</h6>\r\n\t\t\t\t<a-switch default-checked v-model=\"navbarFixedModel\" @change=\"$emit('toggleNavbarPosition', navbarFixedModel)\" />\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t</a-drawer>\r\n\t<!-- / Settings Drawer -->\r\n\r\n</template>\r\n\r\n<script>\r\n\timport 'vue-github-buttons/dist/vue-github-buttons.css'; // Stylesheet\r\n\timport VueGitHubButtons from 'vue-github-buttons';\r\n\timport Vue from 'vue';\r\n\timport { mapState, mapActions } from 'vuex';\r\n\tVue.use(VueGitHubButtons, { useCache: true });\r\n\r\n\texport default ({\r\n\t\tprops: {\r\n\t\t\t// Settings drawer visiblility status.\r\n\t\t\tshowSettingsDrawer: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\r\n\t\t\t// Main sidebar color.\r\n\t\t\tsidebarColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"primary\",\r\n\t\t\t},\r\n\r\n\t\t\t// Main sidebar theme : light, white, dark.\r\n\t\t\tsidebarTheme: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"light\",\r\n\t\t\t},\r\n\r\n\t\t\t// Header fixed status.\r\n\t\t\tnavbarFixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// The wrapper element to attach dropdowns to.\r\n\t\t\t\twrapper: document.body,\r\n\r\n\t\t\t\t// Main sidebar color.\r\n\t\t\t\tsidebarColorModel: this.sidebarColor,\r\n\r\n\t\t\t\t// Main sidebar theme : light, white, dark.\r\n\t\t\t\tsidebarThemeModel: this.sidebarTheme,\r\n\r\n\t\t\t\t// Header fixed status.\r\n\t\t\t\tnavbarFixedModel: this.navbarFixed,\r\n\r\n\t\t\t\trtl: false, // 或者根据需要设置为 true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 移除重复的sidebarColor定义\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['updateSidebarColor']),\r\n\t\t\thandleSidebarColorChange(event) {\r\n\t\t\t\tconst color = event.target.value;\r\n\t\t\t\tthis.updateSidebarColor(color);\r\n\t\t\t\tthis.$emit('updateSidebarColor', color);\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.sidebarColorModel = this.sidebarColor;\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tsidebarColor(newColor) {\r\n\t\t\t\tthis.sidebarColorModel = newColor;\r\n\t\t\t},\r\n\t\t\t'$store.state.sidebarColor'(newColor) {\r\n\t\t\t\tthis.sidebarColorModel = newColor;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted: function(){\r\n\t\t\t// Set the wrapper to the proper element, layout wrapper.\r\n\t\t\tthis.wrapper = document.getElementById('layout-dashboard') ;\r\n\t\t},\r\n\t})\r\n\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardSettingsDrawer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardSettingsDrawer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DashboardSettingsDrawer.vue?vue&type=template&id=10f3f2b3\"\nimport script from \"./DashboardSettingsDrawer.vue?vue&type=script&lang=js\"\nexport * from \"./DashboardSettingsDrawer.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\r\n\t\t<!-- Dashboard Layout -->\r\n\t\t<a-layout class=\"layout-dashboard\" id=\"layout-dashboard\" :class=\"[navbarFixed ? 'navbar-fixed' : '', ! sidebarCollapsed ? 'has-sidebar' : '', layoutClass]\">\r\n\r\n\t\t\t<!-- Main Sidebar -->\r\n\t\t\t<DashboardSidebar\r\n\t\t\t\t:sidebarCollapsed=\"sidebarCollapsed\"\r\n\t\t\t\t:sidebarColor=\"sidebarColor\"\r\n\t\t\t\t:sidebarTheme=\"sidebarTheme\"\r\n\t\t\t\t@toggleSidebar=\"toggleSidebar\"\r\n\t\t\t></DashboardSidebar>\r\n\t\t\t<!-- / Main Sidebar -->\r\n\r\n\t\t\t<!-- Layout Content -->\r\n\t\t\t<a-layout>\r\n\r\n\t\t\t\t<!-- Layout Header's Conditionally Fixed Wrapper -->\r\n\t\t\t\t<DashboardHeader\r\n\t\t\t\t\t:sidebarCollapsed=\"sidebarCollapsed\"\r\n\t\t\t\t\t:navbarFixed=\"navbarFixed\"\r\n\t\t\t\t\t:sidebarColor=\"sidebarColor\"\r\n\t\t\t\t\t@toggleSettingsDrawer=\"toggleSettingsDrawer\"\r\n\t\t\t\t\t@toggleSidebar=\"toggleSidebar\"\r\n\t\t\t\t></DashboardHeader>\r\n\t\t\t\t<!-- / Layout Header's Conditionally Fixed Wrapper -->\r\n\r\n\t\t\t\t<!-- Page Content -->\r\n\t\t\t\t<a-layout-content>\r\n\t\t\t\t\t<router-view />\r\n\t\t\t\t</a-layout-content>\r\n\t\t\t\t<!-- / Page Content -->\r\n\r\n\t\t\t\t<!-- Layout Footer -->\r\n\t\t\t\t<DashboardFooter></DashboardFooter>\r\n\t\t\t\t<!-- / Layout Footer -->\r\n\r\n\r\n\t\t\t\t<!-- Floating Action Button For Toggling Settings Drawer -->\r\n<!--    \t\t\t<a-button class=\"fab\" shape=\"circle\" @click=\"showSettingsDrawer = true\">-->\r\n<!--\t\t\t\t\t<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">-->\r\n<!--\t\t\t\t\t\t<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.4892 3.17094C11.1102 1.60969 8.8898 1.60969 8.51078 3.17094C8.26594 4.17949 7.11045 4.65811 6.22416 4.11809C4.85218 3.28212 3.28212 4.85218 4.11809 6.22416C4.65811 7.11045 4.17949 8.26593 3.17094 8.51078C1.60969 8.8898 1.60969 11.1102 3.17094 11.4892C4.17949 11.7341 4.65811 12.8896 4.11809 13.7758C3.28212 15.1478 4.85218 16.7179 6.22417 15.8819C7.11045 15.3419 8.26594 15.8205 8.51078 16.8291C8.8898 18.3903 11.1102 18.3903 11.4892 16.8291C11.7341 15.8205 12.8896 15.3419 13.7758 15.8819C15.1478 16.7179 16.7179 15.1478 15.8819 13.7758C15.3419 12.8896 15.8205 11.7341 16.8291 11.4892C18.3903 11.1102 18.3903 8.8898 16.8291 8.51078C15.8205 8.26593 15.3419 7.11045 15.8819 6.22416C16.7179 4.85218 15.1478 3.28212 13.7758 4.11809C12.8896 4.65811 11.7341 4.17949 11.4892 3.17094ZM10 13C11.6569 13 13 11.6569 13 10C13 8.34315 11.6569 7 10 7C8.34315 7 7 8.34315 7 10C7 11.6569 8.34315 13 10 13Z\" fill=\"#111827\"/>-->\r\n<!--\t\t\t\t\t</svg>-->\r\n<!--\t\t\t\t  </a-button>-->\r\n\t\t\t\t<!-- / Floating Action Button For Toggling Settings Drawer -->\r\n\r\n\t\t\t\t<!-- Sidebar Overlay -->\r\n\t\t\t\t<div class=\"sidebar-overlay\" @click=\"sidebarCollapsed = true\" v-show=\"! sidebarCollapsed\"></div>\r\n\t\t\t\t<!-- / Sidebar Overlay -->\r\n\r\n\t\t\t</a-layout>\r\n\t\t\t<!-- / Layout Content -->\r\n\r\n\t\t\t<!-- Settings Drawer -->\r\n\t\t\t<DashboardSettingsDrawer\r\n\t\t\t\t:showSettingsDrawer=\"showSettingsDrawer\"\r\n\t\t\t\t:navbarFixed=\"navbarFixed\"\r\n\t\t\t\t:sidebarTheme=\"sidebarTheme\"\r\n\t\t\t\t@toggleSettingsDrawer=\"toggleSettingsDrawer\"\r\n\t\t\t\t@toggleNavbarPosition=\"toggleNavbarPosition\"\r\n\t\t\t\t@updateSidebarTheme=\"updateSidebarTheme\"\r\n\t\t\t\t@updateSidebarColor=\"updateSidebarColor\"\r\n\t\t\t></DashboardSettingsDrawer>\r\n\t\t\t<!-- / Settings Drawer -->\r\n\r\n\t\t</a-layout>\r\n\t\t<!-- / Dashboard Layout -->\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\r\n\timport DashboardSidebar from '../components/Sidebars/DashboardSidebar' ;\r\n\timport DashboardHeader from '../components/Headers/DashboardHeader' ;\r\n\timport DashboardFooter from '../components/Footers/DashboardFooter' ;\r\n\timport DashboardSettingsDrawer from '../components/Sidebars/DashboardSettingsDrawer' ;\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: {\r\n    DashboardSidebar,\r\n    DashboardHeader,\r\n    DashboardFooter,\r\n    DashboardSettingsDrawer\r\n  },\r\n  data() {\r\n    return {\r\n      sidebarCollapsed: false,\r\n      showSettingsDrawer: false,\r\n      navbarFixed: true,\r\n      sidebarTheme: 'light',\r\n      layoutClass: ''\r\n    }\r\n  },\r\n  computed: {\r\n    sidebarColor() {\r\n      return this.$store.state.sidebarColor;\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    toggleSettingsDrawer() {\r\n      this.showSettingsDrawer = !this.showSettingsDrawer;\r\n    },\r\n    toggleNavbarPosition() {\r\n      this.navbarFixed = !this.navbarFixed;\r\n    },\r\n    updateSidebarTheme(theme) {\r\n      this.sidebarTheme = theme;\r\n    },\r\n    updateSidebarColor(color) {\r\n      this.$store.dispatch('updateSidebarColor', color);\r\n    }\r\n  }\r\n}\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Dashboard.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Dashboard.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Dashboard.vue?vue&type=template&id=25c0112e\"\nimport script from \"./Dashboard.vue?vue&type=script&lang=js\"\nexport * from \"./Dashboard.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export default {\r\n  common: {\r\n    home: 'Home',\r\n    title: 'SecureTest Copilot',\r\n    selectNode: 'Select Node',\r\n    selectProject: 'Select Project',\r\n    settings: 'Settings',\r\n    notifications: 'Notifications',\r\n    clearAll: 'Clear All',\r\n    noNotifications: 'No Notifications',\r\n    language: 'Language',\r\n    configureNodes: 'Node Configuration',\r\n    configureProxy: 'Proxy Configuration',\r\n    detectReachableIps: 'Detect Reachable IPs',\r\n    taskProgress: 'Task Progress',\r\n    refresh: 'Refresh',\r\n    darkMode: 'Dark Mode',\r\n    lightMode: 'Light Mode',\r\n    selectedNodes: 'Selected {count} nodes',\r\n    copiedToClipboard: 'Copied to clipboard',\r\n    copyFailed: 'Copy failed',\r\n    clear: 'Clear'\r\n  },\r\n  configuratorset:{\r\n    sidebarColor: 'Sidebar Color',\r\n    sidenavType: 'Sidenav Type',\r\n    navbarFixed: 'Navbar Fixed',\r\n    configurator: 'Configurator',\r\n  },\r\n  headTopic:{\r\n    process: 'Process Table',\r\n    package: 'Package Table',\r\n    hardware: 'Hardware Table',\r\n    mount: 'Mount Table',\r\n    port: 'Network Ports & Sockets',\r\n    docker: 'Docker Table',\r\n    k8s: 'Kubernetes Table',\r\n    fileUpload: 'File Upload',\r\n    fileDownload: 'File Download',\r\n    aiBash: 'Command Line',\r\n    testcase: 'Test Case Management',\r\n  },\r\n  tool: {\r\n    configureTool: 'Configure Tool',\r\n    spiderTool: 'SSP',\r\n    generalTool: 'GeneralTool',\r\n    uploadToolPackage: 'Upload Tool Package (zip)',\r\n    selectToolPackage: 'upload',\r\n    editScript: 'Edit script',\r\n    start: 'start',\r\n    editShellScript: 'Edit Shell',\r\n    confirmScript: 'confirm',\r\n    scriptReady: 'Script Ready',\r\n    localSaveDirectory: 'Local Save Directory',\r\n    viewResult: 'View Result',\r\n    selectReachableIp: 'Select a reachable IP',\r\n    columns: {\r\n      hostName: 'Host Name',\r\n      ip: 'IP',\r\n      status: 'Status',\r\n      progress: 'Progress',\r\n      result: 'Result',\r\n      errorDetails: 'Error Details',\r\n      speed: 'Speed',\r\n      fileSize: 'File Size'\r\n    },\r\n    status: {\r\n      failed: 'Failed'\r\n    }\r\n  },\r\n  sidebar: {\r\n    taskPanel: 'Task Panel',\r\n    envAwareness: 'Env Awareness',\r\n    processInfo: 'Process Info',\r\n    packageInfo: 'Package Info',\r\n    hardwareInfo: 'Hardware Info',\r\n    filesystemInfo: 'Filesys Info',\r\n    portInfo: 'Port Info',\r\n    dockerInfo: 'Docker Info',\r\n    k8sInfo: 'K8S Info',\r\n    codeInfo: '代码信息',\r\n    materialInfo: '资料信息',\r\n    securityTool: 'AI Security Tool',\r\n    fileUpload: 'File Upload',\r\n    fileDown: 'File Down',\r\n    aiBash: 'AI Bash',\r\n    testCases: 'Test Cases',\r\n    llmAutoTesting: 'LLM Auto Testing',\r\n    aiTaintAnalysis: 'AI Taint Analysis',\r\n    smartOrchestration: '预留',\r\n    toolPanel: 'Tool Panel',\r\n    hostConfig: 'Host Config',\r\n    cbhConfig: 'CBH Config',\r\n    repositoryConfig: 'Repo Config',\r\n    executeCase: 'Execute Case'\r\n  },\r\n  fileUpload: {\r\n    selectFile: 'Select File',\r\n    clickToSelect: 'Select File',\r\n    uploadPath: 'Upload Path',\r\n    enterUploadPath: 'Enter Upload Directory',\r\n    startUpload: 'Start Upload',\r\n    uploadProgress: 'Upload Progress',\r\n    uploadResults: 'Upload Results'\r\n  },\r\n  fileDownload: {\r\n    enterDownloadPath: 'Enter remote file path',\r\n    startDownload: 'Start Download',\r\n    downloadProgress: 'Download Progress',\r\n  },\r\n  hostConfig: {\r\n    title: 'Host Configuration',\r\n    addHost: 'Add Host',\r\n    exportSelected: 'Export Selected',\r\n    deleteSelected: 'Delete Selected',\r\n    downloadTemplate: 'Download Tpl',\r\n    uploadTemplate: 'Upload Tpl',\r\n    actions: 'Actions',\r\n    save: 'Save',\r\n    edit: 'Edit',\r\n    copy: 'Copy',\r\n    cancel: 'Cancel',\r\n    delete: 'Delete',\r\n    columns: {\r\n      hostName: 'Host Name',\r\n      ipAddress: 'IP Address',\r\n      sshPort: 'SSH Port',\r\n      loginUser: 'Login User',\r\n      loginPassword: 'Login Password',\r\n      switchRootCmd: 'Switch root cmd',\r\n      switchRootPwd: 'Switch root pwd'\r\n    },\r\n  },\r\n  repositoryConfig: {\r\n    title: 'Repository Configuration',\r\n    addRepository: 'Add Repository',\r\n    exportSelected: 'Export Selected',\r\n    deleteSelected: 'Delete Selected',\r\n    downloadSelected: 'Download Selected',\r\n    downloadTemplate: 'Download Tpl',\r\n    uploadTemplate: 'Upload Tpl',\r\n    selectDownloadPath: 'Select Download Path',\r\n    downloadProgress: 'Download Progress',\r\n    actions: 'Actions',\r\n    save: 'Save',\r\n    edit: 'Edit',\r\n    copy: 'Copy',\r\n    cancel: 'Cancel',\r\n    delete: 'Delete',\r\n    columns: {\r\n      microservice: 'Microservice',\r\n      repositoryUrl: 'Repository URL',\r\n      branchName: 'Branch Name'\r\n    },\r\n    validation: {\r\n      invalidUrl: 'Invalid repository URL',\r\n      unsupportedFormat: 'Unsupported repository format',\r\n      missingBranch: 'Missing branch name',\r\n      parseError: 'Parse failed'\r\n    },\r\n    download: {\r\n      selectPath: 'Please select download path',\r\n      downloading: 'Downloading...',\r\n      starting: 'Starting download...',\r\n      success: 'Download successful',\r\n      failed: 'Download failed',\r\n      partialSuccess: 'Partial download successful',\r\n      cloneError: 'Git clone failed'\r\n    }\r\n  },\r\n  repositoryDownload: {\r\n    title: 'Repository Download Results',\r\n    clear: 'Clear',\r\n    total: 'Total',\r\n    success: 'Success',\r\n    failed: 'Failed',\r\n    downloading: 'Downloading',\r\n    pending: 'Pending',\r\n    completed: 'Completed',\r\n    progress: 'Progress'\r\n  },\r\n  log: {\r\n    title: 'Log Viewer',\r\n    viewLogs: 'View Logs',\r\n    currentNode: 'Node',\r\n    noNodeSelected: 'No node selected',\r\n    selectLevel: 'Select Level',\r\n    refresh: 'Refresh',\r\n    noLogs: 'No logs available',\r\n    fetchError: 'Failed to fetch logs',\r\n  },\r\n  testcase: {\r\n    title: 'Test Case',\r\n    management: 'Case Management',\r\n    detail: 'Case Details',\r\n    searchButton: 'Search',\r\n    resetButton: 'Reset',\r\n    clearResults: 'Clear Results',\r\n  },\r\n  caseColumn: {\r\n    number: 'Case Number',\r\n    name: 'Case Name',\r\n    level: 'Case Level',\r\n    similarity: 'Similarity',\r\n    prepareCondition: 'Precondition',\r\n    testSteps: 'Test Steps',\r\n    expectedResult: 'Expected Result',\r\n  },\r\n  smartOrchestration: {\r\n    title: 'Smart Orchestration',\r\n    smartAnalysis: 'Smart Test Case Analysis',\r\n    startAnalysis: 'Start Analysis',\r\n    caseAnalysis: 'Case Analysis',\r\n    naturalLanguageQuery: 'Natural Language Query',\r\n    queryPlaceholder: 'Please enter natural language query',\r\n    inputRequired: 'Please enter a query',\r\n    searchFailed: 'Search failed',\r\n    searchError: 'Search error',\r\n    resultsCleared: 'Search results cleared',\r\n    topK: 'Top K',\r\n    scoreThreshold: 'Score Threshold',\r\n    searchResults: 'Search Results',\r\n    foundResults: 'Found {count} results',\r\n  },\r\n};", "export default {\r\n  common: {\r\n    home: '首页',\r\n    title: '安全测试助手',\r\n    selectNode: '选择节点',\r\n    selectProject: '选择项目',\r\n    settings: '设置',\r\n    notifications: '通知',\r\n    clearAll: '清除全部',\r\n    noNotifications: '暂无通知',\r\n    language: '语言',\r\n    configureNodes: '节点配置',\r\n    configureProxy: '代理配置',\r\n    detectReachableIps: '检测可用IP',\r\n    taskProgress: '任务进度',\r\n    refresh: '刷新',\r\n    darkMode: '夜间模式',\r\n    lightMode: '日间模式',\r\n    selectedNodes: \"已选择 {count} 个节点\",\r\n    copiedToClipboard: '已复制到剪贴板',\r\n    copyFailed: '复制失败',\r\n    clear: '清除'\r\n  },\r\n  configuratorset:{\r\n    sidebarColor: '侧边栏颜色',\r\n    sidenavType: '侧边栏类型',\r\n    navbarFixed: '导航栏固定',\r\n    configurator: '配置器',\r\n  },\r\n  headTopic:{\r\n    process: '进程列表',\r\n    package: '安装包列表',\r\n    hardware: '硬件列表',\r\n    mount: '挂载列表',\r\n    port: '端口列表',\r\n    docker: '容器列表',\r\n    k8s: '集群列表',\r\n    fileUpload: '文件上传',\r\n    fileDownload: '文件下载',\r\n    aiBash: '命令行',\r\n    testcase: '用例管理',\r\n  },\r\n  tool: {\r\n    configureTool: '工具配置',\r\n    spiderTool: 'SSP工具',\r\n    generalTool: '通用工具',\r\n    uploadToolPackage: '上传工具包 (zip)',\r\n    selectToolPackage: '选择包',\r\n    editScript: '编辑脚本',\r\n    start: '启动',\r\n    editShellScript: '编辑Shell',\r\n    confirmScript: '确认',\r\n    scriptReady: '脚本已就绪',\r\n    localSaveDirectory: '本地保存目录',\r\n    viewResult: '查看结果',\r\n    selectReachableIp: '选择可访问的IP',\r\n    columns: {\r\n      hostName: '主机名',\r\n      ip: 'IP地址',\r\n      status: '状态',\r\n      progress: '进度',\r\n      result: '结果',\r\n      errorDetails: '错误详情',\r\n      speed: '速度',\r\n      fileSize: '文件大小'\r\n    },\r\n    status: {\r\n      failed: '失败'\r\n    }\r\n  },\r\n  sidebar: {\r\n    taskPanel: '任务面板',\r\n    envAwareness: '环境感知',\r\n    processInfo: '进程信息',\r\n    packageInfo: '安装包信息',\r\n    hardwareInfo: '硬件信息',\r\n    filesystemInfo: '文件系统信息',\r\n    portInfo: '端口信息',\r\n    dockerInfo: '容器信息',\r\n    k8sInfo: '集群信息',\r\n    codeInfo: '代码信息',\r\n    materialInfo: '资料信息',\r\n    securityTool: 'AI安全工具',\r\n    fileUpload: '文件上传',\r\n    fileDown: '文件下载',\r\n    aiBash: 'AI Bash',\r\n    testCases: '测试用例',\r\n    llmAutoTesting: 'LLM自动化测试',\r\n    aiTaintAnalysis: '智能污点分析',\r\n    smartOrchestration: '预留',\r\n    toolPanel: '工具面板',\r\n    hostConfig: '主机配置',\r\n    cbhConfig: '堡垒机配置',\r\n    repositoryConfig: '代码仓配置',\r\n    executeCase: '用例执行',\r\n  },\r\n  fileUpload: {\r\n    selectFile: '选择文件',\r\n    clickToSelect: '选择文件',\r\n    uploadPath: '上传目录路径',\r\n    enterUploadPath: '请输入上传目录',\r\n    startUpload: '开始上传',\r\n    uploadProgress: '上传进度',\r\n    uploadResults: '上传结果'\r\n  },\r\n  fileDownload: {\r\n    enterDownloadPath: '请输入下载路径',\r\n    startDownload: '开始下载',\r\n    downloadProgress: '下载进度',\r\n  },\r\n  hostConfig: {\r\n    title: '主机配置',\r\n    addHost: '添加主机',\r\n    exportSelected: '导出选中',\r\n    deleteSelected: '删除选中',\r\n    downloadTemplate: '下载模板',\r\n    uploadTemplate: '上传模板',\r\n    actions: '操作',\r\n    save: '保存',\r\n    edit: '编辑',\r\n    copy: '复制',\r\n    cancel: '取消',\r\n    delete: '删除',\r\n    columns: {\r\n      hostName: '主机名',\r\n      ipAddress: 'IP地址',\r\n      sshPort: 'SSH端口',\r\n      loginUser: '登录用户',\r\n      loginPassword: '登录密码',\r\n      switchRootCmd: 'root切换命令',\r\n      switchRootPwd: 'root切换密码'\r\n    },\r\n  },\r\n  repositoryConfig: {\r\n    title: '代码仓配置',\r\n    addRepository: '添加代码仓',\r\n    exportSelected: '导出选中',\r\n    deleteSelected: '删除选中',\r\n    downloadSelected: '下载选中',\r\n    downloadTemplate: '下载模板',\r\n    uploadTemplate: '上传模板',\r\n    selectDownloadPath: '选择下载路径',\r\n    downloadProgress: '下载进度',\r\n    actions: '操作',\r\n    save: '保存',\r\n    edit: '编辑',\r\n    copy: '复制',\r\n    cancel: '取消',\r\n    delete: '删除',\r\n    columns: {\r\n      microservice: '微服务',\r\n      repositoryUrl: '仓库地址',\r\n      branchName: '送检代码分支'\r\n    },\r\n    validation: {\r\n      invalidUrl: '无效的代码仓地址',\r\n      unsupportedFormat: '不支持的代码仓格式',\r\n      missingBranch: '缺少分支名称',\r\n      parseError: '解析失败'\r\n    },\r\n    download: {\r\n      selectPath: '请选择下载路径',\r\n      downloading: '正在下载...',\r\n      starting: '正在启动下载...',\r\n      success: '下载成功',\r\n      failed: '下载失败',\r\n      partialSuccess: '部分下载成功',\r\n      cloneError: 'Git克隆失败'\r\n    }\r\n  },\r\n  repositoryDownload: {\r\n    title: '代码仓下载结果',\r\n    clear: '清除',\r\n    total: '总计',\r\n    success: '成功',\r\n    failed: '失败',\r\n    downloading: '下载中',\r\n    pending: '等待中',\r\n    completed: '已完成',\r\n    progress: '进度'\r\n  },\r\n  log: {\r\n    title: '日志查看器',\r\n    viewLogs: '查看日志',\r\n    currentNode: '节点',\r\n    noNodeSelected: '未选择节点',\r\n    selectLevel: '选择级别',\r\n    refresh: '刷新',\r\n    noLogs: '暂无日志',\r\n    fetchError: '获取日志失败',\r\n  },\r\n  testcase: {\r\n    title: '测试用例',\r\n    management: '用例管理',\r\n    detail: '用例详情',\r\n    selectLevel: '选择级别',\r\n    searchButton: '搜索',\r\n    resetButton: '重置',\r\n  },\r\n  caseColumn: {\r\n    number: '用例编号',\r\n    name: '用例名称',\r\n    level: '用例级别',\r\n    similarity: '相似度',\r\n    prepareCondition: '前置条件',\r\n    testSteps: '测试步骤',\r\n    expectedResult: '预期结果',\r\n  },\r\n  smartOrchestration: {\r\n    title: '智能编排',\r\n    smartAnalysis: '智能测试用例分析',\r\n    startAnalysis: '开始智能分析',\r\n    caseAnalysis: '用例分析',\r\n    naturalLanguageQuery: '自然语言查询',\r\n    queryPlaceholder: '请输入自然语言查询',\r\n    inputRequired: '请输入查询',\r\n    searchFailed: '搜索失败',\r\n    searchError: '搜索错误',\r\n    resultsCleared: '搜索结果清除',\r\n    topK: 'Top K',\r\n    scoreThreshold: '得分阈值',\r\n    searchResults: '搜索结果',\r\n    foundResults: '找到 {count} 个结果',\r\n  },\r\n};", "import Vue from 'vue';\r\nimport VueI18n from 'vue-i18n';\r\nimport enUS from './locales/en-US';\r\nimport zhCN from './locales/zh-CN';\r\n\r\nVue.use(VueI18n);\r\n\r\nconst messages = {\r\n  'en-US': enUS,\r\n  'zh-CN': zhCN\r\n};\r\n\r\n// 从localStorage获取语言设置，如果没有则使用浏览器默认语言或英文\r\nconst getDefaultLanguage = () => {\r\n  const savedLanguage = localStorage.getItem('language');\r\n  if (savedLanguage && messages[savedLanguage]) {\r\n    return savedLanguage;\r\n  }\r\n  \r\n  // 检测浏览器语言\r\n  const browserLang = navigator.language || navigator.userLanguage;\r\n  const lang = browserLang.startsWith('zh') ? 'zh-CN' : 'en-US';\r\n  \r\n  return lang;\r\n};\r\n\r\nconst i18n = new VueI18n({\r\n  locale: getDefaultLanguage(),\r\n  fallbackLocale: 'en-US',\r\n  messages,\r\n  silentTranslationWarn: true\r\n});\r\n\r\nexport default i18n;\r\n", "import Vue from 'vue';\r\nimport App from './App.vue';\r\nimport router from './router';\r\nimport store from './store';\r\nimport Antd from 'ant-design-vue';\r\nimport 'ant-design-vue/dist/antd.css';\r\nimport axios from './api/axiosInstance';\r\nimport SimpleLayout from './layouts/Simple.vue'\r\nimport DashboardLayout from './layouts/Dashboard.vue'\r\nimport 'xterm/css/xterm.css';\r\nimport i18n from './i18n';\r\nimport VueJsonPretty from 'vue-json-pretty'\r\nimport 'vue-json-pretty/lib/styles.css'\r\n\r\n// 确保默认使用暗黑模式\r\nif (localStorage.getItem('darkMode') === null) {\r\n  localStorage.setItem('darkMode', 'true');\r\n}\r\n\r\nimport './scss/app.scss';\r\n\r\nVue.use(Antd);\r\n\r\nVue.config.productionTip = false\r\n\r\n\r\nVue.component(\"layout-simple\", SimpleLayout);\r\nVue.component(\"layout-dashboard\", DashboardLayout);\r\nVue.component(\"VueJsonPretty\", VueJsonPretty);\r\n\r\nVue.prototype.$axios = axios;\r\n\r\nnew Vue({\r\n  store,\r\n  router,\r\n  i18n,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DashboardSidebar.vue?vue&type=style&index=0&id=476db7da&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DifyChatBot.vue?vue&type=style&index=0&id=67100c59&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FooterAnimation.vue?vue&type=style&index=0&id=bf170ac4&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ThemeToggleButton.vue?vue&type=style&index=0&id=bffad3ac&prod&scoped=true&lang=css\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProjectManager.vue?vue&type=style&index=0&id=368921a4&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LogViewer.vue?vue&type=style&index=0&id=2731ff6c&prod&scoped=true&lang=css\"", "import axios from 'axios';\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.VUE_APP_BASE_URL || 'http://127.0.0.1:9998',\r\n  timeout: 36000,\r\n});\r\n\r\nexport default axiosInstance;\r\n"], "sourceRoot": ""}