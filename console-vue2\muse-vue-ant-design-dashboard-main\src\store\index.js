// store/index.js
import Vue from 'vue';
import Vuex from 'vuex';
import axios from '@/api/axiosInstance';

Vue.use(Vuex);

// 进程列表状态模块
const processListModule = {
    namespaced: true,
    state: {
        currentPage: 1,
        scrollPosition: 0,
        lastViewedPid: null // 添加最后查看的进程ID
    },
    mutations: {
        setCurrentPage(state, page) {
            state.currentPage = page;
        },
        setScrollPosition(state, position) {
            state.scrollPosition = position;
        },
        setLastViewedPid(state, pid) {
            state.lastViewedPid = pid;
        },
        resetState(state) {
            state.currentPage = 1;
            state.scrollPosition = 0;
            // 不重置lastViewedPid，因为我们希望在返回时仍然能够高亮显示
        },
        clearLastViewedPid(state) {
            state.lastViewedPid = null;
        }
    },
    actions: {
        updateCurrentPage({ commit }, page) {
            commit('setCurrentPage', page);
        },
        updateScrollPosition({ commit }, position) {
            commit('setScrollPosition', position);
        },
        updateLastViewedPid({ commit }, pid) {
            commit('setLastViewedPid', pid);
        },
        resetState({ commit }) {
            commit('resetState');
        },
        clearLastViewedPid({ commit }) {
            commit('clearLastViewedPid');
        }
    }
};

export default new Vuex.Store({
    modules: {
        processList: processListModule
    },
    state: {
        nodes: [], // 用于存储节点信息
        selectedNodeIp: null, // 用于存储选中的节点 IP
        activeUploadTask: null,
        activeDownloadTask: null,
        activeTask: null,
        activeToolTask: null,
        repositoryDownloadResults: null, // 存储代码仓下载结果  // 添加 activeTask 状态
        currentProject: localStorage.getItem('currentProject'),
        currentProjectName: localStorage.getItem('currentProjectName'),
        sidebarColor: localStorage.getItem('sidebarColor') || 'primary',
        notifications: [], // 用于存储通知信息
        language: localStorage.getItem('language') || 'en-US', // 当前语言设置
        darkMode: localStorage.getItem('darkMode') !== 'false', // 深色模式状态，默认为true
        smartSearchResults: []
    },
    getters: {
        unreadNotifications: state => state.notifications.filter(n => !n.read).length
    },
    mutations: {
        setNodes(state, nodes) {
            state.nodes = nodes;
        },
        setSelectedNodeIp(state, ip) {
            state.selectedNodeIp = ip;
        },
        setActiveUploadTask(state, task) {
            state.activeUploadTask = task;
        },
        setActiveDownloadTask(state, task) {
            state.activeDownloadTask = task;
        },
        setActiveTask(state, task) {
            state.activeTask = task;
        },
        clearActiveTask(state) {
            state.activeTask = null;
        },
        setActiveToolTask(state, task) {
            state.activeToolTask = task;
        },
        clearActiveToolTask(state) {
            state.activeToolTask = null;
        },
        setRepositoryDownloadResults(state, results) {
            state.repositoryDownloadResults = results;
        },
        clearRepositoryDownloadResults(state) {
            state.repositoryDownloadResults = null;
        },
        setCurrentProject(state, { dbFile, projectName }) {
            state.currentProject = dbFile;
            state.currentProjectName = projectName;
            localStorage.setItem('currentProject', dbFile);
            localStorage.setItem('currentProjectName', projectName || '');
        },
        setSidebarColor(state, color) {
            state.sidebarColor = color;
            localStorage.setItem('sidebarColor', color);
        },
        setLanguage(state, language) {
            state.language = language;
            localStorage.setItem('language', language);
        },
        addNotification(state, notification) {
            state.notifications.unshift(notification); // 添加到数组开头
        },
        markNotificationsAsRead(state) {
            state.notifications.forEach(n => n.read = true);
        },
        clearNotifications(state) {
            state.notifications = [];
        },
        setDarkMode(state, isDark) {
            state.darkMode = isDark;
            localStorage.setItem('darkMode', isDark);
            // 应用深色模式类到根元素
            if (isDark) {
                document.documentElement.classList.add('dark-mode');
            } else {
                document.documentElement.classList.remove('dark-mode');
            }
        },
        // 智能编排搜索相关mutations
        setSmartSearchResults(state, results) {
            state.smartSearchResults = results;
        },
        clearSmartSearch(state) {
            state.smartSearchResults = [];
        }
    },
    actions: {
        async fetchNodes({ state, commit }) {
            try {
                if (!state.currentProject) {
                    commit('setNodes', []);
                    return;
                }

                const response = await axios.get('/api/config', {
                    params: {
                        detail: true,
                        dbFile: state.currentProject
                    }
                });

                if (Array.isArray(response.data)) {
                    commit('setNodes', response.data);
                } else {
                    console.error('Invalid nodes data format:', response.data);
                    commit('setNodes', []);
                }
            } catch (error) {
                console.error('Error fetching nodes:', error);
                commit('setNodes', []);
            }
        },
        updateUploadTask({ commit }, task) {
            commit('setActiveUploadTask', task);
        },
        updateDownloadTask({ commit }, task) {
            commit('setActiveDownloadTask', task);
        },
        updateTask({ commit }, task) {
            commit('setActiveTask', task);
        },
        clearActiveTask({ commit }) {
            commit('clearActiveTask');
        },
        updateToolTask({ commit }, task) {
            commit('setActiveToolTask', task);
        },
        clearActiveToolTask({ commit }) {
            commit('clearActiveToolTask');
        },
        updateRepositoryDownloadResults({ commit }, results) {
            commit('setRepositoryDownloadResults', results);
        },
        clearRepositoryDownloadResults({ commit }) {
            commit('clearRepositoryDownloadResults');
        },
        updateSidebarColor({ commit }, color) {
            commit('setSidebarColor', color);
        },
        updateLanguage({ commit }, language) {
            commit('setLanguage', language);
        },
        addNotification({ commit }, notification) {
            commit('addNotification', {
                ...notification,
                id: Date.now(),
                time: new Date().toLocaleTimeString(),
                read: false
            });
        },
        markNotificationsAsRead({ commit }) {
            commit('markNotificationsAsRead');
        },
        clearNotifications({ commit }) {
            commit('clearNotifications');
        },
        toggleDarkMode({ commit, state }) {
            commit('setDarkMode', !state.darkMode);
        },
        updateSmartSearchResults({ commit }, results) {
            commit('setSmartSearchResults', results);
        },
        clearSmartSearch({ commit }) {
            commit('clearSmartSearch');
        },
        clearProjectStates({ commit, dispatch }) {
            // 清除主状态
            commit('setActiveUploadTask', null);
            commit('setActiveDownloadTask', null);
            commit('clearActiveTask');
            commit('clearActiveToolTask');
            commit('clearRepositoryDownloadResults');
            commit('clearSmartSearch');
            commit('setNodes', []);
            commit('setSelectedNodeIp', null);
            dispatch('processList/resetState', null, { root: true });
            dispatch('processList/clearLastViewedPid', null, { root: true });
        },
        // 切换项目
        switchProject({ commit, dispatch }, { dbFile, projectName }) {
            // 先清除所有状态
            dispatch('clearProjectStates');
            // 然后设置新项目
            commit('setCurrentProject', { dbFile, projectName });
        }
    },
});
