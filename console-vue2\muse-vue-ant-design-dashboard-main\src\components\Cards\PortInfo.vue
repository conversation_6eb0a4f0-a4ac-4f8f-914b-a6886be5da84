<template>
  <a-card
    :bordered="false"
    class="header-solid h-full port-card"
    :bodyStyle="{ padding: 0 }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >
    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width="20" height="20" :class="`text-${sidebarColor}`">
              <path :fill="'currentColor'" d="M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.port') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchPorts" />
        </div>
      </div>
    </template>

    <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
      <a-tab-pane key="tcp" tab="TCP">
        <a-table
          :columns="tcpColumns"
          :data-source="tcpPorts"
          :rowKey="record => `${record.ip}_${record.port}`"
          :pagination="pagination.total > 0 ? pagination : false"
        >
          <template #emptyText>
            <a-empty description="No TCP ports found" />
          </template>
        </a-table>
      </a-tab-pane>

      <a-tab-pane key="udp" tab="UDP">
        <a-table
          :columns="udpColumns"
          :data-source="udpPorts"
          :rowKey="record => `${record.ip}_${record.port}`"
          :pagination="udpPagination.total > 0 ? udpPagination : false"
        >
          <template #emptyText>
            <a-empty description="No UDP ports found" />
          </template>
        </a-table>
      </a-tab-pane>

      <a-tab-pane key="unix_socket" tab="UNIX Socket">
        <a-table
          :columns="unixSocketColumns"
          :data-source="unixSockets"
          :rowKey="record => record.inode"
          :pagination="unixSocketPagination.total > 0 ? unixSocketPagination : false"
        >
          <template #emptyText>
            <a-empty description="No UNIX sockets found" />
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>

    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @cancel="handleModalClose"
      width="600px"
    >
      <template v-slot:footer>
        <a-button @click="handleModalClose">Cancel</a-button>
      </template>
      <div style="white-space: pre-wrap">{{ modalContent.join('\n') }}</div>
    </a-modal>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';

export default {
  components: {
    RefreshButton
  },
  data() {
    return {
      tcpPorts: [],
      udpPorts: [],
      unixSockets: [],
      activeTabKey: 'tcp', // 默认选中TCP标签页
      tcpColumns: [
        {
          title: 'Address',
          key: 'address',
          width: 200,
          customRender: (text, record) => `${record.ip}:${record.port}`,
        },
        {
          title: 'PID',
          dataIndex: 'pid',
          key: 'pid',
          width: 200,
          customRender: (pid, record) => {
            if (!pid) return '-';
            const [pidNum, procName] = pid.split('/');
            return (
              <a onClick={() => this.navigateToProcessDetail(pidNum)}>
                {pid}
              </a>
            );
          },
        },
        {
          title: 'Protocols',
          dataIndex: 'protocols',
          key: 'protocols',
          width: 150,
          customRender: (protocols) => {
            if (!protocols?.offered?.length) return '-';
            return protocols.offered.join(', ');
          },
        },
        {
          title: 'Certificate',
          dataIndex: 'certificate',
          key: 'certificate',
          width: 800,
          customRender: (cert) => {
            if (!cert?.summary?.length) return '-';

            // 证书字段说明
            const certFieldDescriptions = {
              'CN:': '通用名称 - 证书所标识的实体名称',
              'Issuer:': '证书颁发者 - 签发此证书的证书机构',
              'Subject Alt Names:': '主题备用名 - 证书可以保护的其他域名或IP',
              'Chain Status:': '证书链状态 - 验证证书信任链的完整性',
              'Revocation:': '吊销状态 - 检查证书是否被吊销',
              'Validity Period:': '有效期长度 - 证书的有效时间跨度',
              'Expiration Status:': '过期状态 - 证书是否已过期',
              'Key Size:': '密钥大小 - 证书使用的加密密钥长度',
              'Signature Algorithm:': '签名算法 - 用于签发证书的加密算法',
              'Client Auth:': '客户端认证 - 是否支持客户端证书认证',
              'Key Usage:': '密钥用途 - 证书允许的使用场景',
              'Serial Number:': '序列号 - 证书的唯一标识符',
              'Fingerprint SHA256:': '指纹 - 证书的SHA256哈希值',
              'Valid Until:': '有效期至 - 证书的过期时间'
            };

            return (
              <div>
                {cert.summary.map(item => {
                  const fieldName = Object.keys(certFieldDescriptions).find(key => item.startsWith(key));
                  const [label, ...valueParts] = item.split(/(?<=:)\s/);
                  const value = valueParts.join(' ');

                  return (
                    <a-tooltip key={item} placement="right" title={fieldName ? certFieldDescriptions[fieldName] : ''}>
                      <div class="cert-field">
                        <span class="cert-label">{label}</span> {value}
                      </div>
                    </a-tooltip>
                  );
                })}
              </div>
            );
          },
        },
        {
          title: 'HTTP Info',
          dataIndex: 'http_info',
          key: 'http_info',
          width: 150,
          customRender: (httpInfo) => {
            if (!httpInfo?.raw_output) return 'No response';

            // Extract status code from raw response
            const statusCodeMatch = httpInfo.raw_output.match(/HTTP\/[\d.]+ (\d{3})/);
            const statusCode = statusCodeMatch ? statusCodeMatch[1] : 'Unknown';

            return (
              <a onClick={() => this.showDetailsModal('HTTP Response', [
                `Status Code: ${statusCode}`,
                `Protocol: ${httpInfo.protocol}`,
                '---',
                'Raw Response:',
                httpInfo.raw_output
              ])}>
                {statusCode}
              </a>
            );
          },
        },
        {
          title: 'Cipher Suites',
          dataIndex: 'cipher_suites',
          key: 'cipher_suites',
          width: 150,
          customRender: (cipherSuites) => {
            if (!cipherSuites?.details?.length) return '-';
            return (
              <a onClick={() => this.showDetailsModal('Cipher Suites', cipherSuites.details)}>
                {`${cipherSuites.details.length} suites`}
              </a>
            );
          },
        },
        {
          title: 'Vulnerabilities',
          dataIndex: 'vulnerabilities',
          key: 'vulnerabilities',
          width: 150,
          customRender: (vulns) => {
            if (!vulns?.critical?.length) return 'No vulnerabilities';
            const details = vulns.critical.map(v =>
              `${v.name} (${v.severity}): ${v.status}`
            );
            return (
              <a onClick={() => this.showDetailsModal('Vulnerabilities', details)}>
                {`${vulns.critical.length} vulnerabilities`}
              </a>
            );
          },
        },
      ],

      // UDP端口列
      udpColumns: [
        {
          title: 'Proto',
          dataIndex: 'proto',
          key: 'proto',
          width: 80,
        },
        {
          title: 'Recv-Q',
          dataIndex: 'recv_q',
          key: 'recv_q',
          width: 80,
        },
        {
          title: 'Send-Q',
          dataIndex: 'send_q',
          key: 'send_q',
          width: 80,
        },
        {
          title: 'Local Address',
          key: 'local_address',
          width: 180,
          customRender: (_, record) => `${record.ip}:${record.port}`,
        },
        {
          title: 'Foreign Address',
          dataIndex: 'foreign_address',
          key: 'foreign_address',
          width: 180,
        },
        {
          title: 'State',
          dataIndex: 'state',
          key: 'state',
          width: 100,
        },
        {
          title: 'PID/Program',
          dataIndex: 'pid_program',
          key: 'pid_program',
          width: 300,
          customRender: (pid_program) => {
            if (!pid_program) return '-';
            const parts = pid_program.split('/');
            const pid = parts[0];
            return (
              <a onClick={() => this.navigateToProcessDetail(pid)}>
                {pid_program}
              </a>
            );
          },
        },
      ],

      // UNIX Socket列
      unixSocketColumns: [
        {
          title: 'Proto',
          dataIndex: 'proto',
          key: 'proto',
          width: 80,
        },
        {
          title: 'RefCnt',
          dataIndex: 'refcnt',
          key: 'refcnt',
          width: 80,
        },
        {
          title: 'Flags',
          dataIndex: 'flags',
          key: 'flags',
          width: 100,
        },
        {
          title: 'Type',
          dataIndex: 'type',
          key: 'type',
          width: 100,
        },
        {
          title: 'State',
          dataIndex: 'state',
          key: 'state',
          width: 120,
        },
        {
          title: 'I-Node',
          dataIndex: 'inode',
          key: 'inode',
          width: 100,
        },
        {
          title: 'PID/Program',
          dataIndex: 'pid_program',
          key: 'pid_program',
          width: 180,
          customRender: (pid_program) => {
            if (!pid_program) return '-';
            const parts = pid_program.split('/');
            const pid = parts[0];
            return (
              <a onClick={() => this.navigateToProcessDetail(pid)}>
                {pid_program}
              </a>
            );
          },
        },
        {
          title: 'Path',
          dataIndex: 'path',
          key: 'path',
          width: 400,
          customRender: (path) => {
            if (!path) return '-';
            return <div style="word-break: break-word;">{path}</div>;
          },
        },
      ],

      // TCP端口分页
      pagination: {
        current: parseInt(this.$route.query.page) || 1,
        pageSize: 50,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
        onChange: (page) => {
          this.pagination.current = page;
          this.$router.replace({
            query: { ...this.$route.query, page, port_type: 'tcp' }
          });
          this.fetchPorts('tcp');
        },
      },

      // UDP端口分页
      udpPagination: {
        current: parseInt(this.$route.query.page) || 1,
        pageSize: 50,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
        onChange: (page) => {
          this.udpPagination.current = page;
          this.$router.replace({
            query: { ...this.$route.query, page, port_type: 'udp' }
          });
          this.fetchPorts('udp');
        },
      },

      // UNIX Socket分页
      unixSocketPagination: {
        current: parseInt(this.$route.query.page) || 1,
        pageSize: 50,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
        onChange: (page) => {
          this.unixSocketPagination.current = page;
          this.$router.replace({
            query: { ...this.$route.query, page, port_type: 'unix_socket' }
          });
          this.fetchPorts('unix_socket');
        },
      },
      modalVisible: false,
      modalTitle: '',
      modalContent: [],
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp() {
      this.fetchPorts('tcp');
      this.fetchPorts('udp');
      this.fetchPorts('unix_socket');
    },
    '$route.query.page': {
      handler(newPage) {
        const portType = this.$route.query.port_type || 'tcp';
        if (newPage) {
          if (portType === 'tcp' && parseInt(newPage) !== this.pagination.current) {
            this.pagination.current = parseInt(newPage);
            this.fetchPorts('tcp');
          } else if (portType === 'udp' && parseInt(newPage) !== this.udpPagination.current) {
            this.udpPagination.current = parseInt(newPage);
            this.fetchPorts('udp');
          } else if (portType === 'unix_socket' && parseInt(newPage) !== this.unixSocketPagination.current) {
            this.unixSocketPagination.current = parseInt(newPage);
            this.fetchPorts('unix_socket');
          }
        }
      },
      immediate: true
    },
    '$route.query.port_type': {
      handler(newPortType) {
        if (newPortType) {
          this.activeTabKey = newPortType;
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.fetchPorts('tcp');
    this.fetchPorts('udp');
    this.fetchPorts('unix_socket');
  },
  methods: {
    // 处理标签页切换
    handleTabChange(activeKey) {
      this.activeTabKey = activeKey;
      this.$router.replace({
        query: { ...this.$route.query, port_type: activeKey }
      });
      this.fetchPorts(activeKey);
    },

    async fetchPorts(portType = 'tcp') {
      if (!this.selectedNodeIp) {
        this.tcpPorts = [];
        this.udpPorts = [];
        this.unixSockets = [];
        this.pagination.total = 0;
        this.udpPagination.total = 0;
        this.unixSocketPagination.total = 0;
        return;
      }

      try {
        let pagination;
        if (portType === 'tcp') {
          pagination = this.pagination;
        } else if (portType === 'udp') {
          pagination = this.udpPagination;
        } else if (portType === 'unix_socket') {
          pagination = this.unixSocketPagination;
        }

        const { current, pageSize } = pagination;
        const response = await axios.get(`/api/port/${this.selectedNodeIp}`, {
          params: {
            page: current,
            page_size: pageSize,
            port_type: portType,
            dbFile: this.currentProject
          },
        });

        if (portType === 'tcp') {
          this.tcpPorts = response.data.data || response.data;
          this.pagination.total = response.data.total || 0;
        } else if (portType === 'udp') {
          this.udpPorts = response.data.data || response.data;
          this.udpPagination.total = response.data.total || 0;
        } else if (portType === 'unix_socket') {
          this.unixSockets = response.data.data || response.data;
          this.unixSocketPagination.total = response.data.total || 0;
        }
      } catch (error) {
        console.error(`Error fetching ${portType} ports:`, error);
        this.$message.error(`Failed to fetch ${portType} ports data`);

        if (portType === 'tcp') {
          this.tcpPorts = [];
          this.pagination.total = 0;
        } else if (portType === 'udp') {
          this.udpPorts = [];
          this.udpPagination.total = 0;
        } else if (portType === 'unix_socket') {
          this.unixSockets = [];
          this.unixSocketPagination.total = 0;
        }
      }
    },
    navigateToProcessDetail(pid) {
      this.$router.push({
        name: 'ProcessDetail',
        params: { pid: pid },
        query: { page: this.pagination.current }
      });
    },
    showDetailsModal(title, content) {
      this.modalTitle = title;
      this.modalContent = content;
      this.modalVisible = true;
    },
    handleModalClose() {
      this.modalVisible = false;
      this.modalContent = [];
    },
  },
};
</script>

<style scoped lang="scss">
.port-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.card-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ant-table {
  border-radius: 0 0 8px 8px;
}

.ant-table-thead > tr > th {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
}

.ant-table-tbody > tr:hover > td {
  background-color: #fafafa !important;
}

.cert-field {
  margin: 2px 0;

  .cert-label {
    color: #d10d7d;
    font-size: 0.95em;
    min-width: 120px;
    display: inline-block;
  }
}
</style>
