{"version": 3, "sources": ["webpack:///./src/components/Cards/KubernetesInfo.vue?7750", "webpack:///./src/components/Widgets/JsonDetailModal.vue?0280", "webpack:///./src/components/Widgets/JsonDetailModal.vue", "webpack:///src/components/Widgets/JsonDetailModal.vue", "webpack:///./src/components/Widgets/JsonDetailModal.vue?c6b5", "webpack:///./src/components/Widgets/JsonDetailModal.vue?94a2", "webpack:///./src/views/Kubernetes.vue", "webpack:///./src/components/Cards/KubernetesInfo.vue", "webpack:///src/components/Cards/KubernetesInfo.vue", "webpack:///./src/components/Cards/KubernetesInfo.vue?03d0", "webpack:///./src/components/Cards/KubernetesInfo.vue?249a", "webpack:///src/views/Kubernetes.vue", "webpack:///./src/views/Kubernetes.vue?8e6e", "webpack:///./src/views/Kubernetes.vue?7ed8", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "staticRenderFns", "name", "methods", "showDetailModal", "title", "data", "options", "h", "$createElement", "modalWidth", "Math", "min", "width", "window", "innerWidth", "contentHeight", "innerHeight", "ids", "search", "Date", "now", "counter", "theme", "isDarkTheme", "header", "contentElement", "String", "$root", "$confirm", "content", "okText", "icon", "cancelButtonProps", "style", "display", "class", "maskClosable", "getContainer", "document", "body", "append<PERSON><PERSON><PERSON>", "createElement", "setTimeout", "container", "getElementById", "JsonViewer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "deep", "Infinity", "showDoubleQuotes", "showLength", "showLineNumbers", "height", "overflow", "$mount", "$el", "searchInput", "counterElement", "matches", "currentMatchIndex", "highlightMatches", "searchTerm", "textContent", "jsonNodes", "querySelectorAll", "regex", "RegExp", "replace", "for<PERSON>ach", "el", "classList", "remove", "node", "text", "match", "lastIndex", "exec", "push", "index", "length", "add", "error", "console", "navigateToMatch", "max", "currentMatch", "parent", "parentElement", "contains", "expandBtn", "querySelector", "click", "scrollIntoView", "behavior", "block", "searchTimeout", "addEventListener", "e", "clearTimeout", "target", "value", "trim", "key", "preventDefault", "shift<PERSON>ey", "themeButton", "backgroundColor", "copyButton", "textToCopy", "JSON", "stringify", "navigator", "clipboard", "isSecureContext", "writeText", "then", "$message", "success", "$t", "catch", "err", "textArea", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "component", "attrs", "staticClass", "padding", "borderBottom", "scopedSlots", "_u", "fn", "sidebarColor", "_v", "_s", "on", "fetchActiveTabData", "proxy", "ref", "handleTabChange", "activeTab", "apiServerColumns", "apiServerData", "record", "address", "pagination", "loadingApiServers", "_e", "ingressColumns", "ingressData", "namespace", "loadingIngresses", "gatewayColumns", "gatewayData", "loadingGateways", "virtualServiceColumns", "virtualServiceData", "loadingVirtualServices", "serviceColumns", "serviceData", "loadingServices", "networkPolicyColumns", "networkPolicyData", "loadingNetworkPolicies", "podColumns", "podData", "loadingPods", "x", "nodeColumns", "nodeData", "loadingNodes", "secretColumns", "secretData", "loadingSecrets", "configMapColumns", "configMapData", "loadingConfigMaps", "roleColumns", "roleData", "loadingRole", "roleBindingColumns", "roleBindingData", "loadingRoleBinding", "clusterRoleColumns", "clusterRoleData", "loadingClusterRole", "clusterRoleBindingColumns", "clusterRoleBindingData", "loadingClusterRoleBinding", "serviceAccountPermissionsColumns", "serviceAccountPermissionsData", "pod_name", "service_account", "loadingServiceAccountPermissions", "components", "RefreshButton", "JsonDetailModal", "dataIndex", "ellipsis", "customRender", "renderComplexData", "permissions", "Array", "isArray", "parse", "displayText", "summary", "map", "p", "type", "join", "slice", "pageSize", "initialLoad", "computed", "mapState", "watch", "selectedNodeIp", "newIp", "resetData", "mounted", "resourceType", "capitalizeFirstLetter", "response", "axios", "get", "params", "dbFile", "currentProject", "camelCaseToDataName", "camelCase", "withoutK8s", "camelized", "_", "letter", "toUpperCase", "string", "char<PERSON>t", "Object", "entries", "k", "v", "substring", "keys", "$refs", "jsonDetailModal", "KubernetesInfo", "$event", "$emit", "default"], "mappings": "kHAAA,W,oCCAA,W,oFCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAElEE,EAAkB,G,uDCQP,GACfC,KAAA,kBACAC,QAAA,CAOAC,gBAAAC,EAAAC,EAAAC,EAAA,UAAAC,EAAA,KAAAC,eAEAC,EAAAC,KAAAC,IAAAL,EAAAM,OAAA,QAAAC,OAAAC,YACAC,EAAAL,KAAAC,IAAA,OAAAE,OAAAG,aAGAC,EAAA,CACAC,OAAA,UAAAC,KAAAC,MACAC,QAAA,WAAAF,KAAAC,MACAE,MAAA,SAAAH,KAAAC,OAIA,IAAAG,GAAA,EAGA,MAAAC,EAAAjB,EAAA,aACA,wEAAAA,EAAA,aACA,wCAAAA,EAAA,sBACA,sDAAAA,EAAA,cACA,sBAAAH,MAAAG,EAAA,aAEA,wCAAAA,EAAA,iBACAU,EAAAI,SAAA,+EAAAd,EAAA,qBAEAU,EAAAC,OAAA,YACA,qDAAAX,EAAA,sBAEA,kDACA,kBAAAA,EAAA,sBAGAU,EAAAK,MAAA,KACA,YACA,kBAEA,cADA,uDAAAf,EAAA,sBAIA,gBACA,YACA,aAEA,cADA,2DAQAkB,EAAA,kBAAApB,EAAAE,EAAA,aACA,WAAAQ,gGAAA,gEAAAR,EAAA,aAGA,WAAAQ,6NAAA,CACAW,OAAArB,KAKA,KAAAsB,MAAAC,SAAA,CACAxB,MAAAoB,EACAK,QAAAJ,EACAb,MAAAH,EACAqB,OAAAxB,EAAAwB,QAAA,KACAC,KAAA,KACAC,kBAAA,CAAAC,MAAA,CAAAC,QAAA,SACAC,MAAA,eACAC,cAAA,EACAC,iBAAAC,SAAAC,KAAAC,YAAAF,SAAAG,cAAA,UAIAC,WAAA,KACA,qBAAArC,EAAA,CAEA,MAAAsC,EAAAL,SAAAM,eAAA,kBACA,GAAAD,EAAA,CAEA,MAAAE,EAAA,IAAAC,OAAA,CACAnD,OAAAY,KAAAwC,IAAA,CACAC,MAAA,CACA3C,OACA4C,KAAAC,IACAC,kBAAA,EACAC,YAAA,EACAC,iBAAA,GAEApB,MAAA,CACAqB,OAAA,OACAC,SAAA,YAMAV,EAAAW,SACAb,EAAAH,YAAAK,EAAAY,MAKA,MAAAC,EAAApB,SAAAM,eAAA3B,EAAAC,QACAyC,EAAArB,SAAAM,eAAA3B,EAAAI,SAGA,IAAAuC,EAAA,GACAC,GAAA,EAGA,GAAAH,GAAAC,EAAA,CAEA,MAAAG,EAAAC,IAQA,GANAH,EAAA,GACAC,GAAA,EAGAF,EAAAK,YAAA,GAEAD,EAEA,IAEA,MAAAE,EAAA3B,SAAA4B,iBAAA,wBAGAC,EAAA,IAAAC,OAAAL,EAAAM,QAAA,oCA+BA,GA5BA/B,SAAA4B,iBAAA,qBAAAI,QAAAC,IACAA,EAAAC,UAAAC,OAAA,sBAGAnC,SAAA4B,iBAAA,uBAAAI,QAAAC,IACAA,EAAAC,UAAAC,OAAA,wBAIAR,EAAAK,QAAAI,IACA,MAAAC,EAAAD,EAAAV,YACA,IAAAY,EACAT,EAAAU,UAAA,EAEA,cAAAD,EAAAT,EAAAW,KAAAH,IACAf,EAAAmB,KAAA,CACAL,OACAC,KAAAC,EAAA,KAIAA,EAAAI,QAAAb,EAAAU,WACAV,EAAAU,cAMA,IAAAjB,EAAAqB,OAEA,YADAtB,EAAAK,YAAA,QAIAL,EAAAK,YAAA,KAAAJ,EAAAqB,OAGArB,EAAAU,QAAAM,IACAA,EAAAF,KAAAF,UAAAU,IAAA,sBAEA,MAAAC,GACAC,QAAAD,MAAA,QAAAA,KAKAE,EAAAL,IACA,OAAApB,EAAAqB,OAAA,OAGAD,EAAAtE,KAAA4E,IAAA,EAAA5E,KAAAC,IAAAiD,EAAAqB,OAAA,EAAAD,IAGAnB,GAAA,GAAAA,EAAAD,EAAAqB,QACArB,EAAAC,GAAAa,KAAAF,UAAAC,OAAA,sBAIAZ,EAAAmB,EACArB,EAAAK,YAAA,GAAAH,EAAA,KAAAD,EAAAqB,SAGA,MAAAM,EAAA3B,EAAAC,GACA,GAAA0B,EAAA,CACAA,EAAAb,KAAAF,UAAAU,IAAA,sBAGA,IAAAM,EAAAD,EAAAb,KAAAe,cACA,MAAAD,EAAA,CACA,GAAAA,EAAAhB,WAAAgB,EAAAhB,UAAAkB,SAAA,mBAEAF,EAAAhB,UAAAkB,SAAA,gBACA,MAAAC,EAAAH,EAAAI,cAAA,sBACAD,KAAAE,QAGAL,IAAAC,cAIAF,EAAAb,KAAAoB,eAAA,CAAAC,SAAA,SAAAC,MAAA,aAKA,IAAAC,EACAvC,EAAAwC,iBAAA,QAAAC,IAEAF,GAAAG,aAAAH,GACAA,EAAAvD,WAAA,KACAoB,EAAAqC,EAAAE,OAAAC,MAAAC,SACA,OAIA7C,EAAAwC,iBAAA,UAAAC,IACA,UAAAA,EAAAK,MACAL,EAAAM,iBACApB,EAAAc,EAAAO,SAAA7C,EAAA,EAAAA,EAAA,MAMA,MAAA8C,EAAArE,SAAAM,eAAA3B,EAAAK,OACAqF,GACAA,EAAAT,iBAAA,aACA,MAAAvD,EAAAL,SAAAsD,cAAA,mBACAjD,IAEAA,EAAA6B,UAAAkB,SAAA,gBACA/C,EAAA6B,UAAAC,OAAA,eACA9B,EAAA6B,UAAAU,IAAA,cACAvC,EAAAV,MAAA2E,gBAAA,UACArF,GAAA,IAEAoB,EAAA6B,UAAAC,OAAA,cACA9B,EAAA6B,UAAAU,IAAA,eACAvC,EAAAV,MAAA2E,gBAAA,OACArF,GAAA,MAOA,MAAAsF,EAAAvE,SAAAM,eAAA,YACAiE,GACAA,EAAAX,iBAAA,aACA,IACA,MAAAY,EAAA,kBAAAzG,EAAA0G,KAAAC,UAAA3G,EAAA,QAAAqB,OAAArB,GAGA,GAAA4G,UAAAC,WAAArG,OAAAsG,gBACAF,UAAAC,UAAAE,UAAAN,GACAO,KAAA,KACA,KAAAC,SAAAC,QAAA,KAAAC,GAAA,+BAEAC,MAAAC,IACAtC,QAAAD,MAAA,QAAAuC,GACA,KAAAJ,SAAAnC,MAAA,cAEA,CAEA,MAAAwC,EAAArF,SAAAG,cAAA,YACAkF,EAAArB,MAAAQ,EACAxE,SAAAC,KAAAC,YAAAmF,GACAA,EAAAC,SACA,MAAAC,EAAAvF,SAAAwF,YAAA,QACAxF,SAAAC,KAAAwF,YAAAJ,GAEAE,EACA,KAAAP,SAAAC,QAAA,KAAAC,GAAA,6BAEA,KAAAF,SAAAnC,MAAA,KAAAqC,GAAA,uBAGA,MAAAE,GACA,KAAAJ,SAAAnC,MAAA,KAAAqC,GAAA,0BAIA,QCtTuW,I,wBCQnWQ,EAAY,eACd,EACArI,EACAK,GACA,EACA,KACA,KACA,MAIa,OAAAgI,E,kDCnBf,IAAIrI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACmI,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACnI,EAAG,QAAQ,CAACoI,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACnI,EAAG,mBAAmB,IAAI,IAAI,IAE3ME,EAAkB,GCFlBL,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACoI,YAAY,sCAAsCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEE,QAAS,GAAI,UAAY,CAAEC,aAAc,sBAAuBC,YAAYzI,EAAI0I,GAAG,CAAC,CAAC9B,IAAI,QAAQ+B,GAAG,WAAW,MAAO,CAACzI,EAAG,MAAM,CAACoI,YAAY,uBAAuB,CAACpI,EAAG,MAAM,CAACoI,YAAY,kBAAkB,CAACpI,EAAG,MAAM,CAACoI,YAAY,gBAAgB,CAACpI,EAAG,MAAM,CAACqC,MAAM,QAAQvC,EAAI4I,aAAeP,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACnI,EAAG,OAAO,CAACmI,MAAM,CAAC,KAAO,eAAe,EAAI,wgCAAwgCnI,EAAG,KAAK,CAACoI,YAAY,qBAAqB,CAACtI,EAAI6I,GAAG7I,EAAI8I,GAAG9I,EAAI4H,GAAG,uBAAuB1H,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAAC6I,GAAG,CAAC,QAAU/I,EAAIgJ,uBAAuB,OAAOC,OAAM,MAAS,CAAC/I,EAAG,kBAAkB,CAACgJ,IAAI,oBAAoBhJ,EAAG,SAAS,CAACmI,MAAM,CAAC,qBAAqB,kBAAkBU,GAAG,CAAC,OAAS/I,EAAImJ,kBAAkB,CAACjJ,EAAG,aAAa,CAAC0G,IAAI,iBAAiByB,MAAM,CAAC,IAAM,gBAAgB,CAAoB,mBAAlBrI,EAAIoJ,UAAgClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIqJ,iBAAiB,cAAcrJ,EAAIsJ,cAAc,OAAUC,GAAWA,EAAOC,QAAQ,WAAaxJ,EAAIyJ,WAAW,QAAUzJ,EAAI0J,qBAAqB1J,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,cAAcyB,MAAM,CAAC,IAAM,cAAc,CAAoB,gBAAlBrI,EAAIoJ,UAA6BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAI4J,eAAe,cAAc5J,EAAI6J,YAAY,OAAUN,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAI+J,oBAAoB/J,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,cAAcyB,MAAM,CAAC,IAAM,aAAa,CAAoB,gBAAlBrI,EAAIoJ,UAA6BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIgK,eAAe,cAAchK,EAAIiK,YAAY,OAAUV,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIkK,mBAAmBlK,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,sBAAsByB,MAAM,CAAC,IAAM,qBAAqB,CAAoB,wBAAlBrI,EAAIoJ,UAAqClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAImK,sBAAsB,cAAcnK,EAAIoK,mBAAmB,OAAUb,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIqK,0BAA0BrK,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,cAAcyB,MAAM,CAAC,IAAM,aAAa,CAAoB,gBAAlBrI,EAAIoJ,UAA6BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIsK,eAAe,cAActK,EAAIuK,YAAY,OAAUhB,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIwK,mBAAmBxK,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,qBAAqByB,MAAM,CAAC,IAAM,qBAAqB,CAAoB,uBAAlBrI,EAAIoJ,UAAoClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIyK,qBAAqB,cAAczK,EAAI0K,kBAAkB,OAAUnB,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAI2K,0BAA0B3K,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,UAAUyB,MAAM,CAAC,IAAM,SAAS,CAAoB,YAAlBrI,EAAIoJ,UAAyBlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAI4K,WAAW,cAAc5K,EAAI6K,QAAQ,OAAUtB,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAI8K,YAAY,OAAS,CAAEC,EAAG,SAAU/K,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,WAAWyB,MAAM,CAAC,IAAM,UAAU,CAAoB,aAAlBrI,EAAIoJ,UAA0BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIgL,YAAY,cAAchL,EAAIiL,SAAS,OAAU1B,GAAWA,EAAOlJ,KAAK,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIkL,gBAAgBlL,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,aAAayB,MAAM,CAAC,IAAM,YAAY,CAAoB,eAAlBrI,EAAIoJ,UAA4BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAImL,cAAc,cAAcnL,EAAIoL,WAAW,OAAU7B,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIqL,kBAAkBrL,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,iBAAiByB,MAAM,CAAC,IAAM,eAAe,CAAoB,mBAAlBrI,EAAIoJ,UAAgClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIsL,iBAAiB,cAActL,EAAIuL,cAAc,OAAUhC,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIwL,qBAAqBxL,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,WAAWyB,MAAM,CAAC,IAAM,UAAU,CAAoB,aAAlBrI,EAAIoJ,UAA0BlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIyL,YAAY,cAAczL,EAAI0L,SAAS,OAAUnC,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAI2L,eAAe3L,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,mBAAmByB,MAAM,CAAC,IAAM,kBAAkB,CAAoB,qBAAlBrI,EAAIoJ,UAAkClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAI4L,mBAAmB,cAAc5L,EAAI6L,gBAAgB,OAAUtC,GAAW,GAAGA,EAAOO,aAAaP,EAAOlJ,OAAO,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAI8L,sBAAsB9L,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,mBAAmByB,MAAM,CAAC,IAAM,kBAAkB,CAAoB,qBAAlBrI,EAAIoJ,UAAkClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAI+L,mBAAmB,cAAc/L,EAAIgM,gBAAgB,OAAUzC,GAAWA,EAAOlJ,KAAK,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIiM,sBAAsBjM,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,2BAA2ByB,MAAM,CAAC,IAAM,0BAA0B,CAAoB,6BAAlBrI,EAAIoJ,UAA0ClJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIkM,0BAA0B,cAAclM,EAAImM,uBAAuB,OAAU5C,GAAWA,EAAOlJ,KAAK,WAAaL,EAAIyJ,WAAW,QAAUzJ,EAAIoM,6BAA6BpM,EAAI2J,MAAM,GAAGzJ,EAAG,aAAa,CAAC0G,IAAI,iCAAiCyB,MAAM,CAAC,IAAM,yBAAyB,CAAoB,mCAAlBrI,EAAIoJ,UAAgDlJ,EAAG,UAAU,CAACmI,MAAM,CAAC,QAAUrI,EAAIqM,iCAAiC,cAAcrM,EAAIsM,8BAA8B,OAAU/C,GAAW,GAAGA,EAAOO,aAAaP,EAAOgD,YAAYhD,EAAOiD,kBAAkB,WAAaxM,EAAIyJ,WAAW,QAAUzJ,EAAIyM,iCAAiC,OAAS,CAAE1B,EAAG,SAAU/K,EAAI2J,MAAM,IAAI,IAAI,IAEtqNvJ,EAAkB,G,sEC6MP,GACfsM,WAAA,CACAC,qBACAC,wBAEAvM,KAAA,iBACAI,OAAA,MAAAE,EAAA,KAAAC,eACA,OACAwI,UAAA,iBACAE,cAAA,GACAO,YAAA,GACAI,YAAA,GACAG,mBAAA,GACAG,YAAA,GACAG,kBAAA,GACAG,QAAA,GACAI,SAAA,GACAG,WAAA,GACAG,cAAA,GACAG,SAAA,GACAG,gBAAA,GACAG,gBAAA,GACAG,uBAAA,GACAG,8BAAA,GACA5C,mBAAA,EACAK,kBAAA,EACAG,iBAAA,EACAG,wBAAA,EACAG,iBAAA,EACAG,wBAAA,EACAG,aAAA,EACAI,cAAA,EACAG,gBAAA,EACAG,mBAAA,EACAG,aAAA,EACAG,oBAAA,EACAG,oBAAA,EACAG,2BAAA,EACAK,kCAAA,EACApD,iBAAA,CACA,CAAA7I,MAAA,UAAAqM,UAAA,UAAAjG,IAAA,WACA,CAAApG,MAAA,UAAAqM,UAAA,UAAAjG,IAAA,YAEAgD,eAAA,CACA,CAAApJ,MAAA,YAAAqM,UAAA,YAAAjG,IAAA,YAAA5F,MAAA,KACA,CAAAR,MAAA,OAAAqM,UAAA,OAAAjG,IAAA,OAAA5F,MAAA,KACA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,SAEA,CACAvE,MAAA,SACAqM,UAAA,iBACAjG,IAAA,iBACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,oBAGAiF,eAAA,CACA,CAAAxJ,MAAA,YAAAqM,UAAA,YAAAjG,IAAA,YAAA5F,MAAA,KACA,CAAAR,MAAA,OAAAqM,UAAA,OAAAjG,IAAA,OAAA5F,MAAA,KACA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,iBAEA,CACAvE,MAAA,SACAqM,UAAA,iBACAjG,IAAA,iBACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,oBAGAoF,sBAAA,CACA,CAAA3J,MAAA,YAAAqM,UAAA,YAAAjG,IAAA,YAAA5F,MAAA,KACA,CAAAR,MAAA,OAAAqM,UAAA,OAAAjG,IAAA,OAAA5F,MAAA,KACA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,yBAEA,CACAvE,MAAA,SACAqM,UAAA,yBACAjG,IAAA,yBACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,4BAGAuF,eAAA,CACA,CAAA9J,MAAA,YAAAqM,UAAA,YAAAjG,IAAA,YAAA5F,MAAA,KACA,CAAAR,MAAA,OAAAqM,UAAA,OAAAjG,IAAA,OAAA5F,MAAA,KACA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,SAEA,CACAvE,MAAA,SACAqM,UAAA,iBACAjG,IAAA,iBACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,oBAGA0F,qBAAA,CACA,CAAAjK,MAAA,YAAAqM,UAAA,YAAAjG,IAAA,YAAA5F,MAAA,KACA,CAAAR,MAAA,OAAAqM,UAAA,OAAAjG,IAAA,OAAA5F,MAAA,KACA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,UAGA6F,WAAA,CACA,CACApK,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA+L,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,SAEA,CACAvE,MAAA,SACAqM,UAAA,aACAjG,IAAA,aACA5F,MAAA,IACA+L,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,gBAGAiG,YAAA,CACA,CACAxK,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,SAEA,CACAvE,MAAA,SACAqM,UAAA,aACAjG,IAAA,aACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,iBAGAoG,cAAA,CACA,CACA3K,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,SAEA,CACAvE,MAAA,OACAqM,UAAA,cACAjG,IAAA,cACA5F,MAAA,MAGAsK,iBAAA,CACA,CACA9K,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,oBAGA0G,YAAA,CACA,CACAjL,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,QACAqM,UAAA,QACAjG,IAAA,QACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,gBAGA6G,mBAAA,CACA,CACApL,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,WACAqM,UAAA,UACAjG,IAAA,UACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,mBAEA,CACAvE,MAAA,WACAqM,UAAA,WACAjG,IAAA,WACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,mBAGAgH,mBAAA,CACA,CACAvL,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,mBACAqM,UAAA,kBACAjG,IAAA,kBACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,qBAEA,CACAvE,MAAA,QACAqM,UAAA,QACAjG,IAAA,QACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,gBAGAmH,0BAAA,CACA,CACA1L,MAAA,OACAqM,UAAA,OACAjG,IAAA,OACA5F,MAAA,KAEA,CACAR,MAAA,WACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,aAEA,CACAvE,MAAA,WACAqM,UAAA,UACAjG,IAAA,UACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,mBAEA,CACAvE,MAAA,WACAqM,UAAA,WACAjG,IAAA,WACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,GAAA,KAAAiI,kBAAAjI,EAAA,mBAGAsH,iCAAA,CACA,CACA7L,MAAA,WACAqM,UAAA,WACAjG,IAAA,WACA5F,MAAA,KAEA,CACAR,MAAA,YACAqM,UAAA,YACAjG,IAAA,YACA5F,MAAA,KAEA,CACAR,MAAA,kBACAqM,UAAA,kBACAjG,IAAA,kBACA5F,MAAA,KAEA,CACAR,MAAA,cACAqM,UAAA,cACAjG,IAAA,cACA5F,MAAA,IACA8L,UAAA,EACAC,aAAAhI,IACA,IACA,MAAAkI,EAAAC,MAAAC,QAAApI,OAAAoC,KAAAiG,MAAArI,GAAA,GAEA,IAAAsI,EAAA,iBACA,GAAAJ,KAAA5H,OAAA,GACA,MAAAiI,EAAAL,EAAAM,IAAAC,GAAA,GAAAA,EAAAC,SAAAD,EAAAnN,QAAAqN,KAAA,MACAL,EAAAC,EAAAjI,OAAA,GAAAiI,EAAAK,MAAA,YAAAL,EAGA,OAAA3M,EAAA,aACA,wEAAAA,EAAA,cACA,wDACA0M,IAAA1M,EAAA,wBAGA,cACA,6DACAsF,IAAA,KAAA1F,gBAAA,6BAAA0M,KAAA,YAMA,MAAA1H,GAEA,OADAC,QAAAD,MAAA,6BAAAA,GACA,yBAKAkE,WAAA,CACAmE,SAAA,KAEAC,aAAA,IAGAC,SAAA,IACAC,eAAA,qDAEAC,MAAA,CACAC,eAAAC,GACA,KAAAC,YACA,KAAAN,aAAA,EACAK,GACA,KAAAlF,uBAIAoF,UACA,KAAAH,gBACA,KAAAjF,sBAGA1I,QAAA,CACA6I,gBAAAvC,GACA,KAAAwC,UAAAxC,EACA,KAAAoC,sBAEA,2BACA,SAAAiF,eAGA,OAFAzI,QAAAD,MAAA,+BACA,KAAA4I,YAGA,MAAAE,EAAA,KAAAjF,UACA,oBAAAkF,sBAAAD,KAAA,EACA,IACA,IAAAE,EACAA,QAAAC,OAAAC,IAAA,YAAAJ,KAAA,KAAAJ,iBAAA,CACAS,OAAA,CACAC,OAAA,KAAAC,kBAGA,MAAAnO,EAAA8N,EAAA9N,KACA,aAAAoO,oBAAAR,IAAA5N,EACA,MAAA8E,GACAC,QAAAD,MAAA,kBAAA8I,KAAA9I,GACA,aAAAsJ,oBAAAR,IAAA,GACA,QACA,oBAAAC,sBAAAD,KAAA,IAGAQ,oBAAAC,GACA,MAAAC,EAAAD,EAAArK,QAAA,WAGA,kCAAAsK,EACA,sCAGA,MAAAC,EAAAD,EAAAtK,QAAA,aAAAwK,EAAAC,MAAAC,eACA,OAAAH,EAAA,QAEAV,sBAAAc,GACA,OAAAA,EAAAC,OAAA,GAAAF,cAAAC,EAAAzB,MAAA,IAEAQ,YACA,KAAA7E,cAAA,GACA,KAAAO,YAAA,GACA,KAAAI,YAAA,GACA,KAAAG,mBAAA,GACA,KAAAG,YAAA,GACA,KAAAG,kBAAA,GACA,KAAAG,QAAA,GACA,KAAAI,SAAA,GACA,KAAAG,WAAA,GACA,KAAAG,cAAA,GACA,KAAAG,SAAA,GACA,KAAAG,gBAAA,GACA,KAAAG,gBAAA,GACA,KAAAG,uBAAA,GACA,KAAAG,8BAAA,IAEAU,kBAAAjI,EAAAvE,GAAA,MAAAG,EAAA,KAAAC,eACA,IAAAmE,GAAA,SAAAA,EAAA,UAGA,MAAAsI,EAAAH,MAAAC,QAAApI,GAAA,SAAAA,EAAAM,UACA,kBAAAN,GAAA,OAAAA,EACAuK,OAAAC,QAAAxK,GAAA4I,MAAA,KAAAJ,IAAA,EAAAiC,EAAAC,KACA,GAAAD,MAAA,kBAAAC,EAAAtI,KAAAC,UAAAqI,GAAAC,UAAA,MAAAD,KACA/B,KAAA,OAAA4B,OAAAK,KAAA5K,GAAAM,OAAA,YACAvD,OAAAiD,GAAAM,OAAA,GAAAvD,OAAAiD,GAAA4I,MAAA,YAAA7L,OAAAiD,GAEA,OAAApE,EAAA,aACA,4FAAAA,EAAA,cACA,wDAAA0M,IAAA1M,EAAA,wBAEA,cACA,6DACAsF,IAAA,KAAA1F,gBAAAC,EAAAuE,KAAA,aAQAxE,gBAAAC,EAAAC,GAEA,KAAAmP,MAAAC,gBAAAtP,gBAAAC,EAAAC,MC9yBsW,I,wBCQlW2H,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACfsE,WAAA,CACAoD,mBCjBmV,ICO/U,EAAY,eACd,EACA/P,EACAK,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,IAAIL,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACqC,MAAM,CAAC,iBAAkB,QAAQvC,EAAI4I,cAAgBP,MAAM,CAAC,KAAO,UAAUU,GAAG,CAAC,MAAQ,SAASgH,GAAQ,OAAO/P,EAAIgQ,MAAM,cAAc,CAAChQ,EAAI6I,GAAG,IAAI7I,EAAI8I,GAAG9I,EAAI+E,MAAQ/E,EAAI4H,GAAG,mBAAmB,QAEhRxH,EAAkB,G,YCWP,GACf0N,SAAA,IACAC,eAAA,mBAEA1N,KAAA,gBACA+C,MAAA,CACA2B,KAAA,CACA0I,KAAA3L,OACAmO,QAAA,MCrBqW,I,YCOjW7H,EAAY,eACd,EACArI,EACAK,GACA,EACA,KACA,WACA,MAIa,OAAAgI,E", "file": "static/js/chunk-8a9b96a2.5297d144.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./KubernetesInfo.vue?vue&type=style&index=0&id=656af809&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=style&index=0&id=2fcc9902&prod&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div')\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <!-- 组件不直接渲染模态框，而是提供方法 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport VueJsonPretty from 'vue-json-pretty';\r\n\r\nexport default {\r\n  name: 'JsonDetailModal',\r\n  methods: {\r\n    /**\r\n     * 显示JSON详情模态框\r\n     * @param {string} title 模态框标题\r\n     * @param {object|string} data 要显示的数据\r\n     * @param {object} options 配置选项\r\n     */\r\n    showDetailModal(title, data, options = {}) {\r\n      // 计算响应式尺寸\r\n      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);\r\n      const contentHeight = Math.min(700, window.innerHeight * 0.6);\r\n\r\n      // 生成唯一ID\r\n      const ids = {\r\n        search: `search-${Date.now()}`,\r\n        counter: `counter-${Date.now()}`,\r\n        theme: `theme-${Date.now()}`\r\n      };\r\n\r\n      // 默认使用暗色主题\r\n      let isDarkTheme = true;\r\n\r\n      // 创建标题、搜索框和复制按钮\r\n      const header = (\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <a-icon type=\"code\" style=\"margin-right: 8px; font-size: 16px;\" />\r\n            <span style=\"font-weight: 500;\">{title}</span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <div id={ids.counter} style=\"margin-right: 10px; min-width: 60px; text-align: right; color: #666;\"></div>\r\n            <a-input\r\n              id={ids.search}\r\n              placeholder=\"搜索 (Enter: ↓  Shift+Enter: ↑)\"\r\n              allowClear\r\n              prefix={<a-icon type=\"search\" style=\"color: rgba(0,0,0,.25)\" />}\r\n              style=\"width: 250px;\"\r\n            />\r\n            <a-button\r\n              id={ids.theme}\r\n              type=\"link\"\r\n              icon=\"bg-colors\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"切换主题\"\r\n            />\r\n            <a-button\r\n              id=\"copy-btn\"\r\n              type=\"link\"\r\n              icon=\"copy\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"复制内容\"\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n      // 准备内容元素\r\n      const contentElement = typeof data === 'object' ? (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class=\"json-container theme-dark\" id=\"json-container\">\r\n        </div>\r\n      ) : (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>\r\n          {String(data)}\r\n        </div>\r\n      );\r\n\r\n      // 创建模态框\r\n      this.$root.$confirm({\r\n        title: header,\r\n        content: contentElement,\r\n        width: modalWidth,\r\n        okText: options.okText || '关闭',\r\n        icon: null,\r\n        cancelButtonProps: { style: { display: 'none' } },\r\n        class: 'detail-modal',\r\n        maskClosable: false, // 防止点击外部关闭\r\n        getContainer: () => document.body.appendChild(document.createElement('div'))\r\n      });\r\n\r\n      // 在模态框内容区域渲染VueJsonPretty组件\r\n      setTimeout(() => {\r\n        if (typeof data === 'object') {\r\n          // 查找JSON容器\r\n          const container = document.getElementById('json-container');\r\n          if (container) {\r\n            // 创建VueJsonPretty组件实例\r\n            const JsonViewer = new Vue({\r\n              render: h => h(VueJsonPretty, {\r\n                props: {\r\n                  data: data,\r\n                  deep: Infinity, // 设置为Infinity以默认展开所有节点\r\n                  showDoubleQuotes: true,\r\n                  showLength: true,\r\n                  showLineNumbers: true,  // 添加行号显示\r\n                },\r\n                style: {\r\n                  height: '100%',\r\n                  overflow: 'auto'\r\n                }\r\n              })\r\n            });\r\n\r\n            // 挂载组件\r\n            JsonViewer.$mount();\r\n            container.appendChild(JsonViewer.$el);\r\n          }\r\n        }\r\n\r\n        // 获取搜索相关元素\r\n        const searchInput = document.getElementById(ids.search);\r\n        const counterElement = document.getElementById(ids.counter);\r\n\r\n        // 搜索功能变量\r\n        let matches = [];\r\n        let currentMatchIndex = -1;\r\n\r\n        // 如果有搜索框，添加搜索功能\r\n        if (searchInput && counterElement) {\r\n          // 高亮匹配项函数\r\n          const highlightMatches = (searchTerm) => {\r\n            // 重置匹配\r\n            matches = [];\r\n            currentMatchIndex = -1;\r\n\r\n            // 清除计数器\r\n            counterElement.textContent = '';\r\n\r\n            if (!searchTerm) return;\r\n\r\n            try {\r\n              // 查找所有键和值节点\r\n              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');\r\n\r\n              // 创建正则表达式\r\n              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\r\n\r\n              // 移除之前的高亮\r\n              document.querySelectorAll('.vjs-search-match').forEach(el => {\r\n                el.classList.remove('vjs-search-match');\r\n              });\r\n\r\n              document.querySelectorAll('.vjs-search-current').forEach(el => {\r\n                el.classList.remove('vjs-search-current');\r\n              });\r\n\r\n              // 查找匹配项\r\n              jsonNodes.forEach(node => {\r\n                const text = node.textContent;\r\n                let match;\r\n                regex.lastIndex = 0;\r\n\r\n                while ((match = regex.exec(text)) !== null) {\r\n                  matches.push({\r\n                    node: node,\r\n                    text: match[0]\r\n                  });\r\n\r\n                  // 防止无限循环\r\n                  if (match.index === regex.lastIndex) {\r\n                    regex.lastIndex++;\r\n                  }\r\n                }\r\n              });\r\n\r\n              // 更新计数器\r\n              if (matches.length === 0) {\r\n                counterElement.textContent = '无匹配项';\r\n                return;\r\n              }\r\n\r\n              counterElement.textContent = `0/${matches.length}`;\r\n\r\n              // 为所有匹配项添加高亮类\r\n              matches.forEach(match => {\r\n                match.node.classList.add('vjs-search-match');\r\n              });\r\n            } catch (error) {\r\n              console.error('搜索错误:', error);\r\n            }\r\n          };\r\n\r\n          // 导航到特定匹配项\r\n          const navigateToMatch = (index) => {\r\n            if (matches.length === 0) return;\r\n\r\n            // 确保索引在有效范围内\r\n            index = Math.max(0, Math.min(matches.length - 1, index));\r\n\r\n            // 移除之前匹配项的当前高亮\r\n            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {\r\n              matches[currentMatchIndex].node.classList.remove('vjs-search-current');\r\n            }\r\n\r\n            // 更新当前索引和计数器\r\n            currentMatchIndex = index;\r\n            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;\r\n\r\n            // 高亮当前匹配项\r\n            const currentMatch = matches[currentMatchIndex];\r\n            if (currentMatch) {\r\n              currentMatch.node.classList.add('vjs-search-current');\r\n\r\n              // 确保父节点是展开的\r\n              let parent = currentMatch.node.parentElement;\r\n              while (parent) {\r\n                if (parent.classList && parent.classList.contains('vjs-tree-node')) {\r\n                  // 如果节点是折叠的，点击展开按钮\r\n                  if (!parent.classList.contains('is-expanded')) {\r\n                    const expandBtn = parent.querySelector('.vjs-tree-brackets');\r\n                    if (expandBtn) expandBtn.click();\r\n                  }\r\n                }\r\n                parent = parent.parentElement;\r\n              }\r\n\r\n              // 滚动到匹配项\r\n              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          };\r\n\r\n          // 添加搜索输入事件\r\n          let searchTimeout;\r\n          searchInput.addEventListener('input', (e) => {\r\n            // 使用防抖优化性能\r\n            if (searchTimeout) clearTimeout(searchTimeout);\r\n            searchTimeout = setTimeout(() => {\r\n              highlightMatches(e.target.value.trim());\r\n            }, 300);\r\n          });\r\n\r\n          // 添加键盘导航事件\r\n          searchInput.addEventListener('keydown', (e) => {\r\n            if (e.key === 'Enter') {\r\n              e.preventDefault();\r\n              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加主题切换功能\r\n        const themeButton = document.getElementById(ids.theme);\r\n        if (themeButton) {\r\n          themeButton.addEventListener('click', () => {\r\n            const container = document.querySelector('.json-container');\r\n            if (container) {\r\n              // 切换主题类\r\n              if (container.classList.contains('theme-light')) {\r\n                container.classList.remove('theme-light');\r\n                container.classList.add('theme-dark');\r\n                container.style.backgroundColor = '#1e1e1e';\r\n                isDarkTheme = true;\r\n              } else {\r\n                container.classList.remove('theme-dark');\r\n                container.classList.add('theme-light');\r\n                container.style.backgroundColor = '#fff';\r\n                isDarkTheme = false;\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加复制功能\r\n        const copyButton = document.getElementById('copy-btn');\r\n        if (copyButton) {\r\n          copyButton.addEventListener('click', () => {\r\n            try {\r\n              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);\r\n\r\n              // 使用现代Clipboard API\r\n              if (navigator.clipboard && window.isSecureContext) {\r\n                navigator.clipboard.writeText(textToCopy)\r\n                  .then(() => {\r\n                    this.$message.success(this.$t('common.copiedToClipboard'));\r\n                  })\r\n                  .catch(err => {\r\n                    console.error('复制失败:', err);\r\n                    this.$message.error('复制失败');\r\n                  });\r\n              } else {\r\n                // 备用方法\r\n                const textArea = document.createElement('textarea');\r\n                textArea.value = textToCopy;\r\n                document.body.appendChild(textArea);\r\n                textArea.select();\r\n                const successful = document.execCommand('copy');\r\n                document.body.removeChild(textArea);\r\n\r\n                if (successful) {\r\n                  this.$message.success(this.$t('common.copiedToClipboard'));\r\n                } else {\r\n                  this.$message.error(this.$t('common.copyFailed'));\r\n                }\r\n              }\r\n            } catch (err) {\r\n              this.$message.error(this.$t('common.copyFailed'));\r\n            }\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 覆盖详情弹窗的样式 */\r\n.detail-modal {\r\n  .ant-modal-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-content {\r\n    margin-top: 12px !important;\r\n    margin-bottom: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-btns {\r\n    margin-top: 16px !important;\r\n    margin-bottom: 8px !important;\r\n  }\r\n\r\n  /* 复制按钮样式 */\r\n  #copy-btn {\r\n    transition: all 0.3s;\r\n\r\n    &:hover {\r\n      color: #40a9ff !important;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    &:active {\r\n      color: #096dd9 !important;\r\n    }\r\n  }\r\n\r\n  /* 搜索匹配项样式 */\r\n  .vjs-search-match {\r\n    background-color: rgba(255, 255, 0, 0.3);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .vjs-search-current {\r\n    background-color: rgba(255, 165, 0, 0.6);\r\n    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);\r\n  }\r\n\r\n  /* 主题样式 */\r\n  .json-container.theme-dark {\r\n    .vjs-tree {\r\n      background-color: #1e1e1e !important;\r\n      color: #d4d4d4 !important;\r\n\r\n      .vjs-key {\r\n        color: #9cdcfe !important; /* 浅蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-string {\r\n        color: #ce9178 !important; /* 橙红色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-number {\r\n        color: #b5cea8 !important; /* 浅绿色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-boolean {\r\n        color: #569cd6 !important; /* 蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-null {\r\n        color: #c586c0 !important; /* 紫色 */\r\n      }\r\n\r\n      .vjs-tree-brackets {\r\n        color: #d4d4d4 !important; /* 浅灰色 */\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 调整内容区域的左侧边距 */\r\n.ant-modal-confirm-body {\r\n  padding-left: 12px !important;\r\n}\r\n\r\n/* 调整内容区域的图标和文本间距 */\r\n.ant-modal-confirm-body > .anticon {\r\n  display: none !important;\r\n}\r\n\r\n/* 调整标题和内容的左侧边距 */\r\n.ant-modal-confirm-body > .ant-modal-confirm-title,\r\n.ant-modal-confirm-body > .ant-modal-confirm-content {\r\n  margin-left: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n/* 调整内容区域的下边距 */\r\n.detail-modal .ant-modal-confirm-content {\r\n  margin-bottom: 8px !important; /* 适当的下边距 */\r\n}\r\n\r\n/* 调整底部按钮区域的上下边距 */\r\n.detail-modal .ant-modal-confirm-btns {\r\n  margin-top: 8px !important; /* 减小上边距 */\r\n  margin-bottom: 4px !important;\r\n  padding-top: 0 !important; /* 移除上内边距 */\r\n  padding-bottom: 0 !important;\r\n  border-top: none !important; /* 移除分隔线 */\r\n}\r\n\r\n/* 调整底部按钮本身的样式 */\r\n.detail-modal .ant-modal-confirm-btns button {\r\n  margin-top: 0 !important;\r\n  margin-bottom: 0 !important;\r\n  padding: 6px 16px !important;\r\n  height: auto !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./JsonDetailModal.vue?vue&type=template&id=2fcc9902\"\nimport script from \"./JsonDetailModal.vue?vue&type=script&lang=js\"\nexport * from \"./JsonDetailModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./JsonDetailModal.vue?vue&type=style&index=0&id=2fcc9902&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('KubernetesInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full kubernetes-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"32\",\"height\":\"32\",\"viewBox\":\"0 0 32 32\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.k8s')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchActiveTabData}})],1)])]},proxy:true}])},[_c('JsonDetailModal',{ref:\"jsonDetailModal\"}),_c('a-tabs',{attrs:{\"default-active-key\":\"k8s_api_server\"},on:{\"change\":_vm.handleTabChange}},[_c('a-tab-pane',{key:\"k8s_api_server\",attrs:{\"tab\":\"API Servers\"}},[(_vm.activeTab === 'k8s_api_server')?_c('a-table',{attrs:{\"columns\":_vm.apiServerColumns,\"data-source\":_vm.apiServerData,\"rowKey\":(record) => record.address,\"pagination\":_vm.pagination,\"loading\":_vm.loadingApiServers}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_ingress\",attrs:{\"tab\":\"Ingresses\"}},[(_vm.activeTab === 'k8s_ingress')?_c('a-table',{attrs:{\"columns\":_vm.ingressColumns,\"data-source\":_vm.ingressData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingIngresses}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_gateway\",attrs:{\"tab\":\"Gateways\"}},[(_vm.activeTab === 'k8s_gateway')?_c('a-table',{attrs:{\"columns\":_vm.gatewayColumns,\"data-source\":_vm.gatewayData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingGateways}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_virtual_service\",attrs:{\"tab\":\"Virtual Services\"}},[(_vm.activeTab === 'k8s_virtual_service')?_c('a-table',{attrs:{\"columns\":_vm.virtualServiceColumns,\"data-source\":_vm.virtualServiceData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingVirtualServices}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_service\",attrs:{\"tab\":\"Services\"}},[(_vm.activeTab === 'k8s_service')?_c('a-table',{attrs:{\"columns\":_vm.serviceColumns,\"data-source\":_vm.serviceData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingServices}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_network_policy\",attrs:{\"tab\":\"Network Policies\"}},[(_vm.activeTab === 'k8s_network_policy')?_c('a-table',{attrs:{\"columns\":_vm.networkPolicyColumns,\"data-source\":_vm.networkPolicyData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingNetworkPolicies}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_pod\",attrs:{\"tab\":\"Pods\"}},[(_vm.activeTab === 'k8s_pod')?_c('a-table',{attrs:{\"columns\":_vm.podColumns,\"data-source\":_vm.podData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingPods,\"scroll\":{ x: 1500 }}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_node\",attrs:{\"tab\":\"Nodes\"}},[(_vm.activeTab === 'k8s_node')?_c('a-table',{attrs:{\"columns\":_vm.nodeColumns,\"data-source\":_vm.nodeData,\"rowKey\":(record) => record.name,\"pagination\":_vm.pagination,\"loading\":_vm.loadingNodes}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_secret\",attrs:{\"tab\":\"Secrets\"}},[(_vm.activeTab === 'k8s_secret')?_c('a-table',{attrs:{\"columns\":_vm.secretColumns,\"data-source\":_vm.secretData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingSecrets}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_config_map\",attrs:{\"tab\":\"ConfigMaps\"}},[(_vm.activeTab === 'k8s_config_map')?_c('a-table',{attrs:{\"columns\":_vm.configMapColumns,\"data-source\":_vm.configMapData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingConfigMaps}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_role\",attrs:{\"tab\":\"Roles\"}},[(_vm.activeTab === 'k8s_role')?_c('a-table',{attrs:{\"columns\":_vm.roleColumns,\"data-source\":_vm.roleData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingRole}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_role_binding\",attrs:{\"tab\":\"Role Bindings\"}},[(_vm.activeTab === 'k8s_role_binding')?_c('a-table',{attrs:{\"columns\":_vm.roleBindingColumns,\"data-source\":_vm.roleBindingData,\"rowKey\":(record) => `${record.namespace}-${record.name}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingRoleBinding}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_cluster_role\",attrs:{\"tab\":\"Cluster Roles\"}},[(_vm.activeTab === 'k8s_cluster_role')?_c('a-table',{attrs:{\"columns\":_vm.clusterRoleColumns,\"data-source\":_vm.clusterRoleData,\"rowKey\":(record) => record.name,\"pagination\":_vm.pagination,\"loading\":_vm.loadingClusterRole}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_cluster_role_binding\",attrs:{\"tab\":\"Cluster Role Bindings\"}},[(_vm.activeTab === 'k8s_cluster_role_binding')?_c('a-table',{attrs:{\"columns\":_vm.clusterRoleBindingColumns,\"data-source\":_vm.clusterRoleBindingData,\"rowKey\":(record) => record.name,\"pagination\":_vm.pagination,\"loading\":_vm.loadingClusterRoleBinding}}):_vm._e()],1),_c('a-tab-pane',{key:\"k8s_serviceaccount_permissions\",attrs:{\"tab\":\"ServiceAccount Perms\"}},[(_vm.activeTab === 'k8s_serviceaccount_permissions')?_c('a-table',{attrs:{\"columns\":_vm.serviceAccountPermissionsColumns,\"data-source\":_vm.serviceAccountPermissionsData,\"rowKey\":(record) => `${record.namespace}-${record.pod_name}-${record.service_account}`,\"pagination\":_vm.pagination,\"loading\":_vm.loadingServiceAccountPermissions,\"scroll\":{ x: 1200 }}}):_vm._e()],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full kubernetes-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.k8s') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs default-active-key=\"k8s_api_server\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"k8s_api_server\" tab=\"API Servers\">\r\n        <a-table\r\n          :columns=\"apiServerColumns\"\r\n          :data-source=\"apiServerData\"\r\n          :rowKey=\"(record) => record.address\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_api_server'\"\r\n          :loading=\"loadingApiServers\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_ingress\" tab=\"Ingresses\">\r\n        <a-table\r\n          :columns=\"ingressColumns\"\r\n          :data-source=\"ingressData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_ingress'\"\r\n          :loading=\"loadingIngresses\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_gateway\" tab=\"Gateways\">\r\n        <a-table\r\n          :columns=\"gatewayColumns\"\r\n          :data-source=\"gatewayData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_gateway'\"\r\n          :loading=\"loadingGateways\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_virtual_service\" tab=\"Virtual Services\">\r\n        <a-table\r\n          :columns=\"virtualServiceColumns\"\r\n          :data-source=\"virtualServiceData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_virtual_service'\"\r\n          :loading=\"loadingVirtualServices\"\r\n        >\r\n\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_service\" tab=\"Services\">\r\n        <a-table\r\n          :columns=\"serviceColumns\"\r\n          :data-source=\"serviceData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_service'\"\r\n          :loading=\"loadingServices\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_network_policy\" tab=\"Network Policies\">\r\n        <a-table\r\n          :columns=\"networkPolicyColumns\"\r\n          :data-source=\"networkPolicyData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_network_policy'\"\r\n          :loading=\"loadingNetworkPolicies\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_pod\" tab=\"Pods\">\r\n        <a-table\r\n          :columns=\"podColumns\"\r\n          :data-source=\"podData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_pod'\"\r\n          :loading=\"loadingPods\"\r\n          :scroll=\"{ x: 1500 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_node\" tab=\"Nodes\">\r\n        <a-table\r\n          :columns=\"nodeColumns\"\r\n          :data-source=\"nodeData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_node'\"\r\n          :loading=\"loadingNodes\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_secret\" tab=\"Secrets\">\r\n        <a-table\r\n          :columns=\"secretColumns\"\r\n          :data-source=\"secretData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_secret'\"\r\n          :loading=\"loadingSecrets\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_config_map\" tab=\"ConfigMaps\">\r\n        <a-table\r\n          :columns=\"configMapColumns\"\r\n          :data-source=\"configMapData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_config_map'\"\r\n          :loading=\"loadingConfigMaps\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role\" tab=\"Roles\">\r\n        <a-table\r\n          :columns=\"roleColumns\"\r\n          :data-source=\"roleData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role'\"\r\n          :loading=\"loadingRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role_binding\" tab=\"Role Bindings\">\r\n        <a-table\r\n          :columns=\"roleBindingColumns\"\r\n          :data-source=\"roleBindingData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.name}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role_binding'\"\r\n          :loading=\"loadingRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role\" tab=\"Cluster Roles\">\r\n        <a-table\r\n          :columns=\"clusterRoleColumns\"\r\n          :data-source=\"clusterRoleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role'\"\r\n          :loading=\"loadingClusterRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role_binding\" tab=\"Cluster Role Bindings\">\r\n        <a-table\r\n          :columns=\"clusterRoleBindingColumns\"\r\n          :data-source=\"clusterRoleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role_binding'\"\r\n          :loading=\"loadingClusterRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_serviceaccount_permissions\" tab=\"ServiceAccount Perms\">\r\n        <a-table\r\n          :columns=\"serviceAccountPermissionsColumns\"\r\n          :data-source=\"serviceAccountPermissionsData\"\r\n          :rowKey=\"(record) => `${record.namespace}-${record.pod_name}-${record.service_account}`\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_serviceaccount_permissions'\"\r\n          :loading=\"loadingServiceAccountPermissions\"\r\n          :scroll=\"{ x: 1200 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    JsonDetailModal\r\n  },\r\n  name: 'KubernetesInfo',\r\n  data() {\r\n    return {\r\n      activeTab: 'k8s_api_server',\r\n      apiServerData: [],\r\n      ingressData: [],\r\n      gatewayData: [],\r\n      virtualServiceData: [],\r\n      serviceData: [],\r\n      networkPolicyData: [],\r\n      podData: [],\r\n      nodeData: [],\r\n      secretData: [],\r\n      configMapData: [],\r\n      roleData: [],\r\n      roleBindingData: [],\r\n      clusterRoleData: [],\r\n      clusterRoleBindingData: [],\r\n      serviceAccountPermissionsData: [],\r\n      loadingApiServers: false,\r\n      loadingIngresses: false,\r\n      loadingGateways: false,\r\n      loadingVirtualServices: false,\r\n      loadingServices: false,\r\n      loadingNetworkPolicies: false,\r\n      loadingPods: false,\r\n      loadingNodes: false,\r\n      loadingSecrets: false,\r\n      loadingConfigMaps: false,\r\n      loadingRole: false,\r\n      loadingRoleBinding: false,\r\n      loadingClusterRole: false,\r\n      loadingClusterRoleBinding: false,\r\n      loadingServiceAccountPermissions: false,\r\n      apiServerColumns: [\r\n        { title: 'Node ID', dataIndex: 'node_id', key: 'node_id' },\r\n        { title: 'Address', dataIndex: 'address', key: 'address' },\r\n      ],\r\n      ingressColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'ingress_status',\r\n          key: 'ingress_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Ingress Status')\r\n        },\r\n      ],\r\n      gatewayColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'gateway_status',\r\n          key: 'gateway_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Status')\r\n        },\r\n      ],\r\n      virtualServiceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'virtual_service_status',\r\n          key: 'virtual_service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Status')\r\n        },\r\n      ],\r\n      serviceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'service_status',\r\n          key: 'service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Service Status')\r\n        },\r\n      ],\r\n      networkPolicyColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n      ],\r\n      podColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Pod Status')\r\n        },\r\n      ],\r\n      nodeColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Node Status')\r\n        },\r\n      ],\r\n      secretColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Data')\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'secret_type',\r\n          key: 'secret_type',\r\n          width: 120\r\n        },\r\n      ],\r\n      configMapColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'ConfigMap Data')\r\n        },\r\n      ],\r\n      roleColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      roleBindingColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      clusterRoleColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Aggregation Rule',\r\n          dataIndex: 'aggregationRule',\r\n          key: 'aggregationRule',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Aggregation Rule')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      clusterRoleBindingColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      serviceAccountPermissionsColumns: [\r\n        {\r\n          title: 'Pod Name',\r\n          dataIndex: 'pod_name',\r\n          key: 'pod_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Service Account',\r\n          dataIndex: 'service_account',\r\n          key: 'service_account',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Permissions',\r\n          dataIndex: 'permissions',\r\n          key: 'permissions',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            try {\r\n              const permissions = Array.isArray(text) ? text : (text ? JSON.parse(text) : []);\r\n\r\n              let displayText = 'No permissions';\r\n              if (permissions && permissions.length > 0) {\r\n                const summary = permissions.map(p => `${p.type}: ${p.name}`).join(', ');\r\n                displayText = summary.length > 50 ? summary.slice(0, 50) + '...' : summary;\r\n              }\r\n\r\n              return (\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n                  <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">\r\n                    {displayText}\r\n                  </span>\r\n                  <a-button\r\n                    type=\"link\"\r\n                    style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n                    onClick={() => this.showDetailModal('ServiceAccount Permissions', permissions)}\r\n                  >\r\n                    View\r\n                  </a-button>\r\n                </div>\r\n              );\r\n            } catch (error) {\r\n              console.error('Error parsing permissions:', error);\r\n              return 'Error parsing data';\r\n            }\r\n          }\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      initialLoad: true,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      this.initialLoad = true;\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      this[`loading${this.capitalizeFirstLetter(resourceType)}`] = true;\r\n      try {\r\n        let response;\r\n        response = await axios.get(`/api/k8s/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = [];\r\n      } finally {\r\n        this[`loading${this.capitalizeFirstLetter(resourceType)}`] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutK8s = camelCase.replace('k8s_', '');\r\n\r\n      // 特殊处理 serviceaccount_permissions\r\n      if (withoutK8s === 'serviceaccount_permissions') {\r\n        return 'serviceAccountPermissionsData';\r\n      }\r\n\r\n      const camelized = withoutK8s.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\r\n      return camelized + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.apiServerData = [];\r\n      this.ingressData = [];\r\n      this.gatewayData = [];\r\n      this.virtualServiceData = [];\r\n      this.serviceData = [];\r\n      this.networkPolicyData = [];\r\n      this.podData = [];\r\n      this.nodeData = [];\r\n      this.secretData = [];\r\n      this.configMapData = [];\r\n      this.roleData = [];\r\n      this.roleBindingData = [];\r\n      this.clusterRoleData = [];\r\n      this.clusterRoleBindingData = [];\r\n      this.serviceAccountPermissionsData = [];\r\n    },\r\n    renderComplexData(text, title) {\r\n      if (!text || text === 'None') return '-';\r\n\r\n      // 简化显示文本生成逻辑\r\n      const displayText = Array.isArray(text) ? `Array(${text.length})` :\r\n                         typeof text === 'object' && text !== null ?\r\n                           Object.entries(text).slice(0, 2).map(([k, v]) =>\r\n                             `${k}: ${typeof v === 'object' ? JSON.stringify(v).substring(0, 15) : v}`\r\n                           ).join(', ') + (Object.keys(text).length > 2 ? '...' : '') :\r\n                           String(text).length > 50 ? String(text).slice(0, 50) + '...' : String(text);\r\n\r\n      return (\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; padding-right: 8px;\">\r\n          <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">{displayText}</span>\r\n          <a-button\r\n            type=\"link\"\r\n            style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n            onClick={() => this.showDetailModal(title, text)}\r\n          >\r\n            View\r\n          </a-button>\r\n        </div>\r\n      );\r\n    },\r\n\r\n    showDetailModal(title, data) {\r\n      // 使用JsonDetailModal组件的showDetailModal方法\r\n      this.$refs.jsonDetailModal.showDetailModal(title, data);\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.kubernetes-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n\r\n\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./KubernetesInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./KubernetesInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./KubernetesInfo.vue?vue&type=template&id=656af809&scoped=true\"\nimport script from \"./KubernetesInfo.vue?vue&type=script&lang=js\"\nexport * from \"./KubernetesInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./KubernetesInfo.vue?vue&type=style&index=0&id=656af809&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"656af809\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<KubernetesInfo></KubernetesInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport KubernetesInfo from \"@/components/Cards/KubernetesInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        KubernetesInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Kubernetes.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Kubernetes.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Kubernetes.vue?vue&type=template&id=622442df\"\nimport script from \"./Kubernetes.vue?vue&type=script&lang=js\"\nexport * from \"./Kubernetes.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}