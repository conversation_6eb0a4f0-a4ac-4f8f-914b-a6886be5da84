{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue?vue&type=template&id=7d8e9548&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue", "mtime": 1753170222127}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}