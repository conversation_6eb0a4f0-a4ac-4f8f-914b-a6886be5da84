<template>
    <div>
        <!-- 预留的资料信息页面，暂时无内容 -->
        <a-card :bordered="false" class="header-solid h-full" :bodyStyle="{padding: '24px'}">
            <a-row type="flex" align="middle">
                <a-col :span="24">
                    <h5 class="font-semibold">资料信息</h5>
                    <p>此页面用于资料信息功能，敬请期待！</p>
                </a-col>
            </a-row>
        </a-card>
    </div>
</template>

<script>
export default {
    name: 'MaterialInfo',
    data() {
        return {
        };
    },
};
</script>

<style lang="scss" scoped>
/* 样式可以根据需要添加 */
</style> 