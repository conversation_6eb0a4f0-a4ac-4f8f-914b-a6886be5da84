{"version": 3, "sources": ["webpack:///./src/components/Cards/TestCaseInfo.vue?7f8a", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue", "webpack:///src/components/Widgets/TestCaseDetailModal.vue", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?a55e", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?4c7a", "webpack:///./src/components/Widgets/TestCaseDetailModal.vue?a2b5", "webpack:///./src/views/TestCase.vue", "webpack:///./src/components/Cards/TestCaseInfo.vue", "webpack:///src/components/Cards/TestCaseInfo.vue", "webpack:///./src/components/Cards/TestCaseInfo.vue?0a77", "webpack:///./src/components/Cards/TestCaseInfo.vue?3468", "webpack:///src/views/TestCase.vue", "webpack:///./src/views/TestCase.vue?9fb7", "webpack:///./src/views/TestCase.vue?7989", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "visible", "$t", "on", "handleClose", "currentTestcase", "staticClass", "_v", "_s", "Testcase_Number", "Testcase_Name", "getLevelColor", "Testcase_Level", "Testcase_PrepareCondition", "Testcase_TestSteps", "Testcase_ExpectedResult", "_e", "staticRenderFns", "name", "props", "type", "Boolean", "default", "testcase", "Object", "fetchDetails", "data", "loading", "detailedTestcase", "watch", "newVal", "fetchTestcaseDetails", "computed", "methods", "response", "axios", "get", "similarity", "error", "console", "$message", "$emit", "level", "colors", "getSimilarityColor", "component", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "$event", "fetchTestcases", "currentPage", "proxy", "preventDefault", "handleSearch", "apply", "arguments", "model", "value", "searchForm", "callback", "$$v", "$set", "expression", "staticStyle", "prepare_condition", "test_steps", "expected_result", "resetSearch", "testcases", "length", "total", "columns", "pageSize", "current", "showSizeChanger", "showQuickJumper", "onChange", "handlePageChange", "x", "text", "getResultColor", "formatDate", "record", "viewDetails", "detailsVisible", "selectedTestcase", "components", "RefreshButton", "TestCaseDetailModal", "undefined", "created", "mapState", "h", "$createElement", "title", "dataIndex", "width", "align", "customRender", "index", "ellipsis", "click", "slots", "page", "params", "page_size", "date", "moment", "format", "result", "TestCaseInfo", "String"], "mappings": "kHAAA,W,oCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIK,QAAQ,MAAQL,EAAIM,GAAG,mBAAmB,MAAQ,QAAQ,OAAS,MAAMC,GAAG,CAAC,OAASP,EAAIQ,cAAc,CAAER,EAAIS,gBAAiBP,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAW,KAAK,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,qBAAqB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBI,iBAAiB,SAASX,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,mBAAmB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBK,eAAe,SAASZ,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,oBAAoB,KAAO,IAAI,CAACJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIe,cAAcf,EAAIS,gBAAgBO,kBAAkB,CAAChB,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBO,gBAAgB,QAAQ,GAAGd,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,+BAA+B,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBQ,2BAA2B,SAASf,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,wBAAwB,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBS,oBAAoB,SAAShB,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,6BAA6B,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIS,gBAAgBU,yBAAyB,UAAU,GAAGnB,EAAIoB,MAAM,IAEn9CC,EAAkB,G,YC4CP,GACfC,KAAA,sBACAC,MAAA,CACAlB,QAAA,CACAmB,KAAAC,QACAC,SAAA,GAEAC,SAAA,CACAH,KAAAI,OACAF,QAAA,MAGAG,aAAA,CACAL,KAAAC,QACAC,SAAA,IAGAI,OACA,OACAC,SAAA,EACAC,iBAAA,OAGAC,MAAA,CACA5B,QAAA6B,GACAA,GAAA,KAAAL,cAAA,KAAAF,WAAA,KAAAK,kBACA,KAAAG,wBAGAR,WACA,KAAAK,iBAAA,OAGAI,SAAA,CACA3B,kBACA,YAAAuB,kBAAA,KAAAL,WAGAU,QAAA,CACA,6BACA,QAAAV,UAAA,KAAAA,SAAAd,gBAAA,CAEA,KAAAkB,SAAA,EACA,IACA,MAAAO,QAAAC,OAAAC,IAAA,sBAAAb,SAAAd,iBACA,KAAAmB,iBAAA,IACAM,EAAAR,KAEAW,WAAA,KAAAd,SAAAc,YAEA,MAAAC,GACAC,QAAAD,MAAA,cAAAA,GACA,KAAAE,SAAAF,MAAA,cACA,QACA,KAAAX,SAAA,KAIAvB,cACA,KAAAqC,MAAA,SACA,KAAAb,iBAAA,MAGAjB,cAAA+B,GACA,MAAAC,EAAA,CACA,gBACA,mBACA,kBACA,iBACA,mBACA,SACA,YACA,UACA,WACA,WAEA,OAAAA,EAAAD,IAAA,WAGAE,mBAAAP,GACA,OAAAA,GAAA,aACAA,GAAA,aACA,aChI2W,I,wBCQvWQ,EAAY,eACd,EACAlD,EACAsB,GACA,EACA,KACA,WACA,MAIa,OAAA4B,E,6CCnBf,W,yFCAA,IAAIlD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACQ,YAAY,QAAQN,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,iBAAiB,IAAI,IAAI,IAEzMmB,EAAkB,GCFlBtB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACQ,YAAY,kBAAkB,CAACR,EAAG,SAAS,CAACQ,YAAY,YAAYN,MAAM,CAAC,UAAW,GAAO8C,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACnD,EAAG,MAAM,CAACQ,YAAY,uBAAuB,CAACR,EAAG,MAAM,CAACQ,YAAY,kBAAkB,CAACR,EAAG,MAAM,CAACQ,YAAY,gBAAgB,CAACR,EAAG,MAAM,CAACoD,MAAM,QAAQtD,EAAIuD,aAAenD,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,8PAA8PF,EAAG,KAAK,CAACQ,YAAY,qBAAqB,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIM,GAAG,4BAA4BJ,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACK,GAAG,CAAC,QAAU,SAASiD,GAAQ,OAAOxD,EAAIyD,eAAezD,EAAI0D,kBAAkB,OAAOC,OAAM,MAAS,CAACzD,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACR,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,UAAUG,GAAG,CAAC,OAAS,SAASiD,GAAgC,OAAxBA,EAAOI,iBAAwB5D,EAAI6D,aAAaC,MAAM,KAAMC,cAAc,CAAC7D,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,qBAAqB,CAACJ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,mBAAmB,WAAa,IAAI0D,MAAM,CAACC,MAAOjE,EAAIkE,WAAW5C,KAAM6C,SAAS,SAAUC,GAAMpE,EAAIqE,KAAKrE,EAAIkE,WAAY,OAAQE,IAAME,WAAW,sBAAsB,GAAGpE,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,sBAAsB,CAACJ,EAAG,WAAW,CAACqE,YAAY,CAAC,MAAQ,SAASnE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,oBAAoB,WAAa,IAAI0D,MAAM,CAACC,MAAOjE,EAAIkE,WAAWpB,MAAOqB,SAAS,SAAUC,GAAMpE,EAAIqE,KAAKrE,EAAIkE,WAAY,QAASE,IAAME,WAAW,qBAAqB,CAACpE,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIW,GAAG,aAAaT,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIW,GAAG,aAAaT,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIW,GAAG,aAAaT,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIW,GAAG,aAAaT,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIW,GAAG,cAAc,IAAI,GAAGT,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,iCAAiC,CAACJ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,+BAA+B,WAAa,IAAI0D,MAAM,CAACC,MAAOjE,EAAIkE,WAAWM,kBAAmBL,SAAS,SAAUC,GAAMpE,EAAIqE,KAAKrE,EAAIkE,WAAY,oBAAqBE,IAAME,WAAW,mCAAmC,GAAGpE,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,0BAA0B,CAACJ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,wBAAwB,WAAa,IAAI0D,MAAM,CAACC,MAAOjE,EAAIkE,WAAWO,WAAYN,SAAS,SAAUC,GAAMpE,EAAIqE,KAAKrE,EAAIkE,WAAY,aAAcE,IAAME,WAAW,4BAA4B,GAAGpE,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIM,GAAG,+BAA+B,CAACJ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAcJ,EAAIM,GAAG,6BAA6B,WAAa,IAAI0D,MAAM,CAACC,MAAOjE,EAAIkE,WAAWQ,gBAAiBP,SAAS,SAAUC,GAAMpE,EAAIqE,KAAKrE,EAAIkE,WAAY,kBAAmBE,IAAME,WAAW,iCAAiC,GAAGpE,EAAG,cAAc,CAACA,EAAG,WAAW,CAACoD,MAAM,MAAMtD,EAAIuD,aAAegB,YAAY,CAAC,MAAQ,SAASnE,MAAM,CAAC,YAAY,SAAS,QAAUJ,EAAI+B,UAAU,CAAC7B,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIM,GAAG,0BAA0B,MAAM,GAAGJ,EAAG,WAAW,CAACqE,YAAY,CAAC,cAAc,OAAOhE,GAAG,CAAC,MAAQP,EAAI2E,cAAc,CAACzE,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIM,GAAG,yBAAyB,MAAM,IAAI,IAAI,GAAIN,EAAI4E,UAAUC,OAAS,EAAG3E,EAAG,MAAM,CAACQ,YAAY,uBAAuB,CAACR,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAG,UAAUX,EAAIY,GAAGZ,EAAI8E,OAAO,kBAAkB,GAAG9E,EAAIoB,MAAM,GAAGlB,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI+E,QAAQ,cAAc/E,EAAI4E,UAAU,QAAU5E,EAAI+B,QAAQ,WAAa,CACtuH+C,MAAO9E,EAAI8E,MACXE,SAAU,IACVC,QAASjF,EAAI0D,YACbwB,iBAAiB,EACjBC,iBAAiB,EACjBC,SAAUpF,EAAIqF,kBACd,OAAS,CAAEC,EAAG,OAAQpC,YAAYlD,EAAImD,GAAG,CAAC,CAACC,IAAI,sBAAsBC,GAAG,UAAS,KAAEkC,IAAQ,MAAO,CAACrF,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIwF,eAAeD,KAAQ,CAACvF,EAAIW,GAAG,IAAIX,EAAIY,GAAG2E,GAAQ,OAAO,UAAU,CAACnC,IAAI,iBAAiBC,GAAG,UAAS,KAAEkC,IAAQ,MAAO,CAACrF,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIe,cAAcwE,KAAQ,CAACvF,EAAIW,GAAG,IAAIX,EAAIY,GAAG2E,GAAQ,OAAO,UAAU,CAACnC,IAAI,eAAeC,GAAG,UAAS,KAAEkC,IAAQ,MAAO,CAACvF,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIyF,WAAWF,IAAO,QAAQ,CAACnC,IAAI,SAASC,GAAG,UAAS,OAAEqC,IAAU,MAAO,CAACxF,EAAG,UAAU,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQG,GAAG,CAAC,MAAQ,SAASiD,GAAQ,OAAOxD,EAAI2F,YAAYD,MAAW,CAAC1F,EAAIW,GAAG,qBAAqB,UAAUT,EAAG,sBAAsB,CAACE,MAAM,CAAC,QAAUJ,EAAI4F,eAAe,SAAW5F,EAAI6F,kBAAkBtF,GAAG,CAAC,MAAQ,SAASiD,GAAQxD,EAAI4F,gBAAiB,OAAW,IAAI,IAEvyBvE,EAAkB,G,qEC4GP,GACfyE,WAAA,CACAC,qBACAC,4BAEA1E,KAAA,YACAQ,OACA,OACAC,SAAA,EACA6C,UAAA,GACAE,MAAA,EACApB,YAAA,EACAkC,gBAAA,EACAC,iBAAA,KACA3B,WAAA,CACA5C,KAAA,GACAwB,WAAAmD,EACAzB,kBAAA,GACAC,WAAA,GACAC,gBAAA,MAIAwB,UACA,KAAAzC,kBAEArB,SAAA,IACA+D,eAAA,oDACApB,UAAA,MAAAqB,EAAA,KAAAC,eACA,OACA,CACAC,MAAA,IACAC,UAAA,QACAnD,IAAA,QACAoD,MAAA,IACAC,MAAA,SACAC,cAAAnB,EAAAG,EAAAiB,IACA,UAAAjD,YAAA,GAAAiD,EAAA,GAGA,CACAL,MAAA,KAAAhG,GAAA,qBACAiG,UAAA,kBACAnD,IAAA,kBACAoD,MAAA,IACAI,UAAA,EACAF,cAAAnB,EAAAG,IACAU,EAAA,eAAAS,IAAA,KAAAlB,YAAAD,IAAA,2CAAAH,KAGA,CACAe,MAAA,KAAAhG,GAAA,mBACAiG,UAAA,gBACAnD,IAAA,gBACAoD,MAAA,KAGA,CACAF,MAAA,KAAAhG,GAAA,oBACAiG,UAAA,iBACAnD,IAAA,iBACAoD,MAAA,IACAM,MAAA,CAAAJ,aAAA,mBAEA,CACAJ,MAAA,KAAAhG,GAAA,+BACAiG,UAAA,4BACAnD,IAAA,4BACAoD,MAAA,IACAI,UAAA,GAEA,CACAN,MAAA,KAAAhG,GAAA,wBACAiG,UAAA,qBACAnD,IAAA,qBACAoD,MAAA,IACAI,UAAA,GAEA,CACAN,MAAA,KAAAhG,GAAA,6BACAiG,UAAA,0BACAnD,IAAA,0BACAoD,MAAA,IACAI,UAAA,MAKAvE,QAAA,CACA,qBAAA0E,EAAA,GACA,KAAAhF,SAAA,EACA,IAEA,MAAAiF,EAAA,CACAD,OACAE,UAAA,KAIA,KAAA/C,WAAA5C,OAAA0F,EAAA1F,KAAA,KAAA4C,WAAA5C,MACA,KAAA4C,WAAApB,QAAAkE,EAAAlE,MAAA,KAAAoB,WAAApB,OACA,KAAAoB,WAAAM,oBAAAwC,EAAAxC,kBAAA,KAAAN,WAAAM,mBACA,KAAAN,WAAAO,aAAAuC,EAAAvC,WAAA,KAAAP,WAAAO,YACA,KAAAP,WAAAQ,kBAAAsC,EAAAtC,gBAAA,KAAAR,WAAAQ,iBAEA,MAAApC,QAAAC,OAAAC,IAAA,kBAAAwE,WACA,KAAApC,UAAAtC,EAAAR,UACA,KAAAgD,MAAAxC,EAAAR,KAAAgD,MACA,MAAApC,GACAC,QAAAD,MAAA,4BAAAA,GACA,KAAAE,SAAAF,MAAA,6BACA,QACA,KAAAX,SAAA,IAKA8B,eACA,KAAAH,YAAA,EACA,KAAAD,eAAA,IAIAkB,cACA,KAAAT,WAAA,CACA5C,KAAA,GACAwB,WAAAmD,EACAzB,kBAAA,GACAC,WAAA,GACAC,gBAAA,IAEA,KAAAhB,YAAA,EACA,KAAAD,eAAA,IAEAgC,WAAAyB,GACA,OAAAA,EAAAC,IAAAD,GAAAE,OAAA,2BAEA5B,eAAA6B,GACA,MAAAtE,EAAA,CACA,eACA,aACA,kBACA,qBAEA,OAAAA,EAAAsE,IAAA,WAEAtG,cAAA+B,GACA,MAAAC,EAAA,CACA,gBACA,mBACA,kBACA,iBACA,oBAEA,OAAAA,EAAAD,IAAA,WAEA6C,YAAAD,GACA,KAAAG,iBAAAH,EACA,KAAAE,gBAAA,GAEAP,iBAAA0B,GACA,KAAArD,YAAAqD,EACA,KAAAtD,eAAAsD,MCvRoW,I,wBCQhW9D,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACf6C,WAAA,CACAwB,iBCjBiV,ICO7U,EAAY,eACd,EACAvH,EACAsB,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,IAAItB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACoD,MAAM,CAAC,iBAAkB,QAAQtD,EAAIuD,cAAgBnD,MAAM,CAAC,KAAO,UAAUG,GAAG,CAAC,MAAQ,SAASiD,GAAQ,OAAOxD,EAAI6C,MAAM,cAAc,CAAC7C,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIuF,MAAQvF,EAAIM,GAAG,mBAAmB,QAEhRe,EAAkB,G,YCWP,GACfe,SAAA,IACA+D,eAAA,mBAEA7E,KAAA,gBACAC,MAAA,CACAgE,KAAA,CACA/D,KAAA+F,OACA7F,QAAA,MCrBqW,I,YCOjWuB,EAAY,eACd,EACAlD,EACAsB,GACA,EACA,KACA,WACA,MAIa,OAAA4B,E", "file": "static/js/chunk-7dbc10af.8df0027e.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=style&index=0&id=40c4ae3a&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-modal',{attrs:{\"visible\":_vm.visible,\"title\":_vm.$t('testcase.detail'),\"width\":\"800px\",\"footer\":null},on:{\"cancel\":_vm.handleClose}},[(_vm.currentTestcase)?_c('a-descriptions',{attrs:{\"bordered\":\"\"}},[_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.number'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Number)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.name'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Name)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.level'),\"span\":3}},[_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(_vm.currentTestcase.Testcase_Level)}},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_Level)+\" \")])],1),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.prepareCondition'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_PrepareCondition)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.testSteps'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_TestSteps)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":_vm.$t('caseColumn.expectedResult'),\"span\":3}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.currentTestcase.Testcase_ExpectedResult)+\" \")])])],1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <a-modal\n    :visible=\"visible\"\n    :title=\"$t('testcase.detail')\"\n    width=\"800px\"\n    :footer=\"null\"\n    @cancel=\"handleClose\"\n  >\n    <a-descriptions v-if=\"currentTestcase\" bordered>\n      <a-descriptions-item :label=\"$t('caseColumn.number')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_Number }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.name')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_Name }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.level')\" :span=\"3\">\n        <a-tag :color=\"getLevelColor(currentTestcase.Testcase_Level)\">\n          {{ currentTestcase.Testcase_Level }}\n        </a-tag>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.prepareCondition')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_PrepareCondition }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.testSteps')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_TestSteps }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item :label=\"$t('caseColumn.expectedResult')\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ currentTestcase.Testcase_ExpectedResult }}\n        </div>\n      </a-descriptions-item>\n    </a-descriptions>\n  </a-modal>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\n\nexport default {\n  name: 'TestCaseDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    testcase: {\n      type: Object,\n      default: null\n    },\n    // 是否需要通过API获取详细信息（当传入的testcase只有基本信息时）\n    fetchDetails: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      detailedTestcase: null\n    };\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal && this.fetchDetails && this.testcase && !this.detailedTestcase) {\n        this.fetchTestcaseDetails();\n      }\n    },\n    testcase() {\n      this.detailedTestcase = null;\n    }\n  },\n  computed: {\n    currentTestcase() {\n      return this.detailedTestcase || this.testcase;\n    }\n  },\n  methods: {\n    async fetchTestcaseDetails() {\n      if (!this.testcase || !this.testcase.Testcase_Number) return;\n      \n      this.loading = true;\n      try {\n        const response = await axios.get(`/api/testcase/${this.testcase.Testcase_Number}`);\n        this.detailedTestcase = {\n          ...response.data,\n          // 保留原有的相似度信息（如果有的话）\n          similarity: this.testcase.similarity\n        };\n      } catch (error) {\n        console.error('获取测试用例详情失败:', error);\n        this.$message.error('获取测试用例详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    handleClose() {\n      this.$emit('close');\n      this.detailedTestcase = null;\n    },\n    \n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange', \n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n        'P0': 'red',\n        'P1': 'orange',\n        'P2': 'blue', \n        'P3': 'green',\n        'P4': 'gray'\n      };\n      return colors[level] || 'default';\n    },\n    \n    getSimilarityColor(similarity) {\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\n      if (similarity >= 0.6) return '#faad14'; // 橙色\n      return '#f5222d'; // 红色\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.testcase-content {\n  white-space: pre-wrap;\n  word-break: break-word;\n  max-height: 300px;\n  overflow-y: auto;\n  padding: 12px;\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.75);\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCaseDetailModal.vue?vue&type=template&id=59bfc3d1&scoped=true\"\nimport script from \"./TestCaseDetailModal.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseDetailModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./TestCaseDetailModal.vue?vue&type=style&index=0&id=59bfc3d1&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59bfc3d1\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseDetailModal.vue?vue&type=style&index=0&id=59bfc3d1&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('TestCaseInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"layout-content\"},[_c('a-card',{staticClass:\"criclebox\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 448 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.testcase')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":function($event){return _vm.fetchTestcases(_vm.currentPage)}}})],1)])]},proxy:true}])},[_c('div',{staticClass:\"search-form\"},[_c('a-form',{attrs:{\"layout\":\"inline\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.handleSearch.apply(null, arguments)}}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('caseColumn.name')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('caseColumn.name'),\"allowClear\":\"\"},model:{value:(_vm.searchForm.name),callback:function ($$v) {_vm.$set(_vm.searchForm, \"name\", $$v)},expression:\"searchForm.name\"}})],1),_c('a-form-item',{attrs:{\"label\":_vm.$t('caseColumn.level')}},[_c('a-select',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":_vm.$t('caseColumn.level'),\"allowClear\":\"\"},model:{value:(_vm.searchForm.level),callback:function ($$v) {_vm.$set(_vm.searchForm, \"level\", $$v)},expression:\"searchForm.level\"}},[_c('a-select-option',{attrs:{\"value\":\"level 0\"}},[_vm._v(\"Level 0\")]),_c('a-select-option',{attrs:{\"value\":\"level 1\"}},[_vm._v(\"Level 1\")]),_c('a-select-option',{attrs:{\"value\":\"level 2\"}},[_vm._v(\"Level 2\")]),_c('a-select-option',{attrs:{\"value\":\"level 3\"}},[_vm._v(\"Level 3\")]),_c('a-select-option',{attrs:{\"value\":\"level 4\"}},[_vm._v(\"Level 4\")])],1)],1),_c('a-form-item',{attrs:{\"label\":_vm.$t('caseColumn.prepareCondition')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('caseColumn.prepareCondition'),\"allowClear\":\"\"},model:{value:(_vm.searchForm.prepare_condition),callback:function ($$v) {_vm.$set(_vm.searchForm, \"prepare_condition\", $$v)},expression:\"searchForm.prepare_condition\"}})],1),_c('a-form-item',{attrs:{\"label\":_vm.$t('caseColumn.testSteps')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('caseColumn.testSteps'),\"allowClear\":\"\"},model:{value:(_vm.searchForm.test_steps),callback:function ($$v) {_vm.$set(_vm.searchForm, \"test_steps\", $$v)},expression:\"searchForm.test_steps\"}})],1),_c('a-form-item',{attrs:{\"label\":_vm.$t('caseColumn.expectedResult')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('caseColumn.expectedResult'),\"allowClear\":\"\"},model:{value:(_vm.searchForm.expected_result),callback:function ($$v) {_vm.$set(_vm.searchForm, \"expected_result\", $$v)},expression:\"searchForm.expected_result\"}})],1),_c('a-form-item',[_c('a-button',{class:`bg-${_vm.sidebarColor}`,staticStyle:{\"color\":\"white\"},attrs:{\"html-type\":\"submit\",\"loading\":_vm.loading}},[_c('a-icon',{attrs:{\"type\":\"search\"}}),_vm._v(\" \"+_vm._s(_vm.$t('testcase.searchButton'))+\" \")],1),_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},on:{\"click\":_vm.resetSearch}},[_c('a-icon',{attrs:{\"type\":\"reload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('testcase.resetButton'))+\" \")],1)],1)],1),(_vm.testcases.length > 0)?_c('div',{staticClass:\"search-result-count\"},[_c('a-tag',{attrs:{\"color\":\"blue\"}},[_vm._v(\"Found: \"+_vm._s(_vm.total)+\" test cases\")])],1):_vm._e()],1),_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.testcases,\"loading\":_vm.loading,\"pagination\":{\n        total: _vm.total,\n        pageSize: 100,\n        current: _vm.currentPage,\n        showSizeChanger: false,\n        showQuickJumper: true,\n        onChange: _vm.handlePageChange\n      },\"scroll\":{ x: 1500 }},scopedSlots:_vm._u([{key:\"Testcase_LastResult\",fn:function({ text }){return [_c('a-tag',{attrs:{\"color\":_vm.getResultColor(text)}},[_vm._v(\" \"+_vm._s(text || 'N/A')+\" \")])]}},{key:\"Testcase_Level\",fn:function({ text }){return [_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(text)}},[_vm._v(\" \"+_vm._s(text || 'N/A')+\" \")])]}},{key:\"lastModified\",fn:function({ text }){return [_vm._v(\" \"+_vm._s(_vm.formatDate(text))+\" \")]}},{key:\"action\",fn:function({ record }){return [_c('a-space',[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":function($event){return _vm.viewDetails(record)}}},[_vm._v(\" View Details \")])],1)]}}])}),_c('TestCaseDetailModal',{attrs:{\"visible\":_vm.detailsVisible,\"testcase\":_vm.selectedTestcase},on:{\"close\":function($event){_vm.detailsVisible = false}}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item :label=\"$t('caseColumn.name')\">\r\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.level')\">\r\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\r\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\r\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              {{ $t('testcase.searchButton') }}\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('testcase.resetButton') }}\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    columns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n      ];\r\n    },\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCaseInfo.vue?vue&type=template&id=40c4ae3a&scoped=true\"\nimport script from \"./TestCaseInfo.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./TestCaseInfo.vue?vue&type=style&index=0&id=40c4ae3a&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40c4ae3a\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<TestCaseInfo></TestCaseInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport TestCaseInfo from \"@/components/Cards/TestCaseInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        TestCaseInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCase.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCase.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCase.vue?vue&type=template&id=e7339f32\"\nimport script from \"./TestCase.vue?vue&type=script&lang=js\"\nexport * from \"./TestCase.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}