import Vue from 'vue'
import VueRouter from 'vue-router'
import ProjectManager from '../views/ProjectManager.vue'

Vue.use(VueRouter)

let routes = [
	{
		path: '/',
		redirect: '/projects'
	},
	{
		path: '/projects',
		name: 'ProjectManager',
		layout: "simple",
		component: ProjectManager,
	},
	{
		path: '/task',
		name: 'Task',
		layout: "dashboard",
		component: () => import('../views/Task.vue'),
	},
	{
		path: '*',
		component: () => import('../views/404.vue'),
	},
	{
		path: '/process',
		name: 'Process',
		layout: "dashboard",
		component: () => import('../views/Process.vue'),
	},
	{
		path: '/process/:pid',
		name: 'ProcessDetail',
		layout: "dashboard",
		component: () => import('../components/Cards/ProcessDetail.vue'),
	},
	{
		path: '/package',
		name: 'Package',
		layout: "dashboard",
		component: () => import('../views/PackageInfo.vue'),
	},
	{
		path: '/hardware',
		name: 'Hardware',
		layout: "dashboard",
		component: () => import('../views/Hardware.vue'),
	},
	{
		path: '/filesystem',
		name: 'Filesystem',
		layout: "dashboard",
		component: () => import('../views/Filesystem.vue'),
	},
	{
		path: '/port',
		name: 'Port',
		layout: "dashboard",
		component: () => import('../views/Port.vue'),
	},
	{
		path: '/docker',
		name: 'Docker',
		layout: "dashboard",
		component: () => import('../views/Docker.vue'),
	},
	{
		path: '/kubernetes',
		name: 'Kubernetes',
		layout: "dashboard",
		component: () => import('../views/Kubernetes.vue'),
	},
	{
		path: '/code-info',
		name: 'CodeInfo',
		layout: "dashboard",
		component: () => import('../views/CodeInfo.vue'),
	},
	{
		path: '/material-info',
		name: 'MaterialInfo',
		layout: "dashboard",
		component: () => import('../views/MaterialInfo.vue'),
	},
	{
		path: '/config',
		name: 'Config',
		layout: "dashboard",
		component: () => import('../views/Config.vue'),
		props: (route) => ({
			defaultTab: route.hash.replace('#',  '') || 'host'
		})
	},
	{
		path: '/repository',
		name: 'Repository',
		layout: "dashboard",
		component: () => import('../views/Repository.vue'),
	},
	{
		path: '/upload',
		name: 'Upload',
		layout: "dashboard",
		component: () => import('../views/FileUpload.vue'),
	},
	{
		path: '/download',
		name: 'Download',
		layout: "dashboard",
		component: () => import('../views/FileDownload.vue'),
	},
	{
		path: '/aibash',
		name: 'AIBash',
		layout: "dashboard",
		component: () => import('../views/AIBash.vue'),
	},
	{
		path: '/testcase',
		name: 'TestCase',
		layout: "dashboard",
		component: () => import('../views/TestCase.vue'),
	},
	{
		path: '/execute-case',
		name: 'ExecuteCase',
		layout: "dashboard",
		component: () => import('../views/ExecuteCase.vue'),
	},
	{
		path: '/smart-orchestration',
		name: 'SmartOrchestration',
		layout: "dashboard",
		component: () => import('../views/SmartOrchestration.vue'),
	},
	{
		path: '/tools',
		name: 'Tools',
		layout: "dashboard",
		component: () => import('../views/GenerateScript.vue'),
	},
]

// Adding layout property from each route to the meta
// object so it can be accessed later.
function addLayoutToRoute( route, parentLayout = "default" )
{
	route.meta = route.meta || {} ;
	route.meta.layout = route.layout || parentLayout ;

	if( route.children )
	{
		route.children = route.children.map( ( childRoute ) => addLayoutToRoute( childRoute, route.meta.layout ) ) ;
	}
	return route ;
}

routes = routes.map( ( route ) => addLayoutToRoute( route ) ) ;

const router = new VueRouter({
	mode: 'hash',
	base: process.env.BASE_URL,
	routes,
	scrollBehavior (to, from, savedPosition) {
		if ( to.hash ) {
			return {
				selector: to.hash,
				behavior: 'smooth',
			}
		}
		return {
			x: 0,
			y: 0,
			behavior: 'smooth',
		}
	}
})

// 强制第一次导航
router.onReady(() => {
	const currentPath = router.currentRoute.path;
	if (currentPath === '/') {
		router.push('/projects').catch(err => {
			if (err.name !== 'NavigationDuplicated') {
				throw err;
			}
		});
	}
});

// 添加导航守卫
router.beforeEach((to, from, next) => {
	if (to.path  === '/config') {
		const validTabs = ['host', 'cbh']
		const currentTab = to.hash.replace('#',  '')

		if (!validTabs.includes(currentTab))  {
			return next({
				path: '/config',
				hash: '#host',
				replace: true
			})
		}
	}

	const isSamePath = to.path  === from.path
	const isSameHash = to.hash  === from.hash
	const isSameQuery = JSON.stringify(to.query)  === JSON.stringify(from.query)

	// 仅当路径、hash、查询参数完全相同时阻止导航
	if (isSamePath && isSameHash && isSameQuery) {
		next(false)
	} else {
		next()
	}
})

// 添加导航后的回调
router.afterEach((to, from) => {
})

export default router
