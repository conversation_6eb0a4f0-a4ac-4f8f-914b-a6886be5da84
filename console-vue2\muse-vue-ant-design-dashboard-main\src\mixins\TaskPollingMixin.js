/**
 * 任务轮询混入
 * 提供任务轮询相关的通用方法，用于TaskPanel和ToolsPanel组件
 */
import axios from '@/api/axiosInstance';
import { mapState } from 'vuex';

export default {
  computed: {
    ...mapState(['activeTask', 'currentProject']),
    
    // 任务ID
    taskId() {
      return this.activeTask?.task_id;
    },
    
    // 任务进度百分比
    progressPercentage() {
      if (!this.activeTask?.nodes) return 0;
      const nodes = Object.values(this.activeTask.nodes);
      if (nodes.length === 0) return 0;
      const totalProgress = nodes.reduce((sum, node) => sum + (parseInt(node.progress) || 0), 0);
      return Math.round(totalProgress / nodes.length);
    },
    
    // 任务是否正在进行中
    taskInProgress() {
      return this.isProcessing && this.pollInterval;
    },
  },
  
  data() {
    return {
      isProcessing: false,
      pollInterval: null,
      pollDelay: 10000, // 轮询间隔，默认10秒
    };
  },
  
  methods: {
    /**
     * 检查活动任务
     * @param {string} taskType - 任务类型，'task'或'tool'
     * @param {string} apiEndpoint - API端点，例如'task'或'script'
     */
    async checkActiveTask(taskType, apiEndpoint) {
      try {
        const storageKey = `${taskType}Info_${this.currentProject}`;
        const completedKey = `${taskType}Completed_${this.currentProject}`;
        const taskInfo = localStorage.getItem(storageKey);
        const taskCompleted = localStorage.getItem(completedKey);

        if (taskInfo) {
          const { taskId, projectFile } = JSON.parse(taskInfo);

          if (projectFile !== this.currentProject) {
            throw new Error('Task belongs to different project');
          }

          const response = await axios.get(`/api/${apiEndpoint}/${taskId}`);

          if (response.data) {
            this.$store.dispatch('updateTask', response.data);

            if (response.data.nodes) {
              const nodes = Object.values(response.data.nodes);
              const allCompleted = nodes.every(node =>
                ['success', 'failed'].includes(node.status)
              );

              if (!allCompleted && !taskCompleted) {
                this.isProcessing = true;
                this.startPolling(taskId, taskType, apiEndpoint);
              } else if (allCompleted) {
                this.isProcessing = false;
                localStorage.setItem(completedKey, 'true');
              }
            }
          }
        }
      } catch (error) {
        console.error('Error checking active task:', error);
        localStorage.removeItem(`${taskType}Info_${this.currentProject}`);
        localStorage.removeItem(`${taskType}Completed_${this.currentProject}`);
      }
    },

    /**
     * 开始轮询任务状态
     * @param {string} taskId - 任务ID
     * @param {string} taskType - 任务类型，'task'或'tool'
     * @param {string} apiEndpoint - API端点，例如'task'或'script'
     */
    startPolling(taskId, taskType, apiEndpoint) {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }

      const pollStatus = async () => {
        try {
          // 检查轮询是否已停止
          if (!this.pollInterval) {
            console.log('轮询已停止，不再请求数据');
            return;
          }

          // 检查任务是否已标记为完成
          const completedKey = `${taskType}Completed_${this.currentProject}`;
          const taskCompleted = localStorage.getItem(completedKey);
          if (taskCompleted === 'true') {
            console.log('任务已标记为完成，停止轮询');
            this.stopPolling();
            this.isProcessing = false;
            return;
          }

          if (!this.currentProject) {
            throw new Error('No project database selected');
          }

          const response = await axios.get(`/api/${apiEndpoint}/${taskId}`);
          console.log('轮询任务状态:', response.data);

          // 确保任务ID存在
          if (response.data && !response.data.task_id) {
            response.data.task_id = taskId;
          }

          // 更新Vuex中的任务状态
          this.$store.dispatch('updateTask', response.data);

          if (response.data && response.data.nodes) {
            const nodes = Object.values(response.data.nodes);
            
            // 检查是否所有节点都已完成（成功或失败）
            const allCompleted = nodes.every(node =>
              ['success', 'failed'].includes(node.status)
            );
            
            // 检查是否有任何节点失败
            const anyFailed = nodes.some(node => node.status === 'failed' || node.error_detail);

            if (allCompleted) {
              console.log('所有节点已完成处理，停止轮询');
              this.stopPolling();
              this.isProcessing = false;
              localStorage.setItem(completedKey, 'true');

              // 使用混入中的方法添加任务完成通知
              this.addTaskCompletionNotification({
                taskId,
                taskType,
                nodes,
                projectId: this.currentProject,
                templates: this.getNotificationTemplates(taskType, nodes)
              });
            } else if (anyFailed && response.data.status === 'failed') {
              // 如果整个任务状态为失败，也停止轮询
              console.log('任务状态为失败，停止轮询');
              this.stopPolling();
              this.isProcessing = false;
              localStorage.setItem(completedKey, 'true');
              
              // 添加失败通知
              this.addTaskCompletionNotification({
                taskId,
                taskType,
                nodes,
                projectId: this.currentProject,
                templates: {
                  error: `Task failed: ${nodes.filter(node => node.status === 'failed').length} nodes reported failure.`
                }
              });
            }
            
            // 强制更新视图
            this.$forceUpdate();
            
            // 确保TaskProgressCard组件也更新
            this.$nextTick(() => {
              const taskProgressCard = this.$children.find(child => child.$options.name === 'TaskProgressCard');
              if (taskProgressCard) {
                taskProgressCard.$forceUpdate();
              }
            });
          } else if (response.data && response.data.status === 'failed') {
            // 如果响应中只有任务状态且为失败，也停止轮询
            console.log('任务状态为失败，停止轮询');
            this.stopPolling();
            this.isProcessing = false;
            localStorage.setItem(completedKey, 'true');
            
            // 添加失败通知
            this.addTaskCompletionNotification({
              taskId,
              taskType,
              nodes: [],
              projectId: this.currentProject,
              templates: {
                error: `Task failed: The server reported a failure status.`
              }
            });
          }
        } catch (error) {
          console.error('Error polling status:', error);
          if (error.response?.status === 404) {
            this.stopPolling();
            this.isProcessing = false;
          }
        }
      };

      // 立即执行一次
      pollStatus();
      
      // 设置定期轮询
      this.pollInterval = setInterval(pollStatus, this.pollDelay);
      
      // 记录轮询开始时间，用于调试
      console.log(`开始轮询任务 ${taskId}，轮询间隔: ${this.pollDelay}ms`);
    },

    /**
     * 停止轮询
     */
    stopPolling() {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }
    },
    
    /**
     * 获取通知模板
     * @param {string} taskType - 任务类型
     * @param {Array} nodes - 节点数组
     * @returns {Object} 通知模板
     */
    getNotificationTemplates(taskType, nodes) {
      if (taskType === 'tool') {
        return {
          success: `Tool executed successfully on all ${nodes.length} nodes.`,
          error: `${nodes.filter(node => node.status === 'success').length} nodes completed tool execution successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`
        };
      } else {
        return {
          success: `Task completed successfully on all ${nodes.length} nodes.`,
          error: `${nodes.filter(node => node.status === 'success').length} nodes completed successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`
        };
      }
    },
    
    /**
     * 组件销毁前停止轮询
     */
    beforeDestroy() {
      this.stopPolling();
    },
    
    /**
     * 组件停用时停止轮询
     */
    deactivated() {
      this.stopPolling();
    }
  }
};
