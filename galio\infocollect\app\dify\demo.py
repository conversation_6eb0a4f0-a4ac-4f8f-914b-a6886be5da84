import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from dify_client import DifyWorkflowClient

# 配置你的Dify API密钥和基础URL
# 可以通过 get_yaml_settings 方法从 dify.yaml 中获取
client = DifyWorkflowClient(
    api_key="app-VEKrtEdVbvSeZ9zboHRS2Io3",  # 替换为你的实际API KEY
    base_url="http://192.168.18.71/v1",  # 替换为你的Dify实例地址
    app_id="d0c8d179-c56a-493e-86e0-26466f21276a"  # 替换为你的工作流ID
)


# 运行工作流示例
def run_workflow_example():
    print("\n--- 运行工作流示例 ---")
    inputs = {
        "text": "你好，Dify!"
    }
    try:
        outputs = client.run_workflows(inputs=inputs)
        print(f"工作流运行成功，输出: {outputs}")
    except Exception as e:
        print(f"工作流运行失败: {e}")


# 获取工作流日志示例
def get_workflow_logs_example():
    print("\n--- 获取工作流日志示例 ---")
    try:
        has_more, logs = client.get_workflow_logs(page=1)
        if logs:
            for log in logs:
                print(f"日志 ID: {log.get('id')}, 状态: {log.get('status')}")
            print(f"是否还有更多日志: {has_more}")
        else:
            print("没有找到工作流日志。")
    except Exception as e:
        print(f"获取工作流日志失败: {e}")

# 获取单个工作流运行详情示例
def get_workflow_run_detail_example():
    print("\n--- 获取单个工作流运行详情示例 ---")
    # 假设你知道一个工作流的run_id，这里使用一个示例ID，你需要替换为实际的ID
    # run_id = "your_workflow_run_id"
    # try:
    #     inputs, outputs = client.get_workflows_run(workflow_id=run_id)
    #     print(f"工作流运行 {run_id} 的输入: {inputs}")
    #     print(f"工作流运行 {run_id} 的输出: {outputs}")
    # except Exception as e:
    #     print(f"获取工作流运行详情失败: {e}")
    print("请手动提供一个workflow_id来测试此功能。")


if __name__ == "__main__":
    run_workflow_example()
    get_workflow_logs_example()
    get_workflow_run_detail_example()
