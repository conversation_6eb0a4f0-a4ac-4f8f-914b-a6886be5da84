{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/components/Cards/FileTransferProgress.vue", "webpack:///src/components/Cards/FileTransferProgress.vue", "webpack:///./src/components/Cards/FileTransferProgress.vue?d051", "webpack:///./src/components/Cards/FileTransferProgress.vue?a7e0", "webpack:///./src/views/FileUpload.vue", "webpack:///src/views/FileUpload.vue", "webpack:///./src/views/FileUpload.vue?874f", "webpack:///./src/views/FileUpload.vue?44ef", "webpack:///./src/views/FileUpload.vue?78a0", "webpack:///./src/mixins/NotificationMixin.js", "webpack:///./src/components/Cards/FileTransferProgress.vue?4565", "webpack:///./src/components/common/ProxySelector.vue", "webpack:///src/components/common/ProxySelector.vue", "webpack:///./src/components/common/ProxySelector.vue?a770", "webpack:///./src/components/common/ProxySelector.vue?1de0", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.every.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/core-js/internals/array-reduce.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "render", "_vm", "_c", "_self", "staticClass", "staticStyle", "attrs", "title", "overallProgress", "slot", "_v", "_s", "_e", "progressBarStatus", "progressData", "progressColumns", "scopedSlots", "_u", "key", "fn", "text", "record", "error_detail", "time", "type", "message", "staticRenderFns", "name", "props", "String", "required", "taskType", "activeTask", "Object", "default", "formatBytes", "Function", "computed", "h", "$createElement", "columns", "$t", "dataIndex", "width", "ellipsis", "customRender", "colorMap", "color", "status", "bytes_transferred", "file_size", "nodes", "keys", "map", "ip", "node", "host_name", "progress", "speed", "values", "totalProgress", "sum", "Math", "round", "some", "every", "includes", "component", "class", "sidebarColor", "uploading", "on", "handleUpload", "$event", "preventDefault", "apply", "beforeUpload", "fileName", "model", "value", "uploadDir", "callback", "$$v", "expression", "handleProxyChange", "selectedProxyIp", "pageSize", "total", "showSizeChanger", "rowSelection", "activeUploadTask", "uploadResults", "uploadResultsData", "resultColumns", "mixins", "NotificationMixin", "components", "ProxySelector", "FileTransferProgress", "data", "file", "selectedNodes", "pollInterval", "isProcessing", "mapState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "results", "success", "for<PERSON>ach", "push", "failed", "error", "errors", "created", "checkDatabaseStatus", "$store", "dispatch", "taskInfo", "localStorage", "getItem", "currentProject", "taskId", "projectFile", "JSON", "parse", "checkActiveUploadTask", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "mapActions", "maxSize", "size", "$message", "console", "log", "validateUploadPath", "path", "startsWith", "currentDbFile", "$router", "previousTaskInfo", "clearTaskNotificationMark", "e", "formData", "FormData", "append", "stringify", "response", "axios", "post", "encodeURIComponent", "headers", "task_id", "setItem", "removeItem", "startPolling", "_error$response", "messageType", "pollStatus", "Error", "get", "allCompleted", "addTaskCompletionNotification", "projectId", "_error$response2", "setInterval", "taskCompleted", "bytes", "sizes", "i", "floor", "pow", "toFixed", "parseSpeed", "unit", "split", "multiplier", "parseFloat", "$notify", "watch", "handler", "newProject", "oldProject", "immediate", "titles", "templates", "statusMapping", "notificationSent<PERSON>ey", "defaultStatusMapping", "failure", "finalStatusMapping", "successNodes", "filter", "failedNodes", "hasFailures", "notificationTitle", "notificationMessage", "getDefaultErrorTitle", "getDefaultSuccessTitle", "addNotification", "isDetecting", "disabled", "fetchReachableIps", "reachableIps", "selectedIpValue", "_l", "Boolean", "newValue", "$emit", "reachable_ips", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped", "reducer", "noInitial", "accumulator", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "right"], "mappings": "kHACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,oCChB7F,IAAIC,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,YAAY,eAAeC,YAAY,CAAC,aAAa,QAAQC,MAAM,CAAC,MAAQL,EAAIM,QAAQ,MAA0BR,IAAxBE,EAAIO,gBAA+BN,EAAG,WAAW,CAACO,KAAK,SAAS,CAACP,EAAG,OAAO,CAACD,EAAIS,GAAG,qBAAqBT,EAAIU,GAAGV,EAAIO,iBAAiB,SAASP,EAAIW,KAAKV,EAAG,aAAa,CAACG,YAAY,CAAC,gBAAgB,QAAQC,MAAM,CAAC,QAAUL,EAAIO,gBAAgB,OAASP,EAAIY,qBAAqBX,EAAG,UAAU,CAACI,MAAM,CAAC,WAAaL,EAAIa,aAAa,QAAUb,EAAIc,gBAAgB,OAAS,KAAK,YAAa,GAAOC,YAAYf,EAAIgB,GAAG,CAAC,CAACC,IAAI,cAAcC,GAAG,SAASC,EAAMC,GAAQ,MAAO,CAAEA,GAAUA,EAAOC,aAAcpB,EAAG,YAAY,CAACI,MAAM,CAAC,UAAY,YAAY,CAACJ,EAAG,WAAW,CAACO,KAAK,WAAW,CAACP,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGU,EAAOC,aAAaC,SAASrB,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGU,EAAOC,aAAaE,SAAStB,EAAG,IAAI,CAACD,EAAIS,GAAG,YAAYT,EAAIU,GAAGU,EAAOC,aAAaG,cAAcvB,EAAG,SAAS,CAACG,YAAY,CAAC,MAAQ,WAAWC,MAAM,CAAC,KAAO,kBAAkB,GAAGL,EAAIW,aAAa,IAElgCc,EAAkB,GCmCP,G,4DAAA,CACfC,KAAA,kBACAC,MAAA,CACArB,MAAA,CACAiB,KAAAK,OACAC,UAAA,GAEAC,SAAA,CACAP,KAAAK,OACAC,UAAA,GAEAE,WAAA,CACAR,KAAAS,OACAC,QAAA,MAEAC,YAAA,CACAX,KAAAY,SACAN,UAAA,IAGAO,SAAA,CACAtB,kBAAA,MAAAuB,EAAA,KAAAC,eACAC,EAAA,CACA,CACAjC,MAAA,KAAAkC,GAAA,gCACAC,UAAA,KACAxB,IAAA,KACAyB,MAAA,SAEA,CACApC,MAAA,KAAAkC,GAAA,+BACAC,UAAA,YACAxB,IAAA,YACAyB,MAAA,QACAC,UAAA,GAEA,CACArC,MAAA,KAAAkC,GAAA,uBACAC,UAAA,SACAxB,IAAA,SACAyB,MAAA,QACAE,aAAAzB,IACA,MAAA0B,EAAA,CACA,kBACA,sBACA,iBACA,kBACA,oBACA,iBACA,uBAEAC,EAAAD,EAAA1B,IAAA,OACA,OAAAkB,EAAA,eAAAS,UAAA,CAAA3B,MAGA,CACAb,MAAA,KAAAkC,GAAA,yBACAC,UAAA,WACAxB,IAAA,WACAyB,MAAA,QACAE,cAAAzB,EAAAC,IAAAiB,EAAA,OAAAA,EAAA,6BAGAlB,GAAA,OACA,eACA,WAAAC,EAAA2B,OAAA,YACA,WAAA3B,EAAA2B,OAAA,cAAAjD,KAAAuC,EAAA,aAEA,iCACA,KAAAH,YAAAd,EAAA4B,mBAAA,WAAAd,YAAAd,EAAA6B,gBAKA,CACA3C,MAAA,KAAAkC,GAAA,sBACAC,UAAA,QACAxB,IAAA,QACAyB,MAAA,SAEA,CACApC,MAAA,KAAAkC,GAAA,yBACAC,UAAA,YACAxB,IAAA,YACAyB,MAAA,QACAE,aAAAzB,GAAA,KAAAe,YAAAf,IAEA,CACAb,MAAA,KAAAkC,GAAA,6BACAC,UAAA,eACAxB,IAAA,eACAyB,MAAA,OACA3B,YAAA,CAAA6B,aAAA,iBAIA,OAAAL,GAEA1B,eACA,YAAAkB,YAAA,KAAAA,WAAAmB,MACAlB,OAAAmB,KAAA,KAAApB,WAAAmB,OAAAE,IAAAC,IACA,MAAAC,EAAA,KAAAvB,WAAAmB,MAAAG,GACA,OACAA,KACAE,UAAAD,EAAAC,UACAR,OAAAO,EAAAP,OACAS,SAAAF,EAAAE,UAAA,EACAC,MAAAH,EAAAG,OAAA,IACAT,kBAAAM,EAAAN,kBACA3B,aAAAiC,EAAAjC,aACA4B,UAAAK,EAAAL,aAXA,IAeA1C,kBACA,SAAAwB,aAAA,KAAAA,WAAAmB,MAAA,SACA,MAAAA,EAAAlB,OAAA0B,OAAA,KAAA3B,WAAAmB,OACA,OAAAA,EAAArD,OAAA,SAEA,MAAA8D,EAAAT,EAAAzD,OAAA,CAAAmE,EAAAN,IAAAM,GAAAN,EAAAE,UAAA,MACA,OAAAK,KAAAC,MAAAH,EAAAT,EAAArD,SAEAe,oBACA,SAAAmB,aAAA,KAAAA,WAAAmB,MAAA,eAEA,MAAAA,EAAAlB,OAAA0B,OAAA,KAAA3B,WAAAmB,OAAA,IACA,WAAAA,EAAArD,OAAA,SAEAqD,EAAAa,KAAAT,GAAA,WAAAA,EAAAP,QAAA,YACAG,EAAAc,MAAAV,GAAA,wBAAAW,SAAAX,EAAAP,SAAA,UACA,aCvK4W,I,wBCQxWmB,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,OAAAyC,E,oDCnBf,IAAInE,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,CAAC,QAAU,QAAQ,CAACH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACkE,MAAM,QAAQnE,EAAIoE,aAAe/D,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACJ,EAAG,OAAO,CAACI,MAAM,CAAC,KAAO,eAAe,EAAI,8cAA8cJ,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACH,EAAIS,GAAGT,EAAIU,GAAGV,EAAIwC,GAAG,8BAA8BvC,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,QAAUL,EAAIqE,WAAWC,GAAG,CAAC,MAAQtE,EAAIuE,eAAe,CAACvE,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIwC,GAAG,2BAA2B,QAAQ,GAAGvC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,YAAYiE,GAAG,CAAC,OAAS,SAASE,GAAgC,OAAxBA,EAAOC,iBAAwBzE,EAAIuE,aAAaG,MAAM,KAAM9E,cAAc,CAACK,EAAG,cAAc,CAACG,YAAY,CAAC,gBAAgB,OAAOC,MAAM,CAAC,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,WAAW,CAACI,MAAM,CAAC,gBAAgBL,EAAI2E,aAAa,oBAAmB,IAAQ,CAAC1E,EAAG,WAAW,CAACE,YAAY,oBAAoB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,YAAYL,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIwC,GAAG,6BAA6B,MAAM,IAAI,GAAIxC,EAAI4E,SAAU3E,EAAG,OAAO,CAACG,YAAY,CAAC,cAAc,QAAQ,CAACJ,EAAIS,GAAGT,EAAIU,GAAGV,EAAI4E,aAAa5E,EAAIW,MAAM,GAAGV,EAAG,cAAc,CAACG,YAAY,CAAC,gBAAgB,OAAOC,MAAM,CAAC,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,UAAU,CAACI,MAAM,CAAC,YAAcL,EAAIwC,GAAG,+BAA+BqC,MAAM,CAACC,MAAO9E,EAAI+E,UAAWC,SAAS,SAAUC,GAAMjF,EAAI+E,UAAUE,GAAKC,WAAW,gBAAgB,IAAI,IAAI,GAAGjF,EAAG,SAAS,CAACE,YAAY,eAAeE,MAAM,CAAC,KAAO,QAAQ,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,iBAAiB,CAACI,MAAM,CAAC,SAAWL,EAAIqE,WAAWC,GAAG,CAAC,OAAStE,EAAImF,mBAAmBN,MAAM,CAACC,MAAO9E,EAAIoF,gBAAiBJ,SAAS,SAAUC,GAAMjF,EAAIoF,gBAAgBH,GAAKC,WAAW,sBAAsB,IAAI,GAAGjF,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeE,MAAM,CAAC,KAAO,QAAQ,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,UAAU,CAACE,YAAY,uBAAuBE,MAAM,CAAC,WAAaL,EAAIkD,MAAM,QAAUlD,EAAIuC,QAAQ,OAAS,KAAK,WAAa,CAC/sF8C,SAAU,GACVC,MAAOtF,EAAIkD,MAAMrD,OACjB0F,iBAAiB,GACjB,aAAevF,EAAIwF,iBAAiB,IAAI,KAAKvF,EAAG,uBAAuB,CAACI,MAAM,CAAC,MAAQL,EAAIwC,GAAG,6BAA6B,SAAW,SAAS,WAAaxC,EAAIyF,iBAAiB,YAAczF,EAAIkC,eAAgBlC,EAAI0F,cAAezF,EAAG,SAAS,CAACG,YAAY,CAAC,aAAa,QAAQC,MAAM,CAAC,MAAQL,EAAIwC,GAAG,8BAA8B,CAACvC,EAAG,UAAU,CAACI,MAAM,CAAC,WAAaL,EAAI2F,kBAAkB,QAAU3F,EAAI4F,cAAc,OAAS,KAAK,YAAa,MAAU,GAAG5F,EAAIW,MAAM,IAExdc,EAAkB,G,4FCwFP,GACfoE,OAAA,CAAAC,QACAC,WAAA,CACAC,qBACAC,6BAEAC,OACA,OACAC,KAAA,KACAvB,SAAA,GACAwB,cAAA,GACArB,UAAA,sBACAV,WAAA,EACAe,gBAAA,KACArD,WAAA,KACAsE,aAAA,KACAC,cAAA,EACAZ,cAAA,OAGAtD,SAAA,IACAmE,eAAA,8DACAf,eACA,OACAgB,gBAAA,KAAAJ,cACAK,SAAAD,IACA,KAAAJ,cAAAI,KAIAjE,UACA,OACA,CACAjC,MAAA,KAAAkC,GAAA,+BACAC,UAAA,YACAxB,IAAA,aAEA,CACAX,MAAA,KAAAkC,GAAA,gCACAC,UAAA,KACAxB,IAAA,QAIA0E,oBACA,SAAAD,cAAA,SAEA,MAAAgB,EAAA,GAWA,OAVA,KAAAhB,cAAAiB,QAAAC,QAAAvD,IACAqD,EAAAG,KAAA,CAAAxD,KAAAN,OAAA,cAEA,KAAA2C,cAAAoB,OAAAF,QAAAvD,IACAqD,EAAAG,KAAA,CACAxD,KACAN,OAAA,SACAgE,MAAA,KAAArB,cAAAsB,OAAA3D,OAGAqD,GAEAd,gBAAA,MAAAvD,EAAA,KAAAC,eACA,OACA,CAAAhC,MAAA,KAAAkC,GAAA,gCAAAC,UAAA,MACA,CACAnC,MAAA,KAAAkC,GAAA,uBACAC,UAAA,SACAG,aAAAzB,GAAAkB,EAAA,cACA,CAAAS,MAAA,YAAA3B,EAAA,uBACAA,KAIA,CAAAb,MAAA,KAAAkC,GAAA,6BAAAC,UAAA,YAIAwE,UACA,SAAAC,sBACA,OAEA,KAAAC,OAAAC,SAAA,cAEA,MAAAC,EAAAC,aAAAC,QAAA,mBAAAC,gBACA,GAAAH,EAAA,CACA,aAAAI,EAAA,YAAAC,GAAAC,KAAAC,MAAAP,GACAK,IAAA,KAAAF,gBACA,KAAAK,0BAIAC,gBACA,KAAAzB,cACA0B,cAAA,KAAA1B,eAGA2B,QAAA,IACAC,eAAA,qBACAtD,aAAAwB,GACA,MAAA+B,EAAA,WACA,OAAA/B,EAAAgC,KAAAD,GACA,KAAAE,SAAArB,MAAA,oCACA,IAEA,KAAAZ,OACA,KAAAvB,SAAAuB,EAAAzE,MACA,IAGAyD,kBAAA9B,GACAgF,QAAAC,IAAA,oBAAAjF,GACA,KAAA+B,gBAAA/B,GAEAkF,mBAAAC,GACA,OAAAA,EAAAC,WAAA,OAIAD,EAAAvE,SAAA,OAAAuE,EAAAvE,SAAA,OAAAuE,EAAAvE,SAAA,QACA,KAAAmE,SAAArB,MAAA,iDACA,IALA,KAAAqB,SAAArB,MAAA,qCACA,IAQA,qBACA,SAAAZ,OAAA,KAAAC,cAAAvG,SAAA,KAAAuF,gBAEA,YADA,KAAAgD,SAAArB,MAAA,+CAIA,SAAAwB,mBAAA,KAAAxD,WACA,OAIA,MAAA2D,EAAApB,aAAAC,QAAA,kBACA,IAAAmB,EAGA,OAFA,KAAAN,SAAArB,MAAA,4DACA,KAAA4B,QAAA9B,KAAA,aAIA,IACA,KAAAxC,WAAA,EAGA,MAAAuE,EAAAtB,aAAAC,QAAA,mBAAAC,gBACA,GAAAoB,EACA,IACA,aAAAnB,GAAAE,KAAAC,MAAAgB,GACAnB,GACA,KAAAoB,0BAAApB,EAAA,cAAAD,gBAEA,MAAAsB,GACAT,QAAAtB,MAAA,+CAAA+B,GAIA,MAAAC,EAAA,IAAAC,SACAD,EAAAE,OAAA,YAAA9C,MACA4C,EAAAE,OAAA,UAAAtB,KAAAuB,UAAA,KAAA9C,gBACA2C,EAAAE,OAAA,kBAAAlE,WACAgE,EAAAE,OAAA,eAAA7D,iBAEA,MAAA+D,QAAAC,OAAAC,KACA,oCAAAC,mBAAA,KAAA9B,gBACAuB,EACA,CACAQ,QAAA,CACA,wCAKA9B,EAAA0B,EAAAjD,KAAAsD,QACAlC,aAAAmC,QAAA,mBAAAjC,eAAAG,KAAAuB,UAAA,CACAzB,SACAC,YAAA,KAAAF,kBAEAF,aAAAoC,WAAA,4BAAAlC,gBAEA,KAAAmC,aAAAlC,GAEA,MAAAV,GAAA,IAAA6C,EACAvB,QAAAtB,MAAA,gBAAAA,GACA,KAAAvF,QAAA,QAAAgB,GAAA,iCAAAoH,EAAA7C,EAAAoC,gBAAA,IAAAS,GAAA,QAAAA,IAAA1D,YAAA,IAAA0D,OAAA,EAAAA,EAAA7C,UAAAvF,UACA,KAAAqI,YAAA,QACA,KAAAxF,WAAA,IAGA,mBAAAoD,GACA,KAAApB,cACA0B,cAAA,KAAA1B,cAGA,MAAAyD,EAAA,UACA,IACA,SAAAtC,eACA,UAAAuC,MAAA,gCAGA,MAAAZ,QAAAC,OAAAY,IACA,6BAAAvC,YAAA6B,mBAAA,KAAA9B,mBAGA,KAAAL,OAAAC,SAAA,mBAAA+B,EAAAjD,MAEA,MAAA+D,EAAAjI,OAAA0B,OAAAyF,EAAAjD,KAAAhD,OAAAc,MACAV,GAAA,qBAAAW,SAAAX,EAAAP,SAGA,GAAAkH,EAAA,CACAlC,cAAA,KAAA1B,cACA,KAAAhC,WAAA,EACAiD,aAAAmC,QAAA,4BAAAjC,eAAA,QAEA,MAAAtE,EAAAlB,OAAA0B,OAAAyF,EAAAjD,KAAAhD,OAGA,KAAAgH,8BAAA,CACAzC,SACA3F,SAAA,SACAoB,QACAiH,UAAA,KAAA3C,kBAGA,MAAAT,GAAA,IAAAqD,EACA/B,QAAAtB,MAAA,wBAAAA,GACA,eAAAqD,EAAArD,EAAAoC,gBAAA,IAAAiB,OAAA,EAAAA,EAAArH,UACAgF,cAAA,KAAA1B,cACA,KAAAhC,WAAA,EACAiD,aAAAoC,WAAA,mBAAAlC,gBACAF,aAAAoC,WAAA,4BAAAlC,yBAKAsC,IACA,KAAAzD,aAAAgE,YAAAP,EAAA,MAEA,8BACA,IACA,MAAAzC,EAAAC,aAAAC,QAAA,mBAAAC,gBACA8C,EAAAhD,aAAAC,QAAA,4BAAAC,gBAEA,GAAAH,EAAA,CACA,aAAAI,EAAA,YAAAC,GAAAC,KAAAC,MAAAP,GAEA,GAAAK,IAAA,KAAAF,eACA,OAGA,MAAA2B,QAAAC,OAAAY,IACA,6BAAAvC,YAAA6B,mBAAA,KAAA9B,mBAGA,GAAA2B,EAAAjD,KAAA,CACA,KAAAiB,OAAAC,SAAA,mBAAA+B,EAAAjD,MAEA,MAAA+D,EAAAjI,OAAA0B,OAAAyF,EAAAjD,KAAAhD,OAAAc,MACAV,GAAA,qBAAAW,SAAAX,EAAAP,SAGA,GAAAkH,GAAAK,GAGA,GAAAL,EAAA,CACA,KAAA5F,WAAA,EACAiD,aAAAmC,QAAA,4BAAAjC,eAAA,QAEA,MAAAtE,EAAAlB,OAAA0B,OAAAyF,EAAAjD,KAAAhD,OAGA,KAAAgH,8BAAA,CACAzC,SACA3F,SAAA,SACAoB,QACAiH,UAAA,KAAA3C,uBAbA,KAAAnD,WAAA,EACA,KAAAsF,aAAAlC,KAiBA,MAAAV,GACAsB,QAAAtB,MAAA,qCAAAA,KAGA7E,YAAAqI,GACA,IAAAA,EAAA,YACA,MAAAC,EAAA,qBACAC,EAAA5G,KAAA6G,MAAA7G,KAAAyE,IAAAiC,GAAA1G,KAAAyE,IAAA,OACA,UAAAiC,EAAA1G,KAAA8G,IAAA,KAAAF,IAAAG,QAAA,MAAAJ,EAAAC,MAEAI,WAAApH,GACA,IAAAA,GAAA,MAAAA,EAAA,SACA,MAAAqB,EAAAgG,GAAArH,EAAAsH,MAAA,KACAC,EAAA,CACA,QACA,YACA,gBACAF,IAAA,EACA,OAAAG,WAAAnG,GAAAkG,GAEA9D,sBACA,MAAAwB,EAAApB,aAAAC,QAAA,kBACA,QAAAmB,IACA,KAAAwC,QAAAnE,MAAA,CACAzG,MAAA,KAAAkC,GAAA,oBACAhB,QAAA,KAAAgB,GAAA,kCAEA,KAAAmG,QAAA9B,KAAA,cACA,KAKAsE,MAAA,CAEA3D,eAAA,CACA4D,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAAnE,OAAAC,SAAA,yBACA,KAAAf,cACA0B,cAAA,KAAA1B,cAGA,KAAAwB,0BAGA0D,WAAA,KCtamV,I,wBCQ/UrH,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,aAAAyC,E,6CCnBf,W,iFCIe,QACb8D,QAAS,CAYPkC,+BAA8B,OAC5BzC,EAAM,SACN3F,EAAQ,MACRoB,EAAK,UACLiH,EAAS,OACTqB,EAAS,GAAE,UACXC,EAAY,GAAE,cACdC,EAAgB,KAGhB,MAAMC,EAAsB,GAAG7J,aAAoBqI,KAAa1C,IAChE,GAAIH,aAAaC,QAAQoE,GACvB,OAIF,MAAMC,EAAuB,CAC3BjF,QAAS,CAAC,UAAW,aACrBkF,QAAS,CAAC,WAINC,EAAqB,CACzBnF,QAAS,IAAK+E,EAAc/E,SAAW,MAAQiF,EAAqBjF,SACpEkF,QAAS,IAAKH,EAAcG,SAAW,MAAQD,EAAqBC,UAIhEE,EAAe7I,EAAM8I,OAAO1I,GAChCwI,EAAmBnF,QAAQ1C,SAASX,EAAKP,UAAYO,EAAKjC,cAC1DxB,OACIoM,EAAc/I,EAAM8I,OAAO1I,GAC/BwI,EAAmBD,QAAQ5H,SAASX,EAAKP,SAAWO,EAAKjC,cACzDxB,OACIqM,EAAcD,EAAc,EAGlC,IAAIE,EAQAC,EANFD,EADED,EACkBV,EAAOzE,OAASpH,KAAK0M,qBAAqBvK,GAE1C0J,EAAO7E,SAAWhH,KAAK2M,uBAAuBxK,GAMlEsK,EADEF,EACoBT,EAAU1E,OAC9B,GAAGgF,mCAA8CE,kBAE7BR,EAAU9E,SAC9B,OAAOzD,EAAMrD,uCAIjBF,KAAK4M,gBAAgB,CACnBjM,MAAO6L,EACP3K,QAAS4K,EACT7K,KAAM2K,EAAc,QAAU,UAC9BzE,OAAQA,IAIVH,aAAamC,QAAQkC,EAAqB,SAQ5CW,uBAAuBxK,GACrB,MAAM0J,EAAS,CACb,KAAQ,iBACR,OAAU,wBACV,SAAY,0BACZ,KAAQ,4BAEV,OAAOA,EAAO1J,IAAa,uBAQ7BuK,qBAAqBvK,GACnB,MAAM0J,EAAS,CACb,KAAQ,6BACR,OAAU,oCACV,SAAY,sCACZ,KAAQ,wCAEV,OAAOA,EAAO1J,IAAa,mCAS7B+G,0BAA0BpB,EAAQ3F,EAAUqI,GAC1C,MAAMwB,EAAsB,GAAG7J,aAAoBqI,KAAa1C,IAChEH,aAAaoC,WAAWiC,O,oCCzH9B,W,oCCAA,IAAI5L,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,QAAUL,EAAIwM,YAAY,SAAWxM,EAAIyM,UAAUnI,GAAG,CAAC,MAAQtE,EAAI0M,oBAAoB,CAAC1M,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIwC,GAAG,8BAAgC,UAAU,OAAQxC,EAAI2M,aAAa9M,OAAQI,EAAG,WAAW,CAACG,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQC,MAAM,CAAC,YAAcL,EAAIwC,GAAG,2BAA6B,SAAS,SAAWxC,EAAIyM,UAAU5H,MAAM,CAACC,MAAO9E,EAAI4M,gBAAiB5H,SAAS,SAAUC,GAAMjF,EAAI4M,gBAAgB3H,GAAKC,WAAW,oBAAoBlF,EAAI6M,GAAI7M,EAAI2M,cAAc,SAAStJ,GAAI,OAAOpD,EAAG,kBAAkB,CAACgB,IAAIoC,EAAGhD,MAAM,CAAC,MAAQgD,IAAK,CAACrD,EAAIS,GAAG,IAAIT,EAAIU,GAAG2C,GAAI,UAAS,GAAGrD,EAAIW,MAAM,IAEpvBc,EAAkB,G,YC6BP,GACfC,KAAA,gBACAC,MAAA,CAEA8K,SAAA,CACAlL,KAAAuL,QACA7K,SAAA,GAGA6C,MAAA,CACAvD,KAAAK,OACAK,QAAA,OAGAiE,OACA,OACAsG,aAAA,EACAG,aAAA,GACAC,gBAAA,KAAA9H,QAGA1C,SAAA,GAEA+I,MAAA,CAEArG,MAAAiI,GACA,KAAAH,gBAAAG,GAGAH,gBAAAG,GACA,KAAAC,MAAA,QAAAD,GACA,KAAAC,MAAA,SAAAD,KAGA/E,QAAA,CACA,0BACA,KAAAwE,aAAA,EACA,IACA,MAAArD,QAAAC,OAAAY,IAAA,qBACA,KAAA2C,aAAAxD,EAAAjD,KAAA+G,cACA,KAAAN,aAAA9M,SACA,KAAA+M,gBAAA,KAAAD,aAAA,IAEA,MAAA5F,GACAsB,QAAAtB,MAAA,iCAAAA,GACA,KAAAmE,QAAAnE,MAAA,CACAzG,MAAA,QACAkB,QAAA,mCAEA,QACA,KAAAgL,aAAA,MCjFqW,I,YCOjWtI,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,OAAAyC,E,gCClBf,IAAIgJ,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCDhC,IAAIxO,EAAI,EAAQ,QACZyO,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmO,MAAM,GAAQ,CACjD1J,MAAO,SAAe9C,GAGpB,OAFAuM,EAAS9N,MACT6N,EAAUtM,IACFqM,EAAQ5N,MAAM,SAAUmF,EAAO6I,GACrC,IAAKzM,EAAG4D,GAAQ,OAAO6I,MACtB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAIhP,EAAI,EAAQ,QACZyO,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmO,MAAM,GAAQ,CACjD3J,KAAM,SAAc7C,GAGlB,OAFAuM,EAAS9N,MACT6N,EAAUtM,GACHqM,EAAQ5N,MAAM,SAAUmF,EAAO6I,GACpC,GAAIzM,EAAG4D,GAAQ,OAAO6I,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAIhP,EAAI,EAAQ,QACZyO,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmO,MAAM,GAAQ,CACjDjO,OAAQ,SAAgBsO,GACtBN,EAAS9N,MACT6N,EAAUO,GACV,IAAIC,EAAYpO,UAAUC,OAAS,EAC/BoO,EAAcD,OAAYlO,EAAYF,UAAU,GASpD,GARA2N,EAAQ5N,MAAM,SAAUmF,GAClBkJ,GACFA,GAAY,EACZC,EAAcnJ,GAEdmJ,EAAcF,EAAQE,EAAanJ,KAEpC,CAAE8I,aAAa,IACdI,EAAW,MAAME,UAAU,kDAC/B,OAAOD,M,kCCrBX,IAAIE,EAAQ,EAAQ,QAEpBf,EAAOC,QAAU,SAAUe,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,4CCP5D,IAAIb,EAAY,EAAQ,QACpBgB,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMnP,EAAYoP,EAAiBC,GAClDvB,EAAU9N,GACV,IAAIsP,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrBnP,EAAS6O,EAASM,EAAEnP,QACpBqP,EAAQN,EAAW/O,EAAS,EAAI,EAChC4K,EAAImE,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASzE,EACT,MAGF,GADAyE,GAASzE,EACLmE,EAAWM,EAAQ,EAAIrP,GAAUqP,EACnC,MAAMhB,UAAU,+CAGpB,KAAMU,EAAWM,GAAS,EAAIrP,EAASqP,EAAOA,GAASzE,EAAOyE,KAASD,IACrEF,EAAOrP,EAAWqP,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIX3B,EAAOC,QAAU,CAGfrO,KAAM2P,GAAa,GAGnBQ,MAAOR,GAAa", "file": "static/js/chunk-3a5d12e0.df8d3418.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"compact-card\",staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.title}},[(_vm.overallProgress !== undefined)?_c('template',{slot:\"extra\"},[_c('span',[_vm._v(\"Overall Progress: \"+_vm._s(_vm.overallProgress)+\"%\")])]):_vm._e(),_c('a-progress',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"percent\":_vm.overallProgress,\"status\":_vm.progressBarStatus}}),_c('a-table',{attrs:{\"dataSource\":_vm.progressData,\"columns\":_vm.progressColumns,\"rowKey\":\"ip\",\"pagination\":false},scopedSlots:_vm._u([{key:\"errorDetail\",fn:function(text, record){return [(record && record.error_detail)?_c('a-popover',{attrs:{\"placement\":\"topLeft\"}},[_c('template',{slot:\"content\"},[_c('p',[_vm._v(\"Time: \"+_vm._s(record.error_detail.time))]),_c('p',[_vm._v(\"Type: \"+_vm._s(record.error_detail.type))]),_c('p',[_vm._v(\"Message: \"+_vm._s(record.error_detail.message))])]),_c('a-icon',{staticStyle:{\"color\":\"#ff4d4f\"},attrs:{\"type\":\"info-circle\"}})],2):_vm._e()]}}])})],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <a-card\n    :title=\"title\"\n    style=\"margin-top: 16px;\"\n    class=\"compact-card\"\n  >\n    <template v-if=\"overallProgress !== undefined\" slot=\"extra\">\n      <span>Overall Progress: {{ overallProgress }}%</span>\n    </template>\n\n    <a-progress\n      :percent=\"overallProgress\"\n      :status=\"progressBarStatus\"\n      style=\"margin-bottom: 16px;\"\n    />\n\n    <a-table\n      :dataSource=\"progressData\"\n      :columns=\"progressColumns\"\n      rowKey=\"ip\"\n      :pagination=\"false\"\n    >\n      <template slot=\"errorDetail\" slot-scope=\"text, record\">\n        <a-popover v-if=\"record && record.error_detail\" placement=\"topLeft\">\n          <template slot=\"content\">\n            <p>Time: {{ record.error_detail.time }}</p>\n            <p>Type: {{ record.error_detail.type }}</p>\n            <p>Message: {{ record.error_detail.message }}</p>\n          </template>\n          <a-icon type=\"info-circle\" style=\"color: #ff4d4f\" />\n        </a-popover>\n      </template>\n    </a-table>\n  </a-card>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressDisplay',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    taskType: {\n      type: String,\n      required: true\n    },\n    activeTask: {\n      type: Object,\n      default: null\n    },\n    formatBytes: {\n      type: Function,\n      required: true\n    }\n  },\n  computed: {\n    progressColumns() {\n      const columns = [\n        {\n          title: this.$t('hostConfig.columns.ipAddress'),\n          dataIndex: 'ip',\n          key: 'ip',\n          width: '120px'\n        },\n        {\n          title: this.$t('hostConfig.columns.hostName'),\n          dataIndex: 'host_name',\n          key: 'host_name',\n          width: '150px',\n          ellipsis: true\n        },\n        {\n          title: this.$t('tool.columns.status'),\n          dataIndex: 'status',\n          key: 'status',\n          width: '100px',\n          customRender: (text) => {\n            const colorMap = {\n              'pending': '#1890ff',\n              'in_progress': '#1890ff',\n              'paused': '#faad14',\n              'success': '#52c41a',\n              'completed': '#52c41a',\n              'failed': '#f5222d',\n              'downloading': '#1890ff'\n            };\n            const color = colorMap[text] || '#000';\n            return <span style={{ color }}>{text}</span>;\n          }\n        },\n        {\n          title: this.$t('tool.columns.progress'),\n          dataIndex: 'progress',\n          key: 'progress',\n          width: '200px',\n          customRender: (text, record) => (\n            <div>\n              <a-progress\n                percent={text || 0}\n                size=\"small\"\n                status={record.status === 'failed' ? 'exception' :\n                       record.status === 'paused' ? 'normal' : undefined}\n              />\n              <div style=\"font-size: 12px; color: #999\">\n                {this.formatBytes(record.bytes_transferred)} / {this.formatBytes(record.file_size)}\n              </div>\n            </div>\n          )\n        },\n        {\n          title: this.$t('tool.columns.speed'),\n          dataIndex: 'speed',\n          key: 'speed',\n          width: '100px'\n        },\n        {\n          title: this.$t('tool.columns.fileSize'),\n          dataIndex: 'file_size',\n          key: 'file_size',\n          width: '100px',\n          customRender: (text) => this.formatBytes(text)\n        },\n        {\n          title: this.$t('tool.columns.errorDetails'),\n          dataIndex: 'error_detail',\n          key: 'error_detail',\n          width: '60px',\n          scopedSlots: { customRender: 'errorDetail' }\n        }\n      ];\n\n      return columns;\n    },\n    progressData() {\n      if (!this.activeTask || !this.activeTask.nodes) return [];\n      return Object.keys(this.activeTask.nodes).map(ip => {\n        const node = this.activeTask.nodes[ip];\n        return {\n          ip,\n          host_name: node.host_name,\n          status: node.status,\n          progress: node.progress || 0,\n          speed: node.speed || '-',\n          bytes_transferred: node.bytes_transferred,\n          error_detail: node.error_detail,\n          file_size: node.file_size\n        };\n      });\n    },\n    overallProgress() {\n      if (!this.activeTask || !this.activeTask.nodes) return 0;\n      const nodes = Object.values(this.activeTask.nodes);\n      if (nodes.length === 0) return 0;\n\n      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);\n      return Math.round(totalProgress / nodes.length);\n    },\n    progressBarStatus() {\n      if (!this.activeTask || !this.activeTask.nodes) return 'normal';\n\n      const nodes = Object.values(this.activeTask.nodes || {});\n      if (nodes.length === 0) return 'normal';\n\n      if (nodes.some(node => node.status === 'failed')) return 'exception';\n      if (nodes.every(node => ['success', 'completed'].includes(node.status))) return 'success';\n      return 'active';\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* You can add specific styles for ProgressDisplay component here if needed */\n/* Shared styles will be handled in the parent view components or global styles */\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileTransferProgress.vue?vue&type=template&id=258e2dfa&scoped=true\"\nimport script from \"./FileTransferProgress.vue?vue&type=script&lang=js\"\nexport * from \"./FileTransferProgress.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileTransferProgress.vue?vue&type=style&index=0&id=258e2dfa&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"258e2dfa\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"2px\"}},[_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.fileUpload')))])]),_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.uploading},on:{\"click\":_vm.handleUpload}},[_vm._v(\" \"+_vm._s(_vm.$t('fileUpload.startUpload'))+\" \")])],1),_c('div',{staticClass:\"main-content\"},[_c('div',{staticClass:\"left-section\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\"}},[_c('a-form',{attrs:{\"layout\":\"vertical\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.handleUpload.apply(null, arguments)}}},[_c('a-form-item',{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{\"label\":_vm.$t('fileUpload.selectFile')}},[_c('a-upload',{attrs:{\"before-upload\":_vm.beforeUpload,\"show-upload-list\":false}},[_c('a-button',{staticClass:\"nav-style-button\"},[_c('a-icon',{attrs:{\"type\":\"upload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('fileUpload.clickToSelect'))+\" \")],1)],1),(_vm.fileName)?_c('span',{staticStyle:{\"margin-left\":\"8px\"}},[_vm._v(_vm._s(_vm.fileName))]):_vm._e()],1),_c('a-form-item',{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{\"label\":_vm.$t('fileUpload.uploadPath')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('fileUpload.enterUploadPath')},model:{value:(_vm.uploadDir),callback:function ($$v) {_vm.uploadDir=$$v},expression:\"uploadDir\"}})],1)],1)],1),_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.uploading},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedProxyIp),callback:function ($$v) {_vm.selectedProxyIp=$$v},expression:\"selectedProxyIp\"}})],1)],1),_c('div',{staticClass:\"right-section config-table\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('a-table',{staticClass:\"bordered-nodes-table\",attrs:{\"dataSource\":_vm.nodes,\"columns\":_vm.columns,\"rowKey\":\"ip\",\"pagination\":{\n            pageSize: 10,\n            total: _vm.nodes.length,\n            showSizeChanger: false\n          },\"rowSelection\":_vm.rowSelection}})],1)],1)]),_c('FileTransferProgress',{attrs:{\"title\":_vm.$t('fileUpload.uploadProgress'),\"taskType\":'upload',\"activeTask\":_vm.activeUploadTask,\"formatBytes\":_vm.formatBytes}}),(_vm.uploadResults)?_c('a-card',{staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.$t('fileUpload.uploadResults')}},[_c('a-table',{attrs:{\"dataSource\":_vm.uploadResultsData,\"columns\":_vm.resultColumns,\"rowKey\":\"ip\",\"pagination\":false}})],1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"padding: 2px;\">\r\n    <div class=\"card-header-wrapper\">\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"logo-wrapper\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"/>\r\n          </svg>\r\n        </div>\r\n        <h6 class=\"font-semibold m-0\">{{ $t('headTopic.fileUpload') }}</h6>\r\n      </div>\r\n      <a-button\r\n        class=\"nav-style-button\"\r\n        @click=\"handleUpload\"\r\n        :loading=\"uploading\"\r\n      >\r\n        {{ $t('fileUpload.startUpload') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <div class=\"left-section\">\r\n        <a-card size=\"small\" class=\"compact-card\">\r\n          <a-form layout=\"vertical\" @submit.prevent=\"handleUpload\">\r\n            <a-form-item :label=\"$t('fileUpload.selectFile')\" style=\"margin-bottom: 8px;\">\r\n              <a-upload :before-upload=\"beforeUpload\" :show-upload-list=\"false\">\r\n                <a-button class=\"nav-style-button\">\r\n                  <a-icon type=\"upload\" /> {{ $t('fileUpload.clickToSelect') }}\r\n                </a-button>\r\n              </a-upload>\r\n              <span v-if=\"fileName\" style=\"margin-left: 8px;\">{{ fileName }}</span>\r\n            </a-form-item>\r\n\r\n            <a-form-item :label=\"$t('fileUpload.uploadPath')\" style=\"margin-bottom: 8px;\">\r\n              <a-input v-model=\"uploadDir\" :placeholder=\"$t('fileUpload.enterUploadPath')\" />\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureProxy')\">\r\n          <proxy-selector\r\n            v-model=\"selectedProxyIp\"\r\n            :disabled=\"uploading\"\r\n            @change=\"handleProxyChange\"\r\n          />\r\n        </a-card>\r\n      </div>\r\n\r\n      <div class=\"right-section config-table\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureNodes')\">\r\n          <a-table\r\n            :dataSource=\"nodes\"\r\n            :columns=\"columns\"\r\n            rowKey=\"ip\"\r\n            :pagination=\"{\r\n              pageSize: 10,\r\n              total: nodes.length,\r\n              showSizeChanger: false\r\n            }\"\r\n            :rowSelection=\"rowSelection\"\r\n            class=\"bordered-nodes-table\"\r\n            >\r\n          </a-table>\r\n        </a-card>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <FileTransferProgress\r\n      :title=\"$t('fileUpload.uploadProgress')\"\r\n      :taskType=\"'upload'\"\r\n      :activeTask=\"activeUploadTask\"\r\n      :formatBytes=\"formatBytes\"\r\n    />\r\n\r\n    <a-card v-if=\"uploadResults\" :title=\"$t('fileUpload.uploadResults')\" style=\"margin-top: 16px;\">\r\n      <a-table\r\n        :dataSource=\"uploadResultsData\"\r\n        :columns=\"resultColumns\"\r\n        rowKey=\"ip\"\r\n        :pagination=\"false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport axios from '@/api/axiosInstance';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport FileTransferProgress from '@/components/Cards/FileTransferProgress.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin],\r\n  components: {\r\n    ProxySelector,\r\n    FileTransferProgress\r\n  },\r\n  data() {\r\n    return {\r\n      file: null,\r\n      fileName: '',\r\n      selectedNodes: [],\r\n      uploadDir: '/root/.test/Uploads',\r\n      uploading: false,\r\n      selectedProxyIp: null,\r\n      activeTask: null,\r\n      pollInterval: null,\r\n      isProcessing: false,\r\n      uploadResults: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['nodes', 'activeUploadTask', 'currentProject', 'sidebarColor']),\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedNodes,\r\n        onChange: (selectedRowKeys) => {\r\n          this.selectedNodes = selectedRowKeys;\r\n        },\r\n      };\r\n    },\r\n    columns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        }\r\n      ];\r\n    },\r\n    uploadResultsData() {\r\n      if (!this.uploadResults) return [];\r\n\r\n      const results = [];\r\n      this.uploadResults.success.forEach(ip => {\r\n        results.push({ ip, status: 'success' });\r\n      });\r\n      this.uploadResults.failed.forEach(ip => {\r\n        results.push({\r\n          ip,\r\n          status: 'failed',\r\n          error: this.uploadResults.errors[ip]\r\n        });\r\n      });\r\n      return results;\r\n    },\r\n    resultColumns() {\r\n      return [\r\n        { title: this.$t('hostConfig.columns.ipAddress'), dataIndex: 'ip' },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          customRender: (text) => (\r\n            <span style={{ color: text === 'success' ? '#52c41a' : '#f5222d' }}>\r\n              {text}\r\n            </span>\r\n          )\r\n        },\r\n        { title: this.$t('tool.columns.errorDetails'), dataIndex: 'error' }\r\n      ];\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    this.$store.dispatch('fetchNodes');\r\n    // 只检查当前项目的活动上传任务\r\n    const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { taskId, projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveUploadTask();\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.pollInterval) {\r\n      clearInterval(this.pollInterval);\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    beforeUpload(file) {\r\n      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('File size exceeds the 5GB limit');\r\n        return false;\r\n      }\r\n      this.file = file;\r\n      this.fileName = file.name;\r\n      return false;\r\n    },\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      console.log('Proxy IP changed:', ip);\r\n      this.selectedProxyIp = ip;\r\n    },\r\n    validateUploadPath(path) {\r\n      if (!path.startsWith('/')) {\r\n        this.$message.error('Path must start with a slash (/)');\r\n        return false;\r\n      }\r\n      if (path.includes('..') || path.includes('./') || path.includes('~')) {\r\n        this.$message.error('Path cannot contain relative path components');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    async handleUpload() {\r\n      if (!this.file || !this.selectedNodes.length || !this.selectedProxyIp) {\r\n        this.$message.error('Please select a file, nodes, and a proxy IP');\r\n        return;\r\n      }\r\n\r\n      if (!this.validateUploadPath(this.uploadDir)) {\r\n        return;\r\n      }\r\n\r\n      // 获取当前项目的数据库文件\r\n      const currentDbFile = localStorage.getItem('currentProject');\r\n      if (!currentDbFile) {\r\n        this.$message.error('No project selected. Please select a project first.');\r\n        this.$router.push('/projects');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.uploading = true;\r\n\r\n        // 清除之前的上传任务通知记录\r\n        const previousTaskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n        if (previousTaskInfo) {\r\n          try {\r\n            const { taskId } = JSON.parse(previousTaskInfo);\r\n            if (taskId) {\r\n              this.clearTaskNotificationMark(taskId, 'upload', this.currentProject);\r\n            }\r\n          } catch (e) {\r\n            console.error('Error clearing previous upload notification:', e);\r\n          }\r\n        }\r\n\r\n        const formData = new FormData();\r\n        formData.append('file', this.file);\r\n        formData.append('targets', JSON.stringify(this.selectedNodes));\r\n        formData.append('upload_dir', this.uploadDir);\r\n        formData.append('proxyIp', this.selectedProxyIp);\r\n\r\n        const response = await axios.post(\r\n          `/api/file_transfer/upload?dbFile=${encodeURIComponent(this.currentProject)}`,\r\n          formData,\r\n          {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            }\r\n          }\r\n        );\r\n\r\n        const taskId = response.data.task_id;\r\n        localStorage.setItem(`uploadTask_${this.currentProject}`, JSON.stringify({\r\n          taskId,\r\n          projectFile: this.currentProject\r\n        }));\r\n        localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);\r\n\r\n        this.startPolling(taskId);\r\n\r\n      } catch (error) {\r\n        console.error('Upload error:', error);\r\n        this.message = `${this.$t('fileUpload.error')}: ${error.response?.data?.error || error.message}`;\r\n        this.messageType = 'error';\r\n        this.uploading = false;\r\n      }\r\n    },\r\n    async startPolling(taskId) {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n\r\n      const pollStatus = async () => {\r\n        try {\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          this.$store.dispatch('updateUploadTask', response.data);\r\n\r\n          const allCompleted = Object.values(response.data.nodes).every(\r\n            node => ['success', 'failed'].includes(node.status)\r\n          );\r\n\r\n          if (allCompleted) {\r\n            clearInterval(this.pollInterval);\r\n            this.uploading = false;\r\n            localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n            const nodes = Object.values(response.data.nodes);\r\n\r\n            // 使用混入中的方法添加上传完成通知\r\n            this.addTaskCompletionNotification({\r\n              taskId,\r\n              taskType: 'upload',\r\n              nodes,\r\n              projectId: this.currentProject\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Error polling status:', error);\r\n          if (error.response?.status === 404) {\r\n            clearInterval(this.pollInterval);\r\n            this.uploading = false;\r\n            localStorage.removeItem(`uploadTask_${this.currentProject}`);\r\n            localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);\r\n          }\r\n        }\r\n      };\r\n\r\n      await pollStatus();\r\n      this.pollInterval = setInterval(pollStatus, 5000);\r\n    },\r\n    async checkActiveUploadTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`uploadTaskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            return;\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateUploadTask', response.data);\r\n\r\n            const allCompleted = Object.values(response.data.nodes).every(\r\n              node => ['success', 'failed'].includes(node.status)\r\n            );\r\n\r\n            if (!allCompleted && !taskCompleted) {\r\n              this.uploading = true;\r\n              this.startPolling(taskId);\r\n            } else if (allCompleted) {\r\n              this.uploading = false;\r\n              localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n              const nodes = Object.values(response.data.nodes);\r\n\r\n              // 使用混入中的方法添加上传完成通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType: 'upload',\r\n                nodes,\r\n                projectId: this.currentProject\r\n              });\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active upload task:', error);\r\n      }\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n      return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;\r\n    },\r\n    parseSpeed(speed) {\r\n      if (!speed || speed === '-') return 0;\r\n      const [value, unit] = speed.split(' ');\r\n      const multiplier = {\r\n        'B/s': 1,\r\n        'KB/s': 1024,\r\n        'MB/s': 1024 * 1024\r\n      }[unit] || 1;\r\n      return parseFloat(value) * multiplier;\r\n    },\r\n    checkDatabaseStatus() {\r\n      const currentDbFile = localStorage.getItem('currentProject');\r\n      if (!currentDbFile) {\r\n        this.$notify.error({\r\n          title: this.$t('fileUpload.error'),\r\n          message: this.$t('fileUpload.noProjectSelected')\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    }\r\n  },\r\n  watch: {\r\n    // 添加对 currentProject 的监听\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除当前的上传任务状态\r\n          this.$store.dispatch('updateUploadTask', null);\r\n          if (this.pollInterval) {\r\n            clearInterval(this.pollInterval);\r\n          }\r\n          // 检查新项目的活动任务\r\n          this.checkActiveUploadTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.card-header-wrapper {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo-wrapper {\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n}\r\n\r\n/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */\r\n.main-content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 16px;\r\n}\r\n\r\n/* 左右两侧的Flexbox布局 */\r\n.left-section, .right-section {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 左侧卡片样式 */\r\n.left-section .compact-card {\r\n  flex: 1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.left-section .compact-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 右侧卡片样式 */\r\n.right-section .compact-card {\r\n  flex: 1;\r\n}\r\n\r\n/* 响应式布局 - 在移动设备上切换为单列布局 */\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 卡片标题样式 - 使其更紧凑 */\r\n.compact-card >>> .ant-card-head {\r\n  min-height: 40px;\r\n  padding: 0 12px;\r\n}\r\n\r\n.compact-card >>> .ant-card-head-title {\r\n  padding: 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表格样式 - 使用全局样式，不再硬编码边框颜色 */\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileUpload.vue?vue&type=template&id=7fa5d66d&scoped=true\"\nimport script from \"./FileUpload.vue?vue&type=script&lang=js\"\nexport * from \"./FileUpload.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileUpload.vue?vue&type=style&index=0&id=7fa5d66d&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7fa5d66d\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=style&index=0&id=7fa5d66d&prod&scoped=true&lang=css\"", "/**\r\n * 通知混入 - 提供通用的通知处理逻辑\r\n * 用于任务完成、文件上传/下载完成等场景\r\n */\r\nexport default {\r\n  methods: {\r\n    /**\r\n     * 添加任务完成通知\r\n     * @param {Object} options - 通知选项\r\n     * @param {string} options.taskId - 任务ID\r\n     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)\r\n     * @param {Array} options.nodes - 节点数组\r\n     * @param {string} options.projectId - 项目ID\r\n     * @param {Object} options.titles - 自定义标题 {success, error}\r\n     * @param {Object} options.templates - 自定义消息模板 {success, error}\r\n     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}\r\n     */\r\n    addTaskCompletionNotification({\r\n      taskId,\r\n      taskType,\r\n      nodes,\r\n      projectId,\r\n      titles = {},\r\n      templates = {},\r\n      statusMapping = {}\r\n    }) {\r\n      // 检查是否已经发送过通知\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      if (localStorage.getItem(notificationSentKey)) {\r\n        return; // 已经发送过通知，不再重复发送\r\n      }\r\n\r\n      // 设置默认状态映射\r\n      const defaultStatusMapping = {\r\n        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'\r\n        failure: ['failed']                // 失败状态通常是'failed'\r\n      };\r\n\r\n      // 合并自定义状态映射\r\n      const finalStatusMapping = {\r\n        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],\r\n        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]\r\n      };\r\n\r\n      // 计算成功和失败的节点数量\r\n      const successNodes = nodes.filter(node =>\r\n        finalStatusMapping.success.includes(node.status) && !node.error_detail\r\n      ).length;\r\n      const failedNodes = nodes.filter(node =>\r\n        finalStatusMapping.failure.includes(node.status) || node.error_detail\r\n      ).length;\r\n      const hasFailures = failedNodes > 0;\r\n\r\n      // 准备通知标题\r\n      let notificationTitle;\r\n      if (hasFailures) {\r\n        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);\r\n      } else {\r\n        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);\r\n      }\r\n\r\n      // 准备通知内容\r\n      let notificationMessage;\r\n      if (hasFailures) {\r\n        notificationMessage = templates.error ||\r\n          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;\r\n      } else {\r\n        notificationMessage = templates.success ||\r\n          `All ${nodes.length} nodes completed successfully.`;\r\n      }\r\n\r\n      // 添加到全局通知中心\r\n      this.addNotification({\r\n        title: notificationTitle,\r\n        message: notificationMessage,\r\n        type: hasFailures ? 'error' : 'success',\r\n        taskId: taskId\r\n      });\r\n\r\n      // 标记已发送通知\r\n      localStorage.setItem(notificationSentKey, 'true');\r\n    },\r\n\r\n    /**\r\n     * 获取默认的成功标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultSuccessTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed',\r\n        'upload': 'File Upload Completed',\r\n        'download': 'File Download Completed',\r\n        'tool': 'Tool Execution Completed'\r\n      };\r\n      return titles[taskType] || 'Operation Completed';\r\n    },\r\n\r\n    /**\r\n     * 获取默认的错误标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultErrorTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed with Errors',\r\n        'upload': 'File Upload Completed with Errors',\r\n        'download': 'File Download Completed with Errors',\r\n        'tool': 'Tool Execution Completed with Errors'\r\n      };\r\n      return titles[taskType] || 'Operation Completed with Errors';\r\n    },\r\n\r\n    /**\r\n     * 清除任务通知标记\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型\r\n     * @param {string} projectId - 项目ID\r\n     */\r\n    clearTaskNotificationMark(taskId, taskType, projectId) {\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      localStorage.removeItem(notificationSentKey);\r\n    }\r\n  }\r\n};\r\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=style&index=0&id=258e2dfa&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"proxy-selector\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.isDetecting,\"disabled\":_vm.disabled},on:{\"click\":_vm.fetchReachableIps}},[_vm._v(\" \"+_vm._s(_vm.$t('common.detectReachableIps') || '检测可达IP')+\" \")]),(_vm.reachableIps.length)?_c('a-select',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"16px\"},attrs:{\"placeholder\":_vm.$t('tool.selectReachableIp') || '选择可达IP',\"disabled\":_vm.disabled},model:{value:(_vm.selectedIpValue),callback:function ($$v) {_vm.selectedIpValue=$$v},expression:\"selectedIpValue\"}},_vm._l((_vm.reachableIps),function(ip){return _c('a-select-option',{key:ip,attrs:{\"value\":ip}},[_vm._v(\" \"+_vm._s(ip)+\" \")])}),1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"proxy-selector\">\r\n    <!-- 检测IP按钮 -->\r\n    <a-button\r\n      class=\"nav-style-button\"\r\n      @click=\"fetchReachableIps\"\r\n      :loading=\"isDetecting\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      {{ $t('common.detectReachableIps') || '检测可达IP' }}\r\n    </a-button>\r\n\r\n    <!-- IP选择下拉框 -->\r\n    <a-select\r\n      v-if=\"reachableIps.length\"\r\n      v-model=\"selectedIpValue\"\r\n      style=\"width: 100%; margin-top: 16px;\"\r\n      :placeholder=\"$t('tool.selectReachableIp') || '选择可达IP'\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <a-select-option v-for=\"ip in reachableIps\" :key=\"ip\" :value=\"ip\">\r\n        {{ ip }}\r\n      </a-select-option>\r\n    </a-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProxySelector',\r\n  props: {\r\n    // 是否禁用控件\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 初始选中的IP\r\n    value: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isDetecting: false,\r\n      reachableIps: [],\r\n      selectedIpValue: this.value\r\n    };\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {\r\n    // 监听外部传入的value变化\r\n    value(newValue) {\r\n      this.selectedIpValue = newValue;\r\n    },\r\n    // 监听内部selectedIpValue变化，向外发送事件\r\n    selectedIpValue(newValue) {\r\n      this.$emit('input', newValue);\r\n      this.$emit('change', newValue);\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchReachableIps() {\r\n      this.isDetecting = true;\r\n      try {\r\n        const response = await axios.get('/api/proxy/detect');\r\n        this.reachableIps = response.data.reachable_ips;\r\n        if (this.reachableIps.length) {\r\n          this.selectedIpValue = this.reachableIps[0];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error detecting reachable IPs:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to detect reachable IPs'\r\n        });\r\n      } finally {\r\n        this.isDetecting = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProxySelector.vue?vue&type=template&id=46f0b65a&scoped=true\"\nimport script from \"./ProxySelector.vue?vue&type=script&lang=js\"\nexport * from \"./ProxySelector.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46f0b65a\",\n  null\n  \n)\n\nexport default component.exports", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  every: function every(fn) {\n    anObject(this);\n    aFunction(fn);\n    return !iterate(this, function (value, stop) {\n      if (!fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n"], "sourceRoot": ""}