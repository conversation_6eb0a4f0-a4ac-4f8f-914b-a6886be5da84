{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue", "mtime": 1753187219715}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Icon", "axios", "mapState", "CopyMixin", "cacheData", "components", "AIcon", "mixins", "computed", "data", "$data", "hosts", "saving", "loading", "currentPage", "pageSize", "editableColumns", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentDbFile", "localStorage", "getItem", "columns", "title", "dataIndex", "width", "customRender", "text", "record", "index", "$t", "scopedSlots", "align", "created", "$message", "warning", "$router", "push", "fetchHostConfig", "methods", "copyNodeInfo", "newRecord", "key", "Date", "now", "id", "undefined", "editable", "isNew", "host_name", "ip", "map", "item", "$nextTick", "tableBody", "document", "querySelector", "scrollTop", "getColumnTitle", "_this$columns$find", "find", "c", "handleChange", "value", "column", "newData", "target", "edit", "save", "validateHost", "hostData", "post", "dbFile", "success", "error", "cancel", "targetIndex", "findIndex", "filter", "cachedItem", "Object", "assign", "addNewRow", "ssh_port", "login_user", "login_pwd", "switch_root_cmd", "switch_root_pwd", "host", "_host$host_name", "trim", "test", "exist", "some", "h", "response", "get", "params", "detail", "_item$id", "_item$ssh_port", "toString", "_error$response", "onPageChange", "page", "deleteHost", "delete", "_error$response2", "onSelectChange", "batchDelete", "selectedIds", "includes", "length", "ids", "_error$response3", "downloadTemplate", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "console", "handleUpload", "options", "file", "name", "endsWith", "formData", "FormData", "append", "headers", "_error$response4", "exportSelectedHosts", "selectedHosts", "csv<PERSON><PERSON>nt", "join", "header", "blob", "type"], "sources": ["src/components/Cards/HostConfig.vue"], "sourcesContent": ["<template>\r\n  <a-card :bordered=\"false\" class=\"header-solid host-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('hostConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('hostConfig.addHost') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedHosts\"\r\n              >\r\n                {{ $t('hostConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"batchDelete\"\r\n              >\r\n                {{ $t('hostConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  icon=\"download\"\r\n                  class=\"nav-style-button\"\r\n                  @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('hostConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                name=\"file\"\r\n                :customRequest=\"handleUpload\"\r\n                :showUploadList=\"false\"\r\n              >\r\n                <a-button\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('hostConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"hosts\"\r\n        :rowKey=\"(record) => record.key\"\r\n        size=\"middle\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: hosts.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''\"\r\n              @click=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('common.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyNodeInfo(record)\">\r\n              <a-icon type=\"copy\" />\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteHost(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('hostConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式\r\n  },\r\n  data() {\r\n    return {\r\n      ...this.$data,\r\n      hosts: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'host_name',\r\n        'ip',\r\n        'ssh_port',\r\n        'login_user',\r\n        'login_pwd',\r\n        'switch_root_cmd',\r\n        'switch_root_pwd',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          scopedSlots: { customRender: 'host_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          scopedSlots: { customRender: 'ip' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.sshPort'),\r\n          dataIndex: 'ssh_port',\r\n          scopedSlots: { customRender: 'ssh_port' },\r\n          width: 100,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginUser'),\r\n          dataIndex: 'login_user',\r\n          scopedSlots: { customRender: 'login_user' },\r\n          width: 120,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginPassword'),\r\n          dataIndex: 'login_pwd',\r\n          scopedSlots: { customRender: 'login_pwd' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootCmd'),\r\n          dataIndex: 'switch_root_cmd',\r\n          scopedSlots: { customRender: 'switch_root_cmd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootPwd'),\r\n          dataIndex: 'switch_root_pwd',\r\n          scopedSlots: { customRender: 'switch_root_pwd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchHostConfig();\r\n  },\r\n  methods: {\r\n    copyNodeInfo(record) {\r\n      // 创建新的节点数据，复制原节点的所有属性\r\n      const newRecord = {\r\n        ...record,\r\n        key: `new-${Date.now()}`,\r\n        id: undefined,\r\n        editable: true,\r\n        isNew: true,\r\n        host_name: `${record.host_name || ''}_copy`,\r\n        ip: '' // Clear IP as it should be unique\r\n      };\r\n      \r\n      // 在表格开头添加新行\r\n      this.hosts = [newRecord, ...this.hosts];\r\n      this.currentPage = 1; // Reset to first page to show the new row\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n      \r\n      // 滚动到顶部以显示新添加的行\r\n      this.$nextTick(() => {\r\n        const tableBody = document.querySelector('.ant-table-body');\r\n        if (tableBody) {\r\n          tableBody.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    \r\n    getColumnTitle(dataIndex) {\r\n      return this.columns.find((c) => c.dataIndex === dataIndex)?.title || dataIndex;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n    edit(key) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n        target.editable = true;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      try {\r\n        const target = this.hosts.find((item) => item.key === key);\r\n        if (!target || !this.validateHost(target)) return;\r\n\r\n        this.saving = true;\r\n        const hostData = { ...target };\r\n        delete hostData.editable;\r\n        delete hostData.isNew;\r\n\r\n        await axios.post('/api/config/', {\r\n          hosts: [hostData],\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.map((item) =>\r\n          item.key === key ? { ...item, editable: false, isNew: false } : item\r\n        );\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n        this.$message.success('Saved successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to save host');\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.hosts.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.hosts[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.hosts = this.hosts.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.hosts];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.hosts = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.hosts = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          host_name: '',\r\n          ip: '',\r\n          ssh_port: '22',\r\n          login_user: '',\r\n          login_pwd: '',\r\n          switch_root_cmd: '',\r\n          switch_root_pwd: '',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.hosts,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    validateHost(host) {\r\n      if (!host.host_name?.trim()) {\r\n        this.$message.error('Host name is required');\r\n        return false;\r\n      }\r\n      if (!/^(\\d{1,3}\\.){3}\\d{1,3}$/.test(host.ip)) {\r\n        this.$message.error('Invalid IP format');\r\n        return false;\r\n      }\r\n      if (!/^\\d+$/.test(host.ssh_port)) {\r\n        this.$message.error('SSH port must be numeric');\r\n        return false;\r\n      }\r\n      const exist = this.hosts.some((h) => h.ip === host.ip && h.key !== host.key);\r\n      if (exist) {\r\n        this.$message.error('IP address already exists');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    async fetchHostConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/config`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.hosts = response.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `host_${item.host_name}`,\r\n          ssh_port: item.ssh_port?.toString() || '22',\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load hosts');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteHost(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/config/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete host');\r\n        await this.fetchHostConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async batchDelete() {\r\n      try {\r\n        const selectedIds = this.hosts\r\n          .filter(host => this.selectedRowKeys.includes(host.key))\r\n          .map(host => host.id)\r\n          .filter(id => id);\r\n\r\n        if (selectedIds.length === 0) {\r\n          this.$message.warning('No valid hosts selected for deletion');\r\n          return;\r\n        }\r\n\r\n        await axios.post('/api/config/batch-delete', {\r\n          ids: selectedIds,\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.filter(host => !this.selectedRowKeys.includes(host.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Batch deletion completed successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Batch deletion failed');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/config/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'hosts_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n        console.error('Download template error:', error);\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        await axios.post('/api/config/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        await this.fetchHostConfig();\r\n        this.$message.success('Hosts imported successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import hosts');\r\n      }\r\n    },\r\n\r\n    async exportSelectedHosts() {\r\n      try {\r\n        const selectedHosts = this.hosts.filter(host => this.selectedRowKeys.includes(host.key));\r\n\r\n        // Create CSV content\r\n        const headers = [\r\n          'host_name',\r\n          'ip',\r\n          'ssh_port',\r\n          'login_user',\r\n          'login_pwd',\r\n          'switch_root_cmd',\r\n          'switch_root_pwd'\r\n        ];\r\n\r\n        const csvContent = [\r\n          headers.join(','),\r\n          ...selectedHosts.map(host =>\r\n            headers.map(header => host[header] || '').join(',')\r\n          )\r\n        ].join('\\n');\r\n\r\n        // Create and trigger download\r\n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'selected_hosts.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Hosts exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export hosts');\r\n        console.error('Export hosts error:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.host-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 添加按钮组样式 */\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AAoJA;AACA,SAAAA,IAAA;AACA,OAAAC,KAAA;AACA,SAAAC,QAAA;AACA,OAAAC,SAAA;AAEA,IAAAC,SAAA;AAEA;EACAC,UAAA;IACAC,KAAA,EAAAN;EACA;EACAO,MAAA,GAAAJ,SAAA;EACAK,QAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;MACA,QAAAC,KAAA;MACAC,KAAA;MACAC,MAAA;MACAC,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,eAAA,GACA,aACA,MACA,YACA,cACA,aACA,mBACA,kBACA;MACAC,eAAA;MACAC,aAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,IAAA,EAAAC,MAAA,EAAAC,KAAA;UACA,aAAAd,WAAA,aAAAC,QAAA,GAAAa,KAAA;QACA;MACA,GACA;QACAN,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;QACAO,KAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,UAAAd,aAAA;MACA,KAAAe,QAAA,CAAAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;MACA;IACA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACAC,aAAAZ,MAAA;MACA;MACA,MAAAa,SAAA;QACA,GAAAb,MAAA;QACAc,GAAA,SAAAC,IAAA,CAAAC,GAAA;QACAC,EAAA,EAAAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA,KAAArB,MAAA,CAAAqB,SAAA;QACAC,EAAA;MACA;;MAEA;MACA,KAAAtC,KAAA,IAAA6B,SAAA,UAAA7B,KAAA;MACA,KAAAG,WAAA;MACAV,SAAA,QAAAO,KAAA,CAAAuC,GAAA,CAAAC,IAAA;QAAA,GAAAA;MAAA;MACA,KAAAlC,eAAA;;MAEA;MACA,KAAAmC,SAAA;QACA,MAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,SAAA;UACAA,SAAA,CAAAG,SAAA;QACA;MACA;IACA;IAEAC,eAAAlC,SAAA;MAAA,IAAAmC,kBAAA;MACA,SAAAA,kBAAA,QAAArC,OAAA,CAAAsC,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAArC,SAAA,KAAAA,SAAA,eAAAmC,kBAAA,uBAAAA,kBAAA,CAAApC,KAAA,KAAAC,SAAA;IACA;IAEAsC,aAAAC,KAAA,EAAArB,GAAA,EAAAsB,MAAA;MACA,MAAAC,OAAA,YAAArD,KAAA;MACA,MAAAsD,MAAA,GAAAD,OAAA,CAAAL,IAAA,CAAAR,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;MACA,IAAAwB,MAAA;QACAA,MAAA,CAAAF,MAAA,IAAAD,KAAA;QACA,KAAAnD,KAAA,GAAAqD,OAAA;MACA;IACA;IACAE,KAAAzB,GAAA;MACA,MAAAuB,OAAA,YAAArD,KAAA;MACA,MAAAsD,MAAA,GAAAD,OAAA,CAAAL,IAAA,CAAAR,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;MACA,IAAAwB,MAAA;QACA7D,SAAA,GAAA4D,OAAA,CAAAd,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;QACAc,MAAA,CAAAnB,QAAA;QACA,KAAAnC,KAAA,GAAAqD,OAAA;MACA;IACA;IAEA,MAAAG,KAAA1B,GAAA;MACA;QACA,MAAAwB,MAAA,QAAAtD,KAAA,CAAAgD,IAAA,CAAAR,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;QACA,KAAAwB,MAAA,UAAAG,YAAA,CAAAH,MAAA;QAEA,KAAArD,MAAA;QACA,MAAAyD,QAAA;UAAA,GAAAJ;QAAA;QACA,OAAAI,QAAA,CAAAvB,QAAA;QACA,OAAAuB,QAAA,CAAAtB,KAAA;QAEA,MAAA9C,KAAA,CAAAqE,IAAA;UACA3D,KAAA,GAAA0D,QAAA;UACAE,MAAA,OAAArD;QACA;QAEA,KAAAP,KAAA,QAAAA,KAAA,CAAAuC,GAAA,CAAAC,IAAA,IACAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;UAAA,GAAAU,IAAA;UAAAL,QAAA;UAAAC,KAAA;QAAA,IAAAI,IACA;QACA/C,SAAA,QAAAO,KAAA,CAAAuC,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;QACA,KAAAlB,QAAA,CAAAuC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAxC,QAAA,CAAAwC,KAAA;MACA;QACA,KAAA7D,MAAA;MACA;IACA;IAEA8D,OAAAjC,GAAA;MACA,MAAAkC,WAAA,QAAAhE,KAAA,CAAAiE,SAAA,CAAAzB,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;MACA,IAAAkC,WAAA;MAEA,MAAAV,MAAA,QAAAtD,KAAA,CAAAgE,WAAA;MAEA,IAAAV,MAAA,CAAAlB,KAAA;QACA;QACA,KAAApC,KAAA,QAAAA,KAAA,CAAAkE,MAAA,CAAA1B,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;MACA;QACA;QACA,MAAAuB,OAAA,YAAArD,KAAA;QACA,MAAAmE,UAAA,GAAA1E,SAAA,CAAAuD,IAAA,CAAAR,IAAA,IAAAA,IAAA,CAAAV,GAAA,KAAAA,GAAA;QACA,IAAAqC,UAAA;UACAC,MAAA,CAAAC,MAAA,CAAAf,MAAA;YAAA,GAAAa;UAAA;UACA,OAAAb,MAAA,CAAAnB,QAAA;UACA,KAAAnC,KAAA,GAAAqD,OAAA;QACA;MACA;IACA;IAEAiB,UAAA;MACA,KAAAtE,KAAA,IACA;QACA8B,GAAA,SAAAC,IAAA,CAAAC,GAAA;QACAK,SAAA;QACAC,EAAA;QACAiC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAxC,QAAA;QACAC,KAAA;MACA,GACA,QAAApC,KAAA,CACA;MACA,KAAAG,WAAA;MACAV,SAAA,QAAAO,KAAA,CAAAuC,GAAA,CAAAC,IAAA;QAAA,GAAAA;MAAA;MACA,KAAAlC,eAAA;IACA;IAEAmD,aAAAmB,IAAA;MAAA,IAAAC,eAAA;MACA,OAAAA,eAAA,GAAAD,IAAA,CAAAvC,SAAA,cAAAwC,eAAA,eAAAA,eAAA,CAAAC,IAAA;QACA,KAAAxD,QAAA,CAAAwC,KAAA;QACA;MACA;MACA,+BAAAiB,IAAA,CAAAH,IAAA,CAAAtC,EAAA;QACA,KAAAhB,QAAA,CAAAwC,KAAA;QACA;MACA;MACA,aAAAiB,IAAA,CAAAH,IAAA,CAAAL,QAAA;QACA,KAAAjD,QAAA,CAAAwC,KAAA;QACA;MACA;MACA,MAAAkB,KAAA,QAAAhF,KAAA,CAAAiF,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA5C,EAAA,KAAAsC,IAAA,CAAAtC,EAAA,IAAA4C,CAAA,CAAApD,GAAA,KAAA8C,IAAA,CAAA9C,GAAA;MACA,IAAAkD,KAAA;QACA,KAAA1D,QAAA,CAAAwC,KAAA;QACA;MACA;MACA;IACA;IAEA,MAAApC,gBAAA;MACA;QACA,KAAAxB,OAAA;QACA,MAAAiF,QAAA,SAAA7F,KAAA,CAAA8F,GAAA;UACAC,MAAA;YACAC,MAAA;YACA1B,MAAA,OAAArD;UACA;QACA;QACA,KAAAP,KAAA,GAAAmF,QAAA,CAAArF,IAAA,CAAAyC,GAAA,CAAAC,IAAA;UAAA,IAAA+C,QAAA,EAAAC,cAAA;UAAA;YACA,GAAAhD,IAAA;YACAV,GAAA,IAAAyD,QAAA,GAAA/C,IAAA,CAAAP,EAAA,cAAAsD,QAAA,uBAAAA,QAAA,CAAAE,QAAA,eAAAjD,IAAA,CAAAH,SAAA;YACAkC,QAAA,IAAAiB,cAAA,GAAAhD,IAAA,CAAA+B,QAAA,cAAAiB,cAAA,uBAAAA,cAAA,CAAAC,QAAA;YACArD,KAAA;UACA;QAAA;QACA3C,SAAA,QAAAO,KAAA,CAAAuC,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;MACA,SAAAsB,KAAA;QAAA,IAAA4B,eAAA;QACA,KAAApE,QAAA,CAAAwC,KAAA,GAAA4B,eAAA,GAAA5B,KAAA,CAAAqB,QAAA,cAAAO,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAA5F,IAAA,cAAA4F,eAAA,uBAAAA,eAAA,CAAA5B,KAAA;MACA;QACA,KAAA5D,OAAA;MACA;IACA;IACAyF,aAAAC,IAAA;MACA,KAAAzF,WAAA,GAAAyF,IAAA;IACA;IAEA,MAAAC,WAAA7E,MAAA;MACA;QACA,IAAAA,MAAA,CAAAiB,EAAA;UACA,MAAA3C,KAAA,CAAAwG,MAAA,gBAAA9E,MAAA,CAAAiB,EAAA;YACAoD,MAAA;cAAAzB,MAAA,OAAArD;YAAA;UACA;UAEA,KAAAP,KAAA,QAAAA,KAAA,CAAAkE,MAAA,CAAAgB,CAAA,IAAAA,CAAA,CAAApD,GAAA,KAAAd,MAAA,CAAAc,GAAA;UACA,KAAAxB,eAAA,QAAAA,eAAA,CAAA4D,MAAA,CAAApC,GAAA,IAAAA,GAAA,KAAAd,MAAA,CAAAc,GAAA;UACA,KAAAR,QAAA,CAAAuC,OAAA;QACA;UACA,KAAA7D,KAAA,QAAAA,KAAA,CAAAkE,MAAA,CAAAgB,CAAA,IAAAA,CAAA,CAAApD,GAAA,KAAAd,MAAA,CAAAc,GAAA;UACA,KAAAxB,eAAA,QAAAA,eAAA,CAAA4D,MAAA,CAAApC,GAAA,IAAAA,GAAA,KAAAd,MAAA,CAAAc,GAAA;QACA;MACA,SAAAgC,KAAA;QAAA,IAAAiC,gBAAA;QACA,KAAAzE,QAAA,CAAAwC,KAAA,GAAAiC,gBAAA,GAAAjC,KAAA,CAAAqB,QAAA,cAAAY,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAjG,IAAA,cAAAiG,gBAAA,uBAAAA,gBAAA,CAAAjC,KAAA;QACA,WAAApC,eAAA;MACA;IACA;IAEAsE,eAAA1F,eAAA;MACA,KAAAA,eAAA,GAAAA,eAAA;IACA;IAEA,MAAA2F,YAAA;MACA;QACA,MAAAC,WAAA,QAAAlG,KAAA,CACAkE,MAAA,CAAAU,IAAA,SAAAtE,eAAA,CAAA6F,QAAA,CAAAvB,IAAA,CAAA9C,GAAA,GACAS,GAAA,CAAAqC,IAAA,IAAAA,IAAA,CAAA3C,EAAA,EACAiC,MAAA,CAAAjC,EAAA,IAAAA,EAAA;QAEA,IAAAiE,WAAA,CAAAE,MAAA;UACA,KAAA9E,QAAA,CAAAC,OAAA;UACA;QACA;QAEA,MAAAjC,KAAA,CAAAqE,IAAA;UACA0C,GAAA,EAAAH,WAAA;UACAtC,MAAA,OAAArD;QACA;QAEA,KAAAP,KAAA,QAAAA,KAAA,CAAAkE,MAAA,CAAAU,IAAA,UAAAtE,eAAA,CAAA6F,QAAA,CAAAvB,IAAA,CAAA9C,GAAA;QACA,KAAAxB,eAAA;QACA,KAAAgB,QAAA,CAAAuC,OAAA;MACA,SAAAC,KAAA;QAAA,IAAAwC,gBAAA;QACA,KAAAhF,QAAA,CAAAwC,KAAA,GAAAwC,gBAAA,GAAAxC,KAAA,CAAAqB,QAAA,cAAAmB,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAxG,IAAA,cAAAwG,gBAAA,uBAAAA,gBAAA,CAAAxC,KAAA;MACA;IACA;IAEA,MAAAyC,iBAAA;MACA;QACA,MAAApB,QAAA,SAAA7F,KAAA,CAAA8F,GAAA;UACAoB,YAAA;QACA;QAEA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,KAAAC,IAAA,EAAA1B,QAAA,CAAArF,IAAA;QACA,MAAAgH,IAAA,GAAAnE,QAAA,CAAAoE,aAAA;QACAD,IAAA,CAAAE,IAAA,GAAAP,GAAA;QACAK,IAAA,CAAAG,YAAA;QACAtE,QAAA,CAAAuE,IAAA,CAAAC,WAAA,CAAAL,IAAA;QACAA,IAAA,CAAAM,KAAA;QACAzE,QAAA,CAAAuE,IAAA,CAAAG,WAAA,CAAAP,IAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;QAEA,KAAAnF,QAAA,CAAAuC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAxC,QAAA,CAAAwC,KAAA;QACAyD,OAAA,CAAAzD,KAAA,6BAAAA,KAAA;MACA;IACA;IAEA,MAAA0D,aAAAC,OAAA;MACA;QAAAC;MAAA,IAAAD,OAAA;MAEA,KAAAC,IAAA,CAAAC,IAAA,CAAAC,QAAA;QACA,KAAAtG,QAAA,CAAAwC,KAAA;QACA;MACA;MAEA;QACA,MAAA+D,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,SAAAL,IAAA;QACAG,QAAA,CAAAE,MAAA,gBAAAxH,aAAA;QAEA,MAAAjB,KAAA,CAAAqE,IAAA,uBAAAkE,QAAA;UACAG,OAAA;YAAA;UAAA;QACA;QAEA,WAAAtG,eAAA;QACA,KAAAJ,QAAA,CAAAuC,OAAA;MACA,SAAAC,KAAA;QAAA,IAAAmE,gBAAA;QACA,KAAA3G,QAAA,CAAAwC,KAAA,GAAAmE,gBAAA,GAAAnE,KAAA,CAAAqB,QAAA,cAAA8C,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAnI,IAAA,cAAAmI,gBAAA,uBAAAA,gBAAA,CAAAnE,KAAA;MACA;IACA;IAEA,MAAAoE,oBAAA;MACA;QACA,MAAAC,aAAA,QAAAnI,KAAA,CAAAkE,MAAA,CAAAU,IAAA,SAAAtE,eAAA,CAAA6F,QAAA,CAAAvB,IAAA,CAAA9C,GAAA;;QAEA;QACA,MAAAkG,OAAA,IACA,aACA,MACA,YACA,cACA,aACA,mBACA,kBACA;QAEA,MAAAI,UAAA,IACAJ,OAAA,CAAAK,IAAA,OACA,GAAAF,aAAA,CAAA5F,GAAA,CAAAqC,IAAA,IACAoD,OAAA,CAAAzF,GAAA,CAAA+F,MAAA,IAAA1D,IAAA,CAAA0D,MAAA,SAAAD,IAAA,KACA,EACA,CAAAA,IAAA;;QAEA;QACA,MAAAE,IAAA,OAAA1B,IAAA,EAAAuB,UAAA;UAAAI,IAAA;QAAA;QACA,MAAA/B,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAA2B,IAAA;QACA,MAAAzB,IAAA,GAAAnE,QAAA,CAAAoE,aAAA;QACAD,IAAA,CAAAE,IAAA,GAAAP,GAAA;QACAK,IAAA,CAAAG,YAAA;QACAtE,QAAA,CAAAuE,IAAA,CAAAC,WAAA,CAAAL,IAAA;QACAA,IAAA,CAAAM,KAAA;QACAzE,QAAA,CAAAuE,IAAA,CAAAG,WAAA,CAAAP,IAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;QAEA,KAAAnF,QAAA,CAAAuC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAxC,QAAA,CAAAwC,KAAA;QACAyD,OAAA,CAAAzD,KAAA,wBAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}