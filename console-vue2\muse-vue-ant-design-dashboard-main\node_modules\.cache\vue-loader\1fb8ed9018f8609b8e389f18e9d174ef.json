{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue?vue&type=style&index=0&id=6e0962f4&scoped=true&lang=css", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue", "mtime": 1753187219715}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouaG9zdC1jb25maWctY2FyZCB7DQogIG1hcmdpbjogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQo6OnYtZGVlcCAuYW50LXVwbG9hZC1zZWxlY3Qgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQp9DQoNCi8qIOWIoOmZpOaMiemSruS/neaMgee6ouiJsiAqLw0KOjp2LWRlZXAgLmRlbGV0ZS1idXR0b24gew0KICBjb2xvcjogI2ZmNGQ0ZiAhaW1wb3J0YW50Ow0KfQ0KDQo6OnYtZGVlcCAuZGVsZXRlLWJ1dHRvbiAuYW50aWNvbiB7DQogIGNvbG9yOiAjZmY0ZDRmICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOa3u+WKoOaMiemSrue7hOagt+W8jyAqLw0KLmJ1dHRvbi1ncm91cHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIG1hcmdpbi1ib3R0b206IDA7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiAxNnB4Ow0KfQ0KDQouYnV0dG9uLWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiA4cHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi8qIOWTjeW6lOW8j+iwg+aVtCAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5idXR0b24tZ3JvdXBzIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["HostConfig.vue"], "names": [], "mappings": ";AA6iBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HostConfig.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card :bordered=\"false\" class=\"header-solid host-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('hostConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('hostConfig.addHost') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedHosts\"\r\n              >\r\n                {{ $t('hostConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"batchDelete\"\r\n              >\r\n                {{ $t('hostConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  icon=\"download\"\r\n                  class=\"nav-style-button\"\r\n                  @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('hostConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                name=\"file\"\r\n                :customRequest=\"handleUpload\"\r\n                :showUploadList=\"false\"\r\n              >\r\n                <a-button\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('hostConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"hosts\"\r\n        :rowKey=\"(record) => record.key\"\r\n        size=\"middle\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: hosts.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''\"\r\n              @click=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('common.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyNodeInfo(record)\">\r\n              <a-icon type=\"copy\" />\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteHost(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('hostConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式\r\n  },\r\n  data() {\r\n    return {\r\n      ...this.$data,\r\n      hosts: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'host_name',\r\n        'ip',\r\n        'ssh_port',\r\n        'login_user',\r\n        'login_pwd',\r\n        'switch_root_cmd',\r\n        'switch_root_pwd',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          scopedSlots: { customRender: 'host_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          scopedSlots: { customRender: 'ip' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.sshPort'),\r\n          dataIndex: 'ssh_port',\r\n          scopedSlots: { customRender: 'ssh_port' },\r\n          width: 100,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginUser'),\r\n          dataIndex: 'login_user',\r\n          scopedSlots: { customRender: 'login_user' },\r\n          width: 120,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginPassword'),\r\n          dataIndex: 'login_pwd',\r\n          scopedSlots: { customRender: 'login_pwd' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootCmd'),\r\n          dataIndex: 'switch_root_cmd',\r\n          scopedSlots: { customRender: 'switch_root_cmd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootPwd'),\r\n          dataIndex: 'switch_root_pwd',\r\n          scopedSlots: { customRender: 'switch_root_pwd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchHostConfig();\r\n  },\r\n  methods: {\r\n    copyNodeInfo(record) {\r\n      // 创建新的节点数据，复制原节点的所有属性\r\n      const newRecord = {\r\n        ...record,\r\n        key: `new-${Date.now()}`,\r\n        id: undefined,\r\n        editable: true,\r\n        isNew: true,\r\n        host_name: `${record.host_name || ''}_copy`,\r\n        ip: '' // Clear IP as it should be unique\r\n      };\r\n      \r\n      // 在表格开头添加新行\r\n      this.hosts = [newRecord, ...this.hosts];\r\n      this.currentPage = 1; // Reset to first page to show the new row\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n      \r\n      // 滚动到顶部以显示新添加的行\r\n      this.$nextTick(() => {\r\n        const tableBody = document.querySelector('.ant-table-body');\r\n        if (tableBody) {\r\n          tableBody.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    \r\n    getColumnTitle(dataIndex) {\r\n      return this.columns.find((c) => c.dataIndex === dataIndex)?.title || dataIndex;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n    edit(key) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n        target.editable = true;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      try {\r\n        const target = this.hosts.find((item) => item.key === key);\r\n        if (!target || !this.validateHost(target)) return;\r\n\r\n        this.saving = true;\r\n        const hostData = { ...target };\r\n        delete hostData.editable;\r\n        delete hostData.isNew;\r\n\r\n        await axios.post('/api/config/', {\r\n          hosts: [hostData],\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.map((item) =>\r\n          item.key === key ? { ...item, editable: false, isNew: false } : item\r\n        );\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n        this.$message.success('Saved successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to save host');\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.hosts.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.hosts[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.hosts = this.hosts.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.hosts];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.hosts = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.hosts = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          host_name: '',\r\n          ip: '',\r\n          ssh_port: '22',\r\n          login_user: '',\r\n          login_pwd: '',\r\n          switch_root_cmd: '',\r\n          switch_root_pwd: '',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.hosts,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    validateHost(host) {\r\n      if (!host.host_name?.trim()) {\r\n        this.$message.error('Host name is required');\r\n        return false;\r\n      }\r\n      if (!/^(\\d{1,3}\\.){3}\\d{1,3}$/.test(host.ip)) {\r\n        this.$message.error('Invalid IP format');\r\n        return false;\r\n      }\r\n      if (!/^\\d+$/.test(host.ssh_port)) {\r\n        this.$message.error('SSH port must be numeric');\r\n        return false;\r\n      }\r\n      const exist = this.hosts.some((h) => h.ip === host.ip && h.key !== host.key);\r\n      if (exist) {\r\n        this.$message.error('IP address already exists');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    async fetchHostConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/config`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.hosts = response.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `host_${item.host_name}`,\r\n          ssh_port: item.ssh_port?.toString() || '22',\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load hosts');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteHost(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/config/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete host');\r\n        await this.fetchHostConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async batchDelete() {\r\n      try {\r\n        const selectedIds = this.hosts\r\n          .filter(host => this.selectedRowKeys.includes(host.key))\r\n          .map(host => host.id)\r\n          .filter(id => id);\r\n\r\n        if (selectedIds.length === 0) {\r\n          this.$message.warning('No valid hosts selected for deletion');\r\n          return;\r\n        }\r\n\r\n        await axios.post('/api/config/batch-delete', {\r\n          ids: selectedIds,\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.filter(host => !this.selectedRowKeys.includes(host.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Batch deletion completed successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Batch deletion failed');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/config/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'hosts_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n        console.error('Download template error:', error);\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        await axios.post('/api/config/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        await this.fetchHostConfig();\r\n        this.$message.success('Hosts imported successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import hosts');\r\n      }\r\n    },\r\n\r\n    async exportSelectedHosts() {\r\n      try {\r\n        const selectedHosts = this.hosts.filter(host => this.selectedRowKeys.includes(host.key));\r\n\r\n        // Create CSV content\r\n        const headers = [\r\n          'host_name',\r\n          'ip',\r\n          'ssh_port',\r\n          'login_user',\r\n          'login_pwd',\r\n          'switch_root_cmd',\r\n          'switch_root_pwd'\r\n        ];\r\n\r\n        const csvContent = [\r\n          headers.join(','),\r\n          ...selectedHosts.map(host =>\r\n            headers.map(header => host[header] || '').join(',')\r\n          )\r\n        ].join('\\n');\r\n\r\n        // Create and trigger download\r\n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'selected_hosts.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Hosts exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export hosts');\r\n        console.error('Export hosts error:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.host-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 添加按钮组样式 */\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}