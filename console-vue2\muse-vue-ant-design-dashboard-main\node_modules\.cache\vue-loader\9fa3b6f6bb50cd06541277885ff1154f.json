{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PortInfo.vue?vue&type=style&index=0&id=6c0e626a&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PortInfo.vue", "mtime": 1753170356135}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2FyZC1oZWFkZXItd3JhcHBlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmhlYWRlci13cmFwcGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5jZXJ0LWZpZWxkIHsNCiAgbWFyZ2luOiAycHggMDsNCg0KICAuY2VydC1sYWJlbCB7DQogICAgY29sb3I6ICNkMTBkN2Q7DQogICAgZm9udC1zaXplOiAwLjk1ZW07DQogICAgbWluLXdpZHRoOiAxMjBweDsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["PortInfo.vue"], "names": [], "mappings": ";AAshBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PortInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\" width=\"20\" height=\"20\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.port') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchPorts\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs v-model:activeKey=\"activeTabKey\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"tcp\" tab=\"TCP\">\r\n        <a-table\r\n          :columns=\"tcpColumns\"\r\n          :data-source=\"tcpPorts\"\r\n          :rowKey=\"record => `${record.ip}_${record.port}`\"\r\n          :pagination=\"pagination.total > 0 ? pagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No TCP ports found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n\r\n      <a-tab-pane key=\"udp\" tab=\"UDP\">\r\n        <a-table\r\n          :columns=\"udpColumns\"\r\n          :data-source=\"udpPorts\"\r\n          :rowKey=\"record => `${record.ip}_${record.port}`\"\r\n          :pagination=\"udpPagination.total > 0 ? udpPagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No UDP ports found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n\r\n      <a-tab-pane key=\"unix_socket\" tab=\"UNIX Socket\">\r\n        <a-table\r\n          :columns=\"unixSocketColumns\"\r\n          :data-source=\"unixSockets\"\r\n          :rowKey=\"record => record.inode\"\r\n          :pagination=\"unixSocketPagination.total > 0 ? unixSocketPagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No UNIX sockets found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n\r\n    <a-modal\r\n      v-model:visible=\"modalVisible\"\r\n      :title=\"modalTitle\"\r\n      @cancel=\"handleModalClose\"\r\n      width=\"600px\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleModalClose\">Cancel</a-button>\r\n      </template>\r\n      <div style=\"white-space: pre-wrap\">{{ modalContent.join('\\n') }}</div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      tcpPorts: [],\r\n      udpPorts: [],\r\n      unixSockets: [],\r\n      activeTabKey: 'tcp', // 默认选中TCP标签页\r\n      tcpColumns: [\r\n        {\r\n          title: 'Address',\r\n          key: 'address',\r\n          width: 200,\r\n          customRender: (text, record) => `${record.ip}:${record.port}`,\r\n        },\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 200,\r\n          customRender: (pid, record) => {\r\n            if (!pid) return '-';\r\n            const [pidNum, procName] = pid.split('/');\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pidNum)}>\r\n                {pid}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Protocols',\r\n          dataIndex: 'protocols',\r\n          key: 'protocols',\r\n          width: 150,\r\n          customRender: (protocols) => {\r\n            if (!protocols?.offered?.length) return '-';\r\n            return protocols.offered.join(', ');\r\n          },\r\n        },\r\n        {\r\n          title: 'Certificate',\r\n          dataIndex: 'certificate',\r\n          key: 'certificate',\r\n          width: 800,\r\n          customRender: (cert) => {\r\n            if (!cert?.summary?.length) return '-';\r\n\r\n            // 证书字段说明\r\n            const certFieldDescriptions = {\r\n              'CN:': '通用名称 - 证书所标识的实体名称',\r\n              'Issuer:': '证书颁发者 - 签发此证书的证书机构',\r\n              'Subject Alt Names:': '主题备用名 - 证书可以保护的其他域名或IP',\r\n              'Chain Status:': '证书链状态 - 验证证书信任链的完整性',\r\n              'Revocation:': '吊销状态 - 检查证书是否被吊销',\r\n              'Validity Period:': '有效期长度 - 证书的有效时间跨度',\r\n              'Expiration Status:': '过期状态 - 证书是否已过期',\r\n              'Key Size:': '密钥大小 - 证书使用的加密密钥长度',\r\n              'Signature Algorithm:': '签名算法 - 用于签发证书的加密算法',\r\n              'Client Auth:': '客户端认证 - 是否支持客户端证书认证',\r\n              'Key Usage:': '密钥用途 - 证书允许的使用场景',\r\n              'Serial Number:': '序列号 - 证书的唯一标识符',\r\n              'Fingerprint SHA256:': '指纹 - 证书的SHA256哈希值',\r\n              'Valid Until:': '有效期至 - 证书的过期时间'\r\n            };\r\n\r\n            return (\r\n              <div>\r\n                {cert.summary.map(item => {\r\n                  const fieldName = Object.keys(certFieldDescriptions).find(key => item.startsWith(key));\r\n                  const [label, ...valueParts] = item.split(/(?<=:)\\s/);\r\n                  const value = valueParts.join(' ');\r\n\r\n                  return (\r\n                    <a-tooltip key={item} placement=\"right\" title={fieldName ? certFieldDescriptions[fieldName] : ''}>\r\n                      <div class=\"cert-field\">\r\n                        <span class=\"cert-label\">{label}</span> {value}\r\n                      </div>\r\n                    </a-tooltip>\r\n                  );\r\n                })}\r\n              </div>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'HTTP Info',\r\n          dataIndex: 'http_info',\r\n          key: 'http_info',\r\n          width: 150,\r\n          customRender: (httpInfo) => {\r\n            if (!httpInfo?.raw_output) return 'No response';\r\n\r\n            // Extract status code from raw response\r\n            const statusCodeMatch = httpInfo.raw_output.match(/HTTP\\/[\\d.]+ (\\d{3})/);\r\n            const statusCode = statusCodeMatch ? statusCodeMatch[1] : 'Unknown';\r\n\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('HTTP Response', [\r\n                `Status Code: ${statusCode}`,\r\n                `Protocol: ${httpInfo.protocol}`,\r\n                '---',\r\n                'Raw Response:',\r\n                httpInfo.raw_output\r\n              ])}>\r\n                {statusCode}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Cipher Suites',\r\n          dataIndex: 'cipher_suites',\r\n          key: 'cipher_suites',\r\n          width: 150,\r\n          customRender: (cipherSuites) => {\r\n            if (!cipherSuites?.details?.length) return '-';\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('Cipher Suites', cipherSuites.details)}>\r\n                {`${cipherSuites.details.length} suites`}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Vulnerabilities',\r\n          dataIndex: 'vulnerabilities',\r\n          key: 'vulnerabilities',\r\n          width: 150,\r\n          customRender: (vulns) => {\r\n            if (!vulns?.critical?.length) return 'No vulnerabilities';\r\n            const details = vulns.critical.map(v =>\r\n              `${v.name} (${v.severity}): ${v.status}`\r\n            );\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('Vulnerabilities', details)}>\r\n                {`${vulns.critical.length} vulnerabilities`}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n      ],\r\n\r\n      // UDP端口列\r\n      udpColumns: [\r\n        {\r\n          title: 'Proto',\r\n          dataIndex: 'proto',\r\n          key: 'proto',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Recv-Q',\r\n          dataIndex: 'recv_q',\r\n          key: 'recv_q',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Send-Q',\r\n          dataIndex: 'send_q',\r\n          key: 'send_q',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Local Address',\r\n          key: 'local_address',\r\n          width: 180,\r\n          customRender: (_, record) => `${record.ip}:${record.port}`,\r\n        },\r\n        {\r\n          title: 'Foreign Address',\r\n          dataIndex: 'foreign_address',\r\n          key: 'foreign_address',\r\n          width: 180,\r\n        },\r\n        {\r\n          title: 'State',\r\n          dataIndex: 'state',\r\n          key: 'state',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'PID/Program',\r\n          dataIndex: 'pid_program',\r\n          key: 'pid_program',\r\n          width: 300,\r\n          customRender: (pid_program) => {\r\n            if (!pid_program) return '-';\r\n            const parts = pid_program.split('/');\r\n            const pid = parts[0];\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pid)}>\r\n                {pid_program}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n      ],\r\n\r\n      // UNIX Socket列\r\n      unixSocketColumns: [\r\n        {\r\n          title: 'Proto',\r\n          dataIndex: 'proto',\r\n          key: 'proto',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'RefCnt',\r\n          dataIndex: 'refcnt',\r\n          key: 'refcnt',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Flags',\r\n          dataIndex: 'flags',\r\n          key: 'flags',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'type',\r\n          key: 'type',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'State',\r\n          dataIndex: 'state',\r\n          key: 'state',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'I-Node',\r\n          dataIndex: 'inode',\r\n          key: 'inode',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'PID/Program',\r\n          dataIndex: 'pid_program',\r\n          key: 'pid_program',\r\n          width: 180,\r\n          customRender: (pid_program) => {\r\n            if (!pid_program) return '-';\r\n            const parts = pid_program.split('/');\r\n            const pid = parts[0];\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pid)}>\r\n                {pid_program}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Path',\r\n          dataIndex: 'path',\r\n          key: 'path',\r\n          width: 400,\r\n          customRender: (path) => {\r\n            if (!path) return '-';\r\n            return <div style=\"word-break: break-word;\">{path}</div>;\r\n          },\r\n        },\r\n      ],\r\n\r\n      // TCP端口分页\r\n      pagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.pagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'tcp' }\r\n          });\r\n          this.fetchPorts('tcp');\r\n        },\r\n      },\r\n\r\n      // UDP端口分页\r\n      udpPagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.udpPagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'udp' }\r\n          });\r\n          this.fetchPorts('udp');\r\n        },\r\n      },\r\n\r\n      // UNIX Socket分页\r\n      unixSocketPagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.unixSocketPagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'unix_socket' }\r\n          });\r\n          this.fetchPorts('unix_socket');\r\n        },\r\n      },\r\n      modalVisible: false,\r\n      modalTitle: '',\r\n      modalContent: [],\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp() {\r\n      this.fetchPorts('tcp');\r\n      this.fetchPorts('udp');\r\n      this.fetchPorts('unix_socket');\r\n    },\r\n    '$route.query.page': {\r\n      handler(newPage) {\r\n        const portType = this.$route.query.port_type || 'tcp';\r\n        if (newPage) {\r\n          if (portType === 'tcp' && parseInt(newPage) !== this.pagination.current) {\r\n            this.pagination.current = parseInt(newPage);\r\n            this.fetchPorts('tcp');\r\n          } else if (portType === 'udp' && parseInt(newPage) !== this.udpPagination.current) {\r\n            this.udpPagination.current = parseInt(newPage);\r\n            this.fetchPorts('udp');\r\n          } else if (portType === 'unix_socket' && parseInt(newPage) !== this.unixSocketPagination.current) {\r\n            this.unixSocketPagination.current = parseInt(newPage);\r\n            this.fetchPorts('unix_socket');\r\n          }\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    '$route.query.port_type': {\r\n      handler(newPortType) {\r\n        if (newPortType) {\r\n          this.activeTabKey = newPortType;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchPorts('tcp');\r\n    this.fetchPorts('udp');\r\n    this.fetchPorts('unix_socket');\r\n  },\r\n  methods: {\r\n    // 处理标签页切换\r\n    handleTabChange(activeKey) {\r\n      this.activeTabKey = activeKey;\r\n      this.$router.replace({\r\n        query: { ...this.$route.query, port_type: activeKey }\r\n      });\r\n      this.fetchPorts(activeKey);\r\n    },\r\n\r\n    async fetchPorts(portType = 'tcp') {\r\n      if (!this.selectedNodeIp) {\r\n        this.tcpPorts = [];\r\n        this.udpPorts = [];\r\n        this.unixSockets = [];\r\n        this.pagination.total = 0;\r\n        this.udpPagination.total = 0;\r\n        this.unixSocketPagination.total = 0;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        let pagination;\r\n        if (portType === 'tcp') {\r\n          pagination = this.pagination;\r\n        } else if (portType === 'udp') {\r\n          pagination = this.udpPagination;\r\n        } else if (portType === 'unix_socket') {\r\n          pagination = this.unixSocketPagination;\r\n        }\r\n\r\n        const { current, pageSize } = pagination;\r\n        const response = await axios.get(`/api/port/${this.selectedNodeIp}`, {\r\n          params: {\r\n            page: current,\r\n            page_size: pageSize,\r\n            port_type: portType,\r\n            dbFile: this.currentProject\r\n          },\r\n        });\r\n\r\n        if (portType === 'tcp') {\r\n          this.tcpPorts = response.data.data || response.data;\r\n          this.pagination.total = response.data.total || 0;\r\n        } else if (portType === 'udp') {\r\n          this.udpPorts = response.data.data || response.data;\r\n          this.udpPagination.total = response.data.total || 0;\r\n        } else if (portType === 'unix_socket') {\r\n          this.unixSockets = response.data.data || response.data;\r\n          this.unixSocketPagination.total = response.data.total || 0;\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching ${portType} ports:`, error);\r\n        this.$message.error(`Failed to fetch ${portType} ports data`);\r\n\r\n        if (portType === 'tcp') {\r\n          this.tcpPorts = [];\r\n          this.pagination.total = 0;\r\n        } else if (portType === 'udp') {\r\n          this.udpPorts = [];\r\n          this.udpPagination.total = 0;\r\n        } else if (portType === 'unix_socket') {\r\n          this.unixSockets = [];\r\n          this.unixSocketPagination.total = 0;\r\n        }\r\n      }\r\n    },\r\n    navigateToProcessDetail(pid) {\r\n      this.$router.push({\r\n        name: 'ProcessDetail',\r\n        params: { pid: pid },\r\n        query: { page: this.pagination.current }\r\n      });\r\n    },\r\n    showDetailsModal(title, content) {\r\n      this.modalTitle = title;\r\n      this.modalContent = content;\r\n      this.modalVisible = true;\r\n    },\r\n    handleModalClose() {\r\n      this.modalVisible = false;\r\n      this.modalContent = [];\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.cert-field {\r\n  margin: 2px 0;\r\n\r\n  .cert-label {\r\n    color: #d10d7d;\r\n    font-size: 0.95em;\r\n    min-width: 120px;\r\n    display: inline-block;\r\n  }\r\n}\r\n</style>\r\n"]}]}