(function(e){function t(t){for(var s,o,n=t[0],l=t[1],c=t[2],d=0,u=[];d<n.length;d++)o=n[d],Object.prototype.hasOwnProperty.call(i,o)&&i[o]&&u.push(i[o][0]),i[o]=0;for(s in l)Object.prototype.hasOwnProperty.call(l,s)&&(e[s]=l[s]);h&&h(t);while(u.length)u.shift()();return r.push.apply(r,c||[]),a()}function a(){for(var e,t=0;t<r.length;t++){for(var a=r[t],s=!0,o=1;o<a.length;o++){var n=a[o];0!==i[n]&&(s=!1)}s&&(r.splice(t--,1),e=l(l.s=a[0]))}return e}var s={},o={app:0},i={app:0},r=[];function n(e){return l.p+"static/js/"+({}[e]||e)+"."+{"chunk-08a0de52":"e5253694","chunk-1a22db4a":"bbd2dd14","chunk-29a53b76":"583083ec","chunk-129ecfa6":"d536c141","chunk-60b6d59a":"301947dd","chunk-2d0e95df":"9e796ba1","chunk-364a09a0":"ff89fb38","chunk-3a5d12e0":"df8d3418","chunk-4821dd0f":"193b2915","chunk-49d38e76":"b910ab97","chunk-5d9c9d94":"2d32b357","chunk-5e8ae900":"4462f1b5","chunk-65340c0b":"95205db6","chunk-6fdea86c":"fcc82cd1","chunk-7155ac4e":"c5071787","chunk-72e4fea3":"e154e834","chunk-791bd3a8":"eef7974c","chunk-7dbc10af":"8df0027e","chunk-8a9b96a2":"5297d144","chunk-cfa15118":"8a0cb356","chunk-d588eafa":"339ed414","chunk-ddd725e6":"119c50e2"}[e]+".js"}function l(t){if(s[t])return s[t].exports;var a=s[t]={i:t,l:!1,exports:{}};return e[t].call(a.exports,a,a.exports,l),a.l=!0,a.exports}l.e=function(e){var t=[],a={"chunk-08a0de52":1,"chunk-1a22db4a":1,"chunk-29a53b76":1,"chunk-129ecfa6":1,"chunk-60b6d59a":1,"chunk-364a09a0":1,"chunk-3a5d12e0":1,"chunk-4821dd0f":1,"chunk-49d38e76":1,"chunk-5d9c9d94":1,"chunk-5e8ae900":1,"chunk-65340c0b":1,"chunk-6fdea86c":1,"chunk-7155ac4e":1,"chunk-72e4fea3":1,"chunk-791bd3a8":1,"chunk-7dbc10af":1,"chunk-8a9b96a2":1,"chunk-cfa15118":1,"chunk-d588eafa":1,"chunk-ddd725e6":1};o[e]?t.push(o[e]):0!==o[e]&&a[e]&&t.push(o[e]=new Promise((function(t,a){for(var s="static/css/"+({}[e]||e)+"."+{"chunk-08a0de52":"fa3cff66","chunk-1a22db4a":"5af5a0b5","chunk-29a53b76":"8bb04b2d","chunk-129ecfa6":"a943d34a","chunk-60b6d59a":"fcd3ef06","chunk-2d0e95df":"31d6cfe0","chunk-364a09a0":"cf0f0211","chunk-3a5d12e0":"38432d17","chunk-4821dd0f":"7fc11cc4","chunk-49d38e76":"ea20b8c8","chunk-5d9c9d94":"0e433876","chunk-5e8ae900":"0e433876","chunk-65340c0b":"ea22d9f3","chunk-6fdea86c":"c99882b2","chunk-7155ac4e":"763ddbd2","chunk-72e4fea3":"0e433876","chunk-791bd3a8":"add1274d","chunk-7dbc10af":"0ecfc768","chunk-8a9b96a2":"f4e6171b","chunk-cfa15118":"4567523e","chunk-d588eafa":"7c04c7b7","chunk-ddd725e6":"91e8af76"}[e]+".css",i=l.p+s,r=document.getElementsByTagName("link"),n=0;n<r.length;n++){var c=r[n],d=c.getAttribute("data-href")||c.getAttribute("href");if("stylesheet"===c.rel&&(d===s||d===i))return t()}var u=document.getElementsByTagName("style");for(n=0;n<u.length;n++){c=u[n],d=c.getAttribute("data-href");if(d===s||d===i)return t()}var h=document.createElement("link");h.rel="stylesheet",h.type="text/css",h.onload=t,h.onerror=function(t){var s=t&&t.target&&t.target.src||i,r=new Error("Loading CSS chunk "+e+" failed.\n("+s+")");r.code="CSS_CHUNK_LOAD_FAILED",r.request=s,delete o[e],h.parentNode.removeChild(h),a(r)},h.href=i;var p=document.getElementsByTagName("head")[0];p.appendChild(h)})).then((function(){o[e]=0})));var s=i[e];if(0!==s)if(s)t.push(s[2]);else{var r=new Promise((function(t,a){s=i[e]=[t,a]}));t.push(s[2]=r);var c,d=document.createElement("script");d.charset="utf-8",d.timeout=120,l.nc&&d.setAttribute("nonce",l.nc),d.src=n(e);var u=new Error;c=function(t){d.onerror=d.onload=null,clearTimeout(h);var a=i[e];if(0!==a){if(a){var s=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;u.message="Loading chunk "+e+" failed.\n("+s+": "+o+")",u.name="ChunkLoadError",u.type=s,u.request=o,a[1](u)}i[e]=void 0}};var h=setTimeout((function(){c({type:"timeout",target:d})}),12e4);d.onerror=d.onload=c,document.head.appendChild(d)}return Promise.all(t)},l.m=e,l.c=s,l.d=function(e,t,a){l.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},l.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},l.t=function(e,t){if(1&t&&(e=l(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(l.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)l.d(a,s,function(t){return e[t]}.bind(null,s));return a},l.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return l.d(t,"a",t),t},l.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},l.p="/",l.oe=function(e){throw console.error(e),e};var c=window["webpackJsonp"]=window["webpackJsonp"]||[],d=c.push.bind(c);c.push=t,c=c.slice();for(var u=0;u<c.length;u++)t(c[u]);var h=d;r.push([0,"chunk-vendors"]),a()})({0:function(e,t,a){e.exports=a("56d7")},"0a04":function(e,t,a){},"15fb":function(e,t,a){},"1ab8":function(e,t,a){},"1ce9":function(e,t,a){"use strict";a("61e1")},"241b":function(e,t,a){},"381a":function(e,t,a){},4678:function(e,t,a){var s={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df4","./fa.js":"8df4","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b4","./gd.js":"f6b4","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function o(e){var t=i(e);return a(t)}function i(e){if(!a.o(s,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return s[e]}o.keys=function(){return Object.keys(s)},o.resolve=i,e.exports=o,o.id="4678"},"4f87":function(e,t,a){},"56a3":function(e,t,a){"use strict";a("381a")},"56d7":function(e,t,a){"use strict";a.r(t);var s=a("a026"),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[e.isReady?t(e.layout,{tag:"component"},[t("router-view")],1):t("div",[e._v("Loading...")]),t("DifyChatBot")],1)},i=[],r=function(){var e=this,t=e._self._c;return t("div")},n=[],l={name:"DifyChatBot",mounted(){window.difyChatbotConfig={token:"GAi2PqkyQkz1L6jm",baseUrl:"http://**************",systemVariables:{},userVariables:{},containerProps:{},draggable:!0,dragAxis:"both"};const e=document.createElement("script");e.src="http://**************/embed.min.js",e.id="GAi2PqkyQkz1L6jm",e.defer=!0,document.head.appendChild(e);const t=document.createElement("style");t.textContent="\n      #dify-chatbot-bubble-button {\n        background-color: #1C64F2 !important;\n      }\n      #dify-chatbot-bubble-window {\n        width: 32rem !important;\n        height: 48rem !important;\n      }\n    ",document.head.appendChild(t)}},c=l,d=(a("b14b"),a("2877")),u=Object(d["a"])(c,r,n,!1,null,"67100c59",null),h=u.exports,p={name:"App",components:{DifyChatBot:h},data(){return{isReady:!1}},computed:{layout(){return"layout-"+(this.$route.meta.layout||"dashboard")},isDarkMode(){return this.$store.state.darkMode}},created(){this.isReady=!0,this.$store.state.darkMode?document.documentElement.classList.add("dark-mode"):document.documentElement.classList.remove("dark-mode")},watch:{isDarkMode(e){e?document.documentElement.classList.add("dark-mode"):document.documentElement.classList.remove("dark-mode")}}},m=p,g=Object(d["a"])(m,o,i,!1,null,null,null),f=g.exports,b=(a("0643"),a("a573"),a("8c4f")),v=function(){var e=this,t=e._self._c;return t("div",[t("a-card",{attrs:{title:"项目列表"},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-button",{attrs:{type:"primary"},on:{click:e.createNewProject}},[e._v(" 创建新项目 ")])]},proxy:!0}])},[t("a-table",{staticClass:"project-table",attrs:{columns:e.columns,"data-source":e.projects,"row-key":e=>e.dbFile,customRow:e.onCustomRow}})],1)],1)},w=[],C=a("fec3"),k={name:"ProjectManager",data(){const e=this.$createElement;return{columns:[{title:"项目名称",dataIndex:"name",key:"name",width:"250px",ellipsis:!0,customRender:t=>e("span",[e("a-icon",{attrs:{type:"folder"},style:"margin-right: 8px;"}),t])},{title:"数据库文件",dataIndex:"dbFile",key:"dbFile",width:"280px",ellipsis:!0},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:"160px",customRender:e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},{title:"操作",key:"action",width:"180px",align:"center",fixed:"right",customRender:(t,a)=>e("a-space",{attrs:{size:8}},[e("a-button",{attrs:{type:"primary"},on:{click:()=>this.selectProject(a)}},["进入项目"]),e("a-popconfirm",{attrs:{title:"确定要删除这个项目吗？",okText:"确定",cancelText:"取消"},on:{confirm:()=>this.deleteProject(a)}},[e("a-button",{attrs:{type:"danger"}},["删除"])])])}],projects:[],tempProjectName:""}},methods:{onCustomRow(e){return{on:{click:()=>{}}}},async fetchProjects(){try{const e=await C["a"].get("/api/projects");Array.isArray(e.data)?this.projects=e.data.map(e=>({name:e.name||"",dbFile:e.dbFile||"",createdAt:e.createdAt||"",key:e.dbFile||Date.now().toString()})):(this.projects=[],console.error("项目数据格式无效：",e.data))}catch(e){console.error("获取项目列表失败：",e),this.$message.error("获取项目列表失败"),this.projects=[]}},async selectProject(e){if(null===e||void 0===e||!e.dbFile)return console.error("项目数据无效：",e),void this.$message.error("无效的项目数据");try{const t=encodeURIComponent(e.dbFile),a="/api/projects/validate/"+t,s=await C["a"].get(a);s.data.valid?(await this.$store.dispatch("switchProject",{dbFile:e.dbFile,projectName:e.name}),await this.$store.dispatch("fetchNodes"),localStorage.removeItem("activeTaskId"),localStorage.removeItem("taskCompleted"),localStorage.removeItem("activeUploadTaskId"),localStorage.removeItem("activeDownloadTaskId"),localStorage.removeItem("activeToolTaskId"),await this.$router.push("/task"),this.$message.success("成功进入项目")):(console.error("验证失败：",s.data.error),this.$message.error(s.data.error||"数据库文件无效或已损坏"))}catch(a){var t;console.error("项目验证出错：",a),this.$message.error((null===(t=a.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"验证项目失败")}},async deleteProject(e){if(null!==e&&void 0!==e&&e.dbFile)try{await C["a"].delete("/api/projects/"+encodeURIComponent(e.dbFile)),this.$message.success("项目删除成功"),await this.fetchProjects()}catch(t){console.error("Error deleting project:",t),this.$message.error("删除项目失败")}else this.$message.error("无效的项目数据")},async createNewProject(){this.$createElement;try{const t=await new Promise((e,t)=>{this.$confirm({title:"创建新项目",content:e=>e("div",[e("a-input",{attrs:{placeholder:"请输入项目名称"},on:{change:e=>{const t=e.target.value.replace(/[^a-zA-Z0-9_-]/g,"");this.tempProjectName=t,e.target.value=t}}}),e("div",{class:"project-hint-text",style:"font-size: 12px; margin-top: 8px;"},["只允许输入大小写字母、数字、下划线和连字符"])]),okText:"确定",cancelText:"取消",onOk:()=>this.tempProjectName?/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)?void e(this.tempProjectName):(this.$message.warning("项目名称只能包含大小写字母、数字、下划线和连字符"),Promise.reject()):(this.$message.warning("请输入项目名称"),Promise.reject()),onCancel:()=>{t()}})});if(t){var e;const a=await C["a"].post("/api/projects/new",{name:t});await this.fetchProjects(),this.$message.success("新项目创建成功"),null!==(e=a.data)&&void 0!==e&&e.dbFile&&await this.selectProject(a.data)}}catch(t){t&&(console.error("Error creating new project:",t),this.$message.error("创建新项目失败"))}}},created(){this.fetchProjects()}},y=k,j=(a("d851"),Object(d["a"])(y,v,w,!1,null,"368921a4",null)),S=j.exports;s["a"].use(b["a"]);let P=[{path:"/",redirect:"/projects"},{path:"/projects",name:"ProjectManager",layout:"simple",component:S},{path:"/task",name:"Task",layout:"dashboard",component:()=>Promise.all([a.e("chunk-29a53b76"),a.e("chunk-129ecfa6")]).then(a.bind(null,"1d21"))},{path:"*",component:()=>a.e("chunk-2d0e95df").then(a.bind(null,"8cdb"))},{path:"/process",name:"Process",layout:"dashboard",component:()=>a.e("chunk-364a09a0").then(a.bind(null,"da71"))},{path:"/process/:pid",name:"ProcessDetail",layout:"dashboard",component:()=>a.e("chunk-1a22db4a").then(a.bind(null,"0dc8"))},{path:"/package",name:"Package",layout:"dashboard",component:()=>a.e("chunk-d588eafa").then(a.bind(null,"283a"))},{path:"/hardware",name:"Hardware",layout:"dashboard",component:()=>a.e("chunk-6fdea86c").then(a.bind(null,"14b2"))},{path:"/filesystem",name:"Filesystem",layout:"dashboard",component:()=>a.e("chunk-7155ac4e").then(a.bind(null,"7eb5"))},{path:"/port",name:"Port",layout:"dashboard",component:()=>a.e("chunk-4821dd0f").then(a.bind(null,"9110"))},{path:"/docker",name:"Docker",layout:"dashboard",component:()=>a.e("chunk-08a0de52").then(a.bind(null,"0ce3"))},{path:"/kubernetes",name:"Kubernetes",layout:"dashboard",component:()=>a.e("chunk-8a9b96a2").then(a.bind(null,"e2cd"))},{path:"/code-info",name:"CodeInfo",layout:"dashboard",component:()=>a.e("chunk-72e4fea3").then(a.bind(null,"dccb"))},{path:"/material-info",name:"MaterialInfo",layout:"dashboard",component:()=>a.e("chunk-5d9c9d94").then(a.bind(null,"0c6e"))},{path:"/config",name:"Config",layout:"dashboard",component:()=>a.e("chunk-cfa15118").then(a.bind(null,"1071")),props:e=>({defaultTab:e.hash.replace("#","")||"host"})},{path:"/repository",name:"Repository",layout:"dashboard",component:()=>a.e("chunk-49d38e76").then(a.bind(null,"405d"))},{path:"/upload",name:"Upload",layout:"dashboard",component:()=>a.e("chunk-3a5d12e0").then(a.bind(null,"2d74"))},{path:"/download",name:"Download",layout:"dashboard",component:()=>a.e("chunk-791bd3a8").then(a.bind(null,"dc7d"))},{path:"/aibash",name:"AIBash",layout:"dashboard",component:()=>a.e("chunk-ddd725e6").then(a.bind(null,"6db8"))},{path:"/testcase",name:"TestCase",layout:"dashboard",component:()=>a.e("chunk-7dbc10af").then(a.bind(null,"c570"))},{path:"/execute-case",name:"ExecuteCase",layout:"dashboard",component:()=>a.e("chunk-5e8ae900").then(a.bind(null,"c3cd"))},{path:"/smart-orchestration",name:"SmartOrchestration",layout:"dashboard",component:()=>a.e("chunk-65340c0b").then(a.bind(null,"e300"))},{path:"/tools",name:"Tools",layout:"dashboard",component:()=>Promise.all([a.e("chunk-29a53b76"),a.e("chunk-60b6d59a")]).then(a.bind(null,"f720"))}];function L(e,t="default"){return e.meta=e.meta||{},e.meta.layout=e.layout||t,e.children&&(e.children=e.children.map(t=>L(t,e.meta.layout))),e}P=P.map(e=>L(e));const x=new b["a"]({mode:"hash",base:"/",routes:P,scrollBehavior(e,t,a){return e.hash?{selector:e.hash,behavior:"smooth"}:{x:0,y:0,behavior:"smooth"}}});x.onReady(()=>{const e=x.currentRoute.path;"/"===e&&x.push("/projects").catch(e=>{if("NavigationDuplicated"!==e.name)throw e})}),x.beforeEach((e,t,a)=>{if("/config"===e.path){const t=["host","cbh"],s=e.hash.replace("#","");if(!t.includes(s))return a({path:"/config",hash:"#host",replace:!0})}const s=e.path===t.path,o=e.hash===t.hash,i=JSON.stringify(e.query)===JSON.stringify(t.query);s&&o&&i?a(!1):a()}),x.afterEach((e,t)=>{});var T=x,_=(a("2382"),a("4e3e"),a("2f62"));s["a"].use(_["a"]);const N={namespaced:!0,state:{currentPage:1,scrollPosition:0,lastViewedPid:null},mutations:{setCurrentPage(e,t){e.currentPage=t},setScrollPosition(e,t){e.scrollPosition=t},setLastViewedPid(e,t){e.lastViewedPid=t},resetState(e){e.currentPage=1,e.scrollPosition=0},clearLastViewedPid(e){e.lastViewedPid=null}},actions:{updateCurrentPage({commit:e},t){e("setCurrentPage",t)},updateScrollPosition({commit:e},t){e("setScrollPosition",t)},updateLastViewedPid({commit:e},t){e("setLastViewedPid",t)},resetState({commit:e}){e("resetState")},clearLastViewedPid({commit:e}){e("clearLastViewedPid")}}};var M=new _["a"].Store({modules:{processList:N},state:{nodes:[],selectedNodeIp:null,activeUploadTask:null,activeDownloadTask:null,activeTask:null,activeToolTask:null,repositoryDownloadResults:null,currentProject:localStorage.getItem("currentProject"),currentProjectName:localStorage.getItem("currentProjectName"),sidebarColor:localStorage.getItem("sidebarColor")||"primary",notifications:[],language:localStorage.getItem("language")||"en-US",darkMode:"false"!==localStorage.getItem("darkMode"),smartSearchResults:[]},getters:{unreadNotifications:e=>e.notifications.filter(e=>!e.read).length},mutations:{setNodes(e,t){e.nodes=t},setSelectedNodeIp(e,t){e.selectedNodeIp=t},setActiveUploadTask(e,t){e.activeUploadTask=t},setActiveDownloadTask(e,t){e.activeDownloadTask=t},setActiveTask(e,t){e.activeTask=t},clearActiveTask(e){e.activeTask=null},setActiveToolTask(e,t){e.activeToolTask=t},clearActiveToolTask(e){e.activeToolTask=null},setRepositoryDownloadResults(e,t){e.repositoryDownloadResults=t},clearRepositoryDownloadResults(e){e.repositoryDownloadResults=null},setCurrentProject(e,{dbFile:t,projectName:a}){e.currentProject=t,e.currentProjectName=a,localStorage.setItem("currentProject",t),localStorage.setItem("currentProjectName",a||"")},setSidebarColor(e,t){e.sidebarColor=t,localStorage.setItem("sidebarColor",t)},setLanguage(e,t){e.language=t,localStorage.setItem("language",t)},addNotification(e,t){e.notifications.unshift(t)},markNotificationsAsRead(e){e.notifications.forEach(e=>e.read=!0)},clearNotifications(e){e.notifications=[]},setDarkMode(e,t){e.darkMode=t,localStorage.setItem("darkMode",t),t?document.documentElement.classList.add("dark-mode"):document.documentElement.classList.remove("dark-mode")},setSmartSearchResults(e,t){e.smartSearchResults=t},clearSmartSearch(e){e.smartSearchResults=[]}},actions:{async fetchNodes({state:e,commit:t}){try{if(!e.currentProject)return void t("setNodes",[]);const a=await C["a"].get("/api/config",{params:{detail:!0,dbFile:e.currentProject}});Array.isArray(a.data)?t("setNodes",a.data):(console.error("Invalid nodes data format:",a.data),t("setNodes",[]))}catch(a){console.error("Error fetching nodes:",a),t("setNodes",[])}},updateUploadTask({commit:e},t){e("setActiveUploadTask",t)},updateDownloadTask({commit:e},t){e("setActiveDownloadTask",t)},updateTask({commit:e},t){e("setActiveTask",t)},clearActiveTask({commit:e}){e("clearActiveTask")},updateToolTask({commit:e},t){e("setActiveToolTask",t)},clearActiveToolTask({commit:e}){e("clearActiveToolTask")},updateRepositoryDownloadResults({commit:e},t){e("setRepositoryDownloadResults",t)},clearRepositoryDownloadResults({commit:e}){e("clearRepositoryDownloadResults")},updateSidebarColor({commit:e},t){e("setSidebarColor",t)},updateLanguage({commit:e},t){e("setLanguage",t)},addNotification({commit:e},t){e("addNotification",{...t,id:Date.now(),time:(new Date).toLocaleTimeString(),read:!1})},markNotificationsAsRead({commit:e}){e("markNotificationsAsRead")},clearNotifications({commit:e}){e("clearNotifications")},toggleDarkMode({commit:e,state:t}){e("setDarkMode",!t.darkMode)},updateSmartSearchResults({commit:e},t){e("setSmartSearchResults",t)},clearSmartSearch({commit:e}){e("clearSmartSearch")},clearProjectStates({commit:e,dispatch:t}){e("setActiveUploadTask",null),e("setActiveDownloadTask",null),e("clearActiveTask"),e("clearActiveToolTask"),e("clearRepositoryDownloadResults"),e("clearSmartSearch"),e("setNodes",[]),e("setSelectedNodeIp",null),t("processList/resetState",null,{root:!0}),t("processList/clearLastViewedPid",null,{root:!0})},switchProject({commit:e,dispatch:t},{dbFile:a,projectName:s}){t("clearProjectStates"),e("setCurrentProject",{dbFile:a,projectName:s})}}}),z=a("f23d"),D=(a("202f"),function(){var e=this,t=e._self._c;return t("div",[t("a-layout",{staticClass:"layout-simple"},[t("a-layout-content",[t("div",{staticClass:"content-wrapper"},[e.isRouterViewMounted?t("router-view"):t("div",[e._v("Loading...")])],1)])],1)],1)}),$=[],A={name:"SimpleLayout",data(){return{isRouterViewMounted:!1}},mounted(){this.$nextTick(()=>{this.isRouterViewMounted=!0})}},I=A,R=(a("1ce9"),Object(d["a"])(I,D,$,!1,null,"5b12b09b",null)),F=R.exports,B=function(){var e=this,t=e._self._c;return t("div",[t("a-layout",{staticClass:"layout-dashboard",class:[e.navbarFixed?"navbar-fixed":"",e.sidebarCollapsed?"":"has-sidebar",e.layoutClass],attrs:{id:"layout-dashboard"}},[t("DashboardSidebar",{attrs:{sidebarCollapsed:e.sidebarCollapsed,sidebarColor:e.sidebarColor,sidebarTheme:e.sidebarTheme},on:{toggleSidebar:e.toggleSidebar}}),t("a-layout",[t("DashboardHeader",{attrs:{sidebarCollapsed:e.sidebarCollapsed,navbarFixed:e.navbarFixed,sidebarColor:e.sidebarColor},on:{toggleSettingsDrawer:e.toggleSettingsDrawer,toggleSidebar:e.toggleSidebar}}),t("a-layout-content",[t("router-view")],1),t("DashboardFooter"),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.sidebarCollapsed,expression:"! sidebarCollapsed"}],staticClass:"sidebar-overlay",on:{click:function(t){e.sidebarCollapsed=!0}}})],1),t("DashboardSettingsDrawer",{attrs:{showSettingsDrawer:e.showSettingsDrawer,navbarFixed:e.navbarFixed,sidebarTheme:e.sidebarTheme},on:{toggleSettingsDrawer:e.toggleSettingsDrawer,toggleNavbarPosition:e.toggleNavbarPosition,updateSidebarTheme:e.updateSidebarTheme,updateSidebarColor:e.updateSidebarColor}})],1)],1)},E=[],U=function(){var e=this,t=e._self._c;return t("a-layout-sider",{directives:[{name:"show",rawName:"v-show",value:!e.sidebarCollapsed,expression:"!sidebarCollapsed"}],staticClass:"sider-primary",class:["ant-layout-sider-"+e.sidebarColor,"ant-layout-sider-"+e.sidebarTheme],style:{backgroundColor:"transparent"},attrs:{trigger:null,collapsible:"","collapsed-width":0,width:"220",theme:"light"}},[t("div",{staticClass:"sidebar-container"},[t("div",{staticClass:"brand-section"},[t("div",{staticClass:"brand-header"},[t("div",{staticClass:"logo-container",class:"text-"+e.sidebarColor},[t("svg",{staticClass:"ios-logo",attrs:{xmlns:"http://www.w3.org/2000/svg",width:"36px",height:"36px",viewBox:"0 0 48 48"}},[t("rect",{attrs:{width:"48",height:"48",rx:"10",fill:"currentColor",opacity:"0.1"}}),t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-width":"2.2","stroke-linecap":"round","stroke-linejoin":"round",d:"M24 43.5c9.043-3.117 15.488-10.363 16.5-19.589c.28-4.005.256-8.025-.072-12.027a2.54 2.54 0 0 0-2.467-2.366c-4.091-.126-8.846-.808-12.52-4.427a2.05 2.05 0 0 0-2.881 0c-3.675 3.619-8.43 4.301-12.52 4.427a2.54 2.54 0 0 0-2.468 2.366A79.4 79.4 0 0 0 7.5 23.911C8.51 33.137 14.957 40.383 24 43.5z"}},[t("animate",{attrs:{attributeName:"stroke-dasharray",values:"0 150;150 0;0 150",dur:"5s",calcMode:"linear",repeatCount:"indefinite"}})]),t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-width":"0.8","stroke-linecap":"round","stroke-linejoin":"round",opacity:"0.6",d:"M24 39c7.5-2.5 12.5-8.5 13.5-16c.2-3 .2-6 0-9"}}),t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-width":"0.8","stroke-linecap":"round","stroke-linejoin":"round",opacity:"0.6",d:"M24 39c-7.5-2.5-12.5-8.5-13.5-16c-.2-3-.2-6 0-9"}}),t("circle",{attrs:{cx:"24",cy:"20.206",r:"4.5",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",d:"M31.589 32.093a7.589 7.589 0 1 0-15.178 0"}}),t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round",d:"M24 20.2v2.5"}})])]),t("div",{staticClass:"brand-text-container"},[t("div",{staticClass:"brand-name"},[e._v("SecTest Copilot")])])]),t("div",{staticClass:"brand-divider"})]),t("div",{staticClass:"navigation-section"},[t("a-menu",{staticClass:"sidebar-menu",attrs:{theme:"light",mode:"inline","default-open-keys":["securityTool","llmAutoTesting","aiTaintAnalysis"],"inline-collapsed":e.sidebarCollapsed}},[t("a-sub-menu",{key:"envAwareness",scopedSlots:e._u([{key:"title",fn:function({open:a}){return[t("span",{staticClass:"enhanced-title"},[e._v(e._s(e.$t("sidebar.envAwareness")))])]}}])},[t("a-menu-item",{key:"process"},[t("router-link",{attrs:{to:"/process"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"}},[t("path",{attrs:{d:"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.processInfo")))])])],1),t("a-menu-item",{key:"package"},[t("router-link",{attrs:{to:"/package"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 16 16"}},[t("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z","clip-rule":"evenodd"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.packageInfo")))])])],1),t("a-menu-item",{key:"hardware"},[t("router-link",{attrs:{to:"/hardware"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 512 512"}},[t("path",{attrs:{fill:"#ffffff",d:"M160 160h192v192H160z"}}),t("path",{attrs:{fill:"#ffffff",d:"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.hardwareInfo")))])])],1),t("a-menu-item",{key:"filesystem"},[t("router-link",{attrs:{to:"/filesystem"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 16 16"}},[t("path",{attrs:{fill:"#ffffff","fill-rule":"evenodd",d:"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z","clip-rule":"evenodd"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.filesystemInfo")))])])],1),t("a-menu-item",{key:"high-port-info"},[t("router-link",{attrs:{to:"/port"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",width:"20",height:"20"}},[t("path",{attrs:{d:"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.portInfo")))])])],1),t("a-menu-item",{key:"docker"},[t("router-link",{attrs:{to:"/docker"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",width:"20",height:"20",viewBox:"0 0 48 48"}},[t("path",{attrs:{d:"M 22.5 6 C 22.224 6 22 6.224 22 6.5 L 22 9.5 C 22 9.776 22.224 10 22.5 10 L 25.5 10 C 25.776 10 26 9.776 26 9.5 L 26 6.5 C 26 6.224 25.776 6 25.5 6 L 22.5 6 z M 10.5 12 C 10.224 12 10 12.224 10 12.5 L 10 15.5 C 10 15.776 10.224 16 10.5 16 L 13.5 16 C 13.776 16 14 15.776 14 15.5 L 14 12.5 C 14 12.224 13.776 12 13.5 12 L 10.5 12 z M 16.5 12 C 16.224 12 16 12.224 16 12.5 L 16 15.5 C 16 15.776 16.224 16 16.5 16 L 19.5 16 C 19.776 16 20 15.776 20 15.5 L 20 12.5 C 20 12.224 19.776 12 19.5 12 L 16.5 12 z M 22.5 12 C 22.224 12 22 12.224 22 12.5 L 22 15.5 C 22 15.776 22.224 16 22.5 16 L 25.5 16 C 25.776 16 26 15.776 26 15.5 L 26 12.5 C 26 12.224 25.776 12 25.5 12 L 22.5 12 z M 37.478516 14.300781 L 37.025391 14.951172 C 36.458391 15.825172 36.045734 16.787828 35.802734 17.798828 C 35.343734 19.731828 35.621422 21.546656 36.607422 23.097656 C 35.416422 23.758656 33.386 23.986 33 24 L 2 24 C 0.895 24 0 24.895 0 26 C 0 28 0.43371875 30.924625 1.3867188 33.515625 C 2.4757187 36.359625 4.0970781 38.454328 6.2050781 39.736328 C 8.5670781 41.177328 12.404859 42 16.755859 42 C 18.720859 42.006 20.683234 41.828703 22.615234 41.470703 C 25.301234 40.979703 27.885719 40.045078 30.261719 38.705078 C 32.219719 37.576078 33.981469 36.139172 35.480469 34.451172 C 37.985469 31.627172 39.477891 28.4815 40.587891 25.6875 C 40.592891 25.6845 40.596562 25.683688 40.601562 25.679688 C 43.598562 25.800687 45.412625\n                            24.642688 46.390625 23.679688 C 47.008625 23.095688 47.491688 22.38275 47.804688 21.59375 L 48 21.021484 L 47.527344 20.650391 C 47.397344 20.547391 46.182141 19.632812 43.619141 19.632812 C 42.942141 19.635813 42.266609 19.694641 41.599609 19.806641 C 41.103609 16.421641 38.293969 14.769313 38.167969 14.695312 L 37.478516 14.300781 z M 4.5 18 C 4.224 18 4 18.224 4 18.5 L 4 21.5 C 4 21.776 4.224 22 4.5 22 L 7.5 22 C 7.776 22 8 21.776 8 21.5 L 8 18.5 C 8 18.224 7.776 18 7.5 18 L 4.5 18 z M 10.5 18 C 10.224 18 10 18.224 10 18.5 L 10 21.5 C 10 21.776 10.224 22 10.5 22 L 13.5 22 C 13.776 22 14 21.776 14 21.5 L 14 18.5 C 14 18.224 13.776 18 13.5 18 L 10.5 18 z M 16.5 18 C 16.224 18 16 18.224 16 18.5 L 16 21.5 C 16 21.776 16.224 22 16.5 22 L 19.5 22 C 19.776 22 20 21.776 20 21.5 L 20 18.5 C 20 18.224 19.776 18 19.5 18 L 16.5 18 z M 22.5 18 C 22.224 18 22 18.224 22 18.5 L 22 21.5 C 22 21.776 22.224 22 22.5 22 L 25.5 22 C 25.776 22 26 21.776 26 21.5 L 26 18.5 C 26 18.224 25.776 18 25.5 18 L 22.5 18 z M 28.5 18 C 28.224 18 28 18.224 28 18.5 L 28 21.5 C 28 21.776 28.224 22 28.5 22 L 31.5 22 C 31.776 22 32 21.776 32 21.5 L 32 18.5 C 32 18.224 31.776 18 31.5 18 L 28.5 18 z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.dockerInfo")))])])],1),t("a-menu-item",{key:"kubernetes"},[t("router-link",{attrs:{to:"/kubernetes"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"80",height:"80",viewBox:"0 0 32 32"}},[t("path",{attrs:{fill:"#ffffff",d:"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.k8sInfo")))])])],1),t("a-menu-item",{key:"code-info"},[t("router-link",{attrs:{to:"/code-info"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 640 512"}},[t("path",{attrs:{d:"M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.codeInfo")))])])],1),t("a-menu-item",{key:"material-info"},[t("router-link",{attrs:{to:"/material-info"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"19",viewBox:"0 0 384 512"}},[t("path",{attrs:{d:"M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM80 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L80 96c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm54.2 253.8c-6.1 20.3-24.8 34.2-46 34.2L80 416c-8.8 0-16-7.2-16-16s7.2-16 16-16l8.2 0c7.1 0 13.3-4.6 15.3-11.4l14.9-49.5c3.4-11.3 13.8-19.1 25.6-19.1s22.2 7.7 25.6 19.1l11.6 38.6c7.4-6.2 16.8-9.7 26.8-9.7c15.9 0 30.4 9 37.5 23.2l4.4 8.8 54.1 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-6.1 0-11.6-3.4-14.3-8.8l-8.8-17.7c-1.7-3.4-5.1-5.5-8.8-5.5s-7.2 2.1-8.8 5.5l-8.8 17.7c-2.9 5.9-9.2 9.4-15.7 8.8s-12.1-5.1-13.9-11.3L144 349l-9.8 32.8z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.materialInfo")))])])],1)],1),t("a-sub-menu",{key:"securityTool",scopedSlots:e._u([{key:"title",fn:function({open:a}){return[t("span",{staticClass:"enhanced-title"},[e._v(e._s(e.$t("sidebar.securityTool")))])]}}])},[t("a-menu-item",{key:"ai"},[t("router-link",{attrs:{to:"/aiBash"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M9.4 86.6C-3.1 74.1-3.1 53.9 9.4 41.4s32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L178.7 256 9.4 86.6zM256 416l288 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-288 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.aiBash")))])])],1),t("a-menu-item",{key:"file-upload"},[t("router-link",{attrs:{to:"/upload"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.fileUpload")))])])],1),t("a-menu-item",{key:"file-download"},[t("router-link",{attrs:{to:"/download"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.fileDown")))])])],1)],1),t("a-sub-menu",{key:"llmAutoTesting",scopedSlots:e._u([{key:"title",fn:function({open:a}){return[t("span",{staticClass:"enhanced-title"},[e._v(e._s(e.$t("sidebar.llmAutoTesting")))])]}}])},[t("a-menu-item",{key:"case-list"},[t("router-link",{attrs:{to:"/testcase"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.testCases")))])])],1),t("a-menu-item",{key:"execute-case"},[t("router-link",{attrs:{to:"/execute-case"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-464 0c-39.8 0-72-32.2-72-72L-64 168c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32l160 0zM208 128c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.executeCase")))])])],1),t("a-menu-item",{key:"smart-orchestration"},[t("router-link",{attrs:{to:"/smart-orchestration"}},[t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",height:"20",width:"20"}},[t("path",{attrs:{d:"M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 64 0 368 0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-128 64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48l0-16z"}})])]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.smartOrchestration")))])])],1)],1),t("a-sub-menu",{key:"aiTaintAnalysis",scopedSlots:e._u([{key:"title",fn:function({open:a}){return[t("span",{staticClass:"enhanced-title"},[e._v(e._s(e.$t("sidebar.aiTaintAnalysis")))])]}}])})],1)],1),t("div",{staticClass:"footer-section"},[t("footer-animation",{staticClass:"sidebar-footer"})],1)])])},V=[],O=function(){var e=this,t=e._self._c;return t("div",{staticClass:"footer-animation-container"},[t("div",{ref:"animal",staticClass:"animal"},[t("svg",{staticClass:"animal-svg",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"}},[t("path",{attrs:{fill:e.currentColor,d:"M226.5 92.9c14.3 42.9-.3 86.2-32.6 96.8s-70.1-15.6-84.4-58.5s.3-86.2 32.6-96.8s70.1 15.6 84.4 58.5zM100.4 198.6c18.9 32.4 14.3 70.1-10.2 84.1s-59.7-.9-78.5-33.3S-2.7 179.3 21.8 165.3s59.7 .9 78.5 33.3zM69.2 401.2C121.6 259.9 214.7 224 256 224s134.4 35.9 186.8 177.2c3.6 9.7 5.2 20.1 5.2 30.5v1.6c0 25.8-20.9 46.7-46.7 46.7c-11.5 0-22.9-1.4-34-4.2l-88-22c-15.3-3.8-31.3-3.8-46.6 0l-88 22c-11.1 2.8-22.5 4.2-34 4.2C84.9 480 64 459.1 64 433.3v-1.6c0-10.4 1.6-20.8 5.2-30.5zM421.8 282.7c-24.5-14-29.1-51.7-10.2-84.1s54-47.3 78.5-33.3s29.1 51.7 10.2 84.1s-54 47.3-78.5 33.3zM310.1 189.7c-32.3-10.6-46.9-53.9-32.6-96.8s52.1-69.1 84.4-58.5s46.9 53.9 32.6 96.8s-52.1 69.1-84.4 58.5z"}})])])])},H=[],q={data(){return{position:0,direction:1,animationFrame:null,containerWidth:0,animalWidth:40,speed:.6,jumpHeight:0,isJumping:!1,jumpDirection:1,maxJumpHeight:15,jumpSpeed:.3,jumpProbability:.008,colorIndex:0,colors:["#818080","#4096ff","#ff7875","#52c41a","#faad14"],frameCount:0,frameSkip:1,currentColor:"#818080"}},mounted(){this.containerWidth=this.$el.offsetWidth,this.updateAnimalColor(),this.initAnimation(),window.addEventListener("resize",this.handleResize)},beforeUnmount(){window.removeEventListener("resize",this.handleResize),cancelAnimationFrame(this.animationFrame)},methods:{initAnimation(){this.position=0,this.animate()},animate(){this.frameCount++,this.frameCount%(this.frameSkip+1)===0&&(this.position+=this.speed*this.direction,this.position>this.containerWidth-this.animalWidth?(this.direction=-1,this.$refs.animal&&(this.$refs.animal.classList.remove("facing-right"),this.$refs.animal.classList.add("facing-left"))):this.position<0&&(this.direction=1,this.$refs.animal&&(this.$refs.animal.classList.remove("facing-left"),this.$refs.animal.classList.add("facing-right"))),!this.isJumping&&Math.random()<this.jumpProbability&&(this.isJumping=!0,this.jumpDirection=1,this.jumpHeight=0,this.colorIndex=(this.colorIndex+1)%this.colors.length,this.updateAnimalColor()),this.isJumping&&(this.jumpHeight+=this.jumpSpeed*this.jumpDirection,this.jumpHeight>=this.maxJumpHeight?this.jumpDirection=-1:this.jumpHeight<=0&&-1===this.jumpDirection&&(this.isJumping=!1,this.jumpHeight=0))),this.$refs.animal&&(this.$refs.animal.style.left=this.position+"px",this.$refs.animal.style.bottom=5+this.jumpHeight+"px"),this.animationFrame=requestAnimationFrame(this.animate)},handleResize(){this.containerWidth=this.$el.offsetWidth,this.position>this.containerWidth-this.animalWidth&&(this.position=this.containerWidth-this.animalWidth)},updateAnimalColor(){this.currentColor=this.colors[this.colorIndex]}}},W=q,J=(a("be69"),Object(d["a"])(W,O,H,!1,null,"bf170ac4",null)),Z=J.exports,K={components:{FooterAnimation:Z},computed:{...Object(_["e"])(["selectedNodeIp","currentProject"])},props:{sidebarCollapsed:{type:Boolean,default:!1},sidebarColor:{type:String,default:"primary"},sidebarTheme:{type:String,default:"light"}},data(){return{}}},G=K,Q=(a("8163"),Object(d["a"])(G,U,V,!1,null,"476db7da",null)),X=Q.exports,Y=function(){var e=this,t=e._self._c;return t(e.navbarFixed?"a-affix":"div",{tag:"component",attrs:{"offset-top":e.top}},[t("a-layout-header",[t("a-row",{attrs:{type:"flex"}},[t("a-col",{attrs:{span:24,md:12}},[t("div",{staticClass:"header-nav-buttons"},[t("router-link",{staticClass:"header-nav-button",class:[{active:"/"===e.$route.path},"/"===e.$route.path?`bg-${e.sidebarColor} nav-btn-transparent`:""],attrs:{to:"/"}},[t("svg",{staticStyle:{"margin-right":"7px"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",width:"17",height:"17"}},[t("path",{attrs:{d:"M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z",fill:"currentColor"}})]),t("span",{staticClass:"label"},[e._v(e._s(e.$t("common.home")))])]),e.currentProject?t("div",{staticClass:"project-name-display"},[t("span",{staticClass:"project-name-text"},[e._v(e._s(e.getDisplayProjectName()))])]):e._e(),t("router-link",{staticClass:"header-nav-button",class:[{active:e.$route.path.includes("/task")},e.$route.path.includes("/task")?`bg-${e.sidebarColor} nav-btn-transparent`:""],attrs:{to:"/task"}},[t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.taskPanel")))])]),t("router-link",{staticClass:"header-nav-button",class:[{active:e.$route.path.includes("/config")},e.$route.path.includes("/config")?`bg-${e.sidebarColor} nav-btn-transparent`:""],attrs:{to:{path:"/config",hash:"#host"}}},[t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.hostConfig")))])]),t("router-link",{staticClass:"header-nav-button",class:[{active:e.$route.path.includes("/repository")},e.$route.path.includes("/repository")?`bg-${e.sidebarColor} nav-btn-transparent`:""],attrs:{to:"/repository"}},[t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.repositoryConfig")))])]),t("router-link",{staticClass:"header-nav-button",class:[{active:e.$route.path.includes("/tools")},e.$route.path.includes("/tools")?`bg-${e.sidebarColor} nav-btn-transparent`:""],attrs:{to:"/tools"}},[t("span",{staticClass:"label"},[e._v(e._s(e.$t("sidebar.toolPanel")))])])],1)]),t("a-col",{staticClass:"header-control",attrs:{span:24,md:12}},[t("log-viewer"),t("a-dropdown",{attrs:{placement:"bottomRight"}},[t("a",{staticClass:"language-switcher",on:{click:e=>e.preventDefault()}},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[t("circle",{attrs:{cx:"12",cy:"12",r:"10"}}),t("line",{attrs:{x1:"2",y1:"12",x2:"22",y2:"12"}}),t("path",{attrs:{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"}})]),t("span",{staticClass:"language-text"},[e._v(e._s(e.currentLanguageLabel))])]),t("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[t("a-menu-item",{key:"en-US",on:{click:function(t){return e.changeLanguage("en-US")}}},[t("span",{class:{"active-language":"en-US"===e.language}},[e._v("English")])]),t("a-menu-item",{key:"zh-CN",on:{click:function(t){return e.changeLanguage("zh-CN")}}},[t("span",{class:{"active-language":"zh-CN"===e.language}},[e._v("中文")])])],1)],1),t("notification-button"),t("theme-toggle-button"),t("a-button",{ref:"secondarySidebarTriggerBtn",attrs:{type:"link"},on:{click:function(t){return e.$emit("toggleSettingsDrawer",!0)}}},[t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.4892 3.17094C11.1102 1.60969 8.8898 1.60969 8.51078 3.17094C8.26594 4.17949 7.11045 4.65811 6.22416 4.11809C4.85218 3.28212 3.28212 4.85218 4.11809 6.22416C4.65811 7.11045 4.17949 8.26593 3.17094 8.51078C1.60969 8.8898 1.60969 11.1102 3.17094 11.4892C4.17949 11.7341 4.65811 12.8896 4.11809 13.7758C3.28212 15.1478 4.85218 16.7179 6.22417 15.8819C7.11045 15.3419 8.26594 15.8205 8.51078 16.8291C8.8898 18.3903 11.1102 18.3903 11.4892 16.8291C11.7341 15.8205 12.8896 15.3419 13.7758 15.8819C15.1478 16.7179 16.7179 15.1478 15.8819 13.7758C15.3419 12.8896 15.8205 11.7341 16.8291 11.4892C18.3903 11.1102 18.3903 8.8898 16.8291 8.51078C15.8205 8.26593 15.3419 7.11045 15.8819 6.22416C16.7179 4.85218 15.1478 3.28212 13.7758 4.11809C12.8896 4.65811 11.7341 4.17949 11.4892 3.17094ZM10 13C11.6569 13 13 11.6569 13 10C13 8.34315 11.6569 7 10 7C8.34315 7 7 8.34315 7 10C7 11.6569 8.34315 13 10 13Z",fill:"#111827"}})])]),t("a-button",{staticClass:"sidebar-toggler",attrs:{type:"link"},on:{click:function(t){e.$emit("toggleSidebar",!e.sidebarCollapsed),e.resizeEventHandler()}}},[t("svg",{attrs:{width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512"}},[t("path",{attrs:{d:"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"}})])]),t("a-col",{staticClass:"header-control",attrs:{span:24,md:6}},[t("div",{staticClass:"node-selector"},[e.currentProject?[t("a-dropdown",{attrs:{trigger:["click"]},scopedSlots:e._u([{key:"overlay",fn:function(){return[t("a-menu",{staticClass:"node-menu"},e._l(e.nodes,(function(a){return t("a-menu-item",{key:a.ip,on:{click:function(t){return e.selectNode(a)}}},[t("div",{staticClass:"node-menu-item"},[t("div",{staticClass:"ip-address"},[e._v(e._s(a.ip))]),t("div",{staticClass:"host-name",attrs:{title:a.host_name}},[e._v(e._s(a.host_name))])])])})),1)]},proxy:!0}],null,!1,617815065)},[t("a",{staticClass:"ant-dropdown-link node-selector-link",on:{click:e=>e.preventDefault()}},[t("span",{staticClass:"node-name"},[e._v(" "+e._s(e.selectedNode?e.selectedNode.ip:e.$t("common.selectNode"))+" ")]),t("a-icon",{attrs:{type:"down"}})],1)])]:[t("a",{staticClass:"ant-dropdown-link node-selector-link",on:{click:e.goToProjects}},[t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M13 7H7V5h6v2zm0 4H7V9h6v2zm0 4H7v-2h6v2z",fill:"#111827"}}),t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2 4a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0h12v12H4V4z",fill:"#111827"}})]),t("span",[e._v(e._s(e.$t("common.selectProject")))])])]],2)])],1)],1)],1)],1)},ee=[],te=(a("fffc"),function(){var e=this,t=e._self._c;return t("a-popover",{attrs:{trigger:"click",placement:"bottomRight",overlayClassName:"notification-popover",getPopupContainer:()=>e.wrapper},on:{visibleChange:e.onPopoverVisibleChange},scopedSlots:e._u([{key:"content",fn:function(){return[t("div",{staticClass:"notification-container"},[t("div",{staticClass:"notification-header"},[t("span",[e._v(e._s(e.$t("common.notifications")))]),e.notifications.length?t("a-button",{attrs:{type:"link",size:"small"},on:{click:e.clearAllNotifications}},[e._v(" "+e._s(e.$t("common.clearAll"))+" ")]):e._e()],1),t("div",{staticClass:"notification-list"},[e._l(e.notifications,(function(a){return t("div",{key:a.id,class:["notification-item","notification-"+a.type]},[t("div",{staticClass:"notification-title"},[e._v(e._s(a.title))]),t("div",{staticClass:"notification-message"},[e._v(e._s(a.message))]),t("div",{staticClass:"notification-time"},[e._v(e._s(a.time))])])})),e.notifications.length?e._e():t("div",{staticClass:"empty-notification"},[t("div",{staticClass:"empty-message"},[e._v(e._s(e.$t("common.noNotifications")))])])],2)])]},proxy:!0}]),model:{value:e.notificationVisible,callback:function(t){e.notificationVisible=t},expression:"notificationVisible"}},[t("a-badge",{attrs:{count:e.unreadNotifications,showZero:!1,overflowCount:99,numberStyle:{backgroundColor:"#ff4d4f"}}},[t("a",{staticClass:"notification-trigger",on:{click:e.toggleNotifications}},[t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M10 2C6.68632 2 4.00003 4.68629 4.00003 8V11.5858L3.29292 12.2929C3.00692 12.5789 2.92137 13.009 3.07615 13.3827C3.23093 13.7564 3.59557 14 4.00003 14H16C16.4045 14 16.7691 13.7564 16.9239 13.3827C17.0787 13.009 16.9931 12.5789 16.7071 12.2929L16 11.5858V8C16 4.68629 13.3137 2 10 2Z",fill:"#111827"}}),t("path",{attrs:{d:"M10 18C8.34315 18 7 16.6569 7 15H13C13 16.6569 11.6569 18 10 18Z",fill:"#111827"}})])])])],1)}),ae=[],se={name:"NotificationButton",data(){return{notificationVisible:!1,wrapper:document.body}},computed:{...Object(_["e"])(["notifications"]),...Object(_["c"])(["unreadNotifications"])},methods:{...Object(_["b"])(["markNotificationsAsRead","clearNotifications"]),toggleNotifications(e){e.stopPropagation(),this.notificationVisible=!this.notificationVisible,this.notificationVisible&&this.unreadNotifications>0&&this.markNotificationsAsRead()},onPopoverVisibleChange(e){e&&this.unreadNotifications>0&&(this.markNotificationsAsRead(),this.$forceUpdate())},clearAllNotifications(e){e.stopPropagation(),this.clearNotifications()}},mounted(){this.wrapper=document.getElementById("layout-dashboard")}},oe=se,ie=(a("56a3"),Object(d["a"])(oe,te,ae,!1,null,"20888547",null)),re=ie.exports,ne=function(){var e=this,t=e._self._c;return t("div",{staticClass:"log-viewer"},[t("a-tooltip",{attrs:{title:e.$t("log.viewLogs")}},[t("a",{staticClass:"log-button",on:{click:e.showLogModal}},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 448 512"}},[t("path",{attrs:{d:"M160 80c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 352c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-352zM0 272c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 160c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48L0 272zM368 96l32 0c26.5 0 48 21.5 48 48l0 288c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48z"}})])])]),t("a-modal",{attrs:{title:e.$t("log.title"),visible:e.visible,width:1200,footer:null,bodyStyle:{padding:"0"}},on:{cancel:e.handleCancel}},[t("div",{staticClass:"log-modal-content"},[t("div",{staticClass:"log-controls"},[t("div",{staticClass:"log-controls-left"},[t("span",{staticClass:"current-node-info"},[e._v(" "+e._s(e.$t("log.currentNode"))+": "+e._s(e.currentNodeInfo)+" ")]),t("a-select",{staticStyle:{width:"120px","margin-left":"12px"},attrs:{placeholder:e.$t("log.selectLevel"),"allow-clear":""},model:{value:e.selectedLogLevel,callback:function(t){e.selectedLogLevel=t},expression:"selectedLogLevel"}},e._l(e.logLevelOptions,(function(a){return t("a-select-option",{key:a,attrs:{value:a}},[t("span",{staticClass:"log-level-option",class:"level-"+(null===a||void 0===a?void 0:a.toLowerCase())},[e._v(" "+e._s(a)+" ")])])})),1)],1),t("div",{staticClass:"log-controls-right"},[t("a-button",{attrs:{loading:e.loading,type:"primary",size:"small"},on:{click:e.fetchLogs}},[t("a-icon",{attrs:{type:"reload"}}),e._v(" "+e._s(e.$t("log.refresh"))+" ")],1)],1)]),t("div",{ref:"logContent",staticClass:"log-content"},[t("a-spin",{attrs:{spinning:e.loading}},[0!==e.logs.length||e.loading?t("div",{staticClass:"log-list"},e._l(e.filteredLogs,(function(a,s){var o;return t("div",{key:s,staticClass:"log-item",class:e.getLogLevelClass(a.log_level)},[t("div",{staticClass:"log-header"},[t("span",{staticClass:"log-time"},[e._v(e._s(e.formatTime(a.timestamp)))]),t("span",{staticClass:"log-level",class:"level-"+(null===(o=a.log_level)||void 0===o?void 0:o.toLowerCase())},[e._v(" "+e._s(a.log_level)+" ")]),a.module?t("span",{staticClass:"log-module"},[e._v(e._s(a.module))]):e._e()]),t("div",{staticClass:"log-message"},[e._v(e._s(a.log_content))])])})),0):t("div",{staticClass:"no-logs"},[t("a-empty",{attrs:{description:e.$t("log.noLogs")}})],1)])],1),t("div",{staticStyle:{"text-align":"right","margin-top":"16px","padding-top":"16px","margin-right":"16px","margin-bottom":"24px"}},[t("a-pagination",{attrs:{current:e.currentPage,total:e.total,"page-size":e.pageSize,size:"small"},on:{change:e.handlePageChange}})],1)])])],1)},le=[],ce={name:"LogViewer",data(){return{visible:!1,loading:!1,logs:[],pageSize:100,currentPage:1,total:0,selectedLogLevel:null}},computed:{...Object(_["e"])(["nodes","selectedNodeIp","currentProject"]),currentNodeInfo(){if(!this.selectedNodeIp)return this.$t("log.noNodeSelected");const e=this.nodes.find(e=>e.ip===this.selectedNodeIp);return e?""+e.ip:this.selectedNodeIp},filteredLogs(){return this.selectedLogLevel?this.logs.filter(e=>e.log_level===this.selectedLogLevel):this.logs},logLevelOptions(){const e=new Set(this.logs.map(e=>e.log_level).filter(Boolean));return Array.from(e).sort()}},methods:{showLogModal(){this.visible=!0,this.currentPage=1,this.selectedLogLevel=null,this.selectedNodeIp&&this.fetchLogs()},handleCancel(){this.visible=!1},async fetchLogs(){if(this.selectedNodeIp)if(this.currentProject){this.loading=!0;try{const e=await C["a"].get("/api/agent_log/"+this.selectedNodeIp,{params:{page:this.currentPage,page_size:this.pageSize,dbFile:this.currentProject}});e.data&&"object"===typeof e.data&&e.data.data?(this.logs=e.data.data||[],this.total=e.data.total||0):(this.logs=(e.data||[]).sort((e,t)=>{const a=new Date(e.timestamp).getTime(),s=new Date(t.timestamp).getTime();return a-s}),this.total=this.logs.length),this.selectedLogLevel=null,this.$nextTick(()=>{const e=this.$refs.logContent;e&&(e.scrollTop=0)})}catch(e){console.error("Failed to fetch logs:",e),this.$message.error(this.$t("log.fetchError")),this.logs=[],this.total=0}finally{this.loading=!1}}else this.$message.warning(this.$t("common.selectProjectFirst"));else this.$message.warning(this.$t("log.noNodeSelected"))},handlePageChange(e){this.currentPage=e,this.fetchLogs()},formatTime(e){if(!e)return"";const t=new Date(e);return t.toLocaleString()},getLogLevelClass(e){return e?"log-level-"+e.toLowerCase():""}}},de=ce,ue=(a("d9c0"),Object(d["a"])(de,ne,le,!1,null,"2731ff6c",null)),he=ue.exports,pe=function(){var e=this,t=e._self._c;return t("a-tooltip",{attrs:{title:e.isDarkMode?e.$t("common.lightMode"):e.$t("common.darkMode")}},[t("a",{staticClass:"theme-toggle-button",on:{click:e.toggleTheme}},[e.isDarkMode?t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z",fill:"#111827"}}),t("path",{attrs:{d:"M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22",stroke:"#111827","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})]):t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z",fill:"#111827",stroke:"#111827","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])])])},me=[],ge={name:"ThemeToggleButton",computed:{...Object(_["e"])(["darkMode"]),isDarkMode(){return this.darkMode}},methods:{...Object(_["b"])(["toggleDarkMode"]),toggleTheme(){this.toggleDarkMode()}}},fe=ge,be=(a("bf02"),Object(d["a"])(fe,pe,me,!1,null,"bffad3ac",null)),ve=be.exports,we={components:{NotificationButton:re,LogViewer:he,ThemeToggleButton:ve},props:{navbarFixed:{type:Boolean,default:!1},sidebarCollapsed:{type:Boolean,default:!1},sidebarColor:{type:String,default:"primary"}},data(){return{top:0,searchLoading:!1,wrapper:document.body,selectedNode:null}},computed:{...Object(_["e"])(["nodes","currentProject","currentProjectName","selectedNodeIp","language"]),currentLanguageLabel(){return"zh-CN"===this.language?"中文":"English"}},methods:{...Object(_["d"])(["setSelectedNodeIp"]),...Object(_["b"])(["updateLanguage"]),changeLanguage(e){this.updateLanguage(e),this.$i18n.locale=e},selectNode(e){this.selectedNode=e,this.setSelectedNodeIp(e.ip),this.goToProcessInfo(e.ip)},goToProcessInfo(){const e="/task";this.$route.path!==e&&this.$router.push(e).catch(e=>{"NavigationDuplicated"!==e.name&&console.error(e)})},goToProjects(){this.$router.push("/projects")},updateSelectedNode(){var e,t;this.selectedNodeIp&&null!==(e=this.nodes)&&void 0!==e&&e.length?this.selectedNode=this.nodes.find(e=>e.ip===this.selectedNodeIp):null!==(t=this.nodes)&&void 0!==t&&t.length?this.selectNode(this.nodes[0]):this.selectedNode=null},async initializeNodesData(){this.currentProject&&await this.$store.dispatch("fetchNodes")},getDisplayProjectName(){return this.currentProjectName?this.currentProjectName:""}},watch:{currentProject:{immediate:!0,handler(e){e?this.initializeNodesData():(this.$store.commit("setNodes",[]),this.selectedNode=null,this.setSelectedNodeIp(null))}},nodes:{immediate:!0,handler(e){null!==e&&void 0!==e&&e.length?this.updateSelectedNode():(this.selectedNode=null,this.setSelectedNodeIp(null))}}},mounted(){this.wrapper=document.getElementById("layout-dashboard"),this.currentProject&&this.initializeNodesData()},activated(){this.currentProject&&this.initializeNodesData()}},Ce=we,ke=Object(d["a"])(Ce,Y,ee,!1,null,null,null),ye=ke.exports,je=function(){var e=this,t=e._self._c;return t("a-layout-footer",[t("a-row",{attrs:{type:"flex"}},[t("a-col",{attrs:{span:24,md:12}},[t("p",{staticClass:"copyright"},[e._v(" © 2025, made with "),t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.17157 5.17157C4.73367 3.60948 7.26633 3.60948 8.82843 5.17157L10 6.34315L11.1716 5.17157C12.7337 3.60948 15.2663 3.60948 16.8284 5.17157C18.3905 6.73367 18.3905 9.26633 16.8284 10.8284L10 17.6569L3.17157 10.8284C1.60948 9.26633 1.60948 6.73367 3.17157 5.17157Z",fill:"#111827"}})])])])],1)],1)},Se=[],Pe={data(){return{}}},Le=Pe,xe=Object(d["a"])(Le,je,Se,!1,null,null,null),Te=xe.exports,_e=function(){var e=this,t=e._self._c;return t("a-drawer",{staticClass:"settings-drawer",class:[e.rtl?"settings-drawer-rtl":""],attrs:{placement:e.rtl?"left":"right",closable:!1,visible:e.showSettingsDrawer,width:"360",getContainer:()=>e.wrapper},on:{close:function(t){return e.$emit("toggleSettingsDrawer",!1)}}},[t("a-button",{staticClass:"btn-close",attrs:{type:"link"},on:{click:function(t){return e.$emit("toggleSettingsDrawer",!1)}}},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"9",height:"9",viewBox:"0 0 9 9"}},[t("g",{attrs:{id:"close",transform:"translate(0.75 0.75)"}},[t("path",{attrs:{id:"Path",d:"M7.5,0,0,7.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10","stroke-width":"1.5"}}),t("path",{attrs:{id:"Path-2","data-name":"Path",d:"M0,0,7.5,7.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10","stroke-width":"1.5"}})])])]),t("div",{staticClass:"drawer-content"},[t("h6",[e._v(e._s(e.$t("configuratorset.configurator")))]),t("hr"),t("div",{staticClass:"sidebar-color"},[t("h6",[e._v(e._s(e.$t("configuratorset.sidebarColor")))]),t("a-radio-group",{attrs:{defaultValue:"primary"},on:{change:e.handleSidebarColorChange},model:{value:e.sidebarColorModel,callback:function(t){e.sidebarColorModel=t},expression:"sidebarColorModel"}},[t("a-radio-button",{staticClass:"bg-primary",attrs:{value:"primary"}}),t("a-radio-button",{staticClass:"bg-purple",attrs:{value:"purple"}}),t("a-radio-button",{staticClass:"bg-green",attrs:{value:"green"}}),t("a-radio-button",{staticClass:"bg-gray",attrs:{value:"gray"}})],1)],1),t("div",{staticClass:"sidenav-type"},[t("h6",[e._v(e._s(e.$t("configuratorset.sidenavType")))]),t("p",[e._v("Choose between 2 different sidenav types.")]),t("a-radio-group",{attrs:{"button-style":"solid",defaultValue:"primary"},on:{change:function(t){return e.$emit("updateSidebarTheme",t.target.value)}},model:{value:e.sidebarThemeModel,callback:function(t){e.sidebarThemeModel=t},expression:"sidebarThemeModel"}},[t("a-radio-button",{attrs:{value:"light"}},[e._v("TRANSPARENT")]),t("a-radio-button",{attrs:{value:"white"}},[e._v("WHITE")])],1)],1),t("div",{staticClass:"navbar-fixed"},[t("h6",[e._v(e._s(e.$t("configuratorset.navbarFixed")))]),t("a-switch",{attrs:{"default-checked":""},on:{change:function(t){return e.$emit("toggleNavbarPosition",e.navbarFixedModel)}},model:{value:e.navbarFixedModel,callback:function(t){e.navbarFixedModel=t},expression:"navbarFixedModel"}})],1)])],1)},Ne=[],Me=(a("3a06"),a("f676"));s["a"].use(Me["a"],{useCache:!0});var ze={props:{showSettingsDrawer:{type:Boolean,default:!1},sidebarColor:{type:String,default:"primary"},sidebarTheme:{type:String,default:"light"},navbarFixed:{type:Boolean,default:!1}},data(){return{wrapper:document.body,sidebarColorModel:this.sidebarColor,sidebarThemeModel:this.sidebarTheme,navbarFixedModel:this.navbarFixed,rtl:!1}},computed:{},methods:{...Object(_["b"])(["updateSidebarColor"]),handleSidebarColorChange(e){const t=e.target.value;this.updateSidebarColor(t),this.$emit("updateSidebarColor",t)}},created(){this.sidebarColorModel=this.sidebarColor},watch:{sidebarColor(e){this.sidebarColorModel=e},"$store.state.sidebarColor"(e){this.sidebarColorModel=e}},mounted:function(){this.wrapper=document.getElementById("layout-dashboard")}},De=ze,$e=Object(d["a"])(De,_e,Ne,!1,null,null,null),Ae=$e.exports,Ie={name:"Dashboard",components:{DashboardSidebar:X,DashboardHeader:ye,DashboardFooter:Te,DashboardSettingsDrawer:Ae},data(){return{sidebarCollapsed:!1,showSettingsDrawer:!1,navbarFixed:!0,sidebarTheme:"light",layoutClass:""}},computed:{sidebarColor(){return this.$store.state.sidebarColor}},methods:{toggleSidebar(){this.sidebarCollapsed=!this.sidebarCollapsed},toggleSettingsDrawer(){this.showSettingsDrawer=!this.showSettingsDrawer},toggleNavbarPosition(){this.navbarFixed=!this.navbarFixed},updateSidebarTheme(e){this.sidebarTheme=e},updateSidebarColor(e){this.$store.dispatch("updateSidebarColor",e)}}},Re=Ie,Fe=Object(d["a"])(Re,B,E,!1,null,null,null),Be=Fe.exports,Ee=(a("abb2"),a("a925")),Ue={common:{home:"Home",title:"SecureTest Copilot",selectNode:"Select Node",selectProject:"Select Project",settings:"Settings",notifications:"Notifications",clearAll:"Clear All",noNotifications:"No Notifications",language:"Language",configureNodes:"Node Configuration",configureProxy:"Proxy Configuration",detectReachableIps:"Detect Reachable IPs",taskProgress:"Task Progress",refresh:"Refresh",darkMode:"Dark Mode",lightMode:"Light Mode",selectedNodes:"Selected {count} nodes",copiedToClipboard:"Copied to clipboard",copyFailed:"Copy failed",clear:"Clear"},configuratorset:{sidebarColor:"Sidebar Color",sidenavType:"Sidenav Type",navbarFixed:"Navbar Fixed",configurator:"Configurator"},headTopic:{process:"Process Table",package:"Package Table",hardware:"Hardware Table",mount:"Mount Table",port:"Network Ports & Sockets",docker:"Docker Table",k8s:"Kubernetes Table",fileUpload:"File Upload",fileDownload:"File Download",aiBash:"Command Line",testcase:"Test Case Management"},tool:{configureTool:"Configure Tool",spiderTool:"SSP",generalTool:"GeneralTool",uploadToolPackage:"Upload Tool Package (zip)",selectToolPackage:"upload",editScript:"Edit script",start:"start",editShellScript:"Edit Shell",confirmScript:"confirm",scriptReady:"Script Ready",localSaveDirectory:"Local Save Directory",viewResult:"View Result",selectReachableIp:"Select a reachable IP",columns:{hostName:"Host Name",ip:"IP",status:"Status",progress:"Progress",result:"Result",errorDetails:"Error Details",speed:"Speed",fileSize:"File Size"},status:{failed:"Failed"}},sidebar:{taskPanel:"Task Panel",envAwareness:"Env Awareness",processInfo:"Process Info",packageInfo:"Package Info",hardwareInfo:"Hardware Info",filesystemInfo:"Filesys Info",portInfo:"Port Info",dockerInfo:"Docker Info",k8sInfo:"K8S Info",codeInfo:"代码信息",materialInfo:"资料信息",securityTool:"AI Security Tool",fileUpload:"File Upload",fileDown:"File Down",aiBash:"AI Bash",testCases:"Test Cases",llmAutoTesting:"LLM Auto Testing",aiTaintAnalysis:"AI Taint Analysis",smartOrchestration:"预留",toolPanel:"Tool Panel",hostConfig:"Host Config",cbhConfig:"CBH Config",repositoryConfig:"Repo Config",executeCase:"Execute Case"},fileUpload:{selectFile:"Select File",clickToSelect:"Select File",uploadPath:"Upload Path",enterUploadPath:"Enter Upload Directory",startUpload:"Start Upload",uploadProgress:"Upload Progress",uploadResults:"Upload Results"},fileDownload:{enterDownloadPath:"Enter remote file path",startDownload:"Start Download",downloadProgress:"Download Progress"},hostConfig:{title:"Host Configuration",addHost:"Add Host",exportSelected:"Export Selected",deleteSelected:"Delete Selected",downloadTemplate:"Download Tpl",uploadTemplate:"Upload Tpl",actions:"Actions",save:"Save",edit:"Edit",copy:"Copy",cancel:"Cancel",delete:"Delete",columns:{hostName:"Host Name",ipAddress:"IP Address",sshPort:"SSH Port",loginUser:"Login User",loginPassword:"Login Password",switchRootCmd:"Switch root cmd",switchRootPwd:"Switch root pwd"}},repositoryConfig:{title:"Repository Configuration",addRepository:"Add Repository",exportSelected:"Export Selected",deleteSelected:"Delete Selected",downloadSelected:"Download Selected",downloadTemplate:"Download Tpl",uploadTemplate:"Upload Tpl",selectDownloadPath:"Select Download Path",downloadProgress:"Download Progress",actions:"Actions",save:"Save",edit:"Edit",copy:"Copy",cancel:"Cancel",delete:"Delete",columns:{microservice:"Microservice",repositoryUrl:"Repository URL",branchName:"Branch Name"},validation:{invalidUrl:"Invalid repository URL",unsupportedFormat:"Unsupported repository format",missingBranch:"Missing branch name",parseError:"Parse failed"},download:{selectPath:"Please select download path",downloading:"Downloading...",starting:"Starting download...",success:"Download successful",failed:"Download failed",partialSuccess:"Partial download successful",cloneError:"Git clone failed"}},repositoryDownload:{title:"Repository Download Results",clear:"Clear",total:"Total",success:"Success",failed:"Failed",downloading:"Downloading",pending:"Pending",completed:"Completed",progress:"Progress"},log:{title:"Log Viewer",viewLogs:"View Logs",currentNode:"Node",noNodeSelected:"No node selected",selectLevel:"Select Level",refresh:"Refresh",noLogs:"No logs available",fetchError:"Failed to fetch logs"},testcase:{title:"Test Case",management:"Case Management",detail:"Case Details",searchButton:"Search",resetButton:"Reset",clearResults:"Clear Results"},caseColumn:{number:"Case Number",name:"Case Name",level:"Case Level",similarity:"Similarity",prepareCondition:"Precondition",testSteps:"Test Steps",expectedResult:"Expected Result"},smartOrchestration:{title:"Smart Orchestration",smartAnalysis:"Smart Test Case Analysis",startAnalysis:"Start Analysis",caseAnalysis:"Case Analysis",naturalLanguageQuery:"Natural Language Query",queryPlaceholder:"Please enter natural language query",inputRequired:"Please enter a query",searchFailed:"Search failed",searchError:"Search error",resultsCleared:"Search results cleared",topK:"Top K",scoreThreshold:"Score Threshold",searchResults:"Search Results",foundResults:"Found {count} results"}},Ve={common:{home:"首页",title:"安全测试助手",selectNode:"选择节点",selectProject:"选择项目",settings:"设置",notifications:"通知",clearAll:"清除全部",noNotifications:"暂无通知",language:"语言",configureNodes:"节点配置",configureProxy:"代理配置",detectReachableIps:"检测可用IP",taskProgress:"任务进度",refresh:"刷新",darkMode:"夜间模式",lightMode:"日间模式",selectedNodes:"已选择 {count} 个节点",copiedToClipboard:"已复制到剪贴板",copyFailed:"复制失败",clear:"清除"},configuratorset:{sidebarColor:"侧边栏颜色",sidenavType:"侧边栏类型",navbarFixed:"导航栏固定",configurator:"配置器"},headTopic:{process:"进程列表",package:"安装包列表",hardware:"硬件列表",mount:"挂载列表",port:"端口列表",docker:"容器列表",k8s:"集群列表",fileUpload:"文件上传",fileDownload:"文件下载",aiBash:"命令行",testcase:"用例管理"},tool:{configureTool:"工具配置",spiderTool:"SSP工具",generalTool:"通用工具",uploadToolPackage:"上传工具包 (zip)",selectToolPackage:"选择包",editScript:"编辑脚本",start:"启动",editShellScript:"编辑Shell",confirmScript:"确认",scriptReady:"脚本已就绪",localSaveDirectory:"本地保存目录",viewResult:"查看结果",selectReachableIp:"选择可访问的IP",columns:{hostName:"主机名",ip:"IP地址",status:"状态",progress:"进度",result:"结果",errorDetails:"错误详情",speed:"速度",fileSize:"文件大小"},status:{failed:"失败"}},sidebar:{taskPanel:"任务面板",envAwareness:"环境感知",processInfo:"进程信息",packageInfo:"安装包信息",hardwareInfo:"硬件信息",filesystemInfo:"文件系统信息",portInfo:"端口信息",dockerInfo:"容器信息",k8sInfo:"集群信息",codeInfo:"代码信息",materialInfo:"资料信息",securityTool:"AI安全工具",fileUpload:"文件上传",fileDown:"文件下载",aiBash:"AI Bash",testCases:"测试用例",llmAutoTesting:"LLM自动化测试",aiTaintAnalysis:"智能污点分析",smartOrchestration:"预留",toolPanel:"工具面板",hostConfig:"主机配置",cbhConfig:"堡垒机配置",repositoryConfig:"代码仓配置",executeCase:"用例执行"},fileUpload:{selectFile:"选择文件",clickToSelect:"选择文件",uploadPath:"上传目录路径",enterUploadPath:"请输入上传目录",startUpload:"开始上传",uploadProgress:"上传进度",uploadResults:"上传结果"},fileDownload:{enterDownloadPath:"请输入下载路径",startDownload:"开始下载",downloadProgress:"下载进度"},hostConfig:{title:"主机配置",addHost:"添加主机",exportSelected:"导出选中",deleteSelected:"删除选中",downloadTemplate:"下载模板",uploadTemplate:"上传模板",actions:"操作",save:"保存",edit:"编辑",copy:"复制",cancel:"取消",delete:"删除",columns:{hostName:"主机名",ipAddress:"IP地址",sshPort:"SSH端口",loginUser:"登录用户",loginPassword:"登录密码",switchRootCmd:"root切换命令",switchRootPwd:"root切换密码"}},repositoryConfig:{title:"代码仓配置",addRepository:"添加代码仓",exportSelected:"导出选中",deleteSelected:"删除选中",downloadSelected:"下载选中",downloadTemplate:"下载模板",uploadTemplate:"上传模板",selectDownloadPath:"选择下载路径",downloadProgress:"下载进度",actions:"操作",save:"保存",edit:"编辑",copy:"复制",cancel:"取消",delete:"删除",columns:{microservice:"微服务",repositoryUrl:"仓库地址",branchName:"送检代码分支"},validation:{invalidUrl:"无效的代码仓地址",unsupportedFormat:"不支持的代码仓格式",missingBranch:"缺少分支名称",parseError:"解析失败"},download:{selectPath:"请选择下载路径",downloading:"正在下载...",starting:"正在启动下载...",success:"下载成功",failed:"下载失败",partialSuccess:"部分下载成功",cloneError:"Git克隆失败"}},repositoryDownload:{title:"代码仓下载结果",clear:"清除",total:"总计",success:"成功",failed:"失败",downloading:"下载中",pending:"等待中",completed:"已完成",progress:"进度"},log:{title:"日志查看器",viewLogs:"查看日志",currentNode:"节点",noNodeSelected:"未选择节点",selectLevel:"选择级别",refresh:"刷新",noLogs:"暂无日志",fetchError:"获取日志失败"},testcase:{title:"测试用例",management:"用例管理",detail:"用例详情",selectLevel:"选择级别",searchButton:"搜索",resetButton:"重置"},caseColumn:{number:"用例编号",name:"用例名称",level:"用例级别",similarity:"相似度",prepareCondition:"前置条件",testSteps:"测试步骤",expectedResult:"预期结果"},smartOrchestration:{title:"智能编排",smartAnalysis:"智能测试用例分析",startAnalysis:"开始智能分析",caseAnalysis:"用例分析",naturalLanguageQuery:"自然语言查询",queryPlaceholder:"请输入自然语言查询",inputRequired:"请输入查询",searchFailed:"搜索失败",searchError:"搜索错误",resultsCleared:"搜索结果清除",topK:"Top K",scoreThreshold:"得分阈值",searchResults:"搜索结果",foundResults:"找到 {count} 个结果"}};s["a"].use(Ee["a"]);const Oe={"en-US":Ue,"zh-CN":Ve},He=()=>{const e=localStorage.getItem("language");if(e&&Oe[e])return e;const t=navigator.language||navigator.userLanguage,a=t.startsWith("zh")?"zh-CN":"en-US";return a},qe=new Ee["a"]({locale:He(),fallbackLocale:"en-US",messages:Oe,silentTranslationWarn:!0});var We=qe,Je=a("838b"),Ze=a.n(Je);a("b83f"),a("4f87");null===localStorage.getItem("darkMode")&&localStorage.setItem("darkMode","true"),s["a"].use(z["a"]),s["a"].config.productionTip=!1,s["a"].component("layout-simple",F),s["a"].component("layout-dashboard",Be),s["a"].component("VueJsonPretty",Ze.a),s["a"].prototype.$axios=C["a"],new s["a"]({store:M,router:T,i18n:We,render:e=>e(f)}).$mount("#app")},"61e1":function(e,t,a){},"72a1":function(e,t,a){},8163:function(e,t,a){"use strict";a("1ab8")},"85eb":function(e,t,a){},b14b:function(e,t,a){"use strict";a("85eb")},be69:function(e,t,a){"use strict";a("0a04")},bf02:function(e,t,a){"use strict";a("15fb")},d851:function(e,t,a){"use strict";a("241b")},d9c0:function(e,t,a){"use strict";a("72a1")},fec3:function(e,t,a){"use strict";var s=a("cee4");const o=s["a"].create({baseURL:Object({NODE_ENV:"production",BASE_URL:"/"}).VUE_APP_BASE_URL||"http://127.0.0.1:9998",timeout:36e3});t["a"]=o}});
//# sourceMappingURL=app.8a727bee.js.map