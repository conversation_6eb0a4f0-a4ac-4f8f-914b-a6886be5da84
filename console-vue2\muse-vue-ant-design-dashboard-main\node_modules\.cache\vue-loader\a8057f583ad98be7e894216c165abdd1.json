{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue", "mtime": 1753165961985}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ResizableTable.vue"], "names": [], "mappings": ";AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ResizableTable.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\n  <a-table\n    v-bind=\"$attrs\"\n    :columns=\"resizableColumns\"\n    :components=\"tableComponents\"\n    bordered\n    v-on=\"$listeners\"\n  >\n    <template v-for=\"slot in Object.keys($scopedSlots)\" :slot=\"slot\" slot-scope=\"scope\">\n      <slot :name=\"slot\" v-bind=\"scope\"></slot>\n    </template>\n  </a-table>\n</template>\n\n<script>\nexport default {\n  name: 'ResizableTable',\n  props: {\n    columns: {\n      type: Array,\n      required: true\n    }\n  },\n  data() {\n    return {\n      resizableColumns: []\n    };\n  },\n  watch: {\n    columns: {\n      handler(newColumns) {\n        this.resizableColumns = [...newColumns];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  computed: {\n    tableComponents() {\n      return {\n        header: {\n          cell: (h, props, children) => {\n            const { key, ...restProps } = props;\n            const col = this.resizableColumns.find(col => {\n              const k = col.dataIndex || col.key;\n              return k === key;\n            });\n            \n            if (!col || !col.width) {\n              return h('th', { ...restProps }, children);\n            }\n\n            return h(\n              'th',\n              {\n                ...restProps,\n                style: { position: 'relative' }\n              },\n              [\n                ...children,\n                h('div', {\n                  class: 'resize-handle',\n                  on: {\n                    mousedown: (e) => {\n                      e.preventDefault();\n                      const startX = e.clientX;\n                      const startWidth = col.width;\n                      \n                      const onMouseMove = (e) => {\n                        const newWidth = startWidth + (e.clientX - startX);\n                        if (newWidth > 50) {\n                          this.updateColumnWidth(key, newWidth);\n                        }\n                      };\n                      \n                      const onMouseUp = () => {\n                        document.removeEventListener('mousemove', onMouseMove);\n                        document.removeEventListener('mouseup', onMouseUp);\n                        document.body.style.cursor = '';\n                        document.body.style.userSelect = '';\n                      };\n                      \n                      document.body.style.cursor = 'col-resize';\n                      document.body.style.userSelect = 'none';\n                      document.addEventListener('mousemove', onMouseMove);\n                      document.addEventListener('mouseup', onMouseUp);\n                    }\n                  }\n                })\n              ]\n            );\n          }\n        }\n      };\n    }\n  },\n  methods: {\n    updateColumnWidth(key, width) {\n      this.resizableColumns = this.resizableColumns.map(col => {\n        const k = col.dataIndex || col.key;\n        if (k === key) {\n          return { ...col, width };\n        }\n        return col;\n      });\n      \n      // 触发父组件的列宽变化事件\n      this.$emit('columns-change', this.resizableColumns);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n:deep(.ant-table-thead > tr > th) {\n  position: relative;\n  \n  &:hover {\n    .resize-handle {\n      opacity: 1;\n    }\n  }\n}\n\n:deep(.resize-handle) {\n  position: absolute;\n  right: -5px;\n  top: 0;\n  bottom: 0;\n  width: 10px;\n  cursor: col-resize;\n  z-index: 1;\n  opacity: 0;\n  transition: opacity 0.2s;\n  \n  &:hover {\n    opacity: 1;\n    background-color: rgba(24, 144, 255, 0.2);\n  }\n  \n  &:active {\n    background-color: rgba(24, 144, 255, 0.4);\n  }\n}\n</style>\n"]}]}