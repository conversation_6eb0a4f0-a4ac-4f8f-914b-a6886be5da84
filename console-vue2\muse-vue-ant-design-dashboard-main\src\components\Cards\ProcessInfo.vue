<template>
  <a-card
    :bordered="false"
    class="header-solid h-full process-card"
    :bodyStyle="{ padding: 0 }"
  >
    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" :class="`text-${sidebarColor}`">
              <path fill="currentColor" d="M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.process') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchProcesses" />
        </div>
      </div>
    </template>
    <a-table
      :columns="columns"
      :data-source="processes"
      :rowKey="record => record.pid"
      :customRow="rowClick"
      :pagination="pagination"
    >
      <template #emptyText>
        <a-empty description="No processes found" />
      </template>
    </a-table>

    <!-- AI分析模态框 -->
    <a-modal
      v-model:visible="aiAnalysisVisible"
      title="AI Security Analysis"
      width="800px"
      @cancel="handleAIAnalysisClose"
    >
      <template v-slot:footer>
        <a-button @click="handleAIAnalysisClose">Close</a-button>
      </template>

      <div class="ai-analysis-container">
        <div v-if="selectedProcess" class="process-info">
          <p><strong>PID:</strong> {{ selectedProcess.pid }}</p>
        </div>

        <a-skeleton :loading="aiAnalysisLoading" active v-if="aiAnalysisLoading" />

        <div v-if="!aiAnalysisLoading && aiAnalysisResult" class="analysis-results">
          <div v-html="formatMarkdown(aiAnalysisResult)" class="markdown-content"></div>
        </div>

        <div v-if="!aiAnalysisLoading && !aiAnalysisResult && !aiAnalysisError" class="no-analysis">
          <a-empty description="Analyzing process..." />
        </div>

        <div v-if="!aiAnalysisLoading && aiAnalysisError" class="analysis-error">
          <a-alert
            message="Analysis Error"
            :description="aiAnalysisError"
            type="error"
            show-icon
          />
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';

export default {
  components: {
    RefreshButton
  },
  data() {
    return {
      processes: [],
      columns: [
        {
          title: 'PID',
          dataIndex: 'pid',
          key: 'pid',
          width: 120,
        },
        {
          title: 'Process Name',
          dataIndex: 'cmdline',
          key: 'cmdline',
          width: '95%',
          ellipsis: false,
        },
        {
          title: 'AI Analysis',
          key: 'ai_analysis',
          width: '15%',
          align: 'center',
          customRender: (_, record) => (
            <a-button
              class={`bg-${this.sidebarColor}`}
              style="color: white"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                this.showAIAnalysis(record);
              }}
            >
              Analyze
            </a-button>
          ),
        },
      ],
      pagination: {
        pageSize: 100,
        // 使用计算属性来绑定当前页码
        current: 1,
        // 添加分页变化事件处理
        onChange: (page) => {
          // 当页码变化时，更新Vuex中的页码状态
          this.$store.dispatch('processList/updateCurrentPage', page);
        }
      },
      // AI分析相关
      aiAnalysisVisible: false,
      aiAnalysisLoading: false,
      aiAnalysisResult: null,
      aiAnalysisError: null,
      selectedProcess: null,
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
    ...mapState('processList', ['currentPage', 'scrollPosition', 'lastViewedPid']),
  },
  watch: {
    selectedNodeIp: {
      handler() {
        this.fetchProcesses();
      },
      immediate: true
    },
    // 监听Vuex中的currentPage变化，同步到分页组件
    currentPage: {
      handler(newPage) {
        if (newPage && this.pagination.current !== newPage) {
          this.pagination.current = newPage;
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.fetchProcesses();

    // 恢复之前保存的滚动位置和应用高亮效果
    this.$nextTick(() => {
      // 恢复滚动位置
      if (this.scrollPosition > 0) {
        setTimeout(() => {
          window.scrollTo({
            top: this.scrollPosition,
            behavior: 'auto'
          });
        }, 100);
      }

      // 如果有lastViewedPid，尝试应用高亮效果
      if (this.lastViewedPid) {
        setTimeout(this.applyHighlight, 500); // 延迟确保表格已渲染
      }
    });
  },

  updated() {
    // 只有当processes数组有内容且有lastViewedPid时才应用高亮
    if (this.lastViewedPid && this.processes.length > 0) {
      this.applyHighlight();
    }
  },
  // 当用户离开进程列表页面但不是通过点击进程详情时，清除保存的滚动位置和分页信息
  beforeRouteLeave(to, _from, next) {
    // 如果不是导航到进程详情页面，则清除滚动位置和分页信息
    if (to.name !== 'ProcessDetail') {
      this.$store.dispatch('processList/resetState');
      // 清除最后查看的进程ID
      this.$store.dispatch('processList/clearLastViewedPid');
    }
    next();
  },
  methods: {
    async fetchProcesses() {
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        return;
      }
      try {
        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {
          params: {
            fields: 'pid,cmdline',
            dbFile: this.currentProject
          }
        });
        this.processes = response.data;
      } catch (error) {
        console.error('Error fetching processes:', error);
      }
    },
    rowClick(record, _index) {
      // 检查是否是最后查看的进程
      const isLastViewed = this.lastViewedPid && record.pid === this.lastViewedPid;

      return {
        on: {
          click: () => {
            this.viewProcessDetails(record);
          },
        },
        class: {
          // 如果是最后查看的进程，添加高亮类
          'last-viewed-row': isLastViewed
        },
        style: {
          cursor: 'pointer'
        }
      };
    },
    viewProcessDetails(process) {
      // 保存当前滚动位置到Vuex store
      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
      this.$store.dispatch('processList/updateScrollPosition', scrollPosition);

      // 保存当前查看的进程ID
      this.$store.dispatch('processList/updateLastViewedPid', process.pid);

      // 导航到进程详情页面
      this.$router.push({ name: 'ProcessDetail', params: { pid: process.pid } });
    },

    // AI分析相关方法
    showAIAnalysis(process) {
      this.selectedProcess = process;
      this.aiAnalysisVisible = true;
      this.aiAnalysisResult = null;
      this.aiAnalysisError = null;

      // 自动开始分析
      this.requestAIAnalysis();
    },

    // 应用高亮效果到最后查看的进程行
    applyHighlight() {
      if (!this.lastViewedPid) return;

      // 尝试查找包含lastViewedPid的行
      const rows = document.querySelectorAll('.ant-table-row');
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');

        // 检查第一个单元格（PID列）是否包含lastViewedPid
        if (cells.length > 0 && cells[0].textContent.includes(this.lastViewedPid)) {
          // 添加高亮类
          row.classList.add('last-viewed-row');

          // 直接设置样式以确保高亮效果生效
          for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            cell.style.backgroundColor = '#f5f5f5';
            cell.style.borderTop = '1px solid #1890ff';
            cell.style.borderBottom = '1px solid #1890ff';

            // 为第一个单元格添加左侧边框
            if (j === 0) {
              cell.style.borderLeft = '3px solid #1890ff';
            }

            // 为最后一个单元格添加右侧边框
            if (j === cells.length - 1) {
              cell.style.borderRight = '3px solid #1890ff';
            }
          }

          // 找到后退出循环
          break;
        }
      }
    },

    handleAIAnalysisClose() {
      this.aiAnalysisVisible = false;
      this.aiAnalysisResult = null;
      this.aiAnalysisError = null;
    },

    async requestAIAnalysis() {
      if (!this.selectedProcess) {
        this.aiAnalysisError = "No process selected for analysis";
        return;
      }

      if (!this.selectedNodeIp) {
        this.aiAnalysisError = "No node selected";
        return;
      }

      this.aiAnalysisLoading = true;
      this.aiAnalysisError = null;

      try {
        // 获取完整的进程信息
        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {
          params: {
            pid: this.selectedProcess.pid,
            fields: 'pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps',
            dbFile: this.currentProject || ''
          }
        });

        const processData = response.data;

        // 调用AI分析API，设置更长的超时时间
        const aiResponse = await axios.post('/api/ai/analyze/process', {
          process_data: {
            process_list: [processData]
          }
        }, {
          timeout: 600000 // 10分钟超时
        });

        if (aiResponse.data.success) {
          this.aiAnalysisResult = aiResponse.data.analysis;
        } else {
          this.aiAnalysisError = aiResponse.data.error || "Failed to analyze process data";
        }
      } catch (error) {
        console.error("AI analysis error:", error);
        this.aiAnalysisError = error.response?.data?.error || error.message || "An error occurred during analysis";
      } finally {
        this.aiAnalysisLoading = false;
      }
    },

    formatMarkdown(markdown) {
      // 简单的Markdown格式化，可以使用更复杂的库如marked.js
      if (!markdown) return '';

      // 替换标题
      let formatted = markdown
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
        .replace(/^##### (.*$)/gm, '<h5>$1</h5>')
        .replace(/^###### (.*$)/gm, '<h6>$1</h6>');

      // 替换粗体和斜体
      formatted = formatted
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/__(.*?)__/g, '<strong>$1</strong>')
        .replace(/_(.*?)_/g, '<em>$1</em>');

      // 替换代码块
      formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

      // 替换行内代码
      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

      // 替换列表
      formatted = formatted
        .replace(/^\s*\d+\.\s+(.*$)/gm, '<li>$1</li>')
        .replace(/^\s*[-*]\s+(.*$)/gm, '<li>$1</li>');

      // 替换段落
      formatted = formatted.replace(/^(?!<[a-z])(.*$)/gm, function(match) {
        return match.trim() ? '<p>' + match + '</p>' : '';
      });

      // 替换链接
      formatted = formatted.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');

      return formatted;
    },
  },
};
</script>

<style scoped lang="scss">
/* AI分析相关样式 */
.ai-analysis-container {
  padding: 16px;
}

.process-info {
  margin-bottom: 10px;
  padding: 8px 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #1890ff;

  p {
    margin-bottom: 0;
  }
}

.analysis-results {
  margin-top: 10px;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content h4 {
  font-size: 1em;
}

.markdown-content p {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-content code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-content pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
  margin-bottom: 16px;
}

.markdown-content pre code {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: 0;
  word-break: normal;
  white-space: pre;
}

.markdown-content li {
  margin-bottom: 8px;
}



.no-analysis {
  margin-top: 40px;
  text-align: center;
}

.analysis-error {
  margin-top: 20px;
}

/* 最后查看的行样式 - 浅灰色背景和轮廓线 */
.last-viewed-row td {
  background-color: #f5f5f5 !important;
  position: relative;
  border-top: 1px solid #1890ff !important;
  border-bottom: 1px solid #1890ff !important;
}

/* 为第一个单元格添加左侧边框 */
.last-viewed-row td:first-child {
  border-left: 3px solid #1890ff !important;
}

/* 为最后一个单元格添加右侧边框 */
.last-viewed-row td:last-child {
  border-right: 3px solid #1890ff !important;
}

/* 悬停时加强边框效果 */
.last-viewed-row:hover td {
  border-top: 2px solid #1890ff !important;
  border-bottom: 2px solid #1890ff !important;
}
</style>
