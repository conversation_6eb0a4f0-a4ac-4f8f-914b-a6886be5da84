{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\App.vue", "mtime": 1753175872855}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgRGlmeUNoYXRCb3QgZnJvbSAnQC9jb21wb25lbnRzL2NvbW1vbi9EaWZ5Q2hhdEJvdC52dWUnOw0KDQpleHBvcnQgZGVmYXVsdCB7DQoJbmFtZTogJ0FwcCcsDQoJY29tcG9uZW50czogew0KCQlEaWZ5Q2hhdEJvdA0KCX0sDQoJZGF0YSgpIHsNCgkJcmV0dXJuIHsNCgkJCWlzUmVhZHk6IGZhbHNlDQoJCX0NCgl9LA0KCWNvbXB1dGVkOiB7DQoJCS8vIFNldHMgY29tcG9uZW50cyBuYW1lIGJhc2VkIG9uIGN1cnJlbnQgcm91dGUncyBzcGVjaWZpZWQgbGF5b3V0LCBkZWZhdWx0cyB0bw0KCQkvLyA8bGF5b3V0LWRlZmF1bHQ+PC9sYXlvdXQtZGVmYXVsdD4gY29tcG9uZW50Lg0KCQlsYXlvdXQoKSB7DQoJCQlyZXR1cm4gImxheW91dC0iICsgKHRoaXMuJHJvdXRlLm1ldGEubGF5b3V0IHx8ICJkYXNoYm9hcmQiKTsNCgkJfSwNCgkJaXNEYXJrTW9kZSgpIHsNCgkJCXJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXJrTW9kZTsNCgkJfQ0KCX0sDQoJY3JlYXRlZCgpIHsNCgkJdGhpcy5pc1JlYWR5ID0gdHJ1ZTsNCgkJLy8g5Yid5aeL5YyW5rex6Imy5qih5byPDQoJCWlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXJrTW9kZSkgew0KCQkJZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmstbW9kZScpOw0KCQl9IGVsc2Ugew0KCQkJZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmstbW9kZScpOw0KCQl9DQoJfSwNCgl3YXRjaDogew0KCQkvLyDnm5HlkKzmt7HoibLmqKHlvI/lj5jljJYNCgkJaXNEYXJrTW9kZShuZXdWYWx1ZSkgew0KCQkJaWYgKG5ld1ZhbHVlKSB7DQoJCQkJZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmstbW9kZScpOw0KCQkJfSBlbHNlIHsNCgkJCQlkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZSgnZGFyay1tb2RlJyk7DQoJCQl9DQoJCX0NCgl9DQp9DQoNCg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAoBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<!--\r\n\tThis is the main page of the application, the layout component is used here,\r\n\tand the router-view is passed to it.\r\n\tLayout component is dynamically declared based on the layout for each route,\r\n\tspecified in routes list router/index.js .\r\n -->\r\n\r\n<template>\r\n\t<div id=\"app\">\r\n\t\t<component :is=\"layout\" v-if=\"isReady\">\r\n\t\t\t<router-view />\r\n\t\t</component>\r\n\t\t<div v-else>Loading...</div>\r\n\t\t\r\n\t\t<!-- 添加Dify聊天机器人 -->\r\n\t\t<DifyChatBot />\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport DifyChatBot from '@/components/common/DifyChatBot.vue';\r\n\r\nexport default {\r\n\tname: 'App',\r\n\tcomponents: {\r\n\t\tDifyChatBot\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisReady: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// Sets components name based on current route's specified layout, defaults to\r\n\t\t// <layout-default></layout-default> component.\r\n\t\tlayout() {\r\n\t\t\treturn \"layout-\" + (this.$route.meta.layout || \"dashboard\");\r\n\t\t},\r\n\t\tisDarkMode() {\r\n\t\t\treturn this.$store.state.darkMode;\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.isReady = true;\r\n\t\t// 初始化深色模式\r\n\t\tif (this.$store.state.darkMode) {\r\n\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t} else {\r\n\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听深色模式变化\r\n\t\tisDarkMode(newValue) {\r\n\t\t\tif (newValue) {\r\n\t\t\t\tdocument.documentElement.classList.add('dark-mode');\r\n\t\t\t} else {\r\n\t\t\t\tdocument.documentElement.classList.remove('dark-mode');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n</style>\r\n"]}]}