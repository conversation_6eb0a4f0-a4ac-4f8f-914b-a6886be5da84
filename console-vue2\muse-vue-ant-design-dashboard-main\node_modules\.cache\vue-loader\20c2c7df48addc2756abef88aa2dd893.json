{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue", "mtime": 1753170087083}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["FilesystemInfo.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FilesystemInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" fill-rule=\"evenodd\" d=\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\" clip-rule=\"evenodd\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.mount') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchFilesystem\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :dataSource=\"filesystemItems\"\r\n      :rowKey=\"(record) => record.key || record.device\"\r\n      :pagination=\"pagination\"\r\n      :loading=\"loading\"\r\n    >\r\n    </a-table>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  name: 'FilesystemInfo',\r\n  data() {\r\n    return {\r\n      filesystemItems: [],\r\n      loading: false,\r\n      columns: [\r\n        {\r\n          title: 'Device',\r\n          dataIndex: 'device',\r\n          key: 'device',\r\n          width: '20%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Point',\r\n          dataIndex: 'mount_point',\r\n          key: 'mount_point',\r\n          width: '25%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'File System Type',\r\n          dataIndex: 'fs_type',\r\n          key: 'fs_type',\r\n          width: '15%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Options',\r\n          dataIndex: 'mount_options',\r\n          key: 'mount_options',\r\n          width: '40%',\r\n          ellipsis: true,\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchFilesystem();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchFilesystem();\r\n  },\r\n  methods: {\r\n    async fetchFilesystem() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.filesystemItems = [];\r\n        return;\r\n      }\r\n      try {\r\n        // 显示加载状态\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/filesystem/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n\r\n        // 处理数据，确保每条记录有唯一的key\r\n        this.filesystemItems = response.data.map((item, index) => ({\r\n          ...item,\r\n          // 使用组合键作为唯一标识，防止device重复导致的渲染问题\r\n          key: `${item.device}_${item.mount_point}_${index}`\r\n        }));\r\n      } catch (error) {\r\n        console.error('Error fetching filesystem:', error);\r\n        this.filesystemItems = [];\r\n      } finally {\r\n        // 无论成功失败，都关闭加载状态\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n"]}]}