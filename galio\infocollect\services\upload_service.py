import os
import base64
import json
import threading
import time
import uuid
from typing import Dict
from log.logger import log_error
from datamodel.config_datamodel import HostConfig
from app.ssh.manager import SSHConnectionManager
from db.init_db import get_db


class UploadService:
    tasks: Dict[str, dict] = {}

    def __init__(self, db):
        self.db = db
        self.db_file = None

    def create_upload_task(self, file_path: str, targets: list, remote_dir: str, proxy_ip: str) -> str:
        task_id = str(uuid.uuid4())
        file_size = os.path.getsize(file_path)

        nodes = self.db.query(HostConfig).filter(HostConfig.ip.in_(targets)).all()
        if not nodes:
            raise Exception("No nodes found for given target IPs")

        node_configs = {}
        for node in nodes:
            node_configs[node.ip] = {
                "host_name": node.host_name,
            }

        node_status = {}
        for node in nodes:
            node_status[node.ip] = {
                "status": "pending",
                "progress": 0,
                "speed": "0 B/s",
                "bytes_transferred": 0,
                "host_name": node.host_name,
                "error_detail": None,
                "file_size": file_size,
            }

        self.tasks[task_id] = {
            "file_path": file_path,
            "remote_dir": remote_dir,
            "proxy_ip": proxy_ip,
            "nodes": node_status,
            "node_configs": node_configs,
            "metadata": {
                "file_size": file_size,
                "start_time": time.time(),
            },
            "db_file": self.db_file
        }

        return task_id

    def start_transfer(self, task_id: str):
        def transfer_to_node(node_ip: str):
            task = self.tasks[task_id]
            db_file = task["db_file"]
            
            with get_db(db_file) as db:
                node_info = task["nodes"][node_ip]

                try:
                    node_info["status"] = "in_progress"

                    filename = os.path.basename(task["file_path"])
                    mkdir_cmd = f"mkdir -p {task['remote_dir']}"

                    encoded_info = self._encode_node_info({
                        "ip": node_ip,
                        "host_name": node_info["host_name"]
                    }, filename)

                    curl_cmd = (
                        f"cd {task['remote_dir']} && "
                        f"curl -X GET "
                        f"'http://{task['proxy_ip']}:9998/api/file_transfer/upload/{encoded_info}' "
                        f"-o {filename}"
                    )

                    node = db.query(HostConfig).filter_by(ip=node_ip).first()
                    if not node:
                        raise Exception(f"Node not found for IP: {node_ip}")

                    with SSHConnectionManager(node).connection() as ssh:
                        exit_code, output, error = ssh.execute_command(mkdir_cmd)
                        if exit_code != 0:
                            raise Exception(f"Failed to create directory: {error}")

                        exit_code, output, error = ssh.execute_command(curl_cmd)
                        if exit_code != 0:
                            raise Exception(f"File transfer failed: {error}")

                        node_info["status"] = "success"
                        node_info["progress"] = 100
                        node_info["bytes_transferred"] = task["metadata"]["file_size"]
                except Exception as e:
                    node_info["status"] = "failed"
                    node_info["error_detail"] = {
                        "time": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "type": type(e).__name__,
                        "message": str(e)
                    }
                    log_error(f"Failed to transfer to {node_ip}: {str(e)}")

        threads = []
        for node_ip in self.tasks[task_id]["nodes"].keys():
            thread = threading.Thread(target=transfer_to_node, args=(node_ip,))
            thread.start()
            threads.append(thread)

        def update_progress():
            try:
                while True:
                    task = self.tasks.get(task_id)
                    if not task:
                        break

                    all_completed = True
                    for node in task["nodes"].values():
                        if node["status"] in ["pending", "in_progress"]:
                            all_completed = False
                            break

                    if all_completed:
                        try:
                            if os.path.exists(task["file_path"]):
                                os.remove(task["file_path"])
                        except Exception as e:
                            log_error(f"Failed to remove temporary file: {str(e)}")
                        break

                    time.sleep(1)
            finally:
                for thread in threads:
                    thread.join()

        progress_thread = threading.Thread(target=update_progress)
        progress_thread.start()

    def get_task_status(self, task_id: str) -> dict:
        task = self.tasks.get(task_id)
        if not task:
            raise Exception("Task not found")
        return task

    @staticmethod
    def _encode_node_info(node, filename):
        info = {
            "host_ip": node["ip"],
            "file_name": filename
        }
        return base64.b64encode(json.dumps(info).encode()).decode()
