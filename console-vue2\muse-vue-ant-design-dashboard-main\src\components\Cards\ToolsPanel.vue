<template>
  <a-card
    :bordered="false"
    class="header-solid h-full task-card"
    :bodyStyle="{ padding: '8px 16px' }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >
    <!-- 流程图 -->
    <div class="steps-container">
      <a-steps :current="currentStepComputed" class="steps-flow" size="small">
        <a-step>
          <template #icon>
            <a-icon type="upload" class="step-icon" />
          </template>
        </a-step>

        <a-step>
          <template #icon>
            <a-icon type="apartment" class="step-icon" />
          </template>
        </a-step>

        <a-step>
          <template #icon>
            <a-icon type="global" class="step-icon" />
          </template>
        </a-step>


      </a-steps>
    </div>

    <!-- 工具配置区域 -->
    <a-card style="margin: 0 0 16px;" size="small" :title="$t('tool.configureTool')">
      <a-tabs default-active-key="general" @change="handleToolTabChange">
        <!-- 通用工具标签页 -->
        <a-tab-pane key="general" :tab="$t('tool.generalTool') || '通用工具'">
          <general-tool-tab
            ref="generalToolTab"
            :is-processing="isProcessing"
            :selected-row-keys="selectedRowKeys"
            :selected-ip="selectedIp"
            :current-project="currentProject"
            @tool-path-changed="onToolPathChanged"
            @script-saved="onGeneralScriptSaved"
            @start-general-tool="onStartGeneralTool"
          />
        </a-tab-pane>

        <!-- Spider工具标签页 -->
        <a-tab-pane key="spider" :tab="$t('tool.spiderTool') || 'Spider工具'">
          <spider-tool-tab
            ref="spiderToolTab"
            :is-processing="isProcessing"
            :selected-row-keys="selectedRowKeys"
            :selected-ip="selectedIp"
            :current-project="currentProject"
            @script-saved="onSpiderScriptSaved"
            @run-spider="onRunSpider"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 节点选择区域 -->
    <a-card style="margin: 0 0 16px;" size="small" :title="$t('common.configureNodes')">
      <node-selector
        v-model="selectedRowKeys"
        :project-file="currentProject"
        :disabled="isProcessing"
        @input="onNodesSelected"
      />
    </a-card>

    <!-- 代理配置区域 -->
    <a-card style="margin-bottom: 16px;" size="small" :title="$t('common.configureProxy')">
      <proxy-selector
        v-model="selectedIp"
        :disabled="isProcessing"
        @change="handleProxyChange"
      />
    </a-card>

    <!-- 任务状态和日志 - 始终显示 -->
    <task-progress-card :task-type="'tool'" :is-processing="isProcessing" />
  </a-card>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import axios from '@/api/axiosInstance';
import NotificationMixin from '@/mixins/NotificationMixin';
import TaskPollingMixin from '@/mixins/TaskPollingMixin';
import ProxySelector from '@/components/common/ProxySelector.vue';
import TaskProgressCard from '@/components/common/TaskProgressCard.vue';
import NodeSelector from '@/components/common/NodeSelector.vue';
import GeneralToolTab from '@/components/Cards/GeneralToolTab.vue';
import SpiderToolTab from '@/components/Cards/SpiderToolTab.vue';

export default {
  mixins: [NotificationMixin, TaskPollingMixin],
  components: {
    ProxySelector,
    TaskProgressCard,
    NodeSelector,
    GeneralToolTab,
    SpiderToolTab
  },
  data() {
    return {
      selectedRowKeys: [],
      selectedIp: null,
      currentStep: 0,
      activeToolTab: 'general',
      toolPath: '',
      scriptPath: '',
      scriptContent: '',
      localSavePath: '',
      spiderScriptPath: '',
      spiderScriptContent: ''
    };
  },
  computed: {
    ...mapState(['activeToolTask', 'currentProject', 'sidebarColor']),
    taskId: {
      get() {
        return this.activeToolTask?.task_id;
      },
      set(value) {
        this.$store.dispatch('updateToolTask', value ? { task_id: value } : null);
      }
    },
    toolConfigComplete() {
      if (this.activeToolTab === 'general') {
        return this.toolPath && this.scriptContent && this.localSavePath;
      }
      return true;
    },
    currentStepComputed() {
      if (this.isProcessing) {
        return 2;  // 运行中时，点亮前三个图标
      }
      if (!this.toolPath && this.activeToolTab === 'general') {
        return -1;  // 没有上传工具包，所有图标不点亮
      }
      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length === 0) {
        return 0;   // 上传了工具包但未选择节点，点亮第一步图标和连接线
      }
      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length > 0 && !this.selectedIp) {
        return 1;   // 上传了工具包且选择了节点但未选择IP，点亮前两步图标和连接线
      }
      return 2;     // 全部选择完成且未在运行时，点亮前三个图标（删除了启动按钮）
    },

  },
  created() {
    // 检查当前项目的活动任务
    const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);
    if (taskInfo) {
      const { projectFile } = JSON.parse(taskInfo);
      if (projectFile === this.currentProject) {
        this.checkActiveTask();
      } else {
        // 清除任务信息如果属于不同项目
        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);
        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);
        this.$store.dispatch('updateToolTask', null);
      }
    }
  },
  methods: {
    ...mapActions(['addNotification']),

    // 切换工具标签页
    handleToolTabChange(key) {
      this.activeToolTab = key;
    },

    // 处理代理IP变化
    handleProxyChange(ip) {
      this.selectedIp = ip;
    },

    // 处理节点选择变化
    onNodesSelected(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    // 处理工具路径变化
    onToolPathChanged(path) {
      this.toolPath = path;
    },

    // 处理通用脚本保存
    onGeneralScriptSaved(path) {
      this.scriptPath = path;
      // 从子组件获取最新的脚本内容
      if (this.$refs.generalToolTab) {
        const toolData = this.$refs.generalToolTab.getToolData();
        this.scriptContent = toolData.scriptContent;
        this.localSavePath = toolData.localSavePath;
      }
    },

    // 处理Spider脚本保存
    onSpiderScriptSaved(path) {
      this.spiderScriptPath = path;
      // 从子组件获取最新的脚本内容
      if (this.$refs.spiderToolTab) {
        const toolData = this.$refs.spiderToolTab.getToolData();
        this.spiderScriptContent = toolData.spiderScriptContent;
      }
    },

    // 处理通用工具启动
    async onStartGeneralTool(toolData) {
      // 准备请求数据
      const requestData = {
        targets: this.selectedRowKeys,
        proxy_ip: this.selectedIp,
        script_path: toolData.toolPath,
        script_content: toolData.scriptContent,
        result_path: 'result.txt', // 默认结果文件路径
        local_save_path: toolData.localSavePath,
        dbFile: this.currentProject
      };

      // 保存返回的任务ID
      const taskId = await this.startTaskGeneric(
        requestData,
        'run',
        '通用工具任务已启动',
        '通用工具任务启动失败'
      );

      // 如果成功获取到任务ID，确保任务状态被更新
      if (taskId) {
        // 立即执行一次轮询，获取初始状态
        try {
          const response = await axios.get(`/api/script/${taskId}`);
          if (response.data) {
            // 确保更新到Vuex
            this.$store.dispatch('updateToolTask', response.data);

            // 强制更新视图
            this.$forceUpdate();
          }
        } catch (error) {
          console.error('获取初始任务状态失败:', error);
        }
      }
    },

    // 处理Spider运行
    async onRunSpider(requestData) {
      await this.startTaskGeneric(
        requestData,
        'run_spider',
        'Spider任务已启动',
        'Spider任务启动失败'
      );
    },



    /**
     * 通用任务启动方法
     * @param {Object} requestData - 请求数据
     * @param {string} endpoint - API端点
     * @param {string} successMessage - 成功消息
     * @param {string} errorTitle - 错误标题
     * @returns {Promise<string|null>} - 返回任务ID或null
     */
    async startTaskGeneric(requestData, endpoint, successMessage, errorTitle) {
      this.isProcessing = true;

      // 清除之前的工具任务通知记录
      const previousTaskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);
      if (previousTaskInfo) {
        try {
          const { taskId } = JSON.parse(previousTaskInfo);
          if (taskId) {
            this.clearTaskNotificationMark(taskId, 'tool', this.currentProject);
          }
        } catch (e) {
          console.error('Error clearing previous tool notification:', e);
        }
      }

      try {


        const { data: responseData } = await axios.post(`/api/script/${endpoint}`, requestData);

        if (responseData && responseData.task_id) {
          // 保存任务信息到本地存储
          localStorage.setItem(`toolTaskInfo_${this.currentProject}`, JSON.stringify({
            taskId: responseData.task_id,
            projectFile: this.currentProject
          }));
          localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);

          // 更新任务ID
          this.taskId = responseData.task_id;

          // 创建初始任务状态对象并更新到Vuex
          const initialTaskState = {
            task_id: responseData.task_id,
            nodes: {}
          };

          // 为每个选中的节点创建初始状态
          this.selectedRowKeys.forEach(ip => {
            initialTaskState.nodes[ip] = {
              ip: ip,
              host_name: ip, // 使用IP作为主机名，因为我们不再维护nodes数组
              status: 'processing',
              progress: 0
            };
          });

          // 立即更新到Vuex，确保UI立即显示进度条
          this.$store.dispatch('updateToolTask', initialTaskState);

          // 显示任务进度区域
          this.$nextTick(() => {
            // 确保DOM已更新
            const progressElement = document.querySelector('.ant-progress');
            if (progressElement) {
              progressElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          });

          // 立即执行一次轮询，获取初始状态
          try {
            const response = await axios.get(`/api/script/${responseData.task_id}`);


            if (response.data) {
              // 更新Vuex中的任务状态
              this.$store.dispatch('updateToolTask', response.data);
            }
          } catch (pollError) {
            console.error('初始轮询错误:', pollError);
            // 即使初始轮询失败，也不影响后续的定期轮询
          } finally {
            // 开始定期轮询
            this.startPolling(responseData.task_id, 'tool', 'script');
          }

          // 显示成功消息
          this.$message.success(successMessage || '任务已启动');

          // 强制更新视图
          this.$forceUpdate();

          return responseData.task_id;
        }
        return null;
      } catch (error) {
        console.error(`启动${endpoint}任务出错:`, error);
        let errorMessage = '服务器连接错误。';

        if (error.message) {
          errorMessage = error.message;
        } else if (error.response && error.response.data && error.response.data.error) {
          errorMessage = error.response.data.error;
        }

        this.$notify.error({
          title: errorTitle || '任务启动失败',
          message: errorMessage,
        });
        this.isProcessing = false;
        return null;
      }
    },

    // 重写 checkActiveTask 方法，调用混入中的方法
    async checkActiveTask() {
      try {
        const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);
        const taskCompleted = localStorage.getItem(`toolTaskCompleted_${this.currentProject}`);

        if (taskInfo) {
          const { taskId, projectFile } = JSON.parse(taskInfo);

          if (projectFile !== this.currentProject) {
            throw new Error('Task belongs to different project');
          }

          const response = await axios.get(`/api/script/${taskId}`);

          if (response.data) {
            this.$store.dispatch('updateToolTask', response.data);

            if (response.data.nodes) {
              const nodes = Object.values(response.data.nodes);
              const allCompleted = nodes.every(node =>
                ['success', 'failed'].includes(node.status)
              );

              if (!allCompleted && !taskCompleted) {
                this.isProcessing = true;
                this.startPolling(taskId, 'tool', 'script');
              } else if (allCompleted) {
                this.isProcessing = false;
                localStorage.setItem(`toolTaskCompleted_${this.currentProject}`, 'true');
              }
            }
          }
        }
      } catch (error) {
        console.error('Error checking active task:', error);
        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);
        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);
      }
    },

    // 使用混入中的 startPolling 方法，但需要提供特定参数
    startPollingWrapper(taskId) {
      this.startPolling(taskId, 'tool', 'script');
    },

    activated() {
      // 当组件被激活时（从缓存中恢复）立即检查任务状态
      this.checkActiveTask();
    }
  },
  watch: {
    // 监听 currentProject 变化
    currentProject: {
      handler(newProject, oldProject) {
        if (newProject !== oldProject) {
          // 清除之前项目的任务状态
          this.$store.dispatch('updateToolTask', null);
          this.stopPolling();
          // 检查新项目的活动任务
          this.checkActiveTask();
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped lang="scss">
// 基础卡片样式
.task-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
}

// 步骤容器
.steps-container {
  width: 50%;
  margin: 0 auto 24px;
  padding: 12px 0;
}

// 深度选择器样式集中管理
::v-deep {
  .ant-card {
    border-radius: 8px;
    overflow: hidden;
    .ant-card-head {
      background: #f0f2f5;
    }
  }

  .ant-progress {
    border-radius: 3px;
  }
  .ant-tooltip-inner {
    max-width: 500px;
    white-space: pre-wrap;
  }
  .ant-table-tbody > tr:last-child > td {
    border-bottom: 1px solid #f0f0f0;
  }
  .steps-flow {
    .ant-steps-item {
      &-process,
      &-finish {
        .ant-steps-item-container {
          .ant-steps-item-content {
            .ant-steps-item-title::after {
              background-color: #3b4149 !important;
              height: 2px !important;
              top: 25px !important;
            }
          }
        }
      }

      &-wait {
        .ant-steps-item-container {
          .ant-steps-item-content {
            .ant-steps-item-title::after {
              background-color: #d9d9d9 !important;
              height: 2px !important;
              top: 25px !important;
            }
          }
        }

        .step-icon {
          color: #d9d9d9 !important;
        }
      }

      &-icon {
        width: 88px;
        height: 88px;
        line-height: 80px;
        padding: 4px;
        font-size: 40px;
        border-width: 2px;
        margin-top: -20px;
        color: #3b4149;

        .step-icon {
          font-size: 40px;
          color: #3b4149;
        }
      }

      &-tail::after {
        height: 2px;
      }

      &:last-child {
        .step-icon {
          color: #d9d9d9 !important;
        }

        &.ant-steps-item-process,
        &.ant-steps-item-finish {
          .step-icon {
            color: #3b4149 !important;
          }
        }
      }
    }
  }
  .ready-to-start {
    animation: pulse 1.2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .clickable {
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
