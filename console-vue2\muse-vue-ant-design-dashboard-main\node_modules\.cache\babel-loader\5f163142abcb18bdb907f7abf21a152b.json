{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue?vue&type=template&id=40fbb332&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue", "mtime": 1753169488841}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "bordered", "bodyStyle", "padding", "headStyle", "borderBottom", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "xmlns", "width", "height", "viewBox", "fill", "d", "_v", "_s", "$t", "on", "refresh", "fetchHardware", "proxy", "columns", "hardwareItems", "<PERSON><PERSON><PERSON>", "record", "device_info", "pagination", "column", "device_type", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/_Projects_python/Tools/console-vue2/muse-vue-ant-design-dashboard-main/src/components/Cards/HardwareInfo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-card\",\n    {\n      staticClass: \"header-solid h-full hardware-card\",\n      attrs: {\n        bordered: false,\n        bodyStyle: { padding: 0 },\n        headStyle: { borderBottom: \"1px solid #e8e8e8\" }\n      },\n      scopedSlots: _vm._u([\n        {\n          key: \"title\",\n          fn: function() {\n            return [\n              _c(\"div\", { staticClass: \"card-header-wrapper\" }, [\n                _c(\"div\", { staticClass: \"header-wrapper\" }, [\n                  _c(\"div\", { staticClass: \"logo-wrapper\" }, [\n                    _c(\n                      \"svg\",\n                      {\n                        class: `text-${_vm.sidebarColor}`,\n                        attrs: {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          width: \"20\",\n                          height: \"20\",\n                          viewBox: \"0 0 512 512\"\n                        }\n                      },\n                      [\n                        _c(\"path\", {\n                          attrs: {\n                            fill: \"currentColor\",\n                            d: \"M160 160h192v192H160z\"\n                          }\n                        }),\n                        _c(\"path\", {\n                          attrs: {\n                            fill: \"currentColor\",\n                            d:\n                              \"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"\n                          }\n                        })\n                      ]\n                    )\n                  ]),\n                  _c(\"h6\", { staticClass: \"font-semibold m-0\" }, [\n                    _vm._v(_vm._s(_vm.$t(\"headTopic.hardware\")))\n                  ])\n                ]),\n                _c(\n                  \"div\",\n                  [_c(\"RefreshButton\", { on: { refresh: _vm.fetchHardware } })],\n                  1\n                )\n              ])\n            ]\n          },\n          proxy: true\n        }\n      ])\n    },\n    [\n      _c(\"a-table\", {\n        attrs: {\n          columns: _vm.columns,\n          \"data-source\": _vm.hardwareItems,\n          rowKey: record => record.device_info,\n          pagination: _vm.pagination\n        },\n        scopedSlots: _vm._u([\n          {\n            key: \"bodyCell\",\n            fn: function({ column, record }) {\n              return [\n                column.key === \"device_info\"\n                  ? [\n                      _c(\"div\", { staticClass: \"table-hardware-info\" }, [\n                        _c(\"span\", [_vm._v(_vm._s(record.device_info))]),\n                        _c(\"span\", [_vm._v(_vm._s(record.device_type))])\n                      ])\n                    ]\n                  : _vm._e()\n              ]\n            }\n          }\n        ])\n      })\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR;IACEE,WAAW,EAAE,mCAAmC;IAChDC,KAAK,EAAE;MACLC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACzBC,SAAS,EAAE;QAAEC,YAAY,EAAE;MAAoB;IACjD,CAAC;IACDC,WAAW,EAAEV,GAAG,CAACW,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,OAAO;MACZC,EAAE,EAAE,SAAAA,CAAA,EAAW;QACb,OAAO,CACLZ,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAe,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;UACEa,KAAK,EAAE,QAAQd,GAAG,CAACe,YAAY,EAAE;UACjCX,KAAK,EAAE;YACLY,KAAK,EAAE,4BAA4B;YACnCC,KAAK,EAAE,IAAI;YACXC,MAAM,EAAE,IAAI;YACZC,OAAO,EAAE;UACX;QACF,CAAC,EACD,CACElB,EAAE,CAAC,MAAM,EAAE;UACTG,KAAK,EAAE;YACLgB,IAAI,EAAE,cAAc;YACpBC,CAAC,EAAE;UACL;QACF,CAAC,CAAC,EACFpB,EAAE,CAAC,MAAM,EAAE;UACTG,KAAK,EAAE;YACLgB,IAAI,EAAE,cAAc;YACpBC,CAAC,EACC;UACJ;QACF,CAAC,CAAC,CAEN,CAAC,CACF,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;UAAEE,WAAW,EAAE;QAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACwB,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAC7C,CAAC,CACH,CAAC,EACFvB,EAAE,CACA,KAAK,EACL,CAACA,EAAE,CAAC,eAAe,EAAE;UAAEwB,EAAE,EAAE;YAAEC,OAAO,EAAE1B,GAAG,CAAC2B;UAAc;QAAE,CAAC,CAAC,CAAC,EAC7D,CACF,CAAC,CACF,CAAC,CACH;MACH,CAAC;MACDC,KAAK,EAAE;IACT,CAAC,CACF;EACH,CAAC,EACD,CACE3B,EAAE,CAAC,SAAS,EAAE;IACZG,KAAK,EAAE;MACLyB,OAAO,EAAE7B,GAAG,CAAC6B,OAAO;MACpB,aAAa,EAAE7B,GAAG,CAAC8B,aAAa;MAChCC,MAAM,EAAEC,MAAM,IAAIA,MAAM,CAACC,WAAW;MACpCC,UAAU,EAAElC,GAAG,CAACkC;IAClB,CAAC;IACDxB,WAAW,EAAEV,GAAG,CAACW,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,UAAU;MACfC,EAAE,EAAE,SAAAA,CAAS;QAAEsB,MAAM;QAAEH;MAAO,CAAC,EAAE;QAC/B,OAAO,CACLG,MAAM,CAACvB,GAAG,KAAK,aAAa,GACxB,CACEX,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACS,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,EAChDhC,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACS,MAAM,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC,CACH,GACDpC,GAAG,CAACqC,EAAE,CAAC,CAAC,CACb;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBvC,MAAM,CAACwC,aAAa,GAAG,IAAI;AAE3B,SAASxC,MAAM,EAAEuC,eAAe", "ignoreList": []}]}