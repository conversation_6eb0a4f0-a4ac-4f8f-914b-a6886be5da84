{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue?vue&type=style&index=0&id=16fde50e&scoped=true&lang=css", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue", "mtime": 1753169771741}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYW50LWNhcmQgew0KICBtYXJnaW46IDI0cHg7DQp9DQoNCi5hbnQtc3BhY2Ugew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCn0NCi8qLnByb2plY3QtdGFibGUgPj4+IC5hbnQtdGFibGUtdGhlYWQgPiB0ciA+IHRoIHsqLw0KLyogIGZvbnQtd2VpZ2h0OiA2MDA7Ki8NCi8qICBwYWRkaW5nOiAxMnB4IDE2cHggIWltcG9ydGFudDsqLw0KLyp9Ki8NCg0KLyoucHJvamVjdC10YWJsZSA+Pj4gLmFudC10YWJsZS10Ym9keSA+IHRyID4gdGQgeyovDQovKiAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7Ki8NCi8qICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOyovDQovKn0qLw0K"}, {"version": 3, "sources": ["ProjectManager.vue"], "names": [], "mappings": ";AAuPA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ProjectManager.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card title=\"项目列表\">\r\n      <template #extra>\r\n        <a-button type=\"primary\" @click=\"createNewProject\">\r\n          创建新项目\r\n        </a-button>\r\n      </template>\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"projects\"\r\n        :row-key=\"record => record.dbFile\"\r\n        :customRow=\"onCustomRow\"\r\n        class=\"project-table\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProjectManager',\r\n  data() {\r\n    return {\r\n      columns: [\r\n        {\r\n          title: '项目名称',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '250px',\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            return (\r\n            <span>\r\n            <a-icon type=\"folder\" style=\"margin-right: 8px;\" />\r\n            {text}\r\n            </span>\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: '数据库文件',\r\n          dataIndex: 'dbFile',\r\n          key: 'dbFile',\r\n          width: '280px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          dataIndex: 'createdAt',\r\n          key: 'createdAt',\r\n          width: '160px',\r\n          customRender: (text) => {\r\n            return new Date(text).toLocaleString('zh-CN', {\r\n              year: 'numeric',\r\n              month: '2-digit',\r\n              day: '2-digit',\r\n              hour: '2-digit',\r\n              minute: '2-digit'\r\n            });\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: '180px',\r\n          align: 'center',\r\n          fixed: 'right',\r\n          customRender: (text, record) => {\r\n            return (\r\n              <a-space size={8}>\r\n                <a-button\r\n                  type=\"primary\"\r\n                  onClick={() => this.selectProject(record)}\r\n                >\r\n                  进入项目\r\n                </a-button>\r\n                <a-popconfirm\r\n                  title=\"确定要删除这个项目吗？\"\r\n                  onConfirm={() => this.deleteProject(record)}\r\n                  okText=\"确定\"\r\n                  cancelText=\"取消\"\r\n                >\r\n                  <a-button type=\"danger\">删除</a-button>\r\n                </a-popconfirm>\r\n              </a-space>\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      projects: [],\r\n      tempProjectName: ''\r\n    };\r\n  },\r\n  methods: {\r\n    onCustomRow(record) {\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            // 如果需要行点击事件的话，在这里处理\r\n          }\r\n        }\r\n      };\r\n    },\r\n    async fetchProjects() {\r\n      try {\r\n        const response = await axios.get('/api/projects');\r\n        if (Array.isArray(response.data)) {\r\n          this.projects = response.data.map(project => ({\r\n            name: project.name || '',\r\n            dbFile: project.dbFile || '',\r\n            createdAt: project.createdAt || '',\r\n            key: project.dbFile || Date.now().toString()\r\n          }));\r\n        } else {\r\n          this.projects = [];\r\n          console.error('项目数据格式无效：', response.data);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败：', error);\r\n        this.$message.error('获取项目列表失败');\r\n        this.projects = [];\r\n      }\r\n    },\r\n\r\n    async selectProject(project) {\r\n      if (!project?.dbFile) {\r\n        console.error('项目数据无效：', project);\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const encodedDbFile = encodeURIComponent(project.dbFile);\r\n        const validationUrl = `/api/projects/validate/${encodedDbFile}`;\r\n\r\n        const response = await axios.get(validationUrl);\r\n        if (response.data.valid) {\r\n          await this.$store.dispatch('switchProject', { \r\n            dbFile: project.dbFile, \r\n            projectName: project.name \r\n          });\r\n          await this.$store.dispatch('fetchNodes');\r\n\r\n          // 清除所有任务相关的localStorage\r\n          localStorage.removeItem('activeTaskId');\r\n          localStorage.removeItem('taskCompleted');\r\n          localStorage.removeItem('activeUploadTaskId');\r\n          localStorage.removeItem('activeDownloadTaskId');\r\n          localStorage.removeItem('activeToolTaskId');\r\n\r\n          await this.$router.push('/task');\r\n          this.$message.success('成功进入项目');\r\n        } else {\r\n          console.error('验证失败：', response.data.error);\r\n          this.$message.error(response.data.error || '数据库文件无效或已损坏');\r\n        }\r\n      } catch (error) {\r\n        console.error('项目验证出错：', error);\r\n        this.$message.error(error.response?.data?.error || '验证项目失败');\r\n      }\r\n    },\r\n\r\n    async deleteProject(project) {\r\n      if (!project?.dbFile) {\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await axios.delete(`/api/projects/${encodeURIComponent(project.dbFile)}`);\r\n        this.$message.success('项目删除成功');\r\n        await this.fetchProjects();\r\n      } catch (error) {\r\n        console.error('Error deleting project:', error);\r\n        this.$message.error('删除项目失败');\r\n      }\r\n    },\r\n\r\n    async createNewProject() {\r\n      try {\r\n        const projectName = await new Promise((resolve, reject) => {\r\n          this.$confirm({\r\n            title: '创建新项目',\r\n            content: h => (\r\n              <div>\r\n                <a-input\r\n                  placeholder=\"请输入项目名称\"\r\n                  onChange={(e) => {\r\n                    const value = e.target.value.replace(/[^a-zA-Z0-9_-]/g, '');\r\n                    this.tempProjectName = value;\r\n                    e.target.value = value;\r\n                  }}\r\n                />\r\n                <div class=\"project-hint-text\" style=\"font-size: 12px; margin-top: 8px;\">\r\n                  只允许输入大小写字母、数字、下划线和连字符\r\n                </div>\r\n              </div>\r\n            ),\r\n            okText: '确定',\r\n            cancelText: '取消',\r\n            onOk: () => {\r\n              if (!this.tempProjectName) {\r\n                this.$message.warning('请输入项目名称');\r\n                return Promise.reject();\r\n              }\r\n              // 验证项目名称格式\r\n              if (!/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)) {\r\n                this.$message.warning('项目名称只能包含大小写字母、数字、下划线和连字符');\r\n                return Promise.reject();\r\n              }\r\n              resolve(this.tempProjectName);\r\n            },\r\n            onCancel: () => {\r\n              reject();\r\n            }\r\n          });\r\n        });\r\n\r\n        if (projectName) {\r\n          const response = await axios.post('/api/projects/new', {\r\n            name: projectName\r\n          });\r\n\r\n          await this.fetchProjects();\r\n          this.$message.success('新项目创建成功');\r\n          if (response.data?.dbFile) {\r\n            await this.selectProject(response.data);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error) { // 用户取消操作时不显示错误\r\n          console.error('Error creating new project:', error);\r\n          this.$message.error('创建新项目失败');\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjects();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ant-card {\r\n  margin: 24px;\r\n}\r\n\r\n.ant-space {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n/*.project-table >>> .ant-table-thead > tr > th {*/\r\n/*  font-weight: 600;*/\r\n/*  padding: 12px 16px !important;*/\r\n/*}*/\r\n\r\n/*.project-table >>> .ant-table-tbody > tr > td {*/\r\n/*  padding: 12px 16px !important;*/\r\n/*  vertical-align: middle;*/\r\n/*}*/\r\n</style>\r\n"]}]}