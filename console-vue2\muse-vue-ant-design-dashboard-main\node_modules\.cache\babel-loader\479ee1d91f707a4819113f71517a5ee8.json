{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\common\\ResizableTable.vue", "mtime": 1753170977715}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IubWFwLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdSZXNpemFibGVUYWJsZScsCiAgcHJvcHM6IHsKICAgIGNvbHVtbnM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcmVzaXphYmxlQ29sdW1uczogW10KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgY29sdW1uczogewogICAgICBoYW5kbGVyKG5ld0NvbHVtbnMpIHsKICAgICAgICB0aGlzLnJlc2l6YWJsZUNvbHVtbnMgPSBbLi4ubmV3Q29sdW1uc107CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHRhYmxlQ29tcG9uZW50cygpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBoZWFkZXI6IHsKICAgICAgICAgIGNlbGw6IChoLCBwcm9wcywgY2hpbGRyZW4pID0+IHsKICAgICAgICAgICAgY29uc3QgewogICAgICAgICAgICAgIGtleSwKICAgICAgICAgICAgICAuLi5yZXN0UHJvcHMKICAgICAgICAgICAgfSA9IHByb3BzOwogICAgICAgICAgICBjb25zdCBjb2wgPSB0aGlzLnJlc2l6YWJsZUNvbHVtbnMuZmluZChjb2wgPT4gewogICAgICAgICAgICAgIGNvbnN0IGsgPSBjb2wuZGF0YUluZGV4IHx8IGNvbC5rZXk7CiAgICAgICAgICAgICAgcmV0dXJuIGsgPT09IGtleTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGlmICghY29sIHx8ICFjb2wud2lkdGgpIHsKICAgICAgICAgICAgICByZXR1cm4gaCgndGgnLCB7CiAgICAgICAgICAgICAgICAuLi5yZXN0UHJvcHMKICAgICAgICAgICAgICB9LCBjaGlsZHJlbik7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGgoJ3RoJywgewogICAgICAgICAgICAgIC4uLnJlc3RQcm9wcywKICAgICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIFsuLi5jaGlsZHJlbiwgaCgnZGl2JywgewogICAgICAgICAgICAgIGNsYXNzOiAncmVzaXplLWhhbmRsZScsCiAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgIG1vdXNlZG93bjogZSA9PiB7CiAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTsKICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhcnRYID0gZS5jbGllbnRYOwogICAgICAgICAgICAgICAgICBjb25zdCBzdGFydFdpZHRoID0gY29sLndpZHRoOwogICAgICAgICAgICAgICAgICBjb25zdCBvbk1vdXNlTW92ZSA9IGUgPT4gewogICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1dpZHRoID0gc3RhcnRXaWR0aCArIChlLmNsaWVudFggLSBzdGFydFgpOwogICAgICAgICAgICAgICAgICAgIGlmIChuZXdXaWR0aCA+IDUwKSB7CiAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVwZGF0ZUNvbHVtbldpZHRoKGtleSwgbmV3V2lkdGgpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgY29uc3Qgb25Nb3VzZVVwID0gKCkgPT4gewogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG9uTW91c2VNb3ZlKTsKICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgb25Nb3VzZVVwKTsKICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLmN1cnNvciA9ICcnOwogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUudXNlclNlbGVjdCA9ICcnOwogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLmN1cnNvciA9ICdjb2wtcmVzaXplJzsKICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS51c2VyU2VsZWN0ID0gJ25vbmUnOwogICAgICAgICAgICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZW1vdmUnLCBvbk1vdXNlTW92ZSk7CiAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNldXAnLCBvbk1vdXNlVXApOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSldKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH07CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICB1cGRhdGVDb2x1bW5XaWR0aChrZXksIHdpZHRoKSB7CiAgICAgIHRoaXMucmVzaXphYmxlQ29sdW1ucyA9IHRoaXMucmVzaXphYmxlQ29sdW1ucy5tYXAoY29sID0+IHsKICAgICAgICBjb25zdCBrID0gY29sLmRhdGFJbmRleCB8fCBjb2wua2V5OwogICAgICAgIGlmIChrID09PSBrZXkpIHsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIC4uLmNvbCwKICAgICAgICAgICAgd2lkdGgKICAgICAgICAgIH07CiAgICAgICAgfQogICAgICAgIHJldHVybiBjb2w7CiAgICAgIH0pOwoKICAgICAgLy8g6Kem5Y+R54i257uE5Lu255qE5YiX5a695Y+Y5YyW5LqL5Lu2CiAgICAgIHRoaXMuJGVtaXQoJ2NvbHVtbnMtY2hhbmdlJywgdGhpcy5yZXNpemFibGVDb2x1bW5zKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "props", "columns", "type", "Array", "required", "data", "resizableColumns", "watch", "handler", "newColumns", "immediate", "deep", "computed", "tableComponents", "header", "cell", "h", "children", "key", "restProps", "col", "find", "k", "dataIndex", "width", "style", "position", "class", "on", "mousedown", "e", "preventDefault", "startX", "clientX", "startWidth", "onMouseMove", "newWidth", "updateColumnWidth", "onMouseUp", "document", "removeEventListener", "body", "cursor", "userSelect", "addEventListener", "methods", "map", "$emit"], "sources": ["src/components/common/ResizableTable.vue"], "sourcesContent": ["<template>\n  <a-table\n    v-bind=\"$attrs\"\n    :columns=\"resizableColumns\"\n    :components=\"tableComponents\"\n    bordered\n    v-on=\"$listeners\"\n  >\n    <template v-for=\"slot in Object.keys($scopedSlots)\" :slot=\"slot\" slot-scope=\"scope\">\n      <slot :name=\"slot\" v-bind=\"scope\"></slot>\n    </template>\n  </a-table>\n</template>\n\n<script>\nexport default {\n  name: 'ResizableTable',\n  props: {\n    columns: {\n      type: Array,\n      required: true\n    }\n  },\n  data() {\n    return {\n      resizableColumns: []\n    };\n  },\n  watch: {\n    columns: {\n      handler(newColumns) {\n        this.resizableColumns = [...newColumns];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  computed: {\n    tableComponents() {\n      return {\n        header: {\n          cell: (h, props, children) => {\n            const { key, ...restProps } = props;\n            const col = this.resizableColumns.find(col => {\n              const k = col.dataIndex || col.key;\n              return k === key;\n            });\n            \n            if (!col || !col.width) {\n              return h('th', { ...restProps }, children);\n            }\n\n            return h(\n              'th',\n              {\n                ...restProps,\n                style: { position: 'relative' }\n              },\n              [\n                ...children,\n                h('div', {\n                  class: 'resize-handle',\n                  on: {\n                    mousedown: (e) => {\n                      e.preventDefault();\n                      const startX = e.clientX;\n                      const startWidth = col.width;\n                      \n                      const onMouseMove = (e) => {\n                        const newWidth = startWidth + (e.clientX - startX);\n                        if (newWidth > 50) {\n                          this.updateColumnWidth(key, newWidth);\n                        }\n                      };\n                      \n                      const onMouseUp = () => {\n                        document.removeEventListener('mousemove', onMouseMove);\n                        document.removeEventListener('mouseup', onMouseUp);\n                        document.body.style.cursor = '';\n                        document.body.style.userSelect = '';\n                      };\n                      \n                      document.body.style.cursor = 'col-resize';\n                      document.body.style.userSelect = 'none';\n                      document.addEventListener('mousemove', onMouseMove);\n                      document.addEventListener('mouseup', onMouseUp);\n                    }\n                  }\n                })\n              ]\n            );\n          }\n        }\n      };\n    }\n  },\n  methods: {\n    updateColumnWidth(key, width) {\n      this.resizableColumns = this.resizableColumns.map(col => {\n        const k = col.dataIndex || col.key;\n        if (k === key) {\n          return { ...col, width };\n        }\n        return col;\n      });\n      \n      // 触发父组件的列宽变化事件\n      this.$emit('columns-change', this.resizableColumns);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n\n:deep(.resize-handle) {\n  position: absolute;\n  right: -5px;\n  top: 0;\n  bottom: 0;\n  width: 10px;\n  cursor: col-resize;\n  z-index: 1;\n  opacity: 0;\n  transition: opacity 0.2s;\n  \n  &:hover {\n    opacity: 1;\n    background-color: rgba(24, 144, 255, 0.2);\n  }\n  \n  &:active {\n    background-color: rgba(24, 144, 255, 0.4);\n  }\n}\n</style>\n"], "mappings": ";;;AAeA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACAN,OAAA;MACAO,QAAAC,UAAA;QACA,KAAAH,gBAAA,OAAAG,UAAA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACAC,gBAAA;MACA;QACAC,MAAA;UACAC,IAAA,EAAAA,CAAAC,CAAA,EAAAhB,KAAA,EAAAiB,QAAA;YACA;cAAAC,GAAA;cAAA,GAAAC;YAAA,IAAAnB,KAAA;YACA,MAAAoB,GAAA,QAAAd,gBAAA,CAAAe,IAAA,CAAAD,GAAA;cACA,MAAAE,CAAA,GAAAF,GAAA,CAAAG,SAAA,IAAAH,GAAA,CAAAF,GAAA;cACA,OAAAI,CAAA,KAAAJ,GAAA;YACA;YAEA,KAAAE,GAAA,KAAAA,GAAA,CAAAI,KAAA;cACA,OAAAR,CAAA;gBAAA,GAAAG;cAAA,GAAAF,QAAA;YACA;YAEA,OAAAD,CAAA,CACA,MACA;cACA,GAAAG,SAAA;cACAM,KAAA;gBAAAC,QAAA;cAAA;YACA,GACA,CACA,GAAAT,QAAA,EACAD,CAAA;cACAW,KAAA;cACAC,EAAA;gBACAC,SAAA,EAAAC,CAAA;kBACAA,CAAA,CAAAC,cAAA;kBACA,MAAAC,MAAA,GAAAF,CAAA,CAAAG,OAAA;kBACA,MAAAC,UAAA,GAAAd,GAAA,CAAAI,KAAA;kBAEA,MAAAW,WAAA,GAAAL,CAAA;oBACA,MAAAM,QAAA,GAAAF,UAAA,IAAAJ,CAAA,CAAAG,OAAA,GAAAD,MAAA;oBACA,IAAAI,QAAA;sBACA,KAAAC,iBAAA,CAAAnB,GAAA,EAAAkB,QAAA;oBACA;kBACA;kBAEA,MAAAE,SAAA,GAAAA,CAAA;oBACAC,QAAA,CAAAC,mBAAA,cAAAL,WAAA;oBACAI,QAAA,CAAAC,mBAAA,YAAAF,SAAA;oBACAC,QAAA,CAAAE,IAAA,CAAAhB,KAAA,CAAAiB,MAAA;oBACAH,QAAA,CAAAE,IAAA,CAAAhB,KAAA,CAAAkB,UAAA;kBACA;kBAEAJ,QAAA,CAAAE,IAAA,CAAAhB,KAAA,CAAAiB,MAAA;kBACAH,QAAA,CAAAE,IAAA,CAAAhB,KAAA,CAAAkB,UAAA;kBACAJ,QAAA,CAAAK,gBAAA,cAAAT,WAAA;kBACAI,QAAA,CAAAK,gBAAA,YAAAN,SAAA;gBACA;cACA;YACA,GAEA;UACA;QACA;MACA;IACA;EACA;EACAO,OAAA;IACAR,kBAAAnB,GAAA,EAAAM,KAAA;MACA,KAAAlB,gBAAA,QAAAA,gBAAA,CAAAwC,GAAA,CAAA1B,GAAA;QACA,MAAAE,CAAA,GAAAF,GAAA,CAAAG,SAAA,IAAAH,GAAA,CAAAF,GAAA;QACA,IAAAI,CAAA,KAAAJ,GAAA;UACA;YAAA,GAAAE,GAAA;YAAAI;UAAA;QACA;QACA,OAAAJ,GAAA;MACA;;MAEA;MACA,KAAA2B,KAAA,wBAAAzC,gBAAA;IACA;EACA;AACA", "ignoreList": []}]}