{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue?vue&type=style&index=0&id=229ff433&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue", "mtime": 1753175269721}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TaskPanel.vue"], "names": [], "mappings": ";AA0SA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TaskPanel.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full task-card\"\r\n    :bodyStyle=\"{ padding: '8px 16px' }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <!-- 流程图 -->\r\n    <div class=\"steps-container\">\r\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"apartment\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"global\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-tooltip :title=\"getPlayIconTooltip\">\r\n              <a-icon\r\n                type=\"play-circle\"\r\n                class=\"step-icon\"\r\n                :class=\"{\r\n                  'clickable': selectedIp && selectedRowKeys.length > 0 && !isProcessing,\r\n                  'ready-to-start': selectedIp && selectedRowKeys.length > 0 && !isProcessing\r\n                }\"\r\n                @click=\"selectedIp && selectedRowKeys.length > 0 && !isProcessing && handleStart()\"\r\n                :style=\"{\r\n                  color: (selectedIp && selectedRowKeys.length > 0 && !isProcessing)\r\n                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\r\n                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\r\n                }\"\r\n              />\r\n            </a-tooltip>\r\n          </template>\r\n        </a-step>\r\n      </a-steps>\r\n    </div>\r\n\r\n    <!-- 节点选择区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\r\n      <node-selector\r\n        v-model=\"selectedRowKeys\"\r\n        :project-file=\"currentProject\"\r\n        :disabled=\"isProcessing\"\r\n        @input=\"onNodesSelected\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 代理配置区域 -->\r\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\r\n      <proxy-selector\r\n        v-model=\"selectedIp\"\r\n        :disabled=\"isProcessing\"\r\n        @change=\"handleProxyChange\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 任务状态 -->\r\n    <task-progress-card :task-type=\"'task'\" :is-processing=\"isProcessing\" />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\r\nimport NodeSelector from '@/components/common/NodeSelector.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin, TaskPollingMixin],\r\n  components: {\r\n    ProxySelector,\r\n    TaskProgressCard,\r\n    NodeSelector\r\n  },\r\n  data() {\r\n    return {\r\n      selectedRowKeys: [],\r\n      selectedIp: null,\r\n      currentStep: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['activeTask', 'currentProject', 'sidebarColor']),\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n\r\n    taskId: {\r\n      get() {\r\n        return this.activeTask?.task_id;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateTask', value ? { task_id: value } : null);\r\n      }\r\n    },\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n    currentStepComputed() {\r\n      if (this.isProcessing) {\r\n        return 1;  // 运行中时，只点亮前两个图标\r\n      }\r\n      if (this.selectedRowKeys.length === 0) {\r\n        return -1;  // 没有选择任何节点，所有图标不点亮\r\n      }\r\n      if (this.selectedRowKeys.length > 0 && !this.selectedIp) {\r\n        return 0;   // 选择了节点但未选择IP，点亮第一步图标和连接线\r\n      }\r\n      return 2;     // 选择了节点和IP，且未在运行时，点亮所有三个图标和连接线\r\n    },\r\n    getPlayIconTooltip() {\r\n      if (this.isProcessing) {\r\n        return 'Task is in progress...';\r\n      }\r\n      if (!this.selectedRowKeys.length) {\r\n        return 'Please select nodes first';\r\n      }\r\n      if (!this.selectedIp) {\r\n        return 'Please select a proxy IP';\r\n      }\r\n      return 'Click to start collection!'; // 当都选择完成时显示这个提示\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    // 只检查当前项目的活动任务\r\n    const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveTask();\r\n      } else {\r\n        // 清除任务信息如果属于不同项目\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 处理节点选择变化\r\n    onNodesSelected(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n      if (this.selectedRowKeys.length) {\r\n        this.currentStep = 1;\r\n      } else {\r\n        this.currentStep = 0;\r\n      }\r\n    },\r\n\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      this.selectedIp = ip;\r\n    },\r\n\r\n    async handleStart() {\r\n      if (!this.checkDatabaseStatus()) return;\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: 'No Nodes or Proxy Selected',\r\n          message: 'Please select one or more nodes and a reachable IP to collect the data.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isProcessing = true;\r\n      this.notificationSent = false; // 重置通知标志位\r\n\r\n      // 清除之前的任务通知记录\r\n      const previousTaskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n      if (previousTaskInfo) {\r\n        try {\r\n          const { taskId } = JSON.parse(previousTaskInfo);\r\n          if (taskId) {\r\n            this.clearTaskNotificationMark(taskId, 'task', this.currentProject);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error clearing previous task notification:', e);\r\n        }\r\n      }\r\n\r\n      try {\r\n        const { data } = await axios.post('/api/task/collect', {\r\n          targets: this.selectedRowKeys,\r\n          proxy_ip: this.selectedIp,\r\n          dbFile: this.currentProject\r\n        });\r\n\r\n        if (data && data.task_id) {\r\n          localStorage.setItem(`taskInfo_${this.currentProject}`, JSON.stringify({\r\n            taskId: data.task_id,\r\n            projectFile: this.currentProject\r\n          }));\r\n          localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n\r\n          this.taskId = data.task_id;\r\n          this.startPolling(data.task_id, 'task', 'task');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error starting task:', error);\r\n        this.$notify.error({\r\n          title: 'Task Start Failed',\r\n          message: error.message || 'Server connection error.',\r\n        });\r\n        this.isProcessing = false;\r\n      }\r\n    },\r\n\r\n    // 重写 checkActiveTask 方法，调用混入中的方法\r\n    async checkActiveTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`taskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/task/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, 'task', 'task');\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(`taskCompleted_${this.currentProject}`, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n\r\n\r\n    activated() {\r\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\r\n      this.checkActiveTask();\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听 currentProject 变化\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除之前项目的任务状态\r\n          this.$store.dispatch('updateTask', null);\r\n          this.stopPolling();\r\n          // 检查新项目的活动任务\r\n          this.checkActiveTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 基础卡片样式\r\n.task-card {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n// 步骤容器\r\n.steps-container {\r\n  width: 50%;\r\n  margin: 0 auto 24px;\r\n  padding: 12px 0;\r\n}\r\n\r\n// 深度选择器样式集中管理\r\n::v-deep {\r\n  .ant-card {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    .ant-card-head {\r\n      background: #f0f2f5;\r\n    }\r\n  }\r\n\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n  .ant-table-tbody > tr:last-child > td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  .steps-flow {\r\n    .ant-steps-item {\r\n      &-process,\r\n      &-finish {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #3b4149 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      &-wait {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #d9d9d9 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n      }\r\n\r\n      &-icon {\r\n        width: 88px;\r\n        height: 88px;\r\n        line-height: 80px;\r\n        padding: 4px;\r\n        font-size: 40px;\r\n        border-width: 2px;\r\n        margin-top: -20px;\r\n        color: #3b4149;\r\n\r\n        .step-icon {\r\n          font-size: 40px;\r\n          color: #3b4149;\r\n        }\r\n      }\r\n\r\n      &-tail::after {\r\n        height: 2px;\r\n      }\r\n\r\n      &:last-child {\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n\r\n        &.ant-steps-item-process,\r\n        &.ant-steps-item-finish {\r\n          .step-icon {\r\n            color: #3b4149 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ready-to-start {\r\n    animation: pulse 1.2s infinite;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n      opacity: 0.8;\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .clickable {\r\n    cursor: pointer;\r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}