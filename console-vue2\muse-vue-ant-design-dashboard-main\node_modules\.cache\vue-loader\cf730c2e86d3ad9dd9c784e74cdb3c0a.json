{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HostConfig.vue", "mtime": 1753187219715}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HostConfig.vue"], "names": [], "mappings": ";AAoJA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HostConfig.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card :bordered=\"false\" class=\"header-solid host-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('hostConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('hostConfig.addHost') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedHosts\"\r\n              >\r\n                {{ $t('hostConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"batchDelete\"\r\n              >\r\n                {{ $t('hostConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  icon=\"download\"\r\n                  class=\"nav-style-button\"\r\n                  @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('hostConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                name=\"file\"\r\n                :customRequest=\"handleUpload\"\r\n                :showUploadList=\"false\"\r\n              >\r\n                <a-button\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('hostConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"hosts\"\r\n        :rowKey=\"(record) => record.key\"\r\n        size=\"middle\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: hosts.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''\"\r\n              @click=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('common.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyNodeInfo(record)\">\r\n              <a-icon type=\"copy\" />\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteHost(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('hostConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式\r\n  },\r\n  data() {\r\n    return {\r\n      ...this.$data,\r\n      hosts: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'host_name',\r\n        'ip',\r\n        'ssh_port',\r\n        'login_user',\r\n        'login_pwd',\r\n        'switch_root_cmd',\r\n        'switch_root_pwd',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          scopedSlots: { customRender: 'host_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          scopedSlots: { customRender: 'ip' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.sshPort'),\r\n          dataIndex: 'ssh_port',\r\n          scopedSlots: { customRender: 'ssh_port' },\r\n          width: 100,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginUser'),\r\n          dataIndex: 'login_user',\r\n          scopedSlots: { customRender: 'login_user' },\r\n          width: 120,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginPassword'),\r\n          dataIndex: 'login_pwd',\r\n          scopedSlots: { customRender: 'login_pwd' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootCmd'),\r\n          dataIndex: 'switch_root_cmd',\r\n          scopedSlots: { customRender: 'switch_root_cmd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootPwd'),\r\n          dataIndex: 'switch_root_pwd',\r\n          scopedSlots: { customRender: 'switch_root_pwd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchHostConfig();\r\n  },\r\n  methods: {\r\n    copyNodeInfo(record) {\r\n      // 创建新的节点数据，复制原节点的所有属性\r\n      const newRecord = {\r\n        ...record,\r\n        key: `new-${Date.now()}`,\r\n        id: undefined,\r\n        editable: true,\r\n        isNew: true,\r\n        host_name: `${record.host_name || ''}_copy`,\r\n        ip: '' // Clear IP as it should be unique\r\n      };\r\n      \r\n      // 在表格开头添加新行\r\n      this.hosts = [newRecord, ...this.hosts];\r\n      this.currentPage = 1; // Reset to first page to show the new row\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n      \r\n      // 滚动到顶部以显示新添加的行\r\n      this.$nextTick(() => {\r\n        const tableBody = document.querySelector('.ant-table-body');\r\n        if (tableBody) {\r\n          tableBody.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    \r\n    getColumnTitle(dataIndex) {\r\n      return this.columns.find((c) => c.dataIndex === dataIndex)?.title || dataIndex;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n    edit(key) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n        target.editable = true;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      try {\r\n        const target = this.hosts.find((item) => item.key === key);\r\n        if (!target || !this.validateHost(target)) return;\r\n\r\n        this.saving = true;\r\n        const hostData = { ...target };\r\n        delete hostData.editable;\r\n        delete hostData.isNew;\r\n\r\n        await axios.post('/api/config/', {\r\n          hosts: [hostData],\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.map((item) =>\r\n          item.key === key ? { ...item, editable: false, isNew: false } : item\r\n        );\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n        this.$message.success('Saved successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to save host');\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.hosts.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.hosts[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.hosts = this.hosts.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.hosts];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.hosts = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.hosts = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          host_name: '',\r\n          ip: '',\r\n          ssh_port: '22',\r\n          login_user: '',\r\n          login_pwd: '',\r\n          switch_root_cmd: '',\r\n          switch_root_pwd: '',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.hosts,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    validateHost(host) {\r\n      if (!host.host_name?.trim()) {\r\n        this.$message.error('Host name is required');\r\n        return false;\r\n      }\r\n      if (!/^(\\d{1,3}\\.){3}\\d{1,3}$/.test(host.ip)) {\r\n        this.$message.error('Invalid IP format');\r\n        return false;\r\n      }\r\n      if (!/^\\d+$/.test(host.ssh_port)) {\r\n        this.$message.error('SSH port must be numeric');\r\n        return false;\r\n      }\r\n      const exist = this.hosts.some((h) => h.ip === host.ip && h.key !== host.key);\r\n      if (exist) {\r\n        this.$message.error('IP address already exists');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    async fetchHostConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/config`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.hosts = response.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `host_${item.host_name}`,\r\n          ssh_port: item.ssh_port?.toString() || '22',\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load hosts');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteHost(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/config/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete host');\r\n        await this.fetchHostConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async batchDelete() {\r\n      try {\r\n        const selectedIds = this.hosts\r\n          .filter(host => this.selectedRowKeys.includes(host.key))\r\n          .map(host => host.id)\r\n          .filter(id => id);\r\n\r\n        if (selectedIds.length === 0) {\r\n          this.$message.warning('No valid hosts selected for deletion');\r\n          return;\r\n        }\r\n\r\n        await axios.post('/api/config/batch-delete', {\r\n          ids: selectedIds,\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.filter(host => !this.selectedRowKeys.includes(host.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Batch deletion completed successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Batch deletion failed');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/config/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'hosts_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n        console.error('Download template error:', error);\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        await axios.post('/api/config/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        await this.fetchHostConfig();\r\n        this.$message.success('Hosts imported successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import hosts');\r\n      }\r\n    },\r\n\r\n    async exportSelectedHosts() {\r\n      try {\r\n        const selectedHosts = this.hosts.filter(host => this.selectedRowKeys.includes(host.key));\r\n\r\n        // Create CSV content\r\n        const headers = [\r\n          'host_name',\r\n          'ip',\r\n          'ssh_port',\r\n          'login_user',\r\n          'login_pwd',\r\n          'switch_root_cmd',\r\n          'switch_root_pwd'\r\n        ];\r\n\r\n        const csvContent = [\r\n          headers.join(','),\r\n          ...selectedHosts.map(host =>\r\n            headers.map(header => host[header] || '').join(',')\r\n          )\r\n        ].join('\\n');\r\n\r\n        // Create and trigger download\r\n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'selected_hosts.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Hosts exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export hosts');\r\n        console.error('Export hosts error:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.host-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 添加按钮组样式 */\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}