(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1a22db4a"],{"0dc8":function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e._self._c;return t("div",[e.currentProject?[t("div",{staticStyle:{"margin-bottom":"16px"}},[t("a-button",{class:"bg-"+e.sidebarColor,staticStyle:{"margin-right":"16px",color:"white"},attrs:{icon:"rollback",size:"small"},on:{click:e.goToProcessList}},[e._v(" Process ")]),t("a-button",{class:"bg-"+e.sidebarColor,staticStyle:{"margin-right":"16px",color:"white"},attrs:{icon:"rollback",size:"small"},on:{click:e.goBack}},[e._v(" Port ")])],1),t("a-card",{staticClass:"header-solid h-full",attrs:{bordered:!1,bodyStyle:{paddingTop:"16px",paddingBottom:"16px"}},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("a-icon",{class:"text-"+e.sidebarColor,staticStyle:{"font-size":"18px"},attrs:{type:"profile"}})],1),t("h5",{staticClass:"card-title"},[e._v("Process details for pid "+e._s(e.pid))])])])]},proxy:!0}],null,!1,2952082604)},[t("a-row",{attrs:{gutter:[24,24]}},[t("a-col",{attrs:{span:24}},[t("div",{staticClass:"process-details"},e._l(e.orderedFields,(function(s){var r;return t("div",{key:s,staticClass:"process-field"},[t("a-tooltip",{attrs:{placement:"right",title:e.fieldDescriptions[s+":"]}},[t("span",{staticClass:"field-label"},[e._v(e._s(s)+":")]),t("span",{staticClass:"field-value"},[e._v(e._s((null===(r=e.processDetails)||void 0===r?void 0:r[s])||"N/A"))])])],1)})),0)])],1)],1)]:[t("a-alert",{attrs:{message:"No Project Selected",description:"Please select a project first to view process details.",type:"warning","show-icon":""},scopedSlots:e._u([{key:"description",fn:function(){return[e._v(" Please select a project first to view process details. "),t("a-button",{attrs:{type:"link"},on:{click:e.goToProjects}},[e._v("Go to Projects")])]},proxy:!0}])})]],2)},i=[],o=s("2f62"),a=s("fec3"),c={data(){return{pid:this.$route.params.pid,processDetails:null,fromPage:this.$route.query.page||1,orderedFields:["pid","uid","gid","cmdline","state","exe","cwd","capability","environ","memory_maps"],fieldDescriptions:{"pid:":"进程唯一标识符 - 每个进程的唯一标识号","uid:":"用户标识符 - 启动该进程的用户的唯一标识符","gid:":"组标识符 - 启动该进程的用户所属组的唯一标识符","cmdline:":"命令行 - 启动进程时使用的完整命令行参数","state:":"进程状态 - 进程当前的运行状态","exe:":"可执行文件 - 获取指定进程的可执行文件路径","cwd:":"工作目录 - 进程当前工作的目录","capability:":"进程能力 - 与进程相关的能力信息，指示该进程具有哪些特权","environ:":"环境变量 - 与该进程相关联的环境变量列表","memory_maps:":"内存映射 - 该进程使用的内存区域的信息"}}},computed:{...Object(o["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{currentProject:{immediate:!0,handler(e){e&&this.fetchProcessDetails()}},selectedNodeIp:{immediate:!0,handler(e){e&&this.currentProject&&this.fetchProcessDetails()}}},methods:{async fetchProcessDetails(){if(this.selectedNodeIp&&this.currentProject)try{const e=await a["a"].get("/api/processes/"+this.selectedNodeIp,{params:{pid:this.pid,fields:this.orderedFields.join(","),dbFile:this.currentProject}});this.processDetails=e.data}catch(e){console.error("Error fetching process details:",e),this.$message.error("Failed to fetch process details")}else console.error("Node IP or project is not defined")},goBack(){this.$router.push({name:"Port",query:{page:this.fromPage}})},goToProcessList(){const e=this.$route.params.pid;this.$store.dispatch("processList/updateLastViewedPid",e.toString()),this.$router.push({name:"Process"})},goToProjects(){this.$router.push("/projects")}}},l=c,d=(s("3c21"),s("2877")),n=Object(d["a"])(l,r,i,!1,null,"18d06853",null);t["default"]=n.exports},1418:function(e,t,s){},"3c21":function(e,t,s){"use strict";s("1418")}}]);
//# sourceMappingURL=chunk-1a22db4a.bbd2dd14.js.map