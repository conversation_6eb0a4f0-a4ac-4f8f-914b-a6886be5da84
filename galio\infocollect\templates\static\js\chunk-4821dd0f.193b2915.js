(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4821dd0f"],{1184:function(t,e,a){"use strict";a("1a0a")},"1a0a":function(t,e,a){},9110:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",[e("a-row",{attrs:{type:"flex",gutter:24}},[e("a-col",{staticClass:"mb-24",attrs:{span:24}},[e("PortInfo")],1)],1)],1)},o=[],s=function(){var t=this,e=t._self._c;return e("a-card",{staticClass:"header-solid h-full port-card",attrs:{bordered:!1,bodyStyle:{padding:0},headStyle:{borderBottom:"1px solid #e8e8e8"}},scopedSlots:t._u([{key:"title",fn:function(){return[e("div",{staticClass:"card-header-wrapper"},[e("div",{staticClass:"header-wrapper"},[e("div",{staticClass:"logo-wrapper"},[e("svg",{class:"text-"+t.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",width:"20",height:"20"}},[e("path",{attrs:{fill:"currentColor",d:"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z"}})])]),e("h6",{staticClass:"font-semibold m-0"},[t._v(t._s(t.$t("headTopic.port")))])]),e("div",[e("RefreshButton",{on:{refresh:t.fetchPorts}})],1)])]},proxy:!0}])},[e("a-tabs",{on:{change:t.handleTabChange},model:{value:t.activeTabKey,callback:function(e){t.activeTabKey=e},expression:"activeTabKey"}},[e("a-tab-pane",{key:"tcp",attrs:{tab:"TCP"}},[e("a-table",{attrs:{columns:t.tcpColumns,"data-source":t.tcpPorts,rowKey:t=>`${t.ip}_${t.port}`,pagination:t.pagination.total>0&&t.pagination},scopedSlots:t._u([{key:"emptyText",fn:function(){return[e("a-empty",{attrs:{description:"No TCP ports found"}})]},proxy:!0}])})],1),e("a-tab-pane",{key:"udp",attrs:{tab:"UDP"}},[e("a-table",{attrs:{columns:t.udpColumns,"data-source":t.udpPorts,rowKey:t=>`${t.ip}_${t.port}`,pagination:t.udpPagination.total>0&&t.udpPagination},scopedSlots:t._u([{key:"emptyText",fn:function(){return[e("a-empty",{attrs:{description:"No UDP ports found"}})]},proxy:!0}])})],1),e("a-tab-pane",{key:"unix_socket",attrs:{tab:"UNIX Socket"}},[e("a-table",{attrs:{columns:t.unixSocketColumns,"data-source":t.unixSockets,rowKey:t=>t.inode,pagination:t.unixSocketPagination.total>0&&t.unixSocketPagination},scopedSlots:t._u([{key:"emptyText",fn:function(){return[e("a-empty",{attrs:{description:"No UNIX sockets found"}})]},proxy:!0}])})],1)],1),e("a-modal",{attrs:{title:t.modalTitle,width:"600px"},on:{cancel:t.handleModalClose},scopedSlots:t._u([{key:"footer",fn:function(){return[e("a-button",{on:{click:t.handleModalClose}},[t._v("Cancel")])]},proxy:!0}]),model:{value:t.modalVisible,callback:function(e){t.modalVisible=e},expression:"modalVisible"}},[e("div",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(t.modalContent.join("\n")))])])],1)},r=[],n=(a("0643"),a("fffc"),a("a573"),a("2f62")),l=a("fec3"),d=a("f188"),c={components:{RefreshButton:d["a"]},data(){const t=this.$createElement;return{tcpPorts:[],udpPorts:[],unixSockets:[],activeTabKey:"tcp",tcpColumns:[{title:"Address",key:"address",width:200,customRender:(t,e)=>`${e.ip}:${e.port}`},{title:"PID",dataIndex:"pid",key:"pid",width:200,customRender:(e,a)=>{if(!e)return"-";const[i,o]=e.split("/");return t("a",{on:{click:()=>this.navigateToProcessDetail(i)}},[e])}},{title:"Protocols",dataIndex:"protocols",key:"protocols",width:150,customRender:t=>{var e;return null!==t&&void 0!==t&&null!==(e=t.offered)&&void 0!==e&&e.length?t.offered.join(", "):"-"}},{title:"Certificate",dataIndex:"certificate",key:"certificate",width:800,customRender:e=>{var a;if(null===e||void 0===e||null===(a=e.summary)||void 0===a||!a.length)return"-";const i={"CN:":"通用名称 - 证书所标识的实体名称","Issuer:":"证书颁发者 - 签发此证书的证书机构","Subject Alt Names:":"主题备用名 - 证书可以保护的其他域名或IP","Chain Status:":"证书链状态 - 验证证书信任链的完整性","Revocation:":"吊销状态 - 检查证书是否被吊销","Validity Period:":"有效期长度 - 证书的有效时间跨度","Expiration Status:":"过期状态 - 证书是否已过期","Key Size:":"密钥大小 - 证书使用的加密密钥长度","Signature Algorithm:":"签名算法 - 用于签发证书的加密算法","Client Auth:":"客户端认证 - 是否支持客户端证书认证","Key Usage:":"密钥用途 - 证书允许的使用场景","Serial Number:":"序列号 - 证书的唯一标识符","Fingerprint SHA256:":"指纹 - 证书的SHA256哈希值","Valid Until:":"有效期至 - 证书的过期时间"};return t("div",[e.summary.map(e=>{const a=Object.keys(i).find(t=>e.startsWith(t)),[o,...s]=e.split(/(?<=:)\s/),r=s.join(" ");return t("a-tooltip",{key:e,attrs:{placement:"right",title:a?i[a]:""}},[t("div",{class:"cert-field"},[t("span",{class:"cert-label"},[o])," ",r])])})])}},{title:"HTTP Info",dataIndex:"http_info",key:"http_info",width:150,customRender:e=>{if(null===e||void 0===e||!e.raw_output)return"No response";const a=e.raw_output.match(/HTTP\/[\d.]+ (\d{3})/),i=a?a[1]:"Unknown";return t("a",{on:{click:()=>this.showDetailsModal("HTTP Response",["Status Code: "+i,"Protocol: "+e.protocol,"---","Raw Response:",e.raw_output])}},[i])}},{title:"Cipher Suites",dataIndex:"cipher_suites",key:"cipher_suites",width:150,customRender:e=>{var a;return null!==e&&void 0!==e&&null!==(a=e.details)&&void 0!==a&&a.length?t("a",{on:{click:()=>this.showDetailsModal("Cipher Suites",e.details)}},[e.details.length+" suites"]):"-"}},{title:"Vulnerabilities",dataIndex:"vulnerabilities",key:"vulnerabilities",width:150,customRender:e=>{var a;if(null===e||void 0===e||null===(a=e.critical)||void 0===a||!a.length)return"No vulnerabilities";const i=e.critical.map(t=>`${t.name} (${t.severity}): ${t.status}`);return t("a",{on:{click:()=>this.showDetailsModal("Vulnerabilities",i)}},[e.critical.length+" vulnerabilities"])}}],udpColumns:[{title:"Proto",dataIndex:"proto",key:"proto",width:80},{title:"Recv-Q",dataIndex:"recv_q",key:"recv_q",width:80},{title:"Send-Q",dataIndex:"send_q",key:"send_q",width:80},{title:"Local Address",key:"local_address",width:180,customRender:(t,e)=>`${e.ip}:${e.port}`},{title:"Foreign Address",dataIndex:"foreign_address",key:"foreign_address",width:180},{title:"State",dataIndex:"state",key:"state",width:100},{title:"PID/Program",dataIndex:"pid_program",key:"pid_program",width:300,customRender:e=>{if(!e)return"-";const a=e.split("/"),i=a[0];return t("a",{on:{click:()=>this.navigateToProcessDetail(i)}},[e])}}],unixSocketColumns:[{title:"Proto",dataIndex:"proto",key:"proto",width:80},{title:"RefCnt",dataIndex:"refcnt",key:"refcnt",width:80},{title:"Flags",dataIndex:"flags",key:"flags",width:100},{title:"Type",dataIndex:"type",key:"type",width:100},{title:"State",dataIndex:"state",key:"state",width:120},{title:"I-Node",dataIndex:"inode",key:"inode",width:100},{title:"PID/Program",dataIndex:"pid_program",key:"pid_program",width:180,customRender:e=>{if(!e)return"-";const a=e.split("/"),i=a[0];return t("a",{on:{click:()=>this.navigateToProcessDetail(i)}},[e])}},{title:"Path",dataIndex:"path",key:"path",width:400,customRender:e=>e?t("div",{style:"word-break: break-word;"},[e]):"-"}],pagination:{current:parseInt(this.$route.query.page)||1,pageSize:50,total:0,showSizeChanger:!1,showQuickJumper:!1,showTotal:(t,e)=>`${e[0]}-${e[1]} of ${t} items`,onChange:t=>{this.pagination.current=t,this.$router.replace({query:{...this.$route.query,page:t,port_type:"tcp"}}),this.fetchPorts("tcp")}},udpPagination:{current:parseInt(this.$route.query.page)||1,pageSize:50,total:0,showSizeChanger:!1,showQuickJumper:!1,showTotal:(t,e)=>`${e[0]}-${e[1]} of ${t} items`,onChange:t=>{this.udpPagination.current=t,this.$router.replace({query:{...this.$route.query,page:t,port_type:"udp"}}),this.fetchPorts("udp")}},unixSocketPagination:{current:parseInt(this.$route.query.page)||1,pageSize:50,total:0,showSizeChanger:!1,showQuickJumper:!1,showTotal:(t,e)=>`${e[0]}-${e[1]} of ${t} items`,onChange:t=>{this.unixSocketPagination.current=t,this.$router.replace({query:{...this.$route.query,page:t,port_type:"unix_socket"}}),this.fetchPorts("unix_socket")}},modalVisible:!1,modalTitle:"",modalContent:[]}},computed:{...Object(n["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(){this.fetchPorts("tcp"),this.fetchPorts("udp"),this.fetchPorts("unix_socket")},"$route.query.page":{handler(t){const e=this.$route.query.port_type||"tcp";t&&("tcp"===e&&parseInt(t)!==this.pagination.current?(this.pagination.current=parseInt(t),this.fetchPorts("tcp")):"udp"===e&&parseInt(t)!==this.udpPagination.current?(this.udpPagination.current=parseInt(t),this.fetchPorts("udp")):"unix_socket"===e&&parseInt(t)!==this.unixSocketPagination.current&&(this.unixSocketPagination.current=parseInt(t),this.fetchPorts("unix_socket")))},immediate:!0},"$route.query.port_type":{handler(t){t&&(this.activeTabKey=t)},immediate:!0}},mounted(){this.fetchPorts("tcp"),this.fetchPorts("udp"),this.fetchPorts("unix_socket")},methods:{handleTabChange(t){this.activeTabKey=t,this.$router.replace({query:{...this.$route.query,port_type:t}}),this.fetchPorts(t)},async fetchPorts(t="tcp"){if(!this.selectedNodeIp)return this.tcpPorts=[],this.udpPorts=[],this.unixSockets=[],this.pagination.total=0,this.udpPagination.total=0,void(this.unixSocketPagination.total=0);try{let e;"tcp"===t?e=this.pagination:"udp"===t?e=this.udpPagination:"unix_socket"===t&&(e=this.unixSocketPagination);const{current:a,pageSize:i}=e,o=await l["a"].get("/api/port/"+this.selectedNodeIp,{params:{page:a,page_size:i,port_type:t,dbFile:this.currentProject}});"tcp"===t?(this.tcpPorts=o.data.data||o.data,this.pagination.total=o.data.total||0):"udp"===t?(this.udpPorts=o.data.data||o.data,this.udpPagination.total=o.data.total||0):"unix_socket"===t&&(this.unixSockets=o.data.data||o.data,this.unixSocketPagination.total=o.data.total||0)}catch(e){console.error(`Error fetching ${t} ports:`,e),this.$message.error(`Failed to fetch ${t} ports data`),"tcp"===t?(this.tcpPorts=[],this.pagination.total=0):"udp"===t?(this.udpPorts=[],this.udpPagination.total=0):"unix_socket"===t&&(this.unixSockets=[],this.unixSocketPagination.total=0)}},navigateToProcessDetail(t){this.$router.push({name:"ProcessDetail",params:{pid:t},query:{page:this.pagination.current}})},showDetailsModal(t,e){this.modalTitle=t,this.modalContent=e,this.modalVisible=!0},handleModalClose(){this.modalVisible=!1,this.modalContent=[]}}},u=c,p=(a("1184"),a("2877")),h=Object(p["a"])(u,s,r,!1,null,"599999bc",null),f=h.exports,g={components:{PortInfo:f}},y=g,m=Object(p["a"])(y,i,o,!1,null,null,null);e["default"]=m.exports},f188:function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("a-button",{class:["refresh-button","text-"+t.sidebarColor],attrs:{icon:"reload"},on:{click:function(e){return t.$emit("refresh")}}},[t._v(" "+t._s(t.text||t.$t("common.refresh"))+" ")])},o=[],s=a("2f62"),r={computed:{...Object(s["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},n=r,l=a("2877"),d=Object(l["a"])(n,i,o,!1,null,"80cb1374",null);e["a"]=d.exports}}]);
//# sourceMappingURL=chunk-4821dd0f.193b2915.js.map