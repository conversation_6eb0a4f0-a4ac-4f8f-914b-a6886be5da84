{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue", "mtime": 1753187071945}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Icon", "axios", "mapState", "RepositoryDownloadResults", "CopyMixin", "cacheData", "components", "AIcon", "mixins", "computed", "data", "repositories", "saving", "loading", "currentPage", "pageSize", "editableColumns", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentDbFile", "localStorage", "getItem", "downloadTaskId", "downloadPolling", "downloadPollInterval", "columns", "title", "dataIndex", "width", "customRender", "text", "record", "index", "$t", "scopedSlots", "align", "created", "$message", "warning", "$router", "push", "fetchRepositoryConfig", "methods", "copyRepository", "new<PERSON>ey", "Date", "now", "newRecord", "key", "editable", "isNew", "microservice_name", "map", "item", "getColumnTitle", "col", "titleMap", "handleChange", "value", "column", "newData", "target", "find", "edit", "save", "repositoryData", "repository_url", "branch_name", "response", "post", "params", "dbFile", "success", "successful", "failed", "length", "errorMsg", "error", "_error$response", "cancel", "targetIndex", "findIndex", "filter", "cachedItem", "Object", "assign", "addNewRow", "get", "detail", "_item$id", "id", "toString", "_error$response2", "onPageChange", "page", "deleteRepository", "delete", "r", "_error$response3", "onSelectChange", "deleteSelectedRepositories", "selectedIds", "repo", "ids", "includes", "_error$response4", "exportSelectedRepositories", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "downloadTemplate", "handleUpload", "options", "file", "name", "endsWith", "formData", "FormData", "append", "headers", "failedMessages", "join", "$confirm", "content", "showCancelButton", "confirmButtonText", "type", "_error$response5", "downloadSelectedRepositories", "selectedRepositories", "downloadPath", "prompt", "trim", "requestData", "download_path", "destroy", "taskId", "task_id", "initialResults", "status", "reduce", "acc", "progress", "error_detail", "timestamp", "toLocaleString", "$store", "dispatch", "startDownloadPolling", "_error$response6", "message", "clearInterval", "pollDownloadStatus", "setInterval", "console", "log", "taskData", "stopDownloadPolling", "_error$response7", "checkActiveDownloadTask", "taskInfo", "projectFile", "JSON", "parse", "e", "removeItem", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/Cards/RepositoryConfig.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card :bordered=\"false\" class=\"header-solid repository-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('repositoryConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('repositoryConfig.addRepository') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"download\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"downloadSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"deleteSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                type=\"default\"\r\n                icon=\"download\"\r\n                class=\"nav-style-button\"\r\n                @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                :show-upload-list=\"false\"\r\n                :custom-request=\"handleUpload\"\r\n                accept=\".csv\"\r\n              >\r\n                <a-button\r\n                    type=\"default\"\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('repositoryConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"repositories\"\r\n        :rowKey=\"(record) => record.key\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: repositories.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"col === 'repository_url' && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"col === 'repository_url' && text ? 'cursor: pointer' : ''\"\r\n              @click=\"col === 'repository_url' && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyRepository(record)\">\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteRepository(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n\r\n  <!-- 下载结果显示 -->\r\n  <repository-download-results />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport RepositoryDownloadResults from './RepositoryDownloadResults.vue';\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n    RepositoryDownloadResults,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n  },\r\n  data() {\r\n    return {\r\n      repositories: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'microservice_name',\r\n        'repository_url',\r\n        'branch_name',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      downloadTaskId: null,\r\n      downloadPolling: false,\r\n      downloadPollInterval: null,\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.microservice'),\r\n          dataIndex: 'microservice_name',\r\n          scopedSlots: { customRender: 'microservice_name' },\r\n          width: 200,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.repositoryUrl'),\r\n          dataIndex: 'repository_url',\r\n          scopedSlots: { customRender: 'repository_url' },\r\n          width: 300,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.branchName'),\r\n          dataIndex: 'branch_name',\r\n          scopedSlots: { customRender: 'branch_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchRepositoryConfig();\r\n  },\r\n  methods: {\r\n    copyRepository(record) {\r\n      const newKey = `new-${Date.now()}`;\r\n      const newRecord = {\r\n        ...record,\r\n        key: newKey,\r\n        editable: true,\r\n        isNew: true,\r\n        microservice_name: `${record.microservice_name}_copy`,\r\n      };\r\n      \r\n      this.repositories = [newRecord, ...this.repositories];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    getColumnTitle(col) {\r\n      const titleMap = {\r\n        'microservice_name': this.$t('repositoryConfig.columns.microservice'),\r\n        'repository_url': this.$t('repositoryConfig.columns.repositoryUrl'),\r\n        'branch_name': this.$t('repositoryConfig.columns.branchName'),\r\n      };\r\n      return titleMap[col] || col;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    edit(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target.editable = true;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        delete target.editable;\r\n        this.repositories = newData;\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n\r\n        try {\r\n          this.saving = true;\r\n          const repositoryData = {\r\n            microservice_name: target.microservice_name,\r\n            repository_url: target.repository_url,\r\n            branch_name: target.branch_name,\r\n          };\r\n\r\n          const response = await axios.post('/api/repositories', {\r\n            repositories: [repositoryData]\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          if (response.data.success) {\r\n            const { successful, failed } = response.data.data;\r\n\r\n            if (failed.length > 0) {\r\n              // 显示验证错误\r\n              const errorMsg = failed[0].error;\r\n              this.$message.error(`${this.$t('repositoryConfig.validation.parseError')}: ${errorMsg}`);\r\n              return;\r\n            }\r\n\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success('Repository saved successfully');\r\n          } else {\r\n            this.$message.error(response.data.error || 'Failed to save repository');\r\n          }\r\n        } catch (error) {\r\n          this.$message.error(error.response?.data?.error || 'Failed to save repository');\r\n          await this.fetchRepositoryConfig();\r\n        } finally {\r\n          this.saving = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.repositories.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.repositories[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.repositories = this.repositories.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.repositories];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.repositories = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.repositories = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          microservice_name: '',\r\n          repository_url: '',\r\n          branch_name: 'main',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.repositories,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    async fetchRepositoryConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/repositories`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.repositories = response.data.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `repo_${item.microservice_name}`,\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.repositories.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load repositories');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteRepository(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/repositories/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repository');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async deleteSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to delete');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        if (selectedIds.length > 0) {\r\n          await axios.post('/api/repositories/batch-delete', {\r\n            ids: selectedIds\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n        }\r\n\r\n        this.repositories = this.repositories.filter(r => !this.selectedRowKeys.includes(r.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Selected repositories deleted successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repositories');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    async exportSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to export');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        const response = await axios.post('/api/repositories/export', {\r\n          ids: selectedIds\r\n        }, {\r\n          params: { dbFile: this.currentDbFile },\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repositories_export.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Repositories exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export repositories');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/repositories/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repository_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        const response = await axios.post('/api/repositories/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const { successful, failed } = response.data.data;\r\n\r\n          if (failed.length > 0) {\r\n            // 显示验证失败的代码仓\r\n            const failedMessages = failed.map(repo =>\r\n              `${repo.microservice_name || 'Unknown'}: ${repo.error}`\r\n            ).join('\\n');\r\n\r\n            this.$confirm({\r\n              title: this.$t('repositoryConfig.validation.parseError'),\r\n              content: failedMessages,\r\n              showCancelButton: false,\r\n              confirmButtonText: 'OK',\r\n              type: 'warning'\r\n            });\r\n          }\r\n\r\n          if (successful.length > 0) {\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success(`Successfully imported ${successful.length} repositories`);\r\n          }\r\n\r\n          if (failed.length > 0 && successful.length === 0) {\r\n            this.$message.error('No valid repositories found in the file');\r\n          }\r\n        } else {\r\n          this.$message.error(response.data.error || 'Failed to import repositories');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import repositories');\r\n      }\r\n    },\r\n\r\n    async downloadSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to download');\r\n        return;\r\n      }\r\n\r\n      // 获取选中的代码仓\r\n      const selectedRepositories = this.selectedRowKeys\r\n        .map(key => this.repositories.find(r => r.key === key))\r\n        .filter(repo => repo && repo.id);\r\n\r\n      if (selectedRepositories.length === 0) {\r\n        this.$message.warning('No valid repositories selected');\r\n        return;\r\n      }\r\n\r\n      // 弹出输入框让用户输入下载路径\r\n      const downloadPath = prompt(this.$t('repositoryConfig.download.selectPath'), 'D:\\\\downloads');\r\n\r\n      if (!downloadPath || !downloadPath.trim()) {\r\n        return;\r\n      }\r\n\r\n      // 显示开始下载的消息\r\n      this.$message.loading(this.$t('repositoryConfig.download.starting'), 0);\r\n\r\n      try {\r\n        const requestData = {\r\n          repositories: selectedRepositories.map(repo => ({\r\n            id: repo.id,\r\n            microservice_name: repo.microservice_name,\r\n            repository_url: repo.repository_url,\r\n            branch_name: repo.branch_name\r\n          })),\r\n          download_path: downloadPath.trim()\r\n        };\r\n\r\n        const response = await axios.post('/api/repositories/download', requestData, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        this.$message.destroy();\r\n\r\n        if (response.data.success) {\r\n          const taskId = response.data.data.task_id;\r\n          this.downloadTaskId = taskId;\r\n\r\n          // 初始化下载结果状态 - 显示正在进行的状态\r\n          const initialResults = {\r\n            task_id: taskId,\r\n            status: 'running',\r\n            successful: [],\r\n            failed: [],\r\n            repositories: selectedRepositories.reduce((acc, repo) => {\r\n              const key = `${repo.microservice_name}_${repo.repository_url}`;\r\n              acc[key] = {\r\n                microservice_name: repo.microservice_name,\r\n                repository_url: repo.repository_url,\r\n                branch_name: repo.branch_name,\r\n                status: 'pending',\r\n                progress: 0,\r\n                error_detail: null,\r\n                download_path: null\r\n              };\r\n              return acc;\r\n            }, {}),\r\n            timestamp: new Date().toLocaleString()\r\n          };\r\n\r\n          // 存储初始状态到store\r\n          this.$store.dispatch('updateRepositoryDownloadResults', initialResults);\r\n\r\n          this.$message.success('Repository download task started successfully');\r\n\r\n          // 开始轮询任务状态\r\n          this.startDownloadPolling(taskId);\r\n        } else {\r\n          this.$message.error(response.data.error || this.$t('repositoryConfig.download.failed'));\r\n        }\r\n      } catch (error) {\r\n        this.$message.destroy();\r\n        this.$message.error(error.response?.data?.error || error.message || this.$t('repositoryConfig.download.failed'));\r\n      }\r\n    },\r\n\r\n    startDownloadPolling(taskId) {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n      }\r\n\r\n      this.downloadPolling = true;\r\n\r\n      // 立即执行一次查询\r\n      this.pollDownloadStatus(taskId);\r\n\r\n      // 每5秒轮询一次\r\n      this.downloadPollInterval = setInterval(() => {\r\n        this.pollDownloadStatus(taskId);\r\n      }, 5000);\r\n\r\n      console.log(`开始轮询代码仓下载任务 ${taskId}，轮询间隔: 5秒`);\r\n    },\r\n\r\n    async pollDownloadStatus(taskId) {\r\n      try {\r\n        const response = await axios.get(`/api/repositories/download/status/${taskId}`, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const taskData = response.data.data;\r\n\r\n          // 更新store中的下载结果\r\n          this.$store.dispatch('updateRepositoryDownloadResults', {\r\n            ...taskData,\r\n            timestamp: new Date().toLocaleString()\r\n          });\r\n\r\n          // 检查任务是否完成\r\n          if (taskData.status === 'success' || taskData.status === 'failed' || taskData.status === 'partial_success') {\r\n            this.stopDownloadPolling();\r\n\r\n            // 显示完成消息\r\n            if (taskData.status === 'success') {\r\n              this.$message.success(this.$t('repositoryConfig.download.success'));\r\n            } else if (taskData.status === 'failed') {\r\n              this.$message.error(this.$t('repositoryConfig.download.failed'));\r\n            } else {\r\n              this.$message.warning(this.$t('repositoryConfig.download.partialSuccess'));\r\n            }\r\n\r\n            console.log('代码仓下载任务完成，停止轮询');\r\n          }\r\n        } else {\r\n          console.error('获取下载状态失败:', response.data.error);\r\n        }\r\n      } catch (error) {\r\n        console.error('轮询下载状态出错:', error);\r\n        \r\n        // 如果是404错误，可能任务不存在，停止轮询\r\n        if (error.response?.status === 404) {\r\n          this.stopDownloadPolling();\r\n          this.$message.error('Download task not found');\r\n        }\r\n      }\r\n    },\r\n\r\n    stopDownloadPolling() {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n        this.downloadPollInterval = null;\r\n      }\r\n      this.downloadPolling = false;\r\n      this.downloadTaskId = null;\r\n    },\r\n\r\n    checkActiveDownloadTask() {\r\n      // 检查是否有活跃的下载任务\r\n      const taskInfo = localStorage.getItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n      if (taskInfo) {\r\n        try {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n          if (projectFile === this.currentDbFile) {\r\n            this.downloadTaskId = taskId;\r\n            this.startDownloadPolling(taskId);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing repository download task info:', e);\r\n          localStorage.removeItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n        }\r\n      }\r\n    },\r\n\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRepositoryConfig();\r\n    // 检查是否有活跃的下载任务\r\n    this.checkActiveDownloadTask();\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 组件销毁前停止轮询\r\n    this.stopDownloadPolling();\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.repository-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAkKA;AACA,SAAAA,IAAA;AACA,OAAAC,KAAA;AACA,SAAAC,QAAA;AACA,OAAAC,yBAAA;AACA,OAAAC,SAAA;AAEA,IAAAC,SAAA;AAEA;EACAC,UAAA;IACAC,KAAA,EAAAP,IAAA;IACAG;EACA;EACAK,MAAA,GAAAJ,SAAA;EACAK,QAAA,GACA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,MAAA;MACAC,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,eAAA,GACA,qBACA,kBACA,cACA;MACAC,eAAA;MACAC,aAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,cAAA;MACAC,eAAA;MACAC,oBAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,IAAA,EAAAC,MAAA,EAAAC,KAAA;UACA,aAAAjB,WAAA,aAAAC,QAAA,GAAAgB,KAAA;QACA;MACA,GACA;QACAN,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA,OAAAO,EAAA;QACAN,SAAA;QACAO,WAAA;UAAAL,YAAA;QAAA;QACAD,KAAA;QACAO,KAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,UAAAjB,aAAA;MACA,KAAAkB,QAAA,CAAAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;MACA;IACA;IACA,KAAAC,qBAAA;EACA;EACAC,OAAA;IACAC,eAAAZ,MAAA;MACA,MAAAa,MAAA,UAAAC,IAAA,CAAAC,GAAA;MACA,MAAAC,SAAA;QACA,GAAAhB,MAAA;QACAiB,GAAA,EAAAJ,MAAA;QACAK,QAAA;QACAC,KAAA;QACAC,iBAAA,KAAApB,MAAA,CAAAoB,iBAAA;MACA;MAEA,KAAAvC,YAAA,IAAAmC,SAAA,UAAAnC,YAAA;MACA,KAAAG,WAAA;MACAT,SAAA,QAAAM,YAAA,CAAAwC,GAAA,CAAAC,IAAA;QAAA,GAAAA;MAAA;MACA,KAAAnC,eAAA;IACA;IAEAoC,eAAAC,GAAA;MACA,MAAAC,QAAA;QACA,0BAAAvB,EAAA;QACA,uBAAAA,EAAA;QACA,oBAAAA,EAAA;MACA;MACA,OAAAuB,QAAA,CAAAD,GAAA,KAAAA,GAAA;IACA;IAEAE,aAAAC,KAAA,EAAAV,GAAA,EAAAW,MAAA;MACA,MAAAC,OAAA,YAAAhD,YAAA;MACA,MAAAiD,MAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAT,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;MACA,IAAAa,MAAA;QACAA,MAAA,CAAAF,MAAA,IAAAD,KAAA;QACA,KAAA9C,YAAA,GAAAgD,OAAA;MACA;IACA;IAEAG,KAAAf,GAAA;MACA,MAAAY,OAAA,YAAAhD,YAAA;MACA,MAAAiD,MAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAT,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;MACA,IAAAa,MAAA;QACAA,MAAA,CAAAZ,QAAA;QACA,KAAArC,YAAA,GAAAgD,OAAA;MACA;IACA;IAEA,MAAAI,KAAAhB,GAAA;MACA,MAAAY,OAAA,YAAAhD,YAAA;MACA,MAAAiD,MAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAT,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;MACA,IAAAa,MAAA;QACA,OAAAA,MAAA,CAAAZ,QAAA;QACA,KAAArC,YAAA,GAAAgD,OAAA;QACAtD,SAAA,GAAAsD,OAAA,CAAAR,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;QAEA;UACA,KAAAxC,MAAA;UACA,MAAAoD,cAAA;YACAd,iBAAA,EAAAU,MAAA,CAAAV,iBAAA;YACAe,cAAA,EAAAL,MAAA,CAAAK,cAAA;YACAC,WAAA,EAAAN,MAAA,CAAAM;UACA;UAEA,MAAAC,QAAA,SAAAlE,KAAA,CAAAmE,IAAA;YACAzD,YAAA,GAAAqD,cAAA;UACA;YACAK,MAAA;cAAAC,MAAA,OAAApD;YAAA;UACA;UAEA,IAAAiD,QAAA,CAAAzD,IAAA,CAAA6D,OAAA;YACA;cAAAC,UAAA;cAAAC;YAAA,IAAAN,QAAA,CAAAzD,IAAA,CAAAA,IAAA;YAEA,IAAA+D,MAAA,CAAAC,MAAA;cACA;cACA,MAAAC,QAAA,GAAAF,MAAA,IAAAG,KAAA;cACA,KAAAxC,QAAA,CAAAwC,KAAA,SAAA5C,EAAA,+CAAA2C,QAAA;cACA;YACA;YAEA,WAAAnC,qBAAA;YACA,KAAAJ,QAAA,CAAAmC,OAAA;UACA;YACA,KAAAnC,QAAA,CAAAwC,KAAA,CAAAT,QAAA,CAAAzD,IAAA,CAAAkE,KAAA;UACA;QACA,SAAAA,KAAA;UAAA,IAAAC,eAAA;UACA,KAAAzC,QAAA,CAAAwC,KAAA,GAAAC,eAAA,GAAAD,KAAA,CAAAT,QAAA,cAAAU,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAAnE,IAAA,cAAAmE,eAAA,uBAAAA,eAAA,CAAAD,KAAA;UACA,WAAApC,qBAAA;QACA;UACA,KAAA5B,MAAA;QACA;MACA;IACA;IAEAkE,OAAA/B,GAAA;MACA,MAAAgC,WAAA,QAAApE,YAAA,CAAAqE,SAAA,CAAA5B,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;MACA,IAAAgC,WAAA;MAEA,MAAAnB,MAAA,QAAAjD,YAAA,CAAAoE,WAAA;MAEA,IAAAnB,MAAA,CAAAX,KAAA;QACA;QACA,KAAAtC,YAAA,QAAAA,YAAA,CAAAsE,MAAA,CAAA7B,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;MACA;QACA;QACA,MAAAY,OAAA,YAAAhD,YAAA;QACA,MAAAuE,UAAA,GAAA7E,SAAA,CAAAwD,IAAA,CAAAT,IAAA,IAAAA,IAAA,CAAAL,GAAA,KAAAA,GAAA;QACA,IAAAmC,UAAA;UACAC,MAAA,CAAAC,MAAA,CAAAxB,MAAA;YAAA,GAAAsB;UAAA;UACA,OAAAtB,MAAA,CAAAZ,QAAA;UACA,KAAArC,YAAA,GAAAgD,OAAA;QACA;MACA;IACA;IAEA0B,UAAA;MACA,KAAA1E,YAAA,IACA;QACAoC,GAAA,SAAAH,IAAA,CAAAC,GAAA;QACAK,iBAAA;QACAe,cAAA;QACAC,WAAA;QACAlB,QAAA;QACAC,KAAA;MACA,GACA,QAAAtC,YAAA,CACA;MACA,KAAAG,WAAA;MACAT,SAAA,QAAAM,YAAA,CAAAwC,GAAA,CAAAC,IAAA;QAAA,GAAAA;MAAA;MACA,KAAAnC,eAAA;IACA;IAEA,MAAAuB,sBAAA;MACA;QACA,KAAA3B,OAAA;QACA,MAAAsD,QAAA,SAAAlE,KAAA,CAAAqF,GAAA;UACAjB,MAAA;YACAkB,MAAA;YACAjB,MAAA,OAAApD;UACA;QACA;QACA,KAAAP,YAAA,GAAAwD,QAAA,CAAAzD,IAAA,CAAAA,IAAA,CAAAyC,GAAA,CAAAC,IAAA;UAAA,IAAAoC,QAAA;UAAA;YACA,GAAApC,IAAA;YACAL,GAAA,IAAAyC,QAAA,GAAApC,IAAA,CAAAqC,EAAA,cAAAD,QAAA,uBAAAA,QAAA,CAAAE,QAAA,eAAAtC,IAAA,CAAAF,iBAAA;YACAD,KAAA;UACA;QAAA;QACA5C,SAAA,QAAAM,YAAA,CAAAwC,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;MACA,SAAAwB,KAAA;QAAA,IAAAe,gBAAA;QACA,KAAAvD,QAAA,CAAAwC,KAAA,GAAAe,gBAAA,GAAAf,KAAA,CAAAT,QAAA,cAAAwB,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAjF,IAAA,cAAAiF,gBAAA,uBAAAA,gBAAA,CAAAf,KAAA;MACA;QACA,KAAA/D,OAAA;MACA;IACA;IAEA+E,aAAAC,IAAA;MACA,KAAA/E,WAAA,GAAA+E,IAAA;IACA;IAEA,MAAAC,iBAAAhE,MAAA;MACA;QACA,IAAAA,MAAA,CAAA2D,EAAA;UACA,MAAAxF,KAAA,CAAA8F,MAAA,sBAAAjE,MAAA,CAAA2D,EAAA;YACApB,MAAA;cAAAC,MAAA,OAAApD;YAAA;UACA;UAEA,KAAAP,YAAA,QAAAA,YAAA,CAAAsE,MAAA,CAAAe,CAAA,IAAAA,CAAA,CAAAjD,GAAA,KAAAjB,MAAA,CAAAiB,GAAA;UACA,KAAA9B,eAAA,QAAAA,eAAA,CAAAgE,MAAA,CAAAlC,GAAA,IAAAA,GAAA,KAAAjB,MAAA,CAAAiB,GAAA;UACA,KAAAX,QAAA,CAAAmC,OAAA;QACA;UACA,KAAA5D,YAAA,QAAAA,YAAA,CAAAsE,MAAA,CAAAe,CAAA,IAAAA,CAAA,CAAAjD,GAAA,KAAAjB,MAAA,CAAAiB,GAAA;UACA,KAAA9B,eAAA,QAAAA,eAAA,CAAAgE,MAAA,CAAAlC,GAAA,IAAAA,GAAA,KAAAjB,MAAA,CAAAiB,GAAA;QACA;MACA,SAAA6B,KAAA;QAAA,IAAAqB,gBAAA;QACA,KAAA7D,QAAA,CAAAwC,KAAA,GAAAqB,gBAAA,GAAArB,KAAA,CAAAT,QAAA,cAAA8B,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAvF,IAAA,cAAAuF,gBAAA,uBAAAA,gBAAA,CAAArB,KAAA;QACA,WAAApC,qBAAA;MACA;IACA;IAEA0D,eAAAjF,eAAA;MACA,KAAAA,eAAA,GAAAA,eAAA;IACA;IAEA,MAAAkF,2BAAA;MACA,SAAAlF,eAAA,CAAAyD,MAAA;QACA,KAAAtC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA;QACA,MAAA+D,WAAA,QAAAnF,eAAA,CACAkC,GAAA,CAAAJ,GAAA,SAAApC,YAAA,CAAAkD,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAjD,GAAA,KAAAA,GAAA,GACAkC,MAAA,CAAAoB,IAAA,IAAAA,IAAA,IAAAA,IAAA,CAAAZ,EAAA,EACAtC,GAAA,CAAAkD,IAAA,IAAAA,IAAA,CAAAZ,EAAA;QAEA,IAAAW,WAAA,CAAA1B,MAAA;UACA,MAAAzE,KAAA,CAAAmE,IAAA;YACAkC,GAAA,EAAAF;UACA;YACA/B,MAAA;cAAAC,MAAA,OAAApD;YAAA;UACA;QACA;QAEA,KAAAP,YAAA,QAAAA,YAAA,CAAAsE,MAAA,CAAAe,CAAA,UAAA/E,eAAA,CAAAsF,QAAA,CAAAP,CAAA,CAAAjD,GAAA;QACA,KAAA9B,eAAA;QACA,KAAAmB,QAAA,CAAAmC,OAAA;MACA,SAAAK,KAAA;QAAA,IAAA4B,gBAAA;QACA,KAAApE,QAAA,CAAAwC,KAAA,GAAA4B,gBAAA,GAAA5B,KAAA,CAAAT,QAAA,cAAAqC,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAA9F,IAAA,cAAA8F,gBAAA,uBAAAA,gBAAA,CAAA5B,KAAA;QACA,WAAApC,qBAAA;MACA;IACA;IAEA,MAAAiE,2BAAA;MACA,SAAAxF,eAAA,CAAAyD,MAAA;QACA,KAAAtC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA;QACA,MAAA+D,WAAA,QAAAnF,eAAA,CACAkC,GAAA,CAAAJ,GAAA,SAAApC,YAAA,CAAAkD,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAjD,GAAA,KAAAA,GAAA,GACAkC,MAAA,CAAAoB,IAAA,IAAAA,IAAA,IAAAA,IAAA,CAAAZ,EAAA,EACAtC,GAAA,CAAAkD,IAAA,IAAAA,IAAA,CAAAZ,EAAA;QAEA,MAAAtB,QAAA,SAAAlE,KAAA,CAAAmE,IAAA;UACAkC,GAAA,EAAAF;QACA;UACA/B,MAAA;YAAAC,MAAA,OAAApD;UAAA;UACAwF,YAAA;QACA;QAEA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,KAAAC,IAAA,EAAA5C,QAAA,CAAAzD,IAAA;QACA,MAAAsG,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAR,GAAA;QACAK,IAAA,CAAAI,YAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;QACAA,IAAA,CAAAO,KAAA;QACAP,IAAA,CAAAQ,MAAA;QACAZ,MAAA,CAAAC,GAAA,CAAAY,eAAA,CAAAd,GAAA;QAEA,KAAAvE,QAAA,CAAAmC,OAAA;MACA,SAAAK,KAAA;QACA,KAAAxC,QAAA,CAAAwC,KAAA;MACA;IACA;IAEA,MAAA8C,iBAAA;MACA;QACA,MAAAvD,QAAA,SAAAlE,KAAA,CAAAqF,GAAA;UACAoB,YAAA;QACA;QAEA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,KAAAC,IAAA,EAAA5C,QAAA,CAAAzD,IAAA;QACA,MAAAsG,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAR,GAAA;QACAK,IAAA,CAAAI,YAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;QACAA,IAAA,CAAAO,KAAA;QACAP,IAAA,CAAAQ,MAAA;QACAZ,MAAA,CAAAC,GAAA,CAAAY,eAAA,CAAAd,GAAA;QAEA,KAAAvE,QAAA,CAAAmC,OAAA;MACA,SAAAK,KAAA;QACA,KAAAxC,QAAA,CAAAwC,KAAA;MACA;IACA;IAEA,MAAA+C,aAAAC,OAAA;MACA;QAAAC;MAAA,IAAAD,OAAA;MAEA,KAAAC,IAAA,CAAAC,IAAA,CAAAC,QAAA;QACA,KAAA3F,QAAA,CAAAwC,KAAA;QACA;MACA;MAEA;QACA,MAAAoD,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,SAAAL,IAAA;QACAG,QAAA,CAAAE,MAAA,gBAAAhH,aAAA;QAEA,MAAAiD,QAAA,SAAAlE,KAAA,CAAAmE,IAAA,6BAAA4D,QAAA;UACAG,OAAA;YAAA;UAAA;QACA;QAEA,IAAAhE,QAAA,CAAAzD,IAAA,CAAA6D,OAAA;UACA;YAAAC,UAAA;YAAAC;UAAA,IAAAN,QAAA,CAAAzD,IAAA,CAAAA,IAAA;UAEA,IAAA+D,MAAA,CAAAC,MAAA;YACA;YACA,MAAA0D,cAAA,GAAA3D,MAAA,CAAAtB,GAAA,CAAAkD,IAAA,IACA,GAAAA,IAAA,CAAAnD,iBAAA,kBAAAmD,IAAA,CAAAzB,KAAA,EACA,EAAAyD,IAAA;YAEA,KAAAC,QAAA;cACA7G,KAAA,OAAAO,EAAA;cACAuG,OAAA,EAAAH,cAAA;cACAI,gBAAA;cACAC,iBAAA;cACAC,IAAA;YACA;UACA;UAEA,IAAAlE,UAAA,CAAAE,MAAA;YACA,WAAAlC,qBAAA;YACA,KAAAJ,QAAA,CAAAmC,OAAA,0BAAAC,UAAA,CAAAE,MAAA;UACA;UAEA,IAAAD,MAAA,CAAAC,MAAA,QAAAF,UAAA,CAAAE,MAAA;YACA,KAAAtC,QAAA,CAAAwC,KAAA;UACA;QACA;UACA,KAAAxC,QAAA,CAAAwC,KAAA,CAAAT,QAAA,CAAAzD,IAAA,CAAAkE,KAAA;QACA;MACA,SAAAA,KAAA;QAAA,IAAA+D,gBAAA;QACA,KAAAvG,QAAA,CAAAwC,KAAA,GAAA+D,gBAAA,GAAA/D,KAAA,CAAAT,QAAA,cAAAwE,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAjI,IAAA,cAAAiI,gBAAA,uBAAAA,gBAAA,CAAA/D,KAAA;MACA;IACA;IAEA,MAAAgE,6BAAA;MACA,SAAA3H,eAAA,CAAAyD,MAAA;QACA,KAAAtC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAwG,oBAAA,QAAA5H,eAAA,CACAkC,GAAA,CAAAJ,GAAA,SAAApC,YAAA,CAAAkD,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAjD,GAAA,KAAAA,GAAA,GACAkC,MAAA,CAAAoB,IAAA,IAAAA,IAAA,IAAAA,IAAA,CAAAZ,EAAA;MAEA,IAAAoD,oBAAA,CAAAnE,MAAA;QACA,KAAAtC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAyG,YAAA,GAAAC,MAAA,MAAA/G,EAAA;MAEA,KAAA8G,YAAA,KAAAA,YAAA,CAAAE,IAAA;QACA;MACA;;MAEA;MACA,KAAA5G,QAAA,CAAAvB,OAAA,MAAAmB,EAAA;MAEA;QACA,MAAAiH,WAAA;UACAtI,YAAA,EAAAkI,oBAAA,CAAA1F,GAAA,CAAAkD,IAAA;YACAZ,EAAA,EAAAY,IAAA,CAAAZ,EAAA;YACAvC,iBAAA,EAAAmD,IAAA,CAAAnD,iBAAA;YACAe,cAAA,EAAAoC,IAAA,CAAApC,cAAA;YACAC,WAAA,EAAAmC,IAAA,CAAAnC;UACA;UACAgF,aAAA,EAAAJ,YAAA,CAAAE,IAAA;QACA;QAEA,MAAA7E,QAAA,SAAAlE,KAAA,CAAAmE,IAAA,+BAAA6E,WAAA;UACA5E,MAAA;YAAAC,MAAA,OAAApD;UAAA;QACA;QAEA,KAAAkB,QAAA,CAAA+G,OAAA;QAEA,IAAAhF,QAAA,CAAAzD,IAAA,CAAA6D,OAAA;UACA,MAAA6E,MAAA,GAAAjF,QAAA,CAAAzD,IAAA,CAAAA,IAAA,CAAA2I,OAAA;UACA,KAAAhI,cAAA,GAAA+H,MAAA;;UAEA;UACA,MAAAE,cAAA;YACAD,OAAA,EAAAD,MAAA;YACAG,MAAA;YACA/E,UAAA;YACAC,MAAA;YACA9D,YAAA,EAAAkI,oBAAA,CAAAW,MAAA,EAAAC,GAAA,EAAApD,IAAA;cACA,MAAAtD,GAAA,MAAAsD,IAAA,CAAAnD,iBAAA,IAAAmD,IAAA,CAAApC,cAAA;cACAwF,GAAA,CAAA1G,GAAA;gBACAG,iBAAA,EAAAmD,IAAA,CAAAnD,iBAAA;gBACAe,cAAA,EAAAoC,IAAA,CAAApC,cAAA;gBACAC,WAAA,EAAAmC,IAAA,CAAAnC,WAAA;gBACAqF,MAAA;gBACAG,QAAA;gBACAC,YAAA;gBACAT,aAAA;cACA;cACA,OAAAO,GAAA;YACA;YACAG,SAAA,MAAAhH,IAAA,GAAAiH,cAAA;UACA;;UAEA;UACA,KAAAC,MAAA,CAAAC,QAAA,oCAAAT,cAAA;UAEA,KAAAlH,QAAA,CAAAmC,OAAA;;UAEA;UACA,KAAAyF,oBAAA,CAAAZ,MAAA;QACA;UACA,KAAAhH,QAAA,CAAAwC,KAAA,CAAAT,QAAA,CAAAzD,IAAA,CAAAkE,KAAA,SAAA5C,EAAA;QACA;MACA,SAAA4C,KAAA;QAAA,IAAAqF,gBAAA;QACA,KAAA7H,QAAA,CAAA+G,OAAA;QACA,KAAA/G,QAAA,CAAAwC,KAAA,GAAAqF,gBAAA,GAAArF,KAAA,CAAAT,QAAA,cAAA8F,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAvJ,IAAA,cAAAuJ,gBAAA,uBAAAA,gBAAA,CAAArF,KAAA,KAAAA,KAAA,CAAAsF,OAAA,SAAAlI,EAAA;MACA;IACA;IAEAgI,qBAAAZ,MAAA;MACA,SAAA7H,oBAAA;QACA4I,aAAA,MAAA5I,oBAAA;MACA;MAEA,KAAAD,eAAA;;MAEA;MACA,KAAA8I,kBAAA,CAAAhB,MAAA;;MAEA;MACA,KAAA7H,oBAAA,GAAA8I,WAAA;QACA,KAAAD,kBAAA,CAAAhB,MAAA;MACA;MAEAkB,OAAA,CAAAC,GAAA,gBAAAnB,MAAA;IACA;IAEA,MAAAgB,mBAAAhB,MAAA;MACA;QACA,MAAAjF,QAAA,SAAAlE,KAAA,CAAAqF,GAAA,sCAAA8D,MAAA;UACA/E,MAAA;YAAAC,MAAA,OAAApD;UAAA;QACA;QAEA,IAAAiD,QAAA,CAAAzD,IAAA,CAAA6D,OAAA;UACA,MAAAiG,QAAA,GAAArG,QAAA,CAAAzD,IAAA,CAAAA,IAAA;;UAEA;UACA,KAAAoJ,MAAA,CAAAC,QAAA;YACA,GAAAS,QAAA;YACAZ,SAAA,MAAAhH,IAAA,GAAAiH,cAAA;UACA;;UAEA;UACA,IAAAW,QAAA,CAAAjB,MAAA,kBAAAiB,QAAA,CAAAjB,MAAA,iBAAAiB,QAAA,CAAAjB,MAAA;YACA,KAAAkB,mBAAA;;YAEA;YACA,IAAAD,QAAA,CAAAjB,MAAA;cACA,KAAAnH,QAAA,CAAAmC,OAAA,MAAAvC,EAAA;YACA,WAAAwI,QAAA,CAAAjB,MAAA;cACA,KAAAnH,QAAA,CAAAwC,KAAA,MAAA5C,EAAA;YACA;cACA,KAAAI,QAAA,CAAAC,OAAA,MAAAL,EAAA;YACA;YAEAsI,OAAA,CAAAC,GAAA;UACA;QACA;UACAD,OAAA,CAAA1F,KAAA,cAAAT,QAAA,CAAAzD,IAAA,CAAAkE,KAAA;QACA;MACA,SAAAA,KAAA;QAAA,IAAA8F,gBAAA;QACAJ,OAAA,CAAA1F,KAAA,cAAAA,KAAA;;QAEA;QACA,MAAA8F,gBAAA,GAAA9F,KAAA,CAAAT,QAAA,cAAAuG,gBAAA,uBAAAA,gBAAA,CAAAnB,MAAA;UACA,KAAAkB,mBAAA;UACA,KAAArI,QAAA,CAAAwC,KAAA;QACA;MACA;IACA;IAEA6F,oBAAA;MACA,SAAAlJ,oBAAA;QACA4I,aAAA,MAAA5I,oBAAA;QACA,KAAAA,oBAAA;MACA;MACA,KAAAD,eAAA;MACA,KAAAD,cAAA;IACA;IAEAsJ,wBAAA;MACA;MACA,MAAAC,QAAA,GAAAzJ,YAAA,CAAAC,OAAA,gCAAAF,aAAA;MACA,IAAA0J,QAAA;QACA;UACA;YAAAxB,MAAA;YAAAyB;UAAA,IAAAC,IAAA,CAAAC,KAAA,CAAAH,QAAA;UACA,IAAAC,WAAA,UAAA3J,aAAA;YACA,KAAAG,cAAA,GAAA+H,MAAA;YACA,KAAAY,oBAAA,CAAAZ,MAAA;UACA;QACA,SAAA4B,CAAA;UACAV,OAAA,CAAA1F,KAAA,iDAAAoG,CAAA;UACA7J,YAAA,CAAA8J,UAAA,gCAAA/J,aAAA;QACA;MACA;IACA;EAEA;EAEAgK,QAAA;IACA,KAAA1I,qBAAA;IACA;IACA,KAAAmI,uBAAA;EACA;EAEAQ,cAAA;IACA;IACA,KAAAV,mBAAA;EACA;AACA", "ignoreList": []}]}