{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue", "mtime": 1753175269721}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "mapState", "mapActions", "NotificationMixin", "TaskPollingMixin", "ProxySelector", "TaskProgressCard", "NodeSelector", "mixins", "components", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedIp", "currentStep", "computed", "taskId", "get", "_this$activeTask", "activeTask", "task_id", "set", "value", "$store", "dispatch", "currentStepComputed", "isProcessing", "length", "getPlayIconTooltip", "created", "checkDatabaseStatus", "taskInfo", "localStorage", "getItem", "currentProject", "projectFile", "JSON", "parse", "checkActiveTask", "removeItem", "methods", "$notify", "error", "title", "message", "$router", "push", "onNodesSelected", "handleProxyChange", "ip", "handleStart", "warning", "notificationSent", "previousTaskInfo", "clearTaskNotificationMark", "e", "console", "post", "targets", "proxy_ip", "dbFile", "setItem", "stringify", "startPolling", "taskCompleted", "Error", "response", "nodes", "Object", "values", "allCompleted", "every", "node", "includes", "status", "activated", "watch", "handler", "newProject", "oldProject", "stopPolling", "immediate"], "sources": ["src/components/Cards/TaskPanel.vue"], "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full task-card\"\r\n    :bodyStyle=\"{ padding: '8px 16px' }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <!-- 流程图 -->\r\n    <div class=\"steps-container\">\r\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"apartment\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"global\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-tooltip :title=\"getPlayIconTooltip\">\r\n              <a-icon\r\n                type=\"play-circle\"\r\n                class=\"step-icon\"\r\n                :class=\"{\r\n                  'clickable': selectedIp && selectedRowKeys.length > 0 && !isProcessing,\r\n                  'ready-to-start': selectedIp && selectedRowKeys.length > 0 && !isProcessing\r\n                }\"\r\n                @click=\"selectedIp && selectedRowKeys.length > 0 && !isProcessing && handleStart()\"\r\n                :style=\"{\r\n                  color: (selectedIp && selectedRowKeys.length > 0 && !isProcessing)\r\n                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\r\n                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\r\n                }\"\r\n              />\r\n            </a-tooltip>\r\n          </template>\r\n        </a-step>\r\n      </a-steps>\r\n    </div>\r\n\r\n    <!-- 节点选择区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\r\n      <node-selector\r\n        v-model=\"selectedRowKeys\"\r\n        :project-file=\"currentProject\"\r\n        :disabled=\"isProcessing\"\r\n        @input=\"onNodesSelected\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 代理配置区域 -->\r\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\r\n      <proxy-selector\r\n        v-model=\"selectedIp\"\r\n        :disabled=\"isProcessing\"\r\n        @change=\"handleProxyChange\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 任务状态 -->\r\n    <task-progress-card :task-type=\"'task'\" :is-processing=\"isProcessing\" />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\r\nimport NodeSelector from '@/components/common/NodeSelector.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin, TaskPollingMixin],\r\n  components: {\r\n    ProxySelector,\r\n    TaskProgressCard,\r\n    NodeSelector\r\n  },\r\n  data() {\r\n    return {\r\n      selectedRowKeys: [],\r\n      selectedIp: null,\r\n      currentStep: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['activeTask', 'currentProject', 'sidebarColor']),\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n\r\n    taskId: {\r\n      get() {\r\n        return this.activeTask?.task_id;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateTask', value ? { task_id: value } : null);\r\n      }\r\n    },\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n    currentStepComputed() {\r\n      if (this.isProcessing) {\r\n        return 1;  // 运行中时，只点亮前两个图标\r\n      }\r\n      if (this.selectedRowKeys.length === 0) {\r\n        return -1;  // 没有选择任何节点，所有图标不点亮\r\n      }\r\n      if (this.selectedRowKeys.length > 0 && !this.selectedIp) {\r\n        return 0;   // 选择了节点但未选择IP，点亮第一步图标和连接线\r\n      }\r\n      return 2;     // 选择了节点和IP，且未在运行时，点亮所有三个图标和连接线\r\n    },\r\n    getPlayIconTooltip() {\r\n      if (this.isProcessing) {\r\n        return 'Task is in progress...';\r\n      }\r\n      if (!this.selectedRowKeys.length) {\r\n        return 'Please select nodes first';\r\n      }\r\n      if (!this.selectedIp) {\r\n        return 'Please select a proxy IP';\r\n      }\r\n      return 'Click to start collection!'; // 当都选择完成时显示这个提示\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    // 只检查当前项目的活动任务\r\n    const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveTask();\r\n      } else {\r\n        // 清除任务信息如果属于不同项目\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 处理节点选择变化\r\n    onNodesSelected(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n      if (this.selectedRowKeys.length) {\r\n        this.currentStep = 1;\r\n      } else {\r\n        this.currentStep = 0;\r\n      }\r\n    },\r\n\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      this.selectedIp = ip;\r\n    },\r\n\r\n    async handleStart() {\r\n      if (!this.checkDatabaseStatus()) return;\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: 'No Nodes or Proxy Selected',\r\n          message: 'Please select one or more nodes and a reachable IP to collect the data.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isProcessing = true;\r\n      this.notificationSent = false; // 重置通知标志位\r\n\r\n      // 清除之前的任务通知记录\r\n      const previousTaskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n      if (previousTaskInfo) {\r\n        try {\r\n          const { taskId } = JSON.parse(previousTaskInfo);\r\n          if (taskId) {\r\n            this.clearTaskNotificationMark(taskId, 'task', this.currentProject);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error clearing previous task notification:', e);\r\n        }\r\n      }\r\n\r\n      try {\r\n        const { data } = await axios.post('/api/task/collect', {\r\n          targets: this.selectedRowKeys,\r\n          proxy_ip: this.selectedIp,\r\n          dbFile: this.currentProject\r\n        });\r\n\r\n        if (data && data.task_id) {\r\n          localStorage.setItem(`taskInfo_${this.currentProject}`, JSON.stringify({\r\n            taskId: data.task_id,\r\n            projectFile: this.currentProject\r\n          }));\r\n          localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n\r\n          this.taskId = data.task_id;\r\n          this.startPolling(data.task_id, 'task', 'task');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error starting task:', error);\r\n        this.$notify.error({\r\n          title: 'Task Start Failed',\r\n          message: error.message || 'Server connection error.',\r\n        });\r\n        this.isProcessing = false;\r\n      }\r\n    },\r\n\r\n    // 重写 checkActiveTask 方法，调用混入中的方法\r\n    async checkActiveTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`taskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/task/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, 'task', 'task');\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(`taskCompleted_${this.currentProject}`, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n\r\n\r\n    activated() {\r\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\r\n      this.checkActiveTask();\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听 currentProject 变化\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除之前项目的任务状态\r\n          this.$store.dispatch('updateTask', null);\r\n          this.stopPolling();\r\n          // 检查新项目的活动任务\r\n          this.checkActiveTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 基础卡片样式\r\n.task-card {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n// 步骤容器\r\n.steps-container {\r\n  width: 50%;\r\n  margin: 0 auto 24px;\r\n  padding: 12px 0;\r\n}\r\n\r\n// 深度选择器样式集中管理\r\n::v-deep {\r\n  .ant-card {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    .ant-card-head {\r\n      background: #f0f2f5;\r\n    }\r\n  }\r\n\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n  .ant-table-tbody > tr:last-child > td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  .steps-flow {\r\n    .ant-steps-item {\r\n      &-process,\r\n      &-finish {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #3b4149 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      &-wait {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #d9d9d9 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n      }\r\n\r\n      &-icon {\r\n        width: 88px;\r\n        height: 88px;\r\n        line-height: 80px;\r\n        padding: 4px;\r\n        font-size: 40px;\r\n        border-width: 2px;\r\n        margin-top: -20px;\r\n        color: #3b4149;\r\n\r\n        .step-icon {\r\n          font-size: 40px;\r\n          color: #3b4149;\r\n        }\r\n      }\r\n\r\n      &-tail::after {\r\n        height: 2px;\r\n      }\r\n\r\n      &:last-child {\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n\r\n        &.ant-steps-item-process,\r\n        &.ant-steps-item-finish {\r\n          .step-icon {\r\n            color: #3b4149 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ready-to-start {\r\n    animation: pulse 1.2s infinite;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n      opacity: 0.8;\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .clickable {\r\n    cursor: pointer;\r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;AAuEA,OAAAA,KAAA;AACA,SAAAC,QAAA,EAAAC,UAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,YAAA;AAEA;EACAC,MAAA,GAAAL,iBAAA,EAAAC,gBAAA;EACAK,UAAA;IACAJ,aAAA;IACAC,gBAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,eAAA;MACAC,UAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAb,QAAA;IACA;;IAEAc,MAAA;MACAC,IAAA;QAAA,IAAAC,gBAAA;QACA,QAAAA,gBAAA,QAAAC,UAAA,cAAAD,gBAAA,uBAAAA,gBAAA,CAAAE,OAAA;MACA;MACAC,IAAAC,KAAA;QACA,KAAAC,MAAA,CAAAC,QAAA,eAAAF,KAAA;UAAAF,OAAA,EAAAE;QAAA;MACA;IACA;IACA;IACAG,oBAAA;MACA,SAAAC,YAAA;QACA;MACA;MACA,SAAAd,eAAA,CAAAe,MAAA;QACA;MACA;MACA,SAAAf,eAAA,CAAAe,MAAA,cAAAd,UAAA;QACA;MACA;MACA;IACA;IACAe,mBAAA;MACA,SAAAF,YAAA;QACA;MACA;MACA,UAAAd,eAAA,CAAAe,MAAA;QACA;MACA;MACA,UAAAd,UAAA;QACA;MACA;MACA;IACA;EACA;EACAgB,QAAA;IACA,UAAAC,mBAAA;MACA;IACA;IACA;IACA,MAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA,kBAAAC,cAAA;IACA,IAAAH,QAAA;MACA;QAAAI;MAAA,IAAAC,IAAA,CAAAC,KAAA,CAAAN,QAAA;MACA,IAAAI,WAAA,UAAAD,cAAA;QACA,KAAAI,eAAA;MACA;QACA;QACAN,YAAA,CAAAO,UAAA,kBAAAL,cAAA;QACAF,YAAA,CAAAO,UAAA,uBAAAL,cAAA;QACA,KAAAX,MAAA,CAAAC,QAAA;MACA;IACA;EACA;EACAgB,OAAA;IACA,GAAArC,UAAA;IACA2B,oBAAA;MACA,UAAAI,cAAA;QACA,KAAAO,OAAA,CAAAC,KAAA;UACAC,KAAA;UACAC,OAAA;QACA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;MACA;IACA;IAEA;IACAC,gBAAAnC,eAAA;MACA,KAAAA,eAAA,GAAAA,eAAA;MACA,SAAAA,eAAA,CAAAe,MAAA;QACA,KAAAb,WAAA;MACA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAkC,kBAAAC,EAAA;MACA,KAAApC,UAAA,GAAAoC,EAAA;IACA;IAEA,MAAAC,YAAA;MACA,UAAApB,mBAAA;MACA,UAAAlB,eAAA,CAAAe,MAAA,UAAAd,UAAA;QACA,KAAA4B,OAAA,CAAAU,OAAA;UACAR,KAAA;UACAC,OAAA;QACA;QACA;MACA;MAEA,KAAAlB,YAAA;MACA,KAAA0B,gBAAA;;MAEA;MACA,MAAAC,gBAAA,GAAArB,YAAA,CAAAC,OAAA,kBAAAC,cAAA;MACA,IAAAmB,gBAAA;QACA;UACA;YAAArC;UAAA,IAAAoB,IAAA,CAAAC,KAAA,CAAAgB,gBAAA;UACA,IAAArC,MAAA;YACA,KAAAsC,yBAAA,CAAAtC,MAAA,eAAAkB,cAAA;UACA;QACA,SAAAqB,CAAA;UACAC,OAAA,CAAAd,KAAA,+CAAAa,CAAA;QACA;MACA;MAEA;QACA;UAAA5C;QAAA,UAAAV,KAAA,CAAAwD,IAAA;UACAC,OAAA,OAAA9C,eAAA;UACA+C,QAAA,OAAA9C,UAAA;UACA+C,MAAA,OAAA1B;QACA;QAEA,IAAAvB,IAAA,IAAAA,IAAA,CAAAS,OAAA;UACAY,YAAA,CAAA6B,OAAA,kBAAA3B,cAAA,IAAAE,IAAA,CAAA0B,SAAA;YACA9C,MAAA,EAAAL,IAAA,CAAAS,OAAA;YACAe,WAAA,OAAAD;UACA;UACAF,YAAA,CAAAO,UAAA,uBAAAL,cAAA;UAEA,KAAAlB,MAAA,GAAAL,IAAA,CAAAS,OAAA;UACA,KAAA2C,YAAA,CAAApD,IAAA,CAAAS,OAAA;QACA;MACA,SAAAsB,KAAA;QACAc,OAAA,CAAAd,KAAA,yBAAAA,KAAA;QACA,KAAAD,OAAA,CAAAC,KAAA;UACAC,KAAA;UACAC,OAAA,EAAAF,KAAA,CAAAE,OAAA;QACA;QACA,KAAAlB,YAAA;MACA;IACA;IAEA;IACA,MAAAY,gBAAA;MACA;QACA,MAAAP,QAAA,GAAAC,YAAA,CAAAC,OAAA,kBAAAC,cAAA;QACA,MAAA8B,aAAA,GAAAhC,YAAA,CAAAC,OAAA,uBAAAC,cAAA;QAEA,IAAAH,QAAA;UACA;YAAAf,MAAA;YAAAmB;UAAA,IAAAC,IAAA,CAAAC,KAAA,CAAAN,QAAA;UAEA,IAAAI,WAAA,UAAAD,cAAA;YACA,UAAA+B,KAAA;UACA;UAEA,MAAAC,QAAA,SAAAjE,KAAA,CAAAgB,GAAA,cAAAD,MAAA;UAEA,IAAAkD,QAAA,CAAAvD,IAAA;YACA,KAAAY,MAAA,CAAAC,QAAA,eAAA0C,QAAA,CAAAvD,IAAA;YAEA,IAAAuD,QAAA,CAAAvD,IAAA,CAAAwD,KAAA;cACA,MAAAA,KAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAH,QAAA,CAAAvD,IAAA,CAAAwD,KAAA;cACA,MAAAG,YAAA,GAAAH,KAAA,CAAAI,KAAA,CAAAC,IAAA,IACA,sBAAAC,QAAA,CAAAD,IAAA,CAAAE,MAAA,CACA;cAEA,KAAAJ,YAAA,KAAAN,aAAA;gBACA,KAAAtC,YAAA;gBACA,KAAAqC,YAAA,CAAA/C,MAAA;cACA,WAAAsD,YAAA;gBACA,KAAA5C,YAAA;gBACAM,YAAA,CAAA6B,OAAA,uBAAA3B,cAAA;cACA;YACA;UACA;QACA;MACA,SAAAQ,KAAA;QACAc,OAAA,CAAAd,KAAA,gCAAAA,KAAA;QACAV,YAAA,CAAAO,UAAA,kBAAAL,cAAA;QACAF,YAAA,CAAAO,UAAA,uBAAAL,cAAA;MACA;IACA;IAIAyC,UAAA;MACA;MACA,KAAArC,eAAA;IACA;EACA;EACAsC,KAAA;IACA;IACA1C,cAAA;MACA2C,QAAAC,UAAA,EAAAC,UAAA;QACA,IAAAD,UAAA,KAAAC,UAAA;UACA;UACA,KAAAxD,MAAA,CAAAC,QAAA;UACA,KAAAwD,WAAA;UACA;UACA,KAAA1C,eAAA;QACA;MACA;MACA2C,SAAA;IACA;EACA;AACA", "ignoreList": []}]}