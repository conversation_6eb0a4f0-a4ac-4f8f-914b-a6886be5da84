<template>
  <div class="layout-content">
    <a-card :bordered="false">
      <template #title>
        <div class="card-header-wrapper">
          <div class="header-wrapper">
            <div class="logo-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" height="20" width="20" :class="`text-${sidebarColor}`">
                <path :fill="'currentColor'" d="M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"/>
              </svg>
            </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.testcase') }}</h6>
          </div>
          <div>
            <RefreshButton @refresh="fetchTestcases(currentPage)" />
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" @submit.prevent="handleSearch">
          <a-form-item :label="$t('caseColumn.name')">
            <a-input v-model="searchForm.name" :placeholder="$t('caseColumn.name')" allowClear />
          </a-form-item>
          <a-form-item :label="$t('caseColumn.level')">
            <a-select v-model="searchForm.level" :placeholder="$t('caseColumn.level')" style="width: 120px" allowClear>
              <a-select-option value="level 0">Level 0</a-select-option>
              <a-select-option value="level 1">Level 1</a-select-option>
              <a-select-option value="level 2">Level 2</a-select-option>
              <a-select-option value="level 3">Level 3</a-select-option>
              <a-select-option value="level 4">Level 4</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item :label="$t('caseColumn.prepareCondition')">
            <a-input v-model="searchForm.prepare_condition" :placeholder="$t('caseColumn.prepareCondition')" allowClear />
          </a-form-item>
          <a-form-item :label="$t('caseColumn.testSteps')">
            <a-input v-model="searchForm.test_steps" :placeholder="$t('caseColumn.testSteps')" allowClear />
          </a-form-item>
          <a-form-item :label="$t('caseColumn.expectedResult')">
            <a-input v-model="searchForm.expected_result" :placeholder="$t('caseColumn.expectedResult')" allowClear />
          </a-form-item>
          <a-form-item :label="$t('caseColumn.feature')">
            <a-input v-model="searchForm.testcase_feature" :placeholder="$t('caseColumn.feature')" allowClear />
          </a-form-item>
          <a-form-item>
            <a-button html-type="submit" :class="`bg-${sidebarColor}`" style="color: white" :loading="loading">
              <a-icon type="search" />
              {{ $t('testcase.searchButton') }}
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              <a-icon type="reload" />
              {{ $t('testcase.resetButton') }}
            </a-button>
          </a-form-item>
        </a-form>
        <div class="search-result-count" v-if="testcases.length > 0">
          <a-tag color="blue">Found: {{ total }} test cases</a-tag>
        </div>
      </div>

      <!-- Table -->
      <ResizableTable
        :columns="tableColumns"
        :data-source="testcases"
        :loading="loading"
        :pagination="{
          total: total,
          pageSize: 100,
          current: currentPage,
          showSizeChanger: false,
          showQuickJumper: true,
          onChange: handlePageChange
        }"
        :scroll="{ x: 1500 }"
        @columns-change="handleColumnsChange"
      >
        <!-- Custom column renders -->
        <template #Testcase_LastResult="{ text }">
          <a-tag :color="getResultColor(text)">
            {{ text || 'N/A' }}
          </a-tag>
        </template>

        <template #Testcase_Level="{ text }">
          <a-tag :color="getLevelColor(text)">
            {{ text || 'N/A' }}
          </a-tag>
        </template>

        <template #Testcase_Feature="{ text, record }">
          <div>
            <a-input
                v-show="record.editable"
                style="margin: -5px 0"
                :value="text"
                @change="e => handleChange(e.target.value, record.id, 'Testcase_Feature')"
                @pressEnter="() => save(record.id)"
            />
            <div
                v-if="!record.editable"
                class="editable-cell-value-wrap"
                style="padding-right: 24px"
                @click="() => edit(record.id)"
            >
              {{ text }}
            </div>
            <a-space v-else>
              <a-button type="link" @click="() => save(record.id)">{{ $t('common.save') }}</a-button>
              <a-button type="link" @click="() => cancel(record.id)">{{ $t('common.cancel') }}</a-button>
            </a-space>
          </div>
        </template>

        <template #lastModified="{ text }">
          {{ formatDate(text) }}
        </template>

        <template #action="{ record }">
          <a-space>
            <a-button type="link" @click="viewDetails(record)">
              View Details
            </a-button>
          </a-space>
        </template>
      </ResizableTable>

      <!-- Details Modal -->
      <TestCaseDetailModal
        :visible="detailsVisible"
        :testcase="selectedTestcase"
        @close="detailsVisible = false"
      />
    </a-card>
  </div>
</template>

<script>
import axios from '@/api/axiosInstance';
import moment from 'moment';
import {mapState} from "vuex";
import RefreshButton from '../Widgets/RefreshButton.vue';
import TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';
import ResizableTable from '../common/ResizableTable.vue';

export default {
  components: {
    RefreshButton,
    TestCaseDetailModal,
    ResizableTable
  },
  name: 'TestCases',
  data() {
    return {
      loading: false,
      testcases: [],
      total: 0,
      currentPage: 1,
      detailsVisible: false,
      selectedTestcase: null,
      searchForm: {
        name: '',
        level: undefined,
        prepare_condition: '',
        test_steps: '',
        expected_result: '',
        testcase_feature: '',
      },
      tableColumns: [],
      cacheData: [], // 用于存储原始数据，以便在取消编辑时恢复
    };
  },
  created() {
    this.initializeColumns();
    this.fetchTestcases();
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])
  },
  methods: {
    initializeColumns() {
      this.tableColumns = [
        {
          title: '#',
          dataIndex: 'index',
          key: 'index',
          width: 100,
          align: 'center',
          customRender: (_, __, index) => {
            return ((this.currentPage - 1) * 100) + index + 1;
          }
        },
        {
          title: this.$t('caseColumn.number'),
          dataIndex: 'Testcase_Number',
          key: 'Testcase_Number',
          width: 130,
          ellipsis: true,
          customRender: (text, record) => {
            return <a onClick={() => this.viewDetails(record)} style="color: #1890ff; cursor: pointer;">{text}</a>;
          }
        },
        {
          title: this.$t('caseColumn.name'),
          dataIndex: 'Testcase_Name',
          key: 'Testcase_Name',
          width: 200,
          // ellipsis: true,
        },
        {
          title: this.$t('caseColumn.level'),
          dataIndex: 'Testcase_Level',
          key: 'Testcase_Level',
          width: 100,
          slots: { customRender: 'Testcase_Level' },
        },
        {
          title: this.$t('caseColumn.prepareCondition'),
          dataIndex: 'Testcase_PrepareCondition',
          key: 'Testcase_PrepareCondition',
          width: 250,
          ellipsis: true,
        },
        {
          title: this.$t('caseColumn.testSteps'),
          dataIndex: 'Testcase_TestSteps',
          key: 'Testcase_TestSteps',
          width: 400,
          ellipsis: true,
        },
        {
          title: this.$t('caseColumn.expectedResult'),
          dataIndex: 'Testcase_ExpectedResult',
          key: 'Testcase_ExpectedResult',
          width: 400,
          ellipsis: true,
        },
        {
          title: this.$t('caseColumn.feature'),
          dataIndex: 'Testcase_Feature',
          key: 'Testcase_Feature',
          width: 200,
          slots: { customRender: 'Testcase_Feature' },
        },        
      ];
    },

    handleColumnsChange(columns) {
      this.tableColumns = columns;
    },

    async fetchTestcases(page = 1) {
      this.loading = true;
      try {
        // 构建查询参数
        const params = {
          page: page,
          page_size: 100
        };

        // 添加搜索参数
        if (this.searchForm.name) params.name = this.searchForm.name;
        if (this.searchForm.level) params.level = this.searchForm.level;
        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;
        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;
        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;
        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;

        const response = await axios.get('/api/testcase/', { params });
        this.testcases = response.data.data.map(item => ({...item, editable: false}));
        this.cacheData = this.testcases.map(item => ({ ...item }));
        this.total = response.data.total;
      } catch (error) {
        console.error('Error fetching testcases:', error);
        this.$message.error('Failed to load test cases');
      } finally {
        this.loading = false;
      }
    },

    handleChange(value, id, column) {
      const target = this.testcases.find(item => item.id === id);
      if (target) {
        target[column] = value;
      }
    },
    edit(id) {
      const target = this.testcases.find(item => item.id === id);
      if (target) {
        target.editable = true;
      }
    },
    save(id) {
      const target = this.testcases.find(item => item.id === id);
      if (target) {
        // 检查值是否发生变化
        const originalData = this.cacheData.find(item => item.id === id);
        if (originalData && originalData.Testcase_Feature !== target.Testcase_Feature) {
          // 调用后端API保存更改
          axios.put(`/api/testcase/${target.Testcase_Number}`, { Testcase_Feature: target.Testcase_Feature })
              .then(() => {
                this.$message.success('用例特性更新成功');
                target.editable = false;
                // 更新缓存数据
                Object.assign(originalData, target);
              })
              .catch(error => {
                this.$message.error('用例特性更新失败: ' + error.message);
                // 恢复原始值
                Object.assign(target, originalData);
                target.editable = false;
              });
        } else {
          target.editable = false;
        }
      }
    },
    cancel(id) {
      const target = this.testcases.find(item => item.id === id);
      const originalData = this.cacheData.find(item => item.id === id);
      if (target && originalData) {
        Object.assign(target, originalData);
        target.editable = false;
      }
    },

    // 搜索处理函数
    handleSearch() {
      this.currentPage = 1; // 重置到第一页
      this.fetchTestcases(1);
    },

    // 重置搜索表单
    resetSearch() {
      this.searchForm = {
        name: '',
        level: undefined,
        prepare_condition: '',
        test_steps: '',
        expected_result: '',
        testcase_feature: '',
      };
      this.currentPage = 1;
      this.fetchTestcases(1);
    },
    formatDate(date) {
      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';
    },
    getResultColor(result) {
      const colors = {
        'PASS': 'success',
        'FAIL': 'error',
        'BLOCKED': 'warning',
        'NOT RUN': 'default',
      };
      return colors[result] || 'default';
    },
    getLevelColor(level) {
      const colors = {
        'level 0': 'red',
        'level 1': 'orange',
        'level 2': 'green',
        'level 3': 'blue',
        'level 4': 'purple',
      };
      return colors[level] || 'default';
    },
    viewDetails(record) {
      this.selectedTestcase = record;
      this.detailsVisible = true;
    },
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchTestcases(page);
    },
  },
};
</script>

<style lang="scss" scoped>

// .criclebox {
//   background: #fff;
//   border-radius: 12px;
// }

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;

  .ant-form-item {
    margin-bottom: 12px;
  }

  .search-result-count {
    margin-top: 1px;
    padding: 0 1px;
  }
}


</style>
