import os
import yaml
import requests
import urllib3
from urllib3.exceptions import InsecureRequestWarning
from log.logger import log_error
import traceback

yaml_dict = {
    'text_score': 0,
    'text_generate': 1,
    'text_jailbreak': 3,
    'text_match': 2
}


class DifyWorkflowClient:
    def __init__(self, api_key: str = "", base_url: str = "http://192.168.18.71/v1", app_id: str = ""):
        self.api_key = api_key
        self.base_url = base_url
        self.app_id = app_id

    def get_yaml_settings(self, module_name):
        current_directory = os.path.dirname(os.path.abspath(__file__))
        upper_path = os.path.dirname(current_directory)
        yaml_path = os.path.join(upper_path, 'config', 'dify.yaml')
        with open(yaml_path, 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        conf_block = data['workflow']['0'][module_name]
        return conf_block['api_key']

    def _send_request(self, method, endpoint, data_json=None, params=None, stream=False):
        urllib3.disable_warnings(InsecureRequestWarning)  # 禁用安全请求警告
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.request(
                method, url, json=data_json, params=params, headers=headers, stream=stream, verify=False
            )
            response.raise_for_status()  # 检查HTTP响应状态码，如果不是2xx，则抛出HTTPError
            return response
        except requests.exceptions.RequestException as e:
            msg = traceback.format_exc()
            log_error(f"Dify请求失败: {e}, 详细信息: {msg}")
            raise e

    def run_workflows(self, inputs: dict):
        outputs = ''
        endpoint = "/workflows/run"
        json_body = {
            "inputs": inputs,
            "response_mode": "blocking",  # 阻塞模式，等待执行完毕后返回结果
            "user": "powerhub",
            "workflow_id": self.app_id # 将 app_id 替换为 workflow_id
        }

        try:
            response = self._send_request(method="POST", endpoint=endpoint, data_json=json_body)
        except Exception as e:
            msg = traceback.format_exc()
            log_error(f"dify_workflow工作流交互失败，异常: {msg}")
            raise e

        if response.status_code == 200:
            response_dict = response.json()
            data = response_dict["data"]
            status = data["status"]
            if status == "succeeded":
                outputs = data["outputs"]
                return outputs
        return outputs

    def get_workflow_logs(self, page):
        has_more = ''
        data = ''
        endpoint = "/workflows/logs?status=succeeded&&page=" + str(page)
        response = self._send_request(method="GET", endpoint=endpoint)
        if response.status_code == 200:
            response_dict = response.json()
            has_more = response_dict["has_more"]
            data = response_dict["data"]
            return has_more, data
        return has_more, data

    def get_workflows_run(self, workflow_id):
        inputs = ''
        outputs = ''
        endpoint = "/workflows/run/" + workflow_id
        response = self._send_request(method="GET", endpoint=endpoint)
        if response.status_code == 200:
            response_dict = response.json()
            inputs = response_dict["inputs"]
            outputs = response_dict["outputs"]
            return inputs, outputs
        return inputs, outputs

    def get_application_info(self):
        endpoint = "/info"
        response = self._send_request(method="GET", endpoint=endpoint)

    def get_application_parameters(self):
        endpoint = "/parameters"
        response = self._send_request(method="GET", endpoint=endpoint)
        user_input_form = response.json()["user_input_form"]
