<template>
  <!-- Packages Table Card -->
  <a-card
    :bordered="false"
    class="header-solid h-full"
    :bodyStyle="{ padding: 0 }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >

    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" :class="`text-${sidebarColor}`">
                <path fill="currentColor" fill-rule="evenodd" d="M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z" clip-rule="evenodd"/>
              </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.package') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchPackages" />
        </div>
      </div>
    </template>



    <a-table
      :columns="columns"
      :data-source="packages"
      :rowKey="(record) => record.package_name"
      :pagination="pagination"
    >
       <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'package_name'">
          <div class="table-package-info">
            <span>{{ record.package_name }}</span>
            <span>{{ record.package_type }}</span>
          </div>
        </template>
        <template v-else-if="column.key === 'action'">
          <a-button type="link" class="btn-edit">Edit</a-button>
        </template>
      </template>
    </a-table>
  </a-card>
  <!-- / Packages Table Card -->
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';

export default {
  components: {
    RefreshButton
  },
  data() {
    return {
      packages: [],
      columns: [
        {
          title: 'Package Name',
          dataIndex: 'package_name',
          key: 'package_name',
        },
        {
          title: 'Package Type',
          dataIndex: 'package_type',
          key: 'package_type',
        },
      ],
      pagination: {
        pageSize: 100,
      },
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp(newIp) {
      // 当 selectedNodeIp 变化时重新获取包数据
      this.fetchPackages();
    },
  },
  mounted() {
    this.fetchPackages(); // 初始加载时调用
  },
  methods: {
    async fetchPackages() {
      console.log('Selected Node IP:', this.selectedNodeIp);
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        return;
      }
      try {
        const response = await axios.get(`/api/packages/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject // 添加 dbFile 参数
          }
        });
        this.packages = response.data;
      } catch (error) {
        console.error('Error fetching packages:', error);
      }
    },
  },
};
</script>

<style scoped lang="scss">

</style>
