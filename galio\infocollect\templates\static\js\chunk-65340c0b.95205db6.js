(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-65340c0b"],{"2e71":function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{visible:e.visible,title:e.$t("testcase.detail"),width:"800px",footer:null},on:{cancel:e.handleClose}},[e.currentTestcase?t("a-descriptions",{attrs:{bordered:""}},[t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.number"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_Number)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.name"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_Name)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.level"),span:3}},[t("a-tag",{attrs:{color:e.getLevelColor(e.currentTestcase.Testcase_Level)}},[e._v(" "+e._s(e.currentTestcase.Testcase_Level)+" ")])],1),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.prepareCondition"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_PrepareCondition)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.testSteps"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_TestSteps)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.expectedResult"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_ExpectedResult)+" ")])])],1):e._e()],1)},r=[],l=s("fec3"),i={name:"TestCaseDetailModal",props:{visible:{type:Boolean,default:!1},testcase:{type:Object,default:null},fetchDetails:{type:Boolean,default:!1}},data(){return{loading:!1,detailedTestcase:null}},watch:{visible(e){e&&this.fetchDetails&&this.testcase&&!this.detailedTestcase&&this.fetchTestcaseDetails()},testcase(){this.detailedTestcase=null}},computed:{currentTestcase(){return this.detailedTestcase||this.testcase}},methods:{async fetchTestcaseDetails(){if(this.testcase&&this.testcase.Testcase_Number){this.loading=!0;try{const e=await l["a"].get("/api/testcase/"+this.testcase.Testcase_Number);this.detailedTestcase={...e.data,similarity:this.testcase.similarity}}catch(e){console.error("获取测试用例详情失败:",e),this.$message.error("获取测试用例详情失败")}finally{this.loading=!1}}},handleClose(){this.$emit("close"),this.detailedTestcase=null},getLevelColor(e){const t={"level 0":"red","level 1":"orange","level 2":"green","level 3":"blue","level 4":"purple",P0:"red",P1:"orange",P2:"blue",P3:"green",P4:"gray"};return t[e]||"default"},getSimilarityColor(e){return e>=.8?"#52c41a":e>=.6?"#faad14":"#f5222d"}}},n=i,c=(s("867c"),s("2877")),o=Object(c["a"])(n,a,r,!1,null,"59bfc3d1",null);t["a"]=o.exports},4042:function(e,t,s){"use strict";s("c937")},"867c":function(e,t,s){"use strict";s("ab7a")},ab7a:function(e,t,s){},c937:function(e,t,s){},e300:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("SmartOrchestrationInfo")],1)],1)],1)},r=[],l=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full",attrs:{bordered:!1},scopedSlots:e._u([{key:"title",fn:function(){return[t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("smartOrchestration.smartAnalysis")))])]},proxy:!0},{key:"extra",fn:function(){return[t("a-button",{attrs:{type:"primary",loading:e.analyzing,disabled:!e.hasNodeData,icon:"branches"},on:{click:e.showAnalysisModal}},[e._v(" "+e._s(e.$t("smartOrchestration.startAnalysis"))+" ")])]},proxy:!0}])},[t("a-row",{staticClass:"mb-16",attrs:{gutter:16}},[t("a-col",{attrs:{span:24}},[t("a-card",{staticClass:"query-card",attrs:{title:e.$t("smartOrchestration.caseAnalysis"),size:"small"}},[t("a-form",{attrs:{layout:"vertical"}},[t("a-form-item",{attrs:{label:e.$t("smartOrchestration.naturalLanguageQuery")}},[t("a-input-search",{attrs:{placeholder:e.$t("smartOrchestration.queryPlaceholder"),"enter-button":e.$t("testcase.searchButton"),size:"large",loading:e.searching},on:{search:e.searchTestcases},model:{value:e.smartSearchQuery,callback:function(t){e.smartSearchQuery=t},expression:"smartSearchQuery"}})],1),t("a-form-item",[t("a-row",{attrs:{gutter:8}},[t("a-col",{attrs:{span:8}},[t("a-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:50,placeholder:e.$t("smartOrchestration.topK")},model:{value:e.smartSearchTopK,callback:function(t){e.smartSearchTopK=t},expression:"smartSearchTopK"}}),t("div",{staticClass:"param-label"},[e._v(e._s(e.$t("smartOrchestration.topK")))])],1),t("a-col",{attrs:{span:8}},[t("a-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.1,placeholder:e.$t("smartOrchestration.scoreThreshold")},model:{value:e.smartSearchThreshold,callback:function(t){e.smartSearchThreshold=t},expression:"smartSearchThreshold"}}),t("div",{staticClass:"param-label"},[e._v(e._s(e.$t("smartOrchestration.scoreThreshold")))])],1),t("a-col",{attrs:{span:8}},[t("a-button",{attrs:{type:"primary",loading:e.searching,block:"",icon:"search"},on:{click:e.searchTestcases}},[e._v(" "+e._s(e.$t("testcase.searchButton"))+" ")])],1)],1)],1)],1)],1)],1)],1),t("a-row",{staticClass:"mb-16",attrs:{gutter:16}},[t("a-col",{attrs:{span:24}},[t("a-card",{attrs:{title:e.$t("smartOrchestration.searchResults"),size:"small"},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-space",[t("a-tag",{attrs:{color:"blue"}},[e._v(e._s(e.$t("smartOrchestration.foundResults",{count:(e.smartSearchResults||[]).length})))]),t("a-button",{attrs:{type:"link",size:"small",icon:"close"},on:{click:e.clearSearchHistory}},[e._v(" "+e._s(e.$t("common.clear"))+" ")])],1)]},proxy:!0}])},[t("a-table",{attrs:{columns:e.searchResultColumns,"data-source":e.smartSearchResults,pagination:!1,size:"small",scroll:{x:800}},scopedSlots:e._u([{key:"Testcase_Number",fn:function(s,a){return[t("a",{staticStyle:{color:"#1890ff",cursor:"pointer"},on:{click:function(t){return e.viewTestcaseDetail(a)}}},[e._v(" "+e._s(a.Testcase_Number)+" ")])]}},{key:"Testcase_Level",fn:function(s,a){return[t("a-tag",{attrs:{color:e.getLevelColor(a.Testcase_Level)}},[e._v(" "+e._s(a.Testcase_Level)+" ")])]}},{key:"similarity",fn:function(s,a){return[t("a-progress",{attrs:{percent:Math.round(100*a.similarity),size:"small","stroke-color":e.getSimilarityColor(a.similarity)}}),t("span",{staticStyle:{"margin-left":"8px","font-size":"12px"}},[e._v(" "+e._s((100*a.similarity).toFixed(1))+"% ")])]}}])})],1)],1)],1),t("a-row",{staticClass:"mb-16",attrs:{gutter:16}},[t("a-col",{attrs:{span:24}},[e.hasNodeData?t("a-alert",{staticClass:"mb-16",attrs:{message:"节点数据已就绪",description:`已检测到 ${(e.availableDataTypes||[]).length} 种类型的数据：${(e.availableDataTypes||[]).join("、")}`,type:"success","show-icon":""}}):t("a-alert",{staticClass:"mb-16",attrs:{message:"未检测到节点数据",description:"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析",type:"info","show-icon":""}})],1)],1),(e.analysisResults||[]).length>0?t("div",[t("a-divider",{attrs:{orientation:"left"}},[e._v("分析结果")]),t("a-collapse",{staticClass:"mb-16",model:{value:e.activeKeys,callback:function(t){e.activeKeys=t},expression:"activeKeys"}},e._l(e.analysisResults,(function(s,a){return t("a-collapse-panel",{key:a,attrs:{header:`${s.info_type.toUpperCase()} 信息分析 - ${"success"===s.status?"成功":"warning"===s.status?"警告":"失败"}`},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-tag",{attrs:{color:e.getStatusColor(s.status)}},[e._v(" "+e._s(e.getStatusText(s.status))+" ")])]},proxy:!0}],null,!0)},[t("a-descriptions",{staticClass:"mb-16",attrs:{title:"查询信息",column:1,size:"small"}},[t("a-descriptions-item",{attrs:{label:"信息类型"}},[e._v(e._s(s.info_type))]),t("a-descriptions-item",{attrs:{label:"查询文本"}},[e._v(e._s(s.query_text))]),t("a-descriptions-item",{attrs:{label:"匹配用例数"}},[e._v(e._s((s.matched_testcases||[]).length))])],1),t("a-divider",{attrs:{orientation:"left","orientation-margin":"0"}},[e._v("匹配的测试用例")]),t("a-table",{staticClass:"mb-16",attrs:{dataSource:s.matched_testcases,columns:e.testcaseColumns,pagination:!1,size:"small"},scopedSlots:e._u([{key:"bodyCell",fn:function({column:s,record:a}){return["Testcase_Name"===s.key?[t("a-tooltip",{attrs:{title:a.Testcase_Name}},[t("span",[e._v(e._s(e.truncateText(a.Testcase_Name,30)))])])]:e._e(),"Testcase_TestSteps"===s.key?[t("a-tooltip",{attrs:{title:a.Testcase_TestSteps}},[t("span",[e._v(e._s(e.truncateText(a.Testcase_TestSteps,50)))])])]:e._e()]}}],null,!0)}),t("a-divider",{attrs:{orientation:"left","orientation-margin":"0"}},[e._v("执行结果")]),t("a-table",{attrs:{dataSource:s.execution_results,columns:e.executionColumns,pagination:!1,size:"small",expandable:{expandedRowRender:e.expandedRowRender}},scopedSlots:e._u([{key:"bodyCell",fn:function({column:s,record:a}){return["status"===s.key?[t("a-tag",{attrs:{color:e.getStatusColor(a.status)}},[e._v(" "+e._s(e.getStatusText(a.status))+" ")])]:e._e(),"testcase_name"===s.key?[t("a-tooltip",{attrs:{title:a.testcase_name}},[t("span",[e._v(e._s(e.truncateText(a.testcase_name,30)))])])]:e._e()]}}],null,!0)})],1)})),1)],1):e._e(),t("a-modal",{attrs:{title:"智能测试用例分析配置",width:800,confirmLoading:e.analyzing},on:{ok:e.startAnalysis},model:{value:e.analysisModalVisible,callback:function(t){e.analysisModalVisible=t},expression:"analysisModalVisible"}},[t("a-form",{attrs:{layout:"vertical"}},[t("a-form-item",{attrs:{label:"选择节点",required:""}},[t("a-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要分析的节点"},model:{value:e.selectedNodeId,callback:function(t){e.selectedNodeId=t},expression:"selectedNodeId"}},e._l(e.availableNodes,(function(s){return t("a-select-option",{key:s.id,attrs:{value:s.id}},[e._v(" "+e._s(s.name)+" ("+e._s(s.ip)+") ")])})),1)],1),t("a-form-item",{attrs:{label:"选择分析类型",required:""}},[t("a-checkbox-group",{model:{value:e.selectedAnalysisTypes,callback:function(t){e.selectedAnalysisTypes=t},expression:"selectedAnalysisTypes"}},[t("a-row",e._l(e.availableDataTypes,(function(s){return t("a-col",{key:s,attrs:{span:8}},[t("a-checkbox",{attrs:{value:s}},[e._v(e._s(e.getTypeName(s)))])],1)})),1)],1)],1)],1)],1),t("TestCaseDetailModal",{attrs:{visible:e.testcaseDetailVisible,testcase:e.selectedTestcase},on:{close:function(t){e.testcaseDetailVisible=!1}}})],1)},i=[],n=(s("0643"),s("a573"),s("f64c")),c=s("2f62"),o=s("2e71"),d={name:"IntelligentTestCaseInfo",components:{TestCaseDetailModal:o["a"]},data(){return{analyzing:!1,analysisModalVisible:!1,selectedNodeId:null,selectedAnalysisTypes:[],analysisResults:[],activeKeys:["0"],availableNodes:[],availableDataTypes:[],searching:!1,testcaseDetailVisible:!1,selectedTestcase:null,smartSearchQuery:"",smartSearchTopK:10,smartSearchThreshold:.5}},computed:{...Object(c["e"])(["currentProject","currentProjectName","smartSearchResults"]),hasNodeData(){return this.availableNodes.length>0&&this.availableDataTypes.length>0},searchResultColumns(){return[{title:"#",dataIndex:"index",key:"index",width:60,align:"center",customRender:(e,t,s)=>s+1},{title:this.$t("caseColumn.number"),dataIndex:"Testcase_Number",key:"Testcase_Number",width:120,scopedSlots:{customRender:"Testcase_Number"}},{title:this.$t("caseColumn.name"),dataIndex:"Testcase_Name",key:"Testcase_Name",ellipsis:!0,width:300},{title:this.$t("caseColumn.level"),dataIndex:"Testcase_Level",key:"Testcase_Level",width:100,scopedSlots:{customRender:"Testcase_Level"}},{title:this.$t("caseColumn.similarity"),dataIndex:"similarity",key:"similarity",width:150,scopedSlots:{customRender:"similarity"}}]},testcaseColumns(){return[{title:this.$t("caseColumn.number"),dataIndex:"Testcase_Number",key:"Testcase_Number",width:120},{title:this.$t("caseColumn.name"),dataIndex:"Testcase_Name",key:"Testcase_Name",ellipsis:!0},{title:this.$t("caseColumn.level"),dataIndex:"Testcase_Level",key:"Testcase_Level",width:80},{title:this.$t("caseColumn.testSteps"),dataIndex:"Testcase_TestSteps",key:"Testcase_TestSteps",ellipsis:!0}]},executionColumns(){return[{title:this.$t("caseColumn.number"),dataIndex:"testcase_number",key:"testcase_number",width:120},{title:this.$t("caseColumn.name"),dataIndex:"testcase_name",key:"testcase_name",ellipsis:!0},{title:this.$t("smartOrchestration.executionStatus"),dataIndex:"status",key:"status",width:100},{title:this.$t("smartOrchestration.executionMessage"),dataIndex:"message",key:"message",ellipsis:!0}]}},mounted(){this.loadAvailableNodes(),this.detectAvailableDataTypes(),this.lastProjectInfo={project:this.currentProject,projectName:this.currentProjectName}},methods:{async loadAvailableNodes(){try{const e=await this.$axios.get("/api/node/nodes");e.data&&e.data.length>0&&(this.availableNodes=e.data)}catch(e){console.error("加载节点列表失败:",e)}},detectAvailableDataTypes(){const e=["process","package","hardware","filesystem","port","docker","kubernetes"],t=[];e.forEach(e=>{const s=e+"_data";localStorage.getItem(s)&&t.push(e)}),this.availableDataTypes=t},showAnalysisModal(){this.hasNodeData?(this.selectedAnalysisTypes=[...this.availableDataTypes],this.analysisModalVisible=!0,1===this.availableNodes.length&&(this.selectedNodeId=this.availableNodes[0].id)):n["a"].warning("请先收集节点数据")},async startAnalysis(){if(this.selectedNodeId)if(0!==this.selectedAnalysisTypes.length){this.analyzing=!0,this.analysisResults=[];try{for(const e of this.selectedAnalysisTypes){const t=this.getCollectedData(e);if(t){const s=await this.$http.post("/api/intelligent/analyze-and-execute",{node_id:this.selectedNodeId,info_type:e,collected_data:t});this.analysisResults.push(s.data)}}this.analysisModalVisible=!1,this.activeKeys=this.analysisResults.map((e,t)=>t.toString()),n["a"].success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`)}catch(e){console.error("分析失败:",e),n["a"].error("分析过程中出现错误")}finally{this.analyzing=!1}}else n["a"].error("请选择分析类型");else n["a"].error("请选择节点")},getCollectedData(e){const t=e+"_data",s=localStorage.getItem(t);return s?JSON.parse(s):null},getTypeName(e){const t={process:"进程信息",package:"软件包信息",hardware:"硬件信息",filesystem:"文件系统信息",port:"端口信息",docker:"Docker信息",kubernetes:"Kubernetes信息"};return t[e]||e},getStatusColor(e){const t={success:"green",partial:"orange",warning:"orange",failed:"red",error:"red",info:"blue"};return t[e]||"default"},getStatusText(e){const t={success:"成功",partial:"部分成功",warning:"警告",failed:"失败",error:"错误",info:"信息"};return t[e]||e},truncateText(e,t){return e?e.length>t?e.substring(0,t)+"...":e:""},expandedRowRender(e){return e.outputs&&0!==e.outputs.length?`\n        <div style="margin: 16px 0;">\n          <h4>命令执行详情:</h4>\n          ${e.outputs.map((e,t)=>`\n            <div style="margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;">\n              <p><strong>命令 ${t+1}:</strong> <code>${e.command}</code></p>\n              <p><strong>退出码:</strong> <span style="color: ${0===e.exit_code?"green":"red"}">${e.exit_code}</span></p>\n              ${e.output?`<p><strong>输出:</strong><br><pre style="background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;">${e.output}</pre></p>`:""}\n              ${e.error?`<p><strong>错误:</strong><br><pre style="background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;">${e.error}</pre></p>`:""}\n            </div>\n          `).join("")}\n        </div>\n      `:"无执行详情"},async searchTestcases(){if(this.smartSearchQuery.trim()){this.searching=!0;try{const e=await this.$axios.post("/api/vector_testcase/search_with_details",{query:this.smartSearchQuery,top_k:this.smartSearchTopK,score_threshold:this.smartSearchThreshold});"success"===e.data.status?(this.$store.dispatch("updateSmartSearchResults",e.data.results),n["a"].success(this.$t("smartOrchestration.foundResults",{count:(e.data.results||[]).length}))):(n["a"].error(e.data.message||this.$t("smartOrchestration.searchFailed")),this.$store.dispatch("clearSmartSearch"))}catch(e){console.error("搜索测试用例失败:",e),n["a"].error(this.$t("smartOrchestration.searchError")),this.$store.dispatch("clearSmartSearch")}finally{this.searching=!1}}else n["a"].warning("请输入查询内容")},viewTestcaseDetail(e){this.selectedTestcase=e,this.testcaseDetailVisible=!0},clearSearchHistory(){this.$store.dispatch("clearSmartSearch"),n["a"].success(this.$t("smartOrchestration.resultsCleared"))},getSimilarityColor(e){return e>=.8?"#52c41a":e>=.6?"#faad14":"#f5222d"},getLevelColor(e){const t={"level 0":"red","level 1":"orange","level 2":"green","level 3":"blue","level 4":"purple",P0:"red",P1:"orange",P2:"blue",P3:"green",P4:"gray"};return t[e]||"default"}}},u=d,h=(s("4042"),s("2877")),p=Object(h["a"])(u,l,i,!1,null,"1c34ca2e",null),m=p.exports,y={components:{SmartOrchestrationInfo:m}},_=y,b=Object(h["a"])(_,a,r,!1,null,null,null);t["default"]=b.exports}}]);
//# sourceMappingURL=chunk-65340c0b.95205db6.js.map