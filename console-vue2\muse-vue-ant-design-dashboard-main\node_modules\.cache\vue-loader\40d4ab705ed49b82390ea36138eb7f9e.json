{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue?vue&type=template&id=40fbb332&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue", "mtime": 1753169488841}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjwhLS0gSGFyZHdhcmUgVGFibGUgQ2FyZCAtLT4KPGEtY2FyZAogIDpib3JkZXJlZD0iZmFsc2UiCiAgY2xhc3M9ImhlYWRlci1zb2xpZCBoLWZ1bGwgaGFyZHdhcmUtY2FyZCIKICA6Ym9keVN0eWxlPSJ7IHBhZGRpbmc6IDAgfSIKICA6aGVhZFN0eWxlPSJ7IGJvcmRlckJvdHRvbTogJzFweCBzb2xpZCAjZThlOGU4JyB9Igo+CiAgPHRlbXBsYXRlICN0aXRsZT4KICAgIDxkaXYgY2xhc3M9ImNhcmQtaGVhZGVyLXdyYXBwZXIiPgogICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItd3JhcHBlciI+CiAgICAgICAgPGRpdiBjbGFzcz0ibG9nby13cmFwcGVyIj4KICAgICAgICAgIDxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiA6Y2xhc3M9ImB0ZXh0LSR7c2lkZWJhckNvbG9yfWAiPgogICAgICAgICAgICA8cGF0aCA6ZmlsbD0iJ2N1cnJlbnRDb2xvciciIGQ9Ik0xNjAgMTYwaDE5MnYxOTJIMTYweiIvPgogICAgICAgICAgICA8cGF0aCA6ZmlsbD0iJ2N1cnJlbnRDb2xvciciIGQ9Ik00ODAgMTk4di00NGgtMzJWODhhMjQgMjQgMCAwIDAtMjQtMjRoLTY2VjMyaC00NHYzMmgtMzZWMzJoLTQ0djMyaC0zNlYzMmgtNDR2MzJoLTM2VjMyaC00NHYzMkg4OGEyNCAyNCAwIDAgMC0yNCAyNHY2NkgzMnY0NGgzMnYzNkgzMnY0NGgzMnYzNkgzMnY0NGgzMnY2NmEyNCAyNCAwIDAgMCAyNCAyNGg2NnYzMmg0NHYtMzJoMzZ2MzJoNDR2LTMyaDM2djMyaDQ0di0zMmg2NmEyNCAyNCAwIDAgMCAyNC0yNHYtNjZoMzJ2LTQ0aC0zMnYtMzZoMzJ2LTQ0aC0zMnYtMzZabS0zNTItNzBoMjU2djI1NkgxMjhaIi8+CiAgICAgICAgICA8L3N2Zz4KICAgICAgICA8L2Rpdj4KICAgICAgICA8aDYgY2xhc3M9ImZvbnQtc2VtaWJvbGQgbS0wIj57eyAkdCgnaGVhZFRvcGljLmhhcmR3YXJlJykgfX08L2g2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdj4KICAgICAgICA8UmVmcmVzaEJ1dHRvbiBAcmVmcmVzaD0iZmV0Y2hIYXJkd2FyZSIgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L3RlbXBsYXRlPgoKICA8YS10YWJsZQogICAgOmNvbHVtbnM9ImNvbHVtbnMiCiAgICA6ZGF0YS1zb3VyY2U9ImhhcmR3YXJlSXRlbXMiCiAgICA6cm93S2V5PSIocmVjb3JkKSA9PiByZWNvcmQuZGV2aWNlX2luZm8iCiAgICA6cGFnaW5hdGlvbj0icGFnaW5hdGlvbiIKICA+CiAgICA8dGVtcGxhdGUgI2JvZHlDZWxsPSJ7IGNvbHVtbiwgcmVjb3JkIH0iPgogICAgICA8dGVtcGxhdGUgdi1pZj0iY29sdW1uLmtleSA9PT0gJ2RldmljZV9pbmZvJyI+CiAgICAgICAgPGRpdiBjbGFzcz0idGFibGUtaGFyZHdhcmUtaW5mbyI+CiAgICAgICAgICA8c3Bhbj57eyByZWNvcmQuZGV2aWNlX2luZm8gfX08L3NwYW4+CiAgICAgICAgICA8c3Bhbj57eyByZWNvcmQuZGV2aWNlX3R5cGUgfX08L3NwYW4+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L3RlbXBsYXRlPgogIDwvYS10YWJsZT4KPC9hLWNhcmQ+CjwhLS0gLyBIYXJkd2FyZSBUYWJsZSBDYXJkIC0tPgo="}, null]}