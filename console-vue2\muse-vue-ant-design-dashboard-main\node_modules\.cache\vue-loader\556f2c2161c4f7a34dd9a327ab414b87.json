{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\HardwareInfo.vue", "mtime": 1753169488841}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gJ3Z1ZXgnOw0KaW1wb3J0IGF4aW9zIGZyb20gJ0AvYXBpL2F4aW9zSW5zdGFuY2UnOw0KaW1wb3J0IFJlZnJlc2hCdXR0b24gZnJvbSAnLi4vV2lkZ2V0cy9SZWZyZXNoQnV0dG9uLnZ1ZSc7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIFJlZnJlc2hCdXR0b24NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaGFyZHdhcmVJdGVtczogW10sDQogICAgICBjb2x1bW5zOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ0RldmljZSBJbmZvJywNCiAgICAgICAgICBkYXRhSW5kZXg6ICdkZXZpY2VfaW5mbycsDQogICAgICAgICAga2V5OiAnZGV2aWNlX2luZm8nLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICdEZXZpY2UgVHlwZScsDQogICAgICAgICAgZGF0YUluZGV4OiAnZGV2aWNlX3R5cGUnLA0KICAgICAgICAgIGtleTogJ2RldmljZV90eXBlJywNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBwYWdpbmF0aW9uOiB7DQogICAgICAgIHBhZ2VTaXplOiAxMDAsDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwU3RhdGUoWydzZWxlY3RlZE5vZGVJcCcsICdjdXJyZW50UHJvamVjdCcsICdzaWRlYmFyQ29sb3InXSksDQogIH0sDQogIHdhdGNoOiB7DQogICAgc2VsZWN0ZWROb2RlSXAobmV3SXApIHsNCiAgICAgIHRoaXMuZmV0Y2hIYXJkd2FyZSgpOw0KICAgIH0sDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5mZXRjaEhhcmR3YXJlKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBmZXRjaEhhcmR3YXJlKCkgew0KICAgICAgY29uc29sZS5sb2coJ1NlbGVjdGVkIE5vZGUgSVA6JywgdGhpcy5zZWxlY3RlZE5vZGVJcCk7DQogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWROb2RlSXApIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignTm9kZSBJUCBpcyBub3QgZGVmaW5lZCcpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgL2FwaS9oYXJkd2FyZS8ke3RoaXMuc2VsZWN0ZWROb2RlSXB9YCwgew0KICAgICAgICAgIHBhcmFtczogew0KICAgICAgICAgICAgZGJGaWxlOiB0aGlzLmN1cnJlbnRQcm9qZWN0DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5oYXJkd2FyZUl0ZW1zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGhhcmR3YXJlOicsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["HardwareInfo.vue"], "names": [], "mappings": ";AA6CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HardwareInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <!-- Hardware Table Card -->\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full hardware-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M160 160h192v192H160z\"/>\r\n              <path :fill=\"'currentColor'\" d=\"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.hardware') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchHardware\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"hardwareItems\"\r\n      :rowKey=\"(record) => record.device_info\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #bodyCell=\"{ column, record }\">\r\n        <template v-if=\"column.key === 'device_info'\">\r\n          <div class=\"table-hardware-info\">\r\n            <span>{{ record.device_info }}</span>\r\n            <span>{{ record.device_type }}</span>\r\n          </div>\r\n        </template>\r\n      </template>\r\n    </a-table>\r\n  </a-card>\r\n  <!-- / Hardware Table Card -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      hardwareItems: [],\r\n      columns: [\r\n        {\r\n          title: 'Device Info',\r\n          dataIndex: 'device_info',\r\n          key: 'device_info',\r\n        },\r\n        {\r\n          title: 'Device Type',\r\n          dataIndex: 'device_type',\r\n          key: 'device_type',\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchHardware();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchHardware();\r\n  },\r\n  methods: {\r\n    async fetchHardware() {\r\n      console.log('Selected Node IP:', this.selectedNodeIp);\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/hardware/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.hardwareItems = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching hardware:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.hardware-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.table-hardware-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n</style>\r\n"]}]}