{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\views\\ProjectManager.vue", "mtime": 1753169785200}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "name", "data", "h", "$createElement", "columns", "title", "dataIndex", "key", "width", "ellipsis", "customRender", "text", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "align", "fixed", "record", "click", "selectProject", "confirm", "deleteProject", "projects", "tempProjectName", "methods", "onCustomRow", "on", "fetchProjects", "response", "get", "Array", "isArray", "map", "project", "dbFile", "createdAt", "now", "toString", "console", "error", "$message", "encodedDbFile", "encodeURIComponent", "validationUrl", "valid", "$store", "dispatch", "projectName", "localStorage", "removeItem", "$router", "push", "success", "_error$response", "delete", "createNewProject", "Promise", "resolve", "reject", "$confirm", "content", "e", "value", "target", "replace", "okText", "cancelText", "onOk", "warning", "test", "onCancel", "_response$data", "post", "created"], "sources": ["src/views/ProjectManager.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card title=\"项目列表\">\r\n      <template #extra>\r\n        <a-button type=\"primary\" @click=\"createNewProject\">\r\n          创建新项目\r\n        </a-button>\r\n      </template>\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"projects\"\r\n        :row-key=\"record => record.dbFile\"\r\n        :customRow=\"onCustomRow\"\r\n        class=\"project-table\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProjectManager',\r\n  data() {\r\n    return {\r\n      columns: [\r\n        {\r\n          title: '项目名称',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '250px',\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            return (\r\n            <span>\r\n            <a-icon type=\"folder\" style=\"margin-right: 8px;\" />\r\n            {text}\r\n            </span>\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: '数据库文件',\r\n          dataIndex: 'dbFile',\r\n          key: 'dbFile',\r\n          width: '280px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          dataIndex: 'createdAt',\r\n          key: 'createdAt',\r\n          width: '160px',\r\n          customRender: (text) => {\r\n            return new Date(text).toLocaleString('zh-CN', {\r\n              year: 'numeric',\r\n              month: '2-digit',\r\n              day: '2-digit',\r\n              hour: '2-digit',\r\n              minute: '2-digit'\r\n            });\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: '180px',\r\n          align: 'center',\r\n          fixed: 'right',\r\n          customRender: (text, record) => {\r\n            return (\r\n              <a-space size={8}>\r\n                <a-button\r\n                  type=\"primary\"\r\n                  onClick={() => this.selectProject(record)}\r\n                >\r\n                  进入项目\r\n                </a-button>\r\n                <a-popconfirm\r\n                  title=\"确定要删除这个项目吗？\"\r\n                  onConfirm={() => this.deleteProject(record)}\r\n                  okText=\"确定\"\r\n                  cancelText=\"取消\"\r\n                >\r\n                  <a-button type=\"danger\">删除</a-button>\r\n                </a-popconfirm>\r\n              </a-space>\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      projects: [],\r\n      tempProjectName: ''\r\n    };\r\n  },\r\n  methods: {\r\n    onCustomRow(record) {\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            // 如果需要行点击事件的话，在这里处理\r\n          }\r\n        }\r\n      };\r\n    },\r\n    async fetchProjects() {\r\n      try {\r\n        const response = await axios.get('/api/projects');\r\n        if (Array.isArray(response.data)) {\r\n          this.projects = response.data.map(project => ({\r\n            name: project.name || '',\r\n            dbFile: project.dbFile || '',\r\n            createdAt: project.createdAt || '',\r\n            key: project.dbFile || Date.now().toString()\r\n          }));\r\n        } else {\r\n          this.projects = [];\r\n          console.error('项目数据格式无效：', response.data);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败：', error);\r\n        this.$message.error('获取项目列表失败');\r\n        this.projects = [];\r\n      }\r\n    },\r\n\r\n    async selectProject(project) {\r\n      if (!project?.dbFile) {\r\n        console.error('项目数据无效：', project);\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const encodedDbFile = encodeURIComponent(project.dbFile);\r\n        const validationUrl = `/api/projects/validate/${encodedDbFile}`;\r\n\r\n        const response = await axios.get(validationUrl);\r\n        if (response.data.valid) {\r\n          await this.$store.dispatch('switchProject', { \r\n            dbFile: project.dbFile, \r\n            projectName: project.name \r\n          });\r\n          await this.$store.dispatch('fetchNodes');\r\n\r\n          // 清除所有任务相关的localStorage\r\n          localStorage.removeItem('activeTaskId');\r\n          localStorage.removeItem('taskCompleted');\r\n          localStorage.removeItem('activeUploadTaskId');\r\n          localStorage.removeItem('activeDownloadTaskId');\r\n          localStorage.removeItem('activeToolTaskId');\r\n\r\n          await this.$router.push('/task');\r\n          this.$message.success('成功进入项目');\r\n        } else {\r\n          console.error('验证失败：', response.data.error);\r\n          this.$message.error(response.data.error || '数据库文件无效或已损坏');\r\n        }\r\n      } catch (error) {\r\n        console.error('项目验证出错：', error);\r\n        this.$message.error(error.response?.data?.error || '验证项目失败');\r\n      }\r\n    },\r\n\r\n    async deleteProject(project) {\r\n      if (!project?.dbFile) {\r\n        this.$message.error('无效的项目数据');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await axios.delete(`/api/projects/${encodeURIComponent(project.dbFile)}`);\r\n        this.$message.success('项目删除成功');\r\n        await this.fetchProjects();\r\n      } catch (error) {\r\n        console.error('Error deleting project:', error);\r\n        this.$message.error('删除项目失败');\r\n      }\r\n    },\r\n\r\n    async createNewProject() {\r\n      try {\r\n        const projectName = await new Promise((resolve, reject) => {\r\n          this.$confirm({\r\n            title: '创建新项目',\r\n            content: h => (\r\n              <div>\r\n                <a-input\r\n                  placeholder=\"请输入项目名称\"\r\n                  onChange={(e) => {\r\n                    const value = e.target.value.replace(/[^a-zA-Z0-9_-]/g, '');\r\n                    this.tempProjectName = value;\r\n                    e.target.value = value;\r\n                  }}\r\n                />\r\n                <div class=\"project-hint-text\" style=\"font-size: 12px; margin-top: 8px;\">\r\n                  只允许输入大小写字母、数字、下划线和连字符\r\n                </div>\r\n              </div>\r\n            ),\r\n            okText: '确定',\r\n            cancelText: '取消',\r\n            onOk: () => {\r\n              if (!this.tempProjectName) {\r\n                this.$message.warning('请输入项目名称');\r\n                return Promise.reject();\r\n              }\r\n              // 验证项目名称格式\r\n              if (!/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)) {\r\n                this.$message.warning('项目名称只能包含大小写字母、数字、下划线和连字符');\r\n                return Promise.reject();\r\n              }\r\n              resolve(this.tempProjectName);\r\n            },\r\n            onCancel: () => {\r\n              reject();\r\n            }\r\n          });\r\n        });\r\n\r\n        if (projectName) {\r\n          const response = await axios.post('/api/projects/new', {\r\n            name: projectName\r\n          });\r\n\r\n          await this.fetchProjects();\r\n          this.$message.success('新项目创建成功');\r\n          if (response.data?.dbFile) {\r\n            await this.selectProject(response.data);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error) { // 用户取消操作时不显示错误\r\n          console.error('Error creating new project:', error);\r\n          this.$message.error('创建新项目失败');\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjects();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;AAoBA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IAAA,MAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA;UACA,OAAAT,CAAA,UAAAA,CAAA;YAAA;cAAA,QAEA;YAAA;YAAA;UAAA,IACAS,IAAA;QAGA;MACA,GACA;QACAN,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAE,YAAA,EAAAC,IAAA;UACA,WAAAC,IAAA,CAAAD,IAAA,EAAAE,cAAA;YACAC,IAAA;YACAC,KAAA;YACAC,GAAA;YACAC,IAAA;YACAC,MAAA;UACA;QACA;MACA,GACA;QACAb,KAAA;QACAE,GAAA;QACAC,KAAA;QACAW,KAAA;QACAC,KAAA;QACAV,YAAA,EAAAA,CAAAC,IAAA,EAAAU,MAAA;UACA,OAAAnB,CAAA;YAAA;cAAA,QACA;YAAA;UAAA,IAAAA,CAAA;YAAA;cAAA,QAEA;YAAA;YAAA;cAAA,SACAoB,CAAA,UAAAC,aAAA,CAAAF,MAAA;YAAA;UAAA,kCAAAnB,CAAA;YAAA;cAAA,SAKA;cAAA,UAEA;cAAA,cACA;YAAA;YAAA;cAAA,WAFAsB,CAAA,UAAAC,aAAA,CAAAJ,MAAA;YAAA;UAAA,IAAAnB,CAAA;YAAA;cAAA,QAIA;YAAA;UAAA;QAIA;MACA,EACA;MACAwB,QAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAAR,MAAA;MACA;QACAS,EAAA;UACAR,KAAA,EAAAA,CAAA;YACA;UAAA;QAEA;MACA;IACA;IACA,MAAAS,cAAA;MACA;QACA,MAAAC,QAAA,SAAAjC,KAAA,CAAAkC,GAAA;QACA,IAAAC,KAAA,CAAAC,OAAA,CAAAH,QAAA,CAAA/B,IAAA;UACA,KAAAyB,QAAA,GAAAM,QAAA,CAAA/B,IAAA,CAAAmC,GAAA,CAAAC,OAAA;YACArC,IAAA,EAAAqC,OAAA,CAAArC,IAAA;YACAsC,MAAA,EAAAD,OAAA,CAAAC,MAAA;YACAC,SAAA,EAAAF,OAAA,CAAAE,SAAA;YACAhC,GAAA,EAAA8B,OAAA,CAAAC,MAAA,IAAA1B,IAAA,CAAA4B,GAAA,GAAAC,QAAA;UACA;QACA;UACA,KAAAf,QAAA;UACAgB,OAAA,CAAAC,KAAA,cAAAX,QAAA,CAAA/B,IAAA;QACA;MACA,SAAA0C,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACA,KAAAjB,QAAA;MACA;IACA;IAEA,MAAAH,cAAAc,OAAA;MACA,MAAAA,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAAC,MAAA;QACAI,OAAA,CAAAC,KAAA,YAAAN,OAAA;QACA,KAAAO,QAAA,CAAAD,KAAA;QACA;MACA;MAEA;QACA,MAAAE,aAAA,GAAAC,kBAAA,CAAAT,OAAA,CAAAC,MAAA;QACA,MAAAS,aAAA,6BAAAF,aAAA;QAEA,MAAAb,QAAA,SAAAjC,KAAA,CAAAkC,GAAA,CAAAc,aAAA;QACA,IAAAf,QAAA,CAAA/B,IAAA,CAAA+C,KAAA;UACA,WAAAC,MAAA,CAAAC,QAAA;YACAZ,MAAA,EAAAD,OAAA,CAAAC,MAAA;YACAa,WAAA,EAAAd,OAAA,CAAArC;UACA;UACA,WAAAiD,MAAA,CAAAC,QAAA;;UAEA;UACAE,YAAA,CAAAC,UAAA;UACAD,YAAA,CAAAC,UAAA;UACAD,YAAA,CAAAC,UAAA;UACAD,YAAA,CAAAC,UAAA;UACAD,YAAA,CAAAC,UAAA;UAEA,WAAAC,OAAA,CAAAC,IAAA;UACA,KAAAX,QAAA,CAAAY,OAAA;QACA;UACAd,OAAA,CAAAC,KAAA,UAAAX,QAAA,CAAA/B,IAAA,CAAA0C,KAAA;UACA,KAAAC,QAAA,CAAAD,KAAA,CAAAX,QAAA,CAAA/B,IAAA,CAAA0C,KAAA;QACA;MACA,SAAAA,KAAA;QAAA,IAAAc,eAAA;QACAf,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA,GAAAc,eAAA,GAAAd,KAAA,CAAAX,QAAA,cAAAyB,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAAxD,IAAA,cAAAwD,eAAA,uBAAAA,eAAA,CAAAd,KAAA;MACA;IACA;IAEA,MAAAlB,cAAAY,OAAA;MACA,MAAAA,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAAC,MAAA;QACA,KAAAM,QAAA,CAAAD,KAAA;QACA;MACA;MAEA;QACA,MAAA5C,KAAA,CAAA2D,MAAA,kBAAAZ,kBAAA,CAAAT,OAAA,CAAAC,MAAA;QACA,KAAAM,QAAA,CAAAY,OAAA;QACA,WAAAzB,aAAA;MACA,SAAAY,KAAA;QACAD,OAAA,CAAAC,KAAA,4BAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEA,MAAAgB,iBAAA;MAAA,MAAAzD,CAAA,QAAAC,cAAA;MACA;QACA,MAAAgD,WAAA,aAAAS,OAAA,EAAAC,OAAA,EAAAC,MAAA;UACA,KAAAC,QAAA;YACA1D,KAAA;YACA2D,OAAA,EAAA9D,CAAA,IAAAA,CAAA,SAAAA,CAAA;cAAA;gBAAA,eAGA;cAAA;cAAA;gBAAA,UACA+D,CAAA;kBACA,MAAAC,KAAA,GAAAD,CAAA,CAAAE,MAAA,CAAAD,KAAA,CAAAE,OAAA;kBACA,KAAAzC,eAAA,GAAAuC,KAAA;kBACAD,CAAA,CAAAE,MAAA,CAAAD,KAAA,GAAAA,KAAA;gBACA;cAAA;YAAA,IAAAhE,CAAA;cAAA,SAEA;cAAA;YAAA,wIAIA;YACAmE,MAAA;YACAC,UAAA;YACAC,IAAA,EAAAA,CAAA;cACA,UAAA5C,eAAA;gBACA,KAAAiB,QAAA,CAAA4B,OAAA;gBACA,OAAAZ,OAAA,CAAAE,MAAA;cACA;cACA;cACA,wBAAAW,IAAA,MAAA9C,eAAA;gBACA,KAAAiB,QAAA,CAAA4B,OAAA;gBACA,OAAAZ,OAAA,CAAAE,MAAA;cACA;cACAD,OAAA,MAAAlC,eAAA;YACA;YACA+C,QAAA,EAAAA,CAAA;cACAZ,MAAA;YACA;UACA;QACA;QAEA,IAAAX,WAAA;UAAA,IAAAwB,cAAA;UACA,MAAA3C,QAAA,SAAAjC,KAAA,CAAA6E,IAAA;YACA5E,IAAA,EAAAmD;UACA;UAEA,WAAApB,aAAA;UACA,KAAAa,QAAA,CAAAY,OAAA;UACA,KAAAmB,cAAA,GAAA3C,QAAA,CAAA/B,IAAA,cAAA0E,cAAA,eAAAA,cAAA,CAAArC,MAAA;YACA,WAAAf,aAAA,CAAAS,QAAA,CAAA/B,IAAA;UACA;QACA;MACA,SAAA0C,KAAA;QACA,IAAAA,KAAA;UAAA;UACAD,OAAA,CAAAC,KAAA,gCAAAA,KAAA;UACA,KAAAC,QAAA,CAAAD,KAAA;QACA;MACA;IACA;EACA;EACAkC,QAAA;IACA,KAAA9C,aAAA;EACA;AACA", "ignoreList": []}]}