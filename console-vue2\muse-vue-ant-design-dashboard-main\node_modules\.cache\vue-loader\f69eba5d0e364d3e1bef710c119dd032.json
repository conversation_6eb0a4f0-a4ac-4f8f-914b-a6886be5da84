{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue?vue&type=style&index=0&id=1cc90bf5&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\FilesystemInfo.vue", "mtime": 1753169975750}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZmlsZXN5c3RlbS1jYXJkIHsNCiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNik7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcG9zaXRpb246IHJlbGF0aXZlOyAvKiDnoa7kv53ljaHniYfmnInnm7jlr7nlrprkvY3vvIzpmLLmraLlhoXpg6jlhYPntKDmuqLlh7ogKi8NCiAgei1pbmRleDogMTsgLyog6K6+572u5ZCI6YCC55qEei1pbmRleO+8jOmYsuatouS4juWFtuS7luWFg+e0oOWGsueqgSAqLw0KfQ0KDQouYW50LXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogMCAwIDhweCA4cHg7IC8qIOWMuemFjeWNoeeJh+WchuinkiAqLw0KfQ0KDQovKiDkvJjljJbooajmoLzmu5rliqjooYzkuLogKi8NCi8vOjp2LWRlZXAgLmFudC10YWJsZS1ib2R5IHsNCi8vICBvdmVyZmxvdy14OiBhdXRvICFpbXBvcnRhbnQ7DQovL30NCg0KLyog56Gu5L+d6KGo5qC85YaF5a655LiN5Lya5rqi5Ye6ICovDQovLzo6di1kZWVwIC5hbnQtdGFibGUtcm93IHRkIHsNCi8vICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KLy8gIHdoaXRlLXNwYWNlOiBub3JtYWw7DQovL30NCg0KLyog5LyY5YyW6KGo5qC85Zyo56e75Yqo6K6+5aSH5LiK55qE5pi+56S6ICovDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgOjp2LWRlZXAgLmFudC1wYWdpbmF0aW9uIHsNCiAgICBmb250LXNpemU6IDEycHg7DQogIH0NCg0KICA6OnYtZGVlcCAuYW50LXBhZ2luYXRpb24tb3B0aW9ucyB7DQogICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["FilesystemInfo.vue"], "names": [], "mappings": ";AAiIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FilesystemInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full filesystem-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" fill-rule=\"evenodd\" d=\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\" clip-rule=\"evenodd\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.mount') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchFilesystem\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :dataSource=\"filesystemItems\"\r\n      :rowKey=\"(record) => record.key || record.device\"\r\n      :pagination=\"pagination\"\r\n      :loading=\"loading\"\r\n    >\r\n    </a-table>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  name: 'FilesystemInfo',\r\n  data() {\r\n    return {\r\n      filesystemItems: [],\r\n      loading: false,\r\n      columns: [\r\n        {\r\n          title: 'Device',\r\n          dataIndex: 'device',\r\n          key: 'device',\r\n          width: '20%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Point',\r\n          dataIndex: 'mount_point',\r\n          key: 'mount_point',\r\n          width: '25%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'File System Type',\r\n          dataIndex: 'fs_type',\r\n          key: 'fs_type',\r\n          width: '15%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Options',\r\n          dataIndex: 'mount_options',\r\n          key: 'mount_options',\r\n          width: '40%',\r\n          ellipsis: true,\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchFilesystem();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchFilesystem();\r\n  },\r\n  methods: {\r\n    async fetchFilesystem() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.filesystemItems = [];\r\n        return;\r\n      }\r\n      try {\r\n        // 显示加载状态\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/filesystem/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n\r\n        // 处理数据，确保每条记录有唯一的key\r\n        this.filesystemItems = response.data.map((item, index) => ({\r\n          ...item,\r\n          // 使用组合键作为唯一标识，防止device重复导致的渲染问题\r\n          key: `${item.device}_${item.mount_point}_${index}`\r\n        }));\r\n      } catch (error) {\r\n        console.error('Error fetching filesystem:', error);\r\n        this.filesystemItems = [];\r\n      } finally {\r\n        // 无论成功失败，都关闭加载状态\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.filesystem-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n  position: relative; /* 确保卡片有相对定位，防止内部元素溢出 */\r\n  z-index: 1; /* 设置合适的z-index，防止与其他元素冲突 */\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px; /* 匹配卡片圆角 */\r\n}\r\n\r\n/* 优化表格滚动行为 */\r\n//::v-deep .ant-table-body {\r\n//  overflow-x: auto !important;\r\n//}\r\n\r\n/* 确保表格内容不会溢出 */\r\n//::v-deep .ant-table-row td {\r\n//  word-break: break-word;\r\n//  white-space: normal;\r\n//}\r\n\r\n/* 优化表格在移动设备上的显示 */\r\n@media (max-width: 768px) {\r\n  ::v-deep .ant-pagination {\r\n    font-size: 12px;\r\n  }\r\n\r\n  ::v-deep .ant-pagination-options {\r\n    margin-left: 8px;\r\n  }\r\n}\r\n</style>\r\n"]}]}