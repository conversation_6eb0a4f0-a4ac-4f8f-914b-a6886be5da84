(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7dbc10af"],{"0607":function(e,t,s){"use strict";s("8ab1")},"2e71":function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{visible:e.visible,title:e.$t("testcase.detail"),width:"800px",footer:null},on:{cancel:e.handleClose}},[e.currentTestcase?t("a-descriptions",{attrs:{bordered:""}},[t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.number"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_Number)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.name"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_Name)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.level"),span:3}},[t("a-tag",{attrs:{color:e.getLevelColor(e.currentTestcase.Testcase_Level)}},[e._v(" "+e._s(e.currentTestcase.Testcase_Level)+" ")])],1),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.prepareCondition"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_PrepareCondition)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.testSteps"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_TestSteps)+" ")])]),t("a-descriptions-item",{attrs:{label:e.$t("caseColumn.expectedResult"),span:3}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.currentTestcase.Testcase_ExpectedResult)+" ")])])],1):e._e()],1)},l=[],r=s("fec3"),c={name:"TestCaseDetailModal",props:{visible:{type:Boolean,default:!1},testcase:{type:Object,default:null},fetchDetails:{type:Boolean,default:!1}},data(){return{loading:!1,detailedTestcase:null}},watch:{visible(e){e&&this.fetchDetails&&this.testcase&&!this.detailedTestcase&&this.fetchTestcaseDetails()},testcase(){this.detailedTestcase=null}},computed:{currentTestcase(){return this.detailedTestcase||this.testcase}},methods:{async fetchTestcaseDetails(){if(this.testcase&&this.testcase.Testcase_Number){this.loading=!0;try{const e=await r["a"].get("/api/testcase/"+this.testcase.Testcase_Number);this.detailedTestcase={...e.data,similarity:this.testcase.similarity}}catch(e){console.error("获取测试用例详情失败:",e),this.$message.error("获取测试用例详情失败")}finally{this.loading=!1}}},handleClose(){this.$emit("close"),this.detailedTestcase=null},getLevelColor(e){const t={"level 0":"red","level 1":"orange","level 2":"green","level 3":"blue","level 4":"purple",P0:"red",P1:"orange",P2:"blue",P3:"green",P4:"gray"};return t[e]||"default"},getSimilarityColor(e){return e>=.8?"#52c41a":e>=.6?"#faad14":"#f5222d"}}},o=c,n=(s("867c"),s("2877")),i=Object(n["a"])(o,a,l,!1,null,"59bfc3d1",null);t["a"]=i.exports},"867c":function(e,t,s){"use strict";s("ab7a")},"8ab1":function(e,t,s){},ab7a:function(e,t,s){},c570:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("TestCaseInfo")],1)],1)],1)},l=[],r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"layout-content"},[t("a-card",{staticClass:"criclebox",attrs:{bordered:!1},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",height:"20",width:"20"}},[t("path",{attrs:{fill:"currentColor",d:"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.testcase")))])]),t("div",[t("RefreshButton",{on:{refresh:function(t){return e.fetchTestcases(e.currentPage)}}})],1)])]},proxy:!0}])},[t("div",{staticClass:"search-form"},[t("a-form",{attrs:{layout:"inline"},on:{submit:function(t){return t.preventDefault(),e.handleSearch.apply(null,arguments)}}},[t("a-form-item",{attrs:{label:e.$t("caseColumn.name")}},[t("a-input",{attrs:{placeholder:e.$t("caseColumn.name"),allowClear:""},model:{value:e.searchForm.name,callback:function(t){e.$set(e.searchForm,"name",t)},expression:"searchForm.name"}})],1),t("a-form-item",{attrs:{label:e.$t("caseColumn.level")}},[t("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:e.$t("caseColumn.level"),allowClear:""},model:{value:e.searchForm.level,callback:function(t){e.$set(e.searchForm,"level",t)},expression:"searchForm.level"}},[t("a-select-option",{attrs:{value:"level 0"}},[e._v("Level 0")]),t("a-select-option",{attrs:{value:"level 1"}},[e._v("Level 1")]),t("a-select-option",{attrs:{value:"level 2"}},[e._v("Level 2")]),t("a-select-option",{attrs:{value:"level 3"}},[e._v("Level 3")]),t("a-select-option",{attrs:{value:"level 4"}},[e._v("Level 4")])],1)],1),t("a-form-item",{attrs:{label:e.$t("caseColumn.prepareCondition")}},[t("a-input",{attrs:{placeholder:e.$t("caseColumn.prepareCondition"),allowClear:""},model:{value:e.searchForm.prepare_condition,callback:function(t){e.$set(e.searchForm,"prepare_condition",t)},expression:"searchForm.prepare_condition"}})],1),t("a-form-item",{attrs:{label:e.$t("caseColumn.testSteps")}},[t("a-input",{attrs:{placeholder:e.$t("caseColumn.testSteps"),allowClear:""},model:{value:e.searchForm.test_steps,callback:function(t){e.$set(e.searchForm,"test_steps",t)},expression:"searchForm.test_steps"}})],1),t("a-form-item",{attrs:{label:e.$t("caseColumn.expectedResult")}},[t("a-input",{attrs:{placeholder:e.$t("caseColumn.expectedResult"),allowClear:""},model:{value:e.searchForm.expected_result,callback:function(t){e.$set(e.searchForm,"expected_result",t)},expression:"searchForm.expected_result"}})],1),t("a-form-item",[t("a-button",{class:"bg-"+e.sidebarColor,staticStyle:{color:"white"},attrs:{"html-type":"submit",loading:e.loading}},[t("a-icon",{attrs:{type:"search"}}),e._v(" "+e._s(e.$t("testcase.searchButton"))+" ")],1),t("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.resetSearch}},[t("a-icon",{attrs:{type:"reload"}}),e._v(" "+e._s(e.$t("testcase.resetButton"))+" ")],1)],1)],1),e.testcases.length>0?t("div",{staticClass:"search-result-count"},[t("a-tag",{attrs:{color:"blue"}},[e._v("Found: "+e._s(e.total)+" test cases")])],1):e._e()],1),t("a-table",{attrs:{columns:e.columns,"data-source":e.testcases,loading:e.loading,pagination:{total:e.total,pageSize:100,current:e.currentPage,showSizeChanger:!1,showQuickJumper:!0,onChange:e.handlePageChange},scroll:{x:1500}},scopedSlots:e._u([{key:"Testcase_LastResult",fn:function({text:s}){return[t("a-tag",{attrs:{color:e.getResultColor(s)}},[e._v(" "+e._s(s||"N/A")+" ")])]}},{key:"Testcase_Level",fn:function({text:s}){return[t("a-tag",{attrs:{color:e.getLevelColor(s)}},[e._v(" "+e._s(s||"N/A")+" ")])]}},{key:"lastModified",fn:function({text:t}){return[e._v(" "+e._s(e.formatDate(t))+" ")]}},{key:"action",fn:function({record:s}){return[t("a-space",[t("a-button",{attrs:{type:"link"},on:{click:function(t){return e.viewDetails(s)}}},[e._v(" View Details ")])],1)]}}])}),t("TestCaseDetailModal",{attrs:{visible:e.detailsVisible,testcase:e.selectedTestcase},on:{close:function(t){e.detailsVisible=!1}}})],1)],1)},c=[],o=s("fec3"),n=s("c1df"),i=s.n(n),u=s("2f62"),d=s("f188"),p=s("2e71"),h={components:{RefreshButton:d["a"],TestCaseDetailModal:p["a"]},name:"TestCases",data(){return{loading:!1,testcases:[],total:0,currentPage:1,detailsVisible:!1,selectedTestcase:null,searchForm:{name:"",level:void 0,prepare_condition:"",test_steps:"",expected_result:""}}},created(){this.fetchTestcases()},computed:{...Object(u["e"])(["selectedNodeIp","currentProject","sidebarColor"]),columns(){const e=this.$createElement;return[{title:"#",dataIndex:"index",key:"index",width:100,align:"center",customRender:(e,t,s)=>100*(this.currentPage-1)+s+1},{title:this.$t("caseColumn.number"),dataIndex:"Testcase_Number",key:"Testcase_Number",width:130,ellipsis:!0,customRender:(t,s)=>e("a",{on:{click:()=>this.viewDetails(s)},style:"color: #1890ff; cursor: pointer;"},[t])},{title:this.$t("caseColumn.name"),dataIndex:"Testcase_Name",key:"Testcase_Name",width:200},{title:this.$t("caseColumn.level"),dataIndex:"Testcase_Level",key:"Testcase_Level",width:100,slots:{customRender:"Testcase_Level"}},{title:this.$t("caseColumn.prepareCondition"),dataIndex:"Testcase_PrepareCondition",key:"Testcase_PrepareCondition",width:250,ellipsis:!0},{title:this.$t("caseColumn.testSteps"),dataIndex:"Testcase_TestSteps",key:"Testcase_TestSteps",width:400,ellipsis:!0},{title:this.$t("caseColumn.expectedResult"),dataIndex:"Testcase_ExpectedResult",key:"Testcase_ExpectedResult",width:400,ellipsis:!0}]}},methods:{async fetchTestcases(e=1){this.loading=!0;try{const t={page:e,page_size:100};this.searchForm.name&&(t.name=this.searchForm.name),this.searchForm.level&&(t.level=this.searchForm.level),this.searchForm.prepare_condition&&(t.prepare_condition=this.searchForm.prepare_condition),this.searchForm.test_steps&&(t.test_steps=this.searchForm.test_steps),this.searchForm.expected_result&&(t.expected_result=this.searchForm.expected_result);const s=await o["a"].get("/api/testcase/",{params:t});this.testcases=s.data.data,this.total=s.data.total}catch(t){console.error("Error fetching testcases:",t),this.$message.error("Failed to load test cases")}finally{this.loading=!1}},handleSearch(){this.currentPage=1,this.fetchTestcases(1)},resetSearch(){this.searchForm={name:"",level:void 0,prepare_condition:"",test_steps:"",expected_result:""},this.currentPage=1,this.fetchTestcases(1)},formatDate(e){return e?i()(e).format("YYYY-MM-DD HH:mm"):"N/A"},getResultColor(e){const t={PASS:"success",FAIL:"error",BLOCKED:"warning","NOT RUN":"default"};return t[e]||"default"},getLevelColor(e){const t={"level 0":"red","level 1":"orange","level 2":"green","level 3":"blue","level 4":"purple"};return t[e]||"default"},viewDetails(e){this.selectedTestcase=e,this.detailsVisible=!0},handlePageChange(e){this.currentPage=e,this.fetchTestcases(e)}}},m=h,v=(s("0607"),s("2877")),f=Object(v["a"])(m,r,c,!1,null,"40c4ae3a",null),_=f.exports,C={components:{TestCaseInfo:_}},b=C,g=Object(v["a"])(b,a,l,!1,null,null,null);t["default"]=g.exports},f188:function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},l=[],r=s("2f62"),c={computed:{...Object(r["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},o=c,n=s("2877"),i=Object(n["a"])(o,a,l,!1,null,"80cb1374",null);t["a"]=i.exports}}]);
//# sourceMappingURL=chunk-7dbc10af.8df0027e.js.map