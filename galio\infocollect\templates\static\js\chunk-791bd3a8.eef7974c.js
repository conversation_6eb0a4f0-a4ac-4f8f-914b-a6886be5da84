(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-791bd3a8"],{"13d5":function(e,t,s){"use strict";var a=s("23e7"),o=s("d58f").left,r=s("a640"),l=s("2d00"),i=s("605d"),n=r("reduce"),c=!i&&l>79&&l<83;a({target:"Array",proto:!0,forced:!n||c},{reduce:function(e){return o(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"1c49":function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"compact-card",staticStyle:{"margin-top":"16px"},attrs:{title:e.title}},[void 0!==e.overallProgress?t("template",{slot:"extra"},[t("span",[e._v("Overall Progress: "+e._s(e.overallProgress)+"%")])]):e._e(),t("a-progress",{staticStyle:{"margin-bottom":"16px"},attrs:{percent:e.overallProgress,status:e.progressBarStatus}}),t("a-table",{attrs:{dataSource:e.progressData,columns:e.progressColumns,rowKey:"ip",pagination:!1},scopedSlots:e._u([{key:"errorDetail",fn:function(s,a){return[a&&a.error_detail?t("a-popover",{attrs:{placement:"topLeft"}},[t("template",{slot:"content"},[t("p",[e._v("Time: "+e._s(a.error_detail.time))]),t("p",[e._v("Type: "+e._s(a.error_detail.type))]),t("p",[e._v("Message: "+e._s(a.error_detail.message))])]),t("a-icon",{staticStyle:{color:"#ff4d4f"},attrs:{type:"info-circle"}})],2):e._e()]}}])})],2)},o=[],r=(s("13d5"),s("0643"),s("76d6"),s("a573"),s("9d4a"),s("9a9a"),{name:"ProgressDisplay",props:{title:{type:String,required:!0},taskType:{type:String,required:!0},activeTask:{type:Object,default:null},formatBytes:{type:Function,required:!0}},computed:{progressColumns(){const e=this.$createElement,t=[{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip",width:"120px"},{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name",width:"150px",ellipsis:!0},{title:this.$t("tool.columns.status"),dataIndex:"status",key:"status",width:"100px",customRender:t=>{const s={pending:"#1890ff",in_progress:"#1890ff",paused:"#faad14",success:"#52c41a",completed:"#52c41a",failed:"#f5222d",downloading:"#1890ff"},a=s[t]||"#000";return e("span",{style:{color:a}},[t])}},{title:this.$t("tool.columns.progress"),dataIndex:"progress",key:"progress",width:"200px",customRender:(t,s)=>e("div",[e("a-progress",{attrs:{percent:t||0,size:"small",status:"failed"===s.status?"exception":"paused"===s.status?"normal":void 0}}),e("div",{style:"font-size: 12px; color: #999"},[this.formatBytes(s.bytes_transferred)," / ",this.formatBytes(s.file_size)])])},{title:this.$t("tool.columns.speed"),dataIndex:"speed",key:"speed",width:"100px"},{title:this.$t("tool.columns.fileSize"),dataIndex:"file_size",key:"file_size",width:"100px",customRender:e=>this.formatBytes(e)},{title:this.$t("tool.columns.errorDetails"),dataIndex:"error_detail",key:"error_detail",width:"60px",scopedSlots:{customRender:"errorDetail"}}];return t},progressData(){return this.activeTask&&this.activeTask.nodes?Object.keys(this.activeTask.nodes).map(e=>{const t=this.activeTask.nodes[e];return{ip:e,host_name:t.host_name,status:t.status,progress:t.progress||0,speed:t.speed||"-",bytes_transferred:t.bytes_transferred,error_detail:t.error_detail,file_size:t.file_size}}):[]},overallProgress(){if(!this.activeTask||!this.activeTask.nodes)return 0;const e=Object.values(this.activeTask.nodes);if(0===e.length)return 0;const t=e.reduce((e,t)=>e+(t.progress||0),0);return Math.round(t/e.length)},progressBarStatus(){if(!this.activeTask||!this.activeTask.nodes)return"normal";const e=Object.values(this.activeTask.nodes||{});return 0===e.length?"normal":e.some(e=>"failed"===e.status)?"exception":e.every(e=>["success","completed"].includes(e.status))?"success":"active"}}}),l=r,i=(s("52ba"),s("2877")),n=Object(i["a"])(l,a,o,!1,null,"258e2dfa",null);t["a"]=n.exports},"34be":function(e,t,s){},"4f4d":function(e,t,s){"use strict";s("0643"),s("2382");t["a"]={methods:{addTaskCompletionNotification({taskId:e,taskType:t,nodes:s,projectId:a,titles:o={},templates:r={},statusMapping:l={}}){const i=`${t}Notified_${a}_${e}`;if(localStorage.getItem(i))return;const n={success:["success","completed"],failure:["failed"]},c={success:[...l.success||[],...n.success],failure:[...l.failure||[],...n.failure]},d=s.filter(e=>c.success.includes(e.status)&&!e.error_detail).length,u=s.filter(e=>c.failure.includes(e.status)||e.error_detail).length,h=u>0;let p,f;p=h?o.error||this.getDefaultErrorTitle(t):o.success||this.getDefaultSuccessTitle(t),f=h?r.error||`${d} nodes completed successfully, ${u} nodes failed.`:r.success||`All ${s.length} nodes completed successfully.`,this.addNotification({title:p,message:f,type:h?"error":"success",taskId:e}),localStorage.setItem(i,"true")},getDefaultSuccessTitle(e){const t={task:"Task Completed",upload:"File Upload Completed",download:"File Download Completed",tool:"Tool Execution Completed"};return t[e]||"Operation Completed"},getDefaultErrorTitle(e){const t={task:"Task Completed with Errors",upload:"File Upload Completed with Errors",download:"File Download Completed with Errors",tool:"Tool Execution Completed with Errors"};return t[e]||"Operation Completed with Errors"},clearTaskNotificationMark(e,t,s){const a=`${t}Notified_${s}_${e}`;localStorage.removeItem(a)}}}},"52ba":function(e,t,s){"use strict";s("34be")},"5a3b":function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"proxy-selector"},[t("a-button",{staticClass:"nav-style-button",attrs:{loading:e.isDetecting,disabled:e.disabled},on:{click:e.fetchReachableIps}},[e._v(" "+e._s(e.$t("common.detectReachableIps")||"检测可达IP")+" ")]),e.reachableIps.length?t("a-select",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{placeholder:e.$t("tool.selectReachableIp")||"选择可达IP",disabled:e.disabled},model:{value:e.selectedIpValue,callback:function(t){e.selectedIpValue=t},expression:"selectedIpValue"}},e._l(e.reachableIps,(function(s){return t("a-select-option",{key:s,attrs:{value:s}},[e._v(" "+e._s(s)+" ")])})),1):e._e()],1)},o=[],r=s("fec3"),l={name:"ProxySelector",props:{disabled:{type:Boolean,default:!1},value:{type:String,default:null}},data(){return{isDetecting:!1,reachableIps:[],selectedIpValue:this.value}},computed:{},watch:{value(e){this.selectedIpValue=e},selectedIpValue(e){this.$emit("input",e),this.$emit("change",e)}},methods:{async fetchReachableIps(){this.isDetecting=!0;try{const e=await r["a"].get("/api/proxy/detect");this.reachableIps=e.data.reachable_ips,this.reachableIps.length&&(this.selectedIpValue=this.reachableIps[0])}catch(e){console.error("Error detecting reachable IPs:",e),this.$notify.error({title:"Error",message:"Failed to detect reachable IPs"})}finally{this.isDetecting=!1}}}},i=l,n=s("2877"),c=Object(n["a"])(i,a,o,!1,null,"46f0b65a",null);t["a"]=c.exports},"605d":function(e,t,s){var a=s("c6b6"),o=s("da84");e.exports="process"==a(o.process)},"76d6":function(e,t,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{every:function(e){return l(this),r(e),!o(this,(function(t,s){if(!e(t))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9a9a":function(e,t,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{some:function(e){return l(this),r(e),o(this,(function(t,s){if(e(t))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9d4a":function(e,t,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{reduce:function(e){l(this),r(e);var t=arguments.length<2,s=t?void 0:arguments[1];if(o(this,(function(a){t?(t=!1,s=a):s=e(s,a)}),{IS_ITERATOR:!0}),t)throw TypeError("Reduce of empty iterator with no initial value");return s}})},a640:function(e,t,s){"use strict";var a=s("d039");e.exports=function(e,t){var s=[][e];return!!s&&a((function(){s.call(null,t||function(){throw 1},1)}))}},b9ec:function(e,t,s){},d58f:function(e,t,s){var a=s("1c0b"),o=s("7b0b"),r=s("44ad"),l=s("50c4"),i=function(e){return function(t,s,i,n){a(s);var c=o(t),d=r(c),u=l(c.length),h=e?u-1:0,p=e?-1:1;if(i<2)while(1){if(h in d){n=d[h],h+=p;break}if(h+=p,e?h<0:u<=h)throw TypeError("Reduce of empty array with no initial value")}for(;e?h>=0:u>h;h+=p)h in d&&(n=s(n,d[h],h,c));return n}};e.exports={left:i(!1),right:i(!0)}},dc7d:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{padding:"2px"}},[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[t("path",{attrs:{fill:"currentColor",d:"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.fileDownload")))])]),t("a-button",{staticClass:"nav-style-button",attrs:{loading:e.downloading},on:{click:e.handleDownload}},[e._v(" "+e._s(e.$t("fileDownload.startDownload"))+" ")])],1),t("div",{staticClass:"main-content"},[t("div",{staticClass:"left-section"},[t("a-card",{staticClass:"compact-card",attrs:{size:"small",title:e.$t("common.configureProxy")}},[t("proxy-selector",{attrs:{disabled:e.downloading},on:{change:e.handleProxyChange},model:{value:e.selectedProxyIp,callback:function(t){e.selectedProxyIp=t},expression:"selectedProxyIp"}}),t("p",{staticStyle:{"margin-top":"16px",color:"gray","font-size":"12px"}},[e._v(" Default download path: \\infocollect\\cache\\download\\ ")])],1)],1),t("div",{staticClass:"right-section config-table"},[t("a-card",{staticClass:"compact-card",attrs:{size:"small",title:e.$t("common.configureNodes")}},[t("a-table",{staticClass:"bordered-nodes-table",attrs:{dataSource:e.nodesWithPath,columns:e.columns,rowKey:"ip",pagination:{pageSize:10,total:e.nodes.length,showSizeChanger:!1},rowSelection:e.rowSelection},scopedSlots:e._u([{key:"remotePath",fn:function(s,a){return[t("a-input",{attrs:{placeholder:e.$t("fileDownload.enterDownloadPath")},on:{change:t=>e.updateRemotePath(a.ip,t.target.value)},model:{value:a.remotePath,callback:function(t){e.$set(a,"remotePath",t)},expression:"record.remotePath"}})]}}])})],1)],1)]),t("FileTransferProgress",{attrs:{title:e.$t("fileDownload.downloadProgress"),taskType:"download",activeTask:e.activeDownloadTask,formatBytes:e.formatBytes}})],1)},o=[],r=(s("0643"),s("76d6"),s("2382"),s("a573"),s("2f62")),l=s("fec3"),i=s("4f4d"),n=s("5a3b"),c=s("1c49"),d={name:"FileDownload",mixins:[i["a"]],components:{ProxySelector:n["a"],FileTransferProgress:c["a"]},data(){return{selectedNodes:[],nodeRemotePaths:{},downloading:!1,selectedProxyIp:null,pollInterval:null}},computed:{...Object(r["e"])(["nodes","activeDownloadTask","currentProject","sidebarColor"]),activeTask:{get(){return this.activeDownloadTask},set(e){this.$store.dispatch("updateDownloadTask",e)}},columns(){return[{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name"},{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip"},{title:"Remote Path",dataIndex:"remotePath",scopedSlots:{customRender:"remotePath"}}]},nodesWithPath(){return this.nodes.map(e=>({...e,remotePath:this.nodeRemotePaths[e.ip]||""}))},rowSelection(){return{selectedRowKeys:this.selectedNodes,onChange:e=>{this.selectedNodes=e}}}},created(){if(!this.checkDatabaseStatus())return;this.$store.dispatch("fetchNodes");const e=localStorage.getItem("downloadTask_"+this.currentProject);if(e){const{projectFile:t}=JSON.parse(e);t===this.currentProject?this.checkActiveDownloadTask():(localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject),this.$store.dispatch("updateDownloadTask",null))}},methods:{...Object(r["b"])(["addNotification"]),checkDatabaseStatus(){return!!this.currentProject||(this.$notify.error({title:"Database Error",message:"No project database selected. Please select a project first."}),this.$router.push("/projects"),!1)},handleProxyChange(e){console.log("Proxy IP changed:",e),this.selectedProxyIp=e},validatePath(e){return e.startsWith("/")?e.includes("..")||e.includes("./")||e.includes("~")?"Path cannot contain ./, ../ or ~":null:"Path must be absolute (start with /)"},updateRemotePath(e,t){const s=this.validatePath(t);if(s)return this.$message.error(s),void this.$set(this.nodeRemotePaths,e,"");this.$set(this.nodeRemotePaths,e,t)},validatePaths(){const e=this.selectedNodes.map(e=>({ip:e,path:this.nodeRemotePaths[e]||""})).filter(e=>e.path);if(!e.length)return this.$message.error("Please enter remote paths for selected nodes"),null;for(const{ip:t,path:s}of e){const e=this.validatePath(s);if(e)return this.$message.error(`Invalid path for ${t}: ${e}`),null}return e},formatBytes(e){if(!e)return"0 B";const t=1024,s=["B","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return`${parseFloat((e/Math.pow(t,a)).toFixed(2))} ${s[a]}`},async handleDownload(){if(!this.selectedNodes.length)return void this.$message.warning("Please select at least one node");if(!this.selectedProxyIp)return void this.$message.warning("Please select a proxy IP");const e=this.selectedNodes.map(e=>({ip:e,path:this.nodeRemotePaths[e]||""}));try{this.downloading=!0;const t=localStorage.getItem("downloadTask_"+this.currentProject);if(t)try{const{taskId:e}=JSON.parse(t);e&&this.clearTaskNotificationMark(e,"download",this.currentProject)}catch(s){console.error("Error clearing previous download notification:",s)}const a=await l["a"].post("/api/file_transfer/download/start?dbFile="+encodeURIComponent(this.currentProject),{nodes:e,proxyIp:this.selectedProxyIp}),o=a.data.task_id;localStorage.setItem("downloadTask_"+this.currentProject,JSON.stringify({taskId:o,projectFile:this.currentProject})),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject),this.startPolling(o)}catch(a){var t;console.error("Download error:",a),this.downloading=!1,this.$message.error((null===(t=a.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"Failed to start download")}},async startPolling(e){this.pollInterval&&clearInterval(this.pollInterval);const t=async()=>{try{if(!this.currentProject)throw new Error("No project database selected");const t=await l["a"].get(`/api/file_transfer/download/status/${e}?dbFile=${encodeURIComponent(this.currentProject)}`);this.$store.dispatch("updateDownloadTask",t.data);const s=Object.values(t.data.nodes).every(e=>["completed","failed"].includes(e.status));if(s){clearInterval(this.pollInterval),this.downloading=!1,localStorage.setItem("downloadTaskCompleted_"+this.currentProject,"true");const s=Object.values(t.data.nodes);this.addTaskCompletionNotification({taskId:e,taskType:"download",nodes:s,projectId:this.currentProject,templates:{success:`File downloaded successfully to all ${s.length} nodes.`,error:`${s.filter(e=>"completed"===e.status).length} nodes completed file download successfully, ${s.filter(e=>"failed"===e.status).length} nodes failed.`},statusMapping:{success:["completed"],failure:["failed"]}})}}catch(s){var t;console.error("Error polling status:",s),404===(null===(t=s.response)||void 0===t?void 0:t.status)&&(clearInterval(this.pollInterval),this.downloading=!1,localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject))}};await t(),this.pollInterval=setInterval(t,1e4)},async checkActiveDownloadTask(){try{const e=localStorage.getItem("downloadTask_"+this.currentProject),t=localStorage.getItem("downloadTaskCompleted_"+this.currentProject);if(e){const{taskId:s,projectFile:a}=JSON.parse(e);if(a!==this.currentProject)throw new Error("Task belongs to different project");if(!this.currentProject)throw new Error("No project database selected");const o=await l["a"].get(`/api/file_transfer/download/status/${s}?dbFile=${encodeURIComponent(this.currentProject)}`);if(o.data){this.$store.dispatch("updateDownloadTask",o.data);const e=Object.values(o.data.nodes).every(e=>["completed","failed"].includes(e.status));if(e||t){if(e){this.downloading=!1,localStorage.setItem("downloadTaskCompleted_"+this.currentProject,"true");const e=Object.values(o.data.nodes);this.addTaskCompletionNotification({taskId:s,taskType:"download",nodes:e,projectId:this.currentProject,templates:{success:`File downloaded successfully to all ${e.length} nodes.`,error:`${e.filter(e=>"completed"===e.status).length} nodes completed file download successfully, ${e.filter(e=>"failed"===e.status).length} nodes failed.`},statusMapping:{success:["completed"],failure:["failed"]}})}}else this.downloading=!0,this.startPolling(s)}}}catch(e){console.error("Error checking active download task:",e),localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject)}},beforeDestroy(){this.pollInterval&&clearInterval(this.pollInterval),this.$store.dispatch("updateDownloadTask",null)}},watch:{currentProject:{handler(e,t){e!==t&&(this.$store.dispatch("updateDownloadTask",null),this.pollInterval&&clearInterval(this.pollInterval),this.checkActiveDownloadTask())},immediate:!0}}},u=d,h=(s("edf1"),s("2877")),p=Object(h["a"])(u,a,o,!1,null,"36748c18",null);t["default"]=p.exports},edf1:function(e,t,s){"use strict";s("b9ec")}}]);
//# sourceMappingURL=chunk-791bd3a8.eef7974c.js.map