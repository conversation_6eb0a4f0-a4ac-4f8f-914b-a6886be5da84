<template>
  <a-card :bordered="false" class="header-solid host-config-card">
    <template #title>
      <a-row type="flex" align="middle">
        <a-col :span="12">
          <h6 class="font-semibold m-0">{{ $t('hostConfig.title') }}</h6>
        </a-col>
        <a-col :span="12" class="text-right">
          <!-- 在表格上方添加分组按钮布局 -->
          <div class="button-groups">
            <div class="button-group">
              <a-button
                  class="nav-style-button action-button"
                  icon="plus"
                  @click="addNewRow">
                {{ $t('hostConfig.addHost') }}
              </a-button>

              <a-button
                icon="export"
                class="nav-style-button action-button"
                :disabled="selectedRowKeys.length === 0"
                @click="exportSelectedHosts"
              >
                {{ $t('hostConfig.exportSelected') }}
              </a-button>

              <a-button
                type="danger"
                icon="delete"
                class="nav-style-button action-button delete-button"
                :disabled="selectedRowKeys.length === 0"
                @click="batchDelete"
              >
                {{ $t('hostConfig.deleteSelected') }}
              </a-button>
            </div>

            <div class="button-group">
              <a-button
                  icon="download"
                  class="nav-style-button"
                  @click="downloadTemplate"
              >
                {{ $t('hostConfig.downloadTemplate') }}
              </a-button>

              <a-upload
                name="file"
                :customRequest="handleUpload"
                :showUploadList="false"
              >
                <a-button
                    icon="upload"
                    class="nav-style-button"
                >
                  {{ $t('hostConfig.uploadTemplate') }}
                </a-button>
              </a-upload>
            </div>
          </div>
        </a-col>
      </a-row>
    </template>

    <div class="config-table">
      <a-table
        :columns="columns"
        :data-source="hosts"
        :rowKey="(record) => record.key"
        size="middle"
        :pagination="{
          current: currentPage,
          pageSize: pageSize,
          total: hosts.length,
          onChange: onPageChange,
        }"
        :loading="loading"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: record => ({
            disabled: record.editable || record.isNew
          })
        }"
      >
      <template
        v-for="col in editableColumns"
        :slot="col"
        slot-scope="text, record, index"
      >
        <div :key="col">
          <a-input
            v-if="record.editable"
            style="margin: -5px 0"
            :value="text"
            @change="e => handleChange(e.target.value, record.key, col)"
            :placeholder="`Enter ${getColumnTitle(col)}`"
          />
          <span v-else style="display: flex; align-items: center;">
            <a-icon 
              v-if="['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text"
              type="copy" 
              style="cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;"
              @click="copyText(text)"
              @mouseenter="$event.target.style.opacity = '1'"
              @mouseleave="$event.target.style.opacity = '0.6'"
            />
            <span 
              :style="['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''"
              @click="['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? copyText(text) : null"
            >{{ text || '-' }}</span>            
          </span>
        </div>
      </template>

      <template #operation="text, record, index">
        <div class="editable-row-operations">
          <template v-if="record.editable">
            <a-button type="link" @click="() => save(record.key)">{{ $t('common.save') }}</a-button>
            <a-popconfirm
              title="Discard changes?"
              @confirm="() => cancel(record.key)"
            >
              <a-button type="link" danger>{{ $t('common.cancel') }}</a-button>
            </a-popconfirm>
          </template>
          <template v-else>
            <a-button type="link" @click="() => edit(record.key)">{{ $t('common.edit') }}</a-button>
            <a-button type="link" @click="() => copyNodeInfo(record)">
              <a-icon type="copy" />
              {{ $t('common.copy') }}
            </a-button>
            <a-popconfirm
              title="Confirm deletion?"
              @confirm="() => deleteHost(record)"
            >
              <a-button type="link" danger>{{ $t('common.delete') }}</a-button>
            </a-popconfirm>
          </template>
        </div>
      </template>
      </a-table>
    </div>
  </a-card>
</template>

<script>
// 使用 ant-design-vue 内置的图标
import { Icon } from 'ant-design-vue';
import axios from '@/api/axiosInstance';
import {mapState} from "vuex";
import CopyMixin from '@/mixins/CopyMixin';

let cacheData = [];

export default {
  components: {
    AIcon: Icon,
  },
  mixins: [CopyMixin],
  computed: {
    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式
  },
  data() {
    return {
      ...this.$data,
      hosts: [],
      saving: false,
      loading: false,
      currentPage: 1,
      pageSize: 50,
      editableColumns: [
        'host_name',
        'ip',
        'ssh_port',
        'login_user',
        'login_pwd',
        'switch_root_cmd',
        'switch_root_pwd',
      ],
      selectedRowKeys: [],
      currentDbFile: localStorage.getItem('currentProject'),
      columns: [
        {
          title: '#',
          dataIndex: 'index',
          width: 80,
          customRender: (text, record, index) => {
            return ((this.currentPage - 1) * this.pageSize) + index + 1;
          },
        },
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          scopedSlots: { customRender: 'host_name' },
          width: 150,
        },
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          scopedSlots: { customRender: 'ip' },
          width: 150,
        },
        {
          title: this.$t('hostConfig.columns.sshPort'),
          dataIndex: 'ssh_port',
          scopedSlots: { customRender: 'ssh_port' },
          width: 100,
        },
        {
          title: this.$t('hostConfig.columns.loginUser'),
          dataIndex: 'login_user',
          scopedSlots: { customRender: 'login_user' },
          width: 120,
        },
        {
          title: this.$t('hostConfig.columns.loginPassword'),
          dataIndex: 'login_pwd',
          scopedSlots: { customRender: 'login_pwd' },
          width: 150,
        },
        {
          title: this.$t('hostConfig.columns.switchRootCmd'),
          dataIndex: 'switch_root_cmd',
          scopedSlots: { customRender: 'switch_root_cmd' },
          width: 180,
        },
        {
          title: this.$t('hostConfig.columns.switchRootPwd'),
          dataIndex: 'switch_root_pwd',
          scopedSlots: { customRender: 'switch_root_pwd' },
          width: 180,
        },
        {
          title: this.$t('common.actions'),
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          width: 150,
          align: 'center',
        },
      ],
    };
  },
  created() {
    if (!this.currentDbFile) {
      this.$message.warning('Please select a project first');
      this.$router.push('/projects');
      return;
    }
    this.fetchHostConfig();
  },
  methods: {
    copyNodeInfo(record) {
      // 创建新的节点数据，复制原节点的所有属性
      const newRecord = {
        ...record,
        key: `new-${Date.now()}`,
        id: undefined,
        editable: true,
        isNew: true,
        host_name: `${record.host_name || ''}_copy`,
        ip: '' // Clear IP as it should be unique
      };
      
      // 在表格开头添加新行
      this.hosts = [newRecord, ...this.hosts];
      this.currentPage = 1; // Reset to first page to show the new row
      cacheData = this.hosts.map((item) => ({ ...item }));
      this.selectedRowKeys = [];
      
      // 滚动到顶部以显示新添加的行
      this.$nextTick(() => {
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          tableBody.scrollTop = 0;
        }
      });
    },
    
    getColumnTitle(dataIndex) {
      return this.columns.find((c) => c.dataIndex === dataIndex)?.title || dataIndex;
    },

    handleChange(value, key, column) {
      const newData = [...this.hosts];
      const target = newData.find((item) => item.key === key);
      if (target) {
        target[column] = value;
        this.hosts = newData;
      }
    },
    edit(key) {
      const newData = [...this.hosts];
      const target = newData.find((item) => item.key === key);
      if (target) {
        cacheData = newData.map((item) => ({ ...item }));
        target.editable = true;
        this.hosts = newData;
      }
    },

    async save(key) {
      try {
        const target = this.hosts.find((item) => item.key === key);
        if (!target || !this.validateHost(target)) return;

        this.saving = true;
        const hostData = { ...target };
        delete hostData.editable;
        delete hostData.isNew;

        await axios.post('/api/config/', {
          hosts: [hostData],
          dbFile: this.currentDbFile
        });

        this.hosts = this.hosts.map((item) =>
          item.key === key ? { ...item, editable: false, isNew: false } : item
        );
        cacheData = this.hosts.map((item) => ({ ...item }));
        this.$message.success('Saved successfully');
      } catch (error) {
        this.$message.error('Failed to save host');
      } finally {
        this.saving = false;
      }
    },

    cancel(key) {
      const targetIndex = this.hosts.findIndex((item) => item.key === key);
      if (targetIndex === -1) return;
      
      const target = this.hosts[targetIndex];
      
      if (target.isNew) {
        // For new rows, remove them completely
        this.hosts = this.hosts.filter(item => item.key !== key);
      } else {
        // For existing rows, revert changes
        const newData = [...this.hosts];
        const cachedItem = cacheData.find((item) => item.key === key);
        if (cachedItem) {
          Object.assign(target, { ...cachedItem });
          delete target.editable;
          this.hosts = newData;
        }
      }
    },

    addNewRow() {
      this.hosts = [
        {
          key: `new-${Date.now()}`,
          host_name: '',
          ip: '',
          ssh_port: '22',
          login_user: '',
          login_pwd: '',
          switch_root_cmd: '',
          switch_root_pwd: '',
          editable: true,
          isNew: true,
        },
        ...this.hosts,
      ];
      this.currentPage = 1;
      cacheData = this.hosts.map((item) => ({ ...item }));
      this.selectedRowKeys = [];
    },

    validateHost(host) {
      if (!host.host_name?.trim()) {
        this.$message.error('Host name is required');
        return false;
      }
      if (!/^(\d{1,3}\.){3}\d{1,3}$/.test(host.ip)) {
        this.$message.error('Invalid IP format');
        return false;
      }
      if (!/^\d+$/.test(host.ssh_port)) {
        this.$message.error('SSH port must be numeric');
        return false;
      }
      const exist = this.hosts.some((h) => h.ip === host.ip && h.key !== host.key);
      if (exist) {
        this.$message.error('IP address already exists');
        return false;
      }
      return true;
    },

    async fetchHostConfig() {
      try {
        this.loading = true;
        const response = await axios.get(`/api/config`, {
          params: {
            detail: true,
            dbFile: this.currentDbFile
          }
        });
        this.hosts = response.data.map((item) => ({
          ...item,
          key: item.id?.toString() || `host_${item.host_name}`,
          ssh_port: item.ssh_port?.toString() || '22',
          isNew: false,
        }));
        cacheData = this.hosts.map((item) => ({ ...item }));
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to load hosts');
      } finally {
        this.loading = false;
      }
    },
    onPageChange(page) {
      this.currentPage = page;
    },

    async deleteHost(record) {
      try {
        if (record.id) {
          await axios.delete(`/api/config/${record.id}`, {
            params: { dbFile: this.currentDbFile }
          });

          this.hosts = this.hosts.filter((h) => h.key !== record.key);
          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);
          this.$message.success('Deleted successfully');
        } else {
          this.hosts = this.hosts.filter((h) => h.key !== record.key);
          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);
        }
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to delete host');
        await this.fetchHostConfig();
      }
    },

    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    async batchDelete() {
      try {
        const selectedIds = this.hosts
          .filter(host => this.selectedRowKeys.includes(host.key))
          .map(host => host.id)
          .filter(id => id);

        if (selectedIds.length === 0) {
          this.$message.warning('No valid hosts selected for deletion');
          return;
        }

        await axios.post('/api/config/batch-delete', {
          ids: selectedIds,
          dbFile: this.currentDbFile
        });

        this.hosts = this.hosts.filter(host => !this.selectedRowKeys.includes(host.key));
        this.selectedRowKeys = [];
        this.$message.success('Batch deletion completed successfully');
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Batch deletion failed');
      }
    },

    async downloadTemplate() {
      try {
        const response = await axios.get('/api/config/template', {
          responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'hosts_template.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('Template downloaded successfully');
      } catch (error) {
        this.$message.error('Failed to download template');
        console.error('Download template error:', error);
      }
    },

    async handleUpload(options) {
      const { file } = options;

      if (!file.name.endsWith('.csv')) {
        this.$message.error('Please upload CSV file');
        return;
      }

      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('dbFile', this.currentDbFile);

        await axios.post('/api/config/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        await this.fetchHostConfig();
        this.$message.success('Hosts imported successfully');
      } catch (error) {
        this.$message.error(error.response?.data?.error || 'Failed to import hosts');
      }
    },

    async exportSelectedHosts() {
      try {
        const selectedHosts = this.hosts.filter(host => this.selectedRowKeys.includes(host.key));

        // Create CSV content
        const headers = [
          'host_name',
          'ip',
          'ssh_port',
          'login_user',
          'login_pwd',
          'switch_root_cmd',
          'switch_root_pwd'
        ];

        const csvContent = [
          headers.join(','),
          ...selectedHosts.map(host =>
            headers.map(header => host[header] || '').join(',')
          )
        ].join('\n');

        // Create and trigger download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'selected_hosts.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('Hosts exported successfully');
      } catch (error) {
        this.$message.error('Failed to export hosts');
        console.error('Export hosts error:', error);
      }
    },
  },
};
</script>

<style scoped>
.host-config-card {
  margin: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

::v-deep .ant-upload-select {
  display: inline-block;
}

/* 删除按钮保持红色 */
::v-deep .delete-button {
  color: #ff4d4f !important;
}

::v-deep .delete-button .anticon {
  color: #ff4d4f !important;
}

/* 添加按钮组样式 */
.button-groups {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
  flex-wrap: wrap;
  gap: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .button-groups {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
