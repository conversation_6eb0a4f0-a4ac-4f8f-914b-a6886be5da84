(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-08a0de52"],{"0820":function(e,t,s){"use strict";s("11b2")},"0ce3":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("DockerInfo")],1)],1)],1)},n=[],o=function(){var e,t,s,i,n=this,o=n._self._c;return o("a-card",{staticClass:"header-solid h-full docker-card",attrs:{bordered:!1,bodyStyle:{padding:0}},scopedSlots:n._u([{key:"title",fn:function(){return[o("div",{staticClass:"card-header-wrapper"},[o("div",{staticClass:"header-wrapper"},[o("div",{staticClass:"logo-wrapper"},[o("svg",{class:"text-"+n.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"25",height:"25",viewBox:"0 0 24 24"}},[o("path",{attrs:{fill:"currentColor",d:"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z"}})])]),o("h6",{staticClass:"font-semibold m-0"},[n._v(n._s(n.$t("headTopic.docker")))])]),o("div",[o("RefreshButton",{on:{refresh:n.fetchActiveTabData}})],1)])]},proxy:!0}])},[o("JsonDetailModal",{ref:"jsonDetailModal"}),o("div",{staticClass:"container-runtime-tabs"},[o("a-tabs",{model:{value:n.activeEngine,callback:function(e){n.activeEngine=e},expression:"activeEngine"}},[o("a-tab-pane",{key:"docker",attrs:{tab:"Docker"}},[o("a-tabs",{on:{change:n.handleTabChange},model:{value:n.activeTab,callback:function(e){n.activeTab=e},expression:"activeTab"}},[o("a-tab-pane",{key:"docker_host_config",attrs:{tab:"Host Config"}},["docker_host_config"===n.activeTab?o("div",{staticClass:"host-config-container"},[o("a-row",{attrs:{gutter:[16,16]}},[o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Basic Information",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("Docker Version:")]),n._v(" "+n._s(null===(e=n.hostConfigData[0])||void 0===e?void 0:e.docker_version))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Operating System:")]),n._v(" "+n._s(n.getDaemonConfig("OperatingSystem")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Architecture:")]),n._v(" "+n._s(n.getDaemonConfig("Architecture")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Kernel Version:")]),n._v(" "+n._s(n.getDaemonConfig("KernelVersion")))])])],1),o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Resources",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("CPU Cores:")]),n._v(" "+n._s(n.getDaemonConfig("NCPU")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Total Memory:")]),n._v(" "+n._s(n.formatBytes(n.getDaemonConfig("MemTotal"))))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Docker Root Dir:")]),n._v(" "+n._s(n.getDaemonConfig("DockerRootDir")))])])],1),o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Security",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("User in Docker Group:")]),n._v(" "+n._s(null!==(t=n.hostConfigData[0])&&void 0!==t&&t.user_in_docker_group?"Yes":"No"))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Root User:")]),n._v(" "+n._s(null!==(s=n.hostConfigData[0])&&void 0!==s&&s.is_root_user?"Yes":"No"))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Security Options:")]),n._v(" "+n._s((null===(i=n.getDaemonConfig("SecurityOptions"))||void 0===i?void 0:i.join(", "))||"None"))])])],1),o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Container Statistics",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("Total Containers:")]),n._v(" "+n._s(n.getDaemonConfig("Containers")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Running:")]),n._v(" "+n._s(n.getDaemonConfig("ContainersRunning")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Stopped:")]),n._v(" "+n._s(n.getDaemonConfig("ContainersStopped")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Images:")]),n._v(" "+n._s(n.getDaemonConfig("Images")))])])],1),o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Storage Driver",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("Driver:")]),n._v(" "+n._s(n.getDaemonConfig("Driver")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Logging Driver:")]),n._v(" "+n._s(n.getDaemonConfig("LoggingDriver")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Cgroup Driver:")]),n._v(" "+n._s(n.getDaemonConfig("CgroupDriver")))])])],1),o("a-col",{attrs:{span:8}},[o("a-card",{attrs:{title:"Registry Configuration",bordered:!1}},[o("p",{staticClass:"info-item"},[o("strong",[n._v("Index Server:")]),n._v(" "+n._s(n.getDaemonConfig("IndexServerAddress")))]),o("p",{staticClass:"info-item"},[o("strong",[n._v("Registry Mirrors:")])]),o("ul",n._l(n.getRegistryMirrors(),(function(e){return o("li",{key:e,staticClass:"info-item"},[n._v(n._s(e))])})),0)])],1)],1)],1):n._e()]),o("a-tab-pane",{key:"docker_network",attrs:{tab:"Networks"}},["docker_network"===n.activeTab?o("a-table",{attrs:{columns:n.networkColumns,"data-source":n.networkData,rowKey:e=>e.network_id,loading:n.loadingNetworks,pagination:n.pagination}}):n._e()],1),o("a-tab-pane",{key:"docker_container",attrs:{tab:"Containers"}},["docker_container"===n.activeTab?o("a-table",{attrs:{columns:n.containerColumns,"data-source":n.containerData,scroll:{x:1500},pagination:{pageSize:20},"row-key":e=>e.container_id,loading:n.loadingContainers},scopedSlots:n._u([{key:"mountsColumn",fn:function({record:e}){return[e?o("div",[e.mounts&&e.mounts.length?n._l(e.mounts,(function(e,t){return o("div",{key:t},[n._v(" "+n._s(e.Source)+" → "+n._s(e.Destination)+" ")])})):o("span",[n._v("N/A")])],2):n._e()]}}],null,!1,2075805027)}):n._e(),o("network-listening-modal",{attrs:{visible:n.networkDetailsVisible,"network-data":n.selectedNetwork},on:{"update:visible":function(e){n.networkDetailsVisible=e},close:n.handleNetworkDetailsClose}})],1)],1)],1),o("a-tab-pane",{key:"crictl",attrs:{tab:"CRI"}},[o("crictl-info",{attrs:{"node-ip":n.selectedNodeIp}})],1)],1)],1),o("a-modal",{attrs:{title:"Mount Details",width:"800px"},on:{cancel:n.handleMountDetailsClose},scopedSlots:n._u([{key:"footer",fn:function(){return[o("a-button",{on:{click:n.handleMountDetailsClose}},[n._v("Cancel")])]},proxy:!0}]),model:{value:n.mountDetailsVisible,callback:function(e){n.mountDetailsVisible=e},expression:"mountDetailsVisible"}},[n.selectedMountContainer?[o("div",{staticClass:"mounts-container"},n._l(n.getAllMounts(),(function(e,t){return o("div",{key:t,staticClass:"mount-item"},[o("div",{staticClass:"mount-path"},[o("span",{staticClass:"mount-source"},[n._v(n._s(e.Source))]),o("span",{staticClass:"mount-arrow"},[n._v("→")]),o("span",{staticClass:"mount-dest"},[n._v(n._s(e.Destination))])]),o("div",{staticClass:"mount-details"},[e.Mode?o("span",{staticClass:"mount-tag"},[n._v(n._s(e.Mode))]):n._e(),o("span",{staticClass:"mount-tag"},[n._v(n._s(e.RW?"RW":"RO"))]),e.Propagation?o("span",{staticClass:"mount-tag"},[n._v(n._s(e.Propagation))]):n._e()])])})),0)]:n._e()],2),o("a-modal",{attrs:{title:"Environment Variables",width:"800px"},on:{cancel:n.handleEnvironmentDetailsClose},scopedSlots:n._u([{key:"footer",fn:function(){return[o("a-button",{on:{click:n.handleEnvironmentDetailsClose}},[n._v("Close")])]},proxy:!0}]),model:{value:n.environmentDetailsVisible,callback:function(e){n.environmentDetailsVisible=e},expression:"environmentDetailsVisible"}},[n.selectedEnvironment?[o("div",{staticClass:"env-container"},n._l(n.getAllEnvironmentVars(),(function(e,t){return o("div",{key:t,staticClass:"env-item"},[n._v(" "+n._s(e)+" ")])})),0)]:n._e()],2),o("process-table",{attrs:{visible:n.processDetailsVisible,"process-container":n.selectedProcessContainer,"user-field":"uid","include-tty":!0},on:{"update:visible":function(e){n.processDetailsVisible=e},close:n.handleProcessDetailsClose}}),o("process-detail-modal",{attrs:{visible:n.processDetailInfoVisible,"process-info":n.selectedProcessInfo,"user-field":"uid"},on:{"update:visible":function(e){n.processDetailInfoVisible=e},close:n.handleProcessDetailInfoClose}})],1)},a=[],r=(s("0643"),s("a573"),s("2f62")),l=s("fec3"),c=s("f188"),d=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full crictl-card",attrs:{bordered:!1,bodyStyle:{padding:0}}},[t("JsonDetailModal",{ref:"jsonDetailModal"}),t("div",{staticClass:"crictl-info-container"},[t("a-tabs",{on:{change:e.handleTabChange},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("a-tab-pane",{key:"crictl_host_config",attrs:{tab:"Host Config"}},["crictl_host_config"===e.activeTab?t("div",{staticClass:"host-config-container"},[t("a-row",{attrs:{gutter:[16,16]}},e._l(e.hostConfigCards,(function(s){return t("a-col",{key:s.title,attrs:{span:8}},[t("a-card",{staticClass:"host-config-card",attrs:{title:s.title,bordered:!1}},e._l(s.items,(function(s,i){return t("p",{key:i,staticClass:"info-item"},[t("strong",[e._v(e._s(s.label)+":")]),e._v(" "+e._s(s.getValue(e.hostConfig))+" ")])})),0)],1)})),1)],1):e._e()]),t("a-tab-pane",{key:"crictl_pod",attrs:{tab:"Pods"}},[t("a-table",{attrs:{columns:e.podColumns,"data-source":e.podData,rowKey:e=>e.pod_id,loading:e.loadingPods,pagination:{pageSize:30},scroll:{x:1500}}})],1),t("a-tab-pane",{key:"crictl_container",attrs:{tab:"Containers"}},[t("a-table",{attrs:{columns:e.containerColumns,"data-source":e.containerData,rowKey:e=>e.container_id,loading:e.loadingContainers,pagination:{pageSize:20},scroll:{x:1800}}})],1)],1)],1),t("network-listening-modal",{attrs:{visible:e.networkDetailsVisible,"network-data":e.selectedNetwork},on:{"update:visible":function(t){e.networkDetailsVisible=t},close:e.handleNetworkDetailsClose}}),t("a-modal",{attrs:{title:"Environment Variables",width:"800px"},on:{cancel:e.handleEnvironmentDetailsClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("a-button",{on:{click:e.handleEnvironmentDetailsClose}},[e._v("Close")])]},proxy:!0}]),model:{value:e.environmentDetailsVisible,callback:function(t){e.environmentDetailsVisible=t},expression:"environmentDetailsVisible"}},[e.selectedEnvironment?[t("div",{staticClass:"env-container"},e._l(e.getAllEnvironmentVars(),(function(s,i){return t("div",{key:i,staticClass:"env-item"},[e._v(" "+e._s(s)+" ")])})),0)]:e._e()],2),t("a-modal",{attrs:{title:"Mount Details",width:"800px"},on:{cancel:e.handleMountDetailsClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("a-button",{on:{click:e.handleMountDetailsClose}},[e._v("Cancel")])]},proxy:!0}]),model:{value:e.mountDetailsVisible,callback:function(t){e.mountDetailsVisible=t},expression:"mountDetailsVisible"}},[e.selectedMountContainer?[t("div",{staticClass:"mounts-container"},e._l(e.getAllMounts(),(function(s,i){return t("div",{key:i,staticClass:"mount-item"},[t("div",{staticClass:"mount-path"},[t("span",{staticClass:"mount-source"},[e._v(e._s(s.host_path))]),t("span",{staticClass:"mount-arrow"},[e._v("→")]),t("span",{staticClass:"mount-dest"},[e._v(e._s(s.container_path))])]),t("div",{staticClass:"mount-details"},[t("span",{staticClass:"mount-tag"},[e._v(e._s(s.readonly?"RO":"RW"))]),s.propagation?t("span",{staticClass:"mount-tag"},[e._v(" "+e._s(s.propagation.replace("PROPAGATION_",""))+" ")]):e._e(),s.selinux_relabel?t("span",{staticClass:"mount-tag"},[e._v("SELinux Relabel")]):e._e()])])})),0)]:e._e()],2),t("process-table",{attrs:{visible:e.processDetailsVisible,"process-container":e.selectedProcessContainer,"user-field":"user","include-tty":!1},on:{"update:visible":function(t){e.processDetailsVisible=t},close:e.handleProcessDetailsClose}}),t("process-detail-modal",{attrs:{visible:e.processDetailInfoVisible,"process-info":e.selectedProcessInfo,"user-field":"user"},on:{"update:visible":function(t){e.processDetailInfoVisible=t},close:e.handleProcessDetailInfoClose}})],1)},p=[],u=(s("fffc"),s("b854")),h=function(){var e=this,t=e._self._c;return t("a-modal",{staticClass:"process-detail-modal",attrs:{visible:e.localVisible,title:"Process Detailed Information",width:"800px"},on:{cancel:e.handleClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("a-button",{on:{click:e.handleClose}},[e._v("Close")])]},proxy:!0}])},[e.processInfo?[t("a-tabs",{attrs:{"default-active-key":"1"}},[t("a-tab-pane",{key:"1",attrs:{tab:"Basic Info"}},[t("a-descriptions",{attrs:{bordered:"",column:1}},[t("a-descriptions-item",{attrs:{label:"PID"}},[e._v(e._s(e.processInfo.pid))]),t("a-descriptions-item",{attrs:{label:"PPID"}},[e._v(e._s(e.processInfo.ppid))]),t("a-descriptions-item",{attrs:{label:"UID"}},[e._v(e._s(e.processInfo[e.userField]||"N/A"))]),t("a-descriptions-item",{attrs:{label:"GID"}},[e._v(e._s(e.processInfo.gid||"N/A"))]),t("a-descriptions-item",{attrs:{label:"State"}},[e._v(e._s(e.processInfo.state||"N/A"))]),t("a-descriptions-item",{attrs:{label:"Command"}},[e._v(e._s(e.processInfo.cmd))]),t("a-descriptions-item",{attrs:{label:"Executable Path"}},[e._v(e._s(e.processInfo.exe||"N/A"))]),t("a-descriptions-item",{attrs:{label:"Working Directory"}},[e._v(e._s(e.processInfo.cwd||"N/A"))])],1)],1),e.processInfo.capability?t("a-tab-pane",{key:"2",attrs:{tab:"Capabilities"}},[t("pre",{staticClass:"detail-pre"},[e._v(e._s(e.processInfo.capability))])]):e._e(),e.processInfo.environ?t("a-tab-pane",{key:"3",attrs:{tab:"Environment Variables"}},[t("pre",{staticClass:"detail-pre"},[e._v(e._s(e.processInfo.environ))])]):e._e(),e.processInfo.memory_maps?t("a-tab-pane",{key:"4",attrs:{tab:"Memory Maps"}},[t("pre",{staticClass:"detail-pre"},[e._v(e._s(e.processInfo.memory_maps))])]):e._e()],1)]:e._e()],2)},m=[],v={name:"ProcessDetailModal",props:{visible:{type:Boolean,required:!0},processInfo:{type:Object,default:null},userField:{type:String,default:"user"}},data(){return{localVisible:this.visible}},watch:{visible(e){this.localVisible=e}},methods:{handleClose(){this.localVisible=!1,this.$emit("update:visible",!1),this.$emit("close")}}},f=v,g=(s("a366"),s("2877")),_=Object(g["a"])(f,h,m,!1,null,"655588c5",null),b=_.exports,y=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{staticClass:"process-table-modal",attrs:{visible:e.localVisible,title:"Process Details",width:"1500px"},on:{cancel:e.handleClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("a-button",{on:{click:e.handleClose}},[e._v("Close")])]},proxy:!0}])},[e.processContainer?[t("a-table",{attrs:{columns:e.processColumns,dataSource:e.getProcessList(),pagination:{pageSize:10},size:"middle"}})]:e._e()],2),t("process-detail-modal",{attrs:{visible:e.processDetailInfoVisible,"process-info":e.selectedProcessInfo,"user-field":e.userField},on:{"update:visible":function(t){e.processDetailInfoVisible=t},close:e.handleProcessDetailInfoClose}})],1)},C=[],w={name:"ProcessTable",components:{ProcessDetailModal:b},props:{visible:{type:Boolean,required:!0},processContainer:{type:Object,default:null},userField:{type:String,default:"user"},includeTty:{type:Boolean,default:!1}},data(){return{localVisible:this.visible,processDetailInfoVisible:!1,selectedProcessInfo:null}},watch:{visible(e){this.localVisible=e}},computed:{processColumns(){const e=this.$createElement,t=[{title:"PID",dataIndex:"pid",key:"pid",width:"60px"},{title:"PPID",dataIndex:"ppid",key:"ppid",width:"60px"},{title:"UID",dataIndex:this.userField,key:this.userField,width:"80px"},{title:"GID",dataIndex:"gid",key:"gid",width:"60px"},{title:"State",dataIndex:"state",key:"state",width:"80px"},{title:"Start Time",dataIndex:"stime",key:"stime",width:"100px"}];return this.includeTty&&t.push({title:"TTY",dataIndex:"tty",key:"tty",width:"60px"}),t.push({title:"Time",dataIndex:"time",key:"time",width:"60px"},{title:"Command",dataIndex:"cmd",key:"cmd",width:"500px",ellipsis:!1,customRender:e=>({children:e,props:{style:{whiteSpace:"normal",wordBreak:"break-word"}}})},{title:"Details",key:"details",width:"100px",align:"center",customRender:(t,s)=>{const i=s.exe||s.cwd||s.capability||s.environ||s.memory_maps;return i?e("a-button",{attrs:{type:"link",size:"small"},on:{click:e=>{e.stopPropagation(),this.showProcessDetailInfo(s)}}},["View Details"]):"N/A"}}),t}},methods:{handleClose(){this.localVisible=!1,this.$emit("update:visible",!1),this.$emit("close")},getProcessList(){var e;if(null===(e=this.processContainer)||void 0===e||!e.processes)return[];const t=this.processContainer.processes;return t.process_list&&t.process_list.length>0?t.process_list:t.pid&&Array.isArray(t.pid)?t.pid.map((e,s)=>({pid:t.pid[s],ppid:t.ppid[s],[this.userField]:t[this.userField][s],cmd:t.cmd[s],time:t.time[s],stime:t.stime[s]})):[]},showProcessDetailInfo(e){this.selectedProcessInfo=e,this.processDetailInfoVisible=!0},handleProcessDetailInfoClose(){this.processDetailInfoVisible=!1,this.selectedProcessInfo=null}}},k=w,D=(s("38d3"),Object(g["a"])(k,y,C,!1,null,"424fe87c",null)),x=D.exports,I=function(){var e=this,t=e._self._c;return t("a-modal",{staticClass:"network-listening-modal",attrs:{visible:e.localVisible,title:"Network Listening Details",width:"800px"},on:{cancel:e.handleClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("a-button",{on:{click:e.handleClose}},[e._v("Close")])]},proxy:!0}])},[e.networkData?[t("a-table",{attrs:{columns:e.networkListeningColumns,dataSource:e.getNetworkListening(),pagination:{pageSize:20},size:"middle"}})]:e._e()],2)},N=[],V={name:"NetworkListeningModal",props:{visible:{type:Boolean,required:!0},networkData:{type:Object,default:null}},data(){return{localVisible:this.visible,networkListeningColumns:[{title:"Protocol",dataIndex:"proto",key:"proto",width:"80px"},{title:"Local Address",dataIndex:"local_address",key:"local_address",width:"150px"},{title:"Foreign Address",dataIndex:"foreign_address",key:"foreign_address",width:"150px"},{title:"State",dataIndex:"state",key:"state",width:"100px"},{title:"PID/Program",dataIndex:"pid_program",key:"pid_program",width:"120px"}]}},watch:{visible(e){this.localVisible=e}},methods:{handleClose(){this.localVisible=!1,this.$emit("update:visible",!1),this.$emit("close")},getNetworkListening(){var e;return null!==(e=this.networkData)&&void 0!==e&&e.exposures&&this.networkData.exposures.listening_ports||[]}}},P=V,S=(s("0820"),Object(g["a"])(P,I,N,!1,null,"18c6d2e8",null)),M=S.exports,A=function(){var e=this,t=e._self._c;return t("div",[0===e.listeningPorts.length?t("div",[e._v("No listening ports")]):t("div",{staticStyle:{"font-size":"12px"}},[e.listeningPorts.length>0?t("div",{staticStyle:{padding:"2px 0"}},[t("div",[t("span",{staticClass:"proto-text"},[e._v(e._s(e.listeningPorts[0].proto))]),t("span",[e._v(" "+e._s(e.listeningPorts[0].local_address))]),t("span",{staticClass:"path-arrow"},[e._v(" → ")]),t("span",[e._v(e._s(e.listeningPorts[0].foreign_address))]),t("span",{staticClass:"program-text"},[e._v(" ("+e._s(e.listeningPorts[0].pid_program)+")")])]),e.listeningPorts.length>1?t("div",{staticStyle:{"margin-top":"4px"}},[t("a-button",{staticStyle:{"padding-left":"0"},attrs:{type:"link",size:"small"},on:{click:function(t){return t.stopPropagation(),e.showDetails.apply(null,arguments)}}},[e._v(" +"+e._s(e.listeningPorts.length-1)+" more... ")])],1):e._e()]):e._e()])])},R=[],E={name:"NetworkListeningCell",props:{record:{type:Object,required:!0},parseJsonField:{type:Function,default:(e,t)=>{try{return Array.isArray(e)||"object"===typeof e&&null!==e?e:"string"===typeof e?JSON.parse(e):t}catch(s){return console.warn("Failed to parse JSON field:",s),t}}}},computed:{listeningPorts(){try{const e="string"===typeof this.record.exposures?JSON.parse(this.record.exposures):this.record.exposures;return(null===e||void 0===e?void 0:e.listening_ports)||[]}catch(e){return console.error("Failed to parse exposed services:",e),[]}}},methods:{showDetails(e){e&&e.stopPropagation(),this.$emit("show-details",this.record),this.$emit("showDetails",this.record)}}},j=E,T=(s("53d4"),Object(g["a"])(j,A,R,!1,null,"167d61b4",null)),O=T.exports,F=function(){var e=this,t=e._self._c;return t("div",[0===e.mounts.length?t("div",[e._v("N/A")]):t("div",{staticStyle:{"font-size":"12px"}},[e.mounts.length>0?t("div",{staticStyle:{padding:"2px 0"}},[t("div",[t("span",{staticClass:"source-path"},[e._v(e._s(e.mounts[0].Source||"N/A"))]),t("span",{staticClass:"path-arrow"},[e._v(" → ")]),t("span",{staticClass:"dest-path"},[e._v(e._s(e.mounts[0].Destination||"N/A"))]),t("span",{staticClass:"mount-tag",staticStyle:{"margin-left":"5px"}},[e._v(e._s(e.mounts[0].Mode||""))]),t("span",{staticClass:"mount-tag"},[e._v(e._s(e.mounts[0].RW?"RW":"RO"))])]),e.mounts.length>1?t("div",{staticStyle:{"margin-top":"4px"}},[t("a-button",{staticStyle:{"padding-left":"0"},attrs:{type:"link",size:"small"},on:{click:e.showDetails}},[e._v(" +"+e._s(e.mounts.length-1)+" more... ")])],1):e._e()]):e._e()])])},$=[],L={name:"MountCell",props:{record:{type:Object,required:!0},parseJsonField:{type:Function,default:(e,t)=>{try{return Array.isArray(e)||"object"===typeof e&&null!==e?e:"string"===typeof e?JSON.parse(e):t}catch(s){return console.warn("Failed to parse JSON field:",s),t}}}},computed:{mounts(){try{return this.parseJsonField(this.record.mounts,[])}catch(e){return console.error("Failed to parse mounts:",e),[]}}},methods:{showDetails(){this.$emit("show-details",this.record)}}},J=L,z=(s("3a7d"),Object(g["a"])(J,F,$,!1,null,"70290246",null)),B=z.exports,q=function(){var e=this,t=e._self._c;return t("div",[0===e.mounts.length?t("div",[e._v("N/A")]):t("div",{staticStyle:{"font-size":"12px"}},[e.mounts.length>0?t("div",{staticStyle:{padding:"2px 0"}},[t("div",[t("span",{staticClass:"source-path"},[e._v(e._s(e.mounts[0].host_path||"N/A"))]),t("span",{staticClass:"path-arrow"},[e._v(" → ")]),t("span",{staticClass:"dest-path"},[e._v(e._s(e.mounts[0].container_path||"N/A"))]),t("span",{staticClass:"mount-tag"},[e._v(e._s(e.mounts[0].readonly?"RO":"RW"))]),e.mounts[0].propagation?t("span",{staticClass:"mount-tag"},[e._v(e._s(e.mounts[0].propagation.replace("PROPAGATION_","")))]):e._e()]),e.mounts.length>1?t("div",{staticStyle:{"margin-top":"4px"}},[t("a-button",{staticStyle:{"padding-left":"0"},attrs:{type:"link",size:"small"},on:{click:e.showDetails}},[e._v(" +"+e._s(e.mounts.length-1)+" more... ")])],1):e._e()]):e._e()])])},W=[],U={name:"CriMountCell",props:{record:{type:Object,required:!0}},computed:{mounts(){try{return"string"===typeof this.record.mounts?JSON.parse(this.record.mounts):this.record.mounts||[]}catch(e){return console.error("Failed to parse mounts:",e),[]}}},methods:{showDetails(){this.$emit("show-details",this.record)}}},H=U,Y=(s("ac5e"),Object(g["a"])(H,q,W,!1,null,"3be0b9ad",null)),G=Y.exports,K={components:{JsonDetailModal:u["a"],ProcessDetailModal:b,ProcessTable:x,NetworkListeningModal:M,NetworkListeningCell:O,CriMountCell:G},name:"CrictlInfo",props:{nodeIp:{type:String,required:!0}},data(){const e=this.$createElement;return{hostConfigCards:[{title:"Version Information",items:[{label:"Version",getValue:e=>{if(!e.version_info)return"N/A";const t=e.version_info.match(/Version:\s*(.+)/);return t?t[1].trim():"N/A"}},{label:"Runtime Name",getValue:e=>{if(!e.version_info)return"N/A";const t=e.version_info.match(/RuntimeName:\s*(.+)/);return t?t[1].trim():"N/A"}},{label:"Runtime Version",getValue:e=>{if(!e.version_info)return"N/A";const t=e.version_info.match(/RuntimeVersion:\s*(.+)/);return t?t[1].trim():"N/A"}},{label:"Runtime API Version",getValue:e=>{if(!e.version_info)return"N/A";const t=e.version_info.match(/RuntimeApiVersion:\s*(.+)/);return t?t[1].trim():"N/A"}}]},{title:"Runtime Status",items:[{label:"Runtime Ready",getValue:e=>{var t;const s=null===(t=e.runtime_info)||void 0===t||null===(t=t.status)||void 0===t||null===(t=t.conditions)||void 0===t?void 0:t.find(e=>"RuntimeReady"===e.type);return s?""+(s.status?"Ready":"Not Ready"):"N/A"}},{label:"Network Ready",getValue:e=>{var t;const s=null===(t=e.runtime_info)||void 0===t||null===(t=t.status)||void 0===t||null===(t=t.conditions)||void 0===t?void 0:t.find(e=>"NetworkReady"===e.type);return s?""+(s.status?"Ready":"Not Ready"):"N/A"}},{label:"Sandbox Image",getValue:e=>{var t;return(null===(t=e.runtime_info)||void 0===t||null===(t=t.config)||void 0===t?void 0:t.sandboxImage)||"N/A"}}]},{title:"Security",items:[{label:"Root User",getValue:e=>e.is_root_user?"Yes":"No"}]}],hostConfig:{},podData:[],containerData:[],loadingHostConfig:!1,loadingPods:!1,loadingContainers:!1,activeTab:"crictl_host_config",podColumns:[{title:"Pod ID",dataIndex:"pod_id",key:"pod_id",width:"120px",ellipsis:!0},{title:"Name",dataIndex:["metadata","name"],key:"name",width:"160px",ellipsis:!0},{title:"Namespace",dataIndex:["metadata","namespace"],key:"namespace",width:"80px"},{title:"State",dataIndex:"state",key:"state",width:"120px"},{title:"Network",dataIndex:"network",key:"network",width:"180px",customRender:(e,t)=>{const s=t.network;if(!s||0===Object.keys(s).length)return"N/A";const i=[];return s.ip&&i.push(s.ip),s.additionalIps&&s.additionalIps.length>0&&i.push(...s.additionalIps),i.length>0?i.join(",  "):"N/A"}}],containerColumns:[{title:"Container ID",dataIndex:"container_id",key:"container_id",width:"140px",ellipsis:!1},{title:"Name",dataIndex:"inspect_data",key:"name",width:"15%",ellipsis:!1,customRender:(e,t)=>{try{var s;const e="string"===typeof t.inspect_data?JSON.parse(t.inspect_data):t.inspect_data;return{children:(null===e||void 0===e||null===(s=e.status)||void 0===s||null===(s=s.metadata)||void 0===s?void 0:s.name)||"N/A",props:{style:{whiteSpace:"normal",wordBreak:"break-word"}}}}catch(i){return console.warn("Failed to render container name:",i),"N/A"}}},{title:"Environment",dataIndex:"env",key:"env",width:"200px",customRender:(t,s)=>{try{const t="string"===typeof s.env?JSON.parse(s.env):s.env||[];return t.length?e("div",{style:"font-size: 12px;"},[t.slice(0,1).map((t,s)=>e("div",{key:s,style:"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"},[t.length>200?t.substring(0,200)+"...":t])),t.length>1&&e("a-button",{attrs:{type:"link",size:"small"},on:{click:e=>{e.stopPropagation(),this.showEnvironmentDetails(s)}}},["+",t.length-1," more..."])]):"N/A"}catch(i){return console.warn("Failed to render env vars:",i),"None"}}},{title:"Mounts",dataIndex:"mounts",key:"mounts",width:"25%",ellipsis:!1,customRender:(t,s)=>e("cri-mount-cell",{attrs:{record:s},on:{"show-details":this.showMountDetails}})},{title:"Network Listening",dataIndex:"exposures",key:"exposures",width:"25%",ellipsis:!1,customRender:(t,s)=>e("network-listening-cell",{attrs:{record:s},on:{"show-details":this.showNetworkDetails}})},{title:"Processes",dataIndex:"processes",key:"processes",width:"120px",align:"center",customRender:(t,s)=>{if(!s||!s.processes)return"N/A";const i="string"===typeof s.processes?JSON.parse(s.processes):s.processes,n=i.pid?i.pid.length:0;return e("a-button",{attrs:{type:"link",size:"small"},on:{click:e=>{e.stopPropagation(),this.showProcessDetails(s)}}},[n," ",1===n?"process":"processes"])}},{title:"Inspect Data",dataIndex:"inspect_data",key:"inspect_data",width:"150px",align:"center",customRender:(t,s)=>e("span",{style:"display: inline-block; line-height: 22px;"},[e("a",{on:{click:e=>{e.stopPropagation(),this.showInspectDetails(s)}},style:"color: #1890ff; cursor: pointer;"},["View Inspect"])])}],networkDetailsVisible:!1,selectedNetwork:null,mountDetailsVisible:!1,selectedMountContainer:null,environmentDetailsVisible:!1,selectedEnvironment:null,processDetailsVisible:!1,selectedProcessContainer:null,processDetailInfoVisible:!1,selectedProcessInfo:null}},computed:{...Object(r["e"])(["selectedNodeIp","currentProject"])},watch:{selectedNodeIp(e){e&&this.fetchActiveTabData()}},created(){this.selectedNodeIp&&this.fetchActiveTabData()},methods:{async fetchActiveTabData(){if(!this.selectedNodeIp||!this.currentProject)return;const e="loading"+(this.activeTab.replace("crictl_","").charAt(0).toUpperCase()+this.activeTab.slice(7));this[e]=!0;try{const t=await l["a"].get(`/api/crictl/${this.activeTab}/${this.selectedNodeIp}`,{params:{dbFile:this.currentProject}}),s=this.activeTab.replace("crictl_","")+"Data";"crictl_host_config"===this.activeTab?this.hostConfig=t.data:this[s]=t.data}catch(t){console.error(`Error fetching ${this.activeTab}:`,t)}finally{this[e]=!1}},handleTabChange(e){this.activeTab=e,this.fetchActiveTabData()},showNetworkDetails(e){this.selectedNetwork={...e,exposures:"string"===typeof e.exposures?JSON.parse(e.exposures):e.exposures},this.networkDetailsVisible=!0},handleNetworkDetailsClose(){this.networkDetailsVisible=!1,this.selectedNetwork=null},showMountDetails(e){this.selectedMountContainer={...e,mounts:"string"===typeof e.mounts?JSON.parse(e.mounts):e.mounts},this.mountDetailsVisible=!0},handleMountDetailsClose(){this.mountDetailsVisible=!1,this.selectedMountContainer=null},getAllMounts(){var e;return null!==(e=this.selectedMountContainer)&&void 0!==e&&e.mounts?this.selectedMountContainer.mounts:[]},showEnvironmentDetails(e){this.selectedEnvironment={...e,env:"string"===typeof e.env?JSON.parse(e.env):e.env||[]},this.environmentDetailsVisible=!0},handleEnvironmentDetailsClose(){this.environmentDetailsVisible=!1,this.selectedEnvironment=null},getAllEnvironmentVars(){var e;return null!==(e=this.selectedEnvironment)&&void 0!==e&&e.env?this.selectedEnvironment.env:[]},showProcessDetails(e){this.selectedProcessContainer=e,this.processDetailsVisible=!0},handleProcessDetailsClose(){this.processDetailsVisible=!1,this.selectedProcessContainer=null},getProcessList(){var e;if(null===(e=this.selectedProcessContainer)||void 0===e||!e.processes)return[];const t=this.selectedProcessContainer.processes;return t.process_list&&t.process_list.length>0?t.process_list:t.pid.map((e,s)=>({pid:t.pid[s],ppid:t.ppid[s],user:t.user[s],cmd:t.cmd[s],time:t.time[s],stime:t.stime[s]}))},showProcessDetailInfo(e){this.selectedProcessInfo=e,this.processDetailInfoVisible=!0},handleProcessDetailInfoClose(){this.processDetailInfoVisible=!1,this.selectedProcessInfo=null},showInspectDetails(e){try{const t="string"===typeof e.inspect_data?JSON.parse(e.inspect_data):e.inspect_data||{};this.$refs.jsonDetailModal.showDetailModal("Container Inspect Data",t)}catch(t){console.error("Failed to parse inspect data:",t),this.$message.error("Failed to parse inspect data")}}}},Q=K,X=(s("7a74"),Object(g["a"])(Q,d,p,!1,null,"ef13263e",null)),Z=X.exports,ee={components:{RefreshButton:c["a"],CrictlInfo:Z,JsonDetailModal:u["a"],ProcessDetailModal:b,ProcessTable:x,NetworkListeningModal:M,NetworkListeningCell:O,MountCell:B},name:"DockerInfo",props:{},data(){const e=this.$createElement;return{activeTab:"docker_host_config",hostConfigData:[],containerData:[],networkData:[],loadingHostConfig:!1,loadingContainers:!1,loadingNetworks:!1,hostConfigColumns:[{title:"Docker Version",dataIndex:"docker_version",key:"docker_version"},{title:"Daemon Config",dataIndex:"daemon_config",key:"daemon_config"},{title:"In Docker Group",dataIndex:"user_in_docker_group",key:"user_in_docker_group",render:e=>e?"Yes":"No"},{title:"Is Root",dataIndex:"is_root_user",key:"is_root_user",render:e=>e?"Yes":"No"}],containerColumns:[{title:"Container ID",dataIndex:"container_id",key:"container_id",width:"150px",ellipsis:!1},{title:"Name",dataIndex:"inspect_data",key:"name",width:"15%",ellipsis:!1,customRender:(e,t)=>{try{const e=this.parseJsonField(t.inspect_data,{});return{children:e.Name||"N/A",props:{style:{whiteSpace:"normal",wordBreak:"break-word"}}}}catch(s){return console.warn("Failed to render container name:",s),"N/A"}}},{title:"Privileged",dataIndex:"inspect_data",key:"privileged",width:"100px",customRender:(e,t)=>{try{var s;const e=this.parseJsonField(t.inspect_data,{}),i=null===(s=e.HostConfig)||void 0===s?void 0:s.Privileged;return!0===i?"Yes":"No"}catch(i){return console.warn("Failed to render privileged status:",i),"No"}}},{title:"Environment",dataIndex:"env",key:"env",width:"200px",customRender:(t,s)=>{try{const t=this.parseJsonField(s.env,[]);return t.length?e("div",{style:"font-size: 12px;"},[t.slice(0,1).map((t,s)=>e("div",{key:s,style:"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"},[t.length>200?t.substring(0,200)+"...":t])),t.length>1&&e("a-button",{attrs:{type:"link",size:"small"},on:{click:e=>{e.stopPropagation(),this.showEnvironmentDetails(s)}}},["+",t.length-1," more..."])]):"No environment variables"}catch(i){return console.warn("Failed to render env vars:",i),"None"}}},{title:"Mounts",dataIndex:"mounts",key:"mounts",width:"25%",ellipsis:!1,customRender:(t,s)=>e("mount-cell",{attrs:{record:s,parseJsonField:this.parseJsonField},on:{"show-details":this.showMountDetails}})},{title:"Network Listening",dataIndex:"exposures",key:"exposures",width:"25%",ellipsis:!1,customRender:(t,s)=>e("network-listening-cell",{attrs:{record:s,parseJsonField:this.parseJsonField},on:{"show-details":this.showNetworkDetails}})},{title:"Processes",dataIndex:"processes",key:"processes",width:"120px",align:"center",customRender:(t,s)=>{try{var i;const t=this.parseJsonField(s.processes,{});return null!==t&&void 0!==t&&null!==(i=t.process_list)&&void 0!==i&&i.length?e("a",{on:{click:e=>{e.stopPropagation(),this.showProcessDetails(s)}}},[t.process_list.length," processes"]):"N/A"}catch(n){return console.warn("Failed to render processes:",n),"None"}}},{title:"Inspect Data",dataIndex:"inspect_data",key:"inspect_data",width:"150px",align:"center",customRender:(t,s)=>e("span",{style:"display: inline-block; line-height: 22px;"},[e("a",{on:{click:e=>{e.stopPropagation(),this.showInspectDetails(s)}},style:"color: #1890ff; cursor: pointer;"},["View Inspect"])])}],networkColumns:[{title:"Name",dataIndex:"name",key:"name",width:"120px",customRender:e=>e||"None"},{title:"Network ID",dataIndex:"network_id",key:"network_id",width:"180px",customRender:e=>e||"None"},{title:"Driver",dataIndex:"driver",key:"driver",width:"100px",customRender:e=>e||"None"},{title:"Scope",dataIndex:"scope",key:"scope",width:"100px",customRender:e=>e||"None"},{title:"IPv6",dataIndex:"ipv6",key:"ipv6",width:"80px",customRender:e=>void 0===e||null===e?"None":e?"Yes":"No"},{title:"Internal",dataIndex:"internal",key:"internal",width:"80px",customRender:e=>void 0===e||null===e?"None":e?"Yes":"No"},{title:"Labels",dataIndex:"labels",key:"labels",width:"120px",customRender:e=>e&&0!==Object.keys(e).length?Object.entries(e).map(([e,t])=>`${e}=${t}`).join(", "):"None"},{title:"Created",dataIndex:"created_at",key:"created_at",width:"160px",customRender:e=>e||"None"}],pagination:{pageSize:100},resourceMapping:{docker_host_config:"get_docker_host_config",docker_container:"get_docker_containers",docker_network:"get_docker_networks"},networkDetailsVisible:!1,selectedNetwork:null,mountDetailsVisible:!1,selectedMountContainer:null,environmentDetailsVisible:!1,selectedEnvironment:null,processDetailsVisible:!1,selectedProcessContainer:null,processDetailInfoVisible:!1,selectedProcessInfo:null,activeEngine:"docker"}},computed:{...Object(r["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(e){this.resetData(),e&&this.fetchActiveTabData()}},mounted(){this.selectedNodeIp&&this.fetchActiveTabData()},methods:{handleTabChange(e){this.activeTab=e,this.resetData(),this.fetchActiveTabData()},async fetchActiveTabData(){if(!this.selectedNodeIp)return console.error("Node IP is not defined"),void this.resetData();const e=this.activeTab,t=this.camelCaseToDataName(e),s="loading"+this.capitalizeFirstLetter(t.replace("Data",""));this[s]=!0;try{const i=await l["a"].get(`/api/docker/${e}/${this.selectedNodeIp}`,{params:{dbFile:this.currentProject}}),n=i.data;this[t]="docker_host_config"===e&&n?[n]:n}catch(i){console.error(`Error fetching ${e}:`,i),this[t]=[]}finally{this[s]=!1}},camelCaseToDataName(e){const t=e.replace("docker_","");return t.replace(/_([a-z])/g,(e,t)=>t.toUpperCase())+"Data"},capitalizeFirstLetter(e){return e.charAt(0).toUpperCase()+e.slice(1)},resetData(){this.hostConfigData=[],this.containerData=[],this.networkData=[]},getDaemonConfig(e){var t;return null!==(t=this.hostConfigData[0])&&void 0!==t&&t.daemon_config?this.hostConfigData[0].daemon_config[e]:null},getRegistryMirrors(){const e=this.getDaemonConfig("RegistryConfig");return(null===e||void 0===e?void 0:e.Mirrors)||[]},formatBytes(e){if(!e)return"0 B";const t=1024,s=["B","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return`${parseFloat((e/Math.pow(t,i)).toFixed(2))} ${s[i]}`},parseJsonField(e,t){try{return Array.isArray(e)||"object"===typeof e&&null!==e?this.cleanReactiveObject(e):"string"===typeof e?JSON.parse(e):t}catch(s){return console.warn("Failed to parse JSON field:",s),t}},showInspectDetails(e){try{const t=this.parseJsonField(e.inspect_data,{});this.$refs.jsonDetailModal.showDetailModal("Container Inspect Data",t)}catch(t){console.error("Failed to parse inspect data:",t),this.$message.error("Failed to parse inspect data")}},renderPreviewWithMore(e,t,s,i){const n=this.$createElement;return e&&e.length?n("div",{style:"font-size: 12px;"},[e.slice(0,2).map((e,s)=>t(e,s)),e.length>2&&n("a-button",{attrs:{type:"link",size:"small"},on:{click:e=>{e.stopPropagation(),i()}}},["+",e.length-2," ",s])]):"None"},cleanReactiveObject(e){if(!e)return null;if(Array.isArray(e))return e.map(e=>this.cleanReactiveObject(e));if("object"===typeof e){const t={};for(const s in e)s.startsWith("_")||s.startsWith("$")||"__ob__"===s||(t[s]=this.cleanReactiveObject(e[s]));return t}return e},getProcessList(){var e;return null!==(e=this.selectedProcessContainer)&&void 0!==e&&null!==(e=e.processes)&&void 0!==e&&e.process_list?this.selectedProcessContainer.processes.process_list:[]},getNetworkListening(){var e;if(null===(e=this.selectedContainer)||void 0===e||!e.exposures)return[];const t=this.parseJsonField(this.selectedContainer.exposures,{});return t.listening_ports||[]},showNetworkDetails(e){this.selectedNetwork={...e,exposures:this.parseJsonField(e.exposures,{})},this.networkDetailsVisible=!0},handleNetworkDetailsClose(){this.networkDetailsVisible=!1,this.selectedNetwork=null},showMountDetails(e){this.selectedMountContainer={...e,mounts:this.parseJsonField(e.mounts,[])},this.mountDetailsVisible=!0},handleMountDetailsClose(){this.mountDetailsVisible=!1,this.selectedMountContainer=null},getAllMounts(){var e;return null!==(e=this.selectedMountContainer)&&void 0!==e&&e.mounts?this.selectedMountContainer.mounts:[]},showEnvironmentDetails(e){this.selectedEnvironment={...e,env:this.parseJsonField(e.env,[])},this.environmentDetailsVisible=!0},handleEnvironmentDetailsClose(){this.environmentDetailsVisible=!1,this.selectedEnvironment=null},getAllEnvironmentVars(){var e;return null!==(e=this.selectedEnvironment)&&void 0!==e&&e.env?this.selectedEnvironment.env:[]},showProcessDetails(e){this.selectedProcessContainer={...e,processes:this.parseJsonField(e.processes,{})},this.processDetailsVisible=!0},handleProcessDetailsClose(){this.processDetailsVisible=!1,this.selectedProcessContainer=null},showProcessDetailInfo(e){this.selectedProcessInfo=e,this.processDetailInfoVisible=!0},handleProcessDetailInfoClose(){this.processDetailInfoVisible=!1,this.selectedProcessInfo=null}}},te=ee,se=(s("1a67"),Object(g["a"])(te,o,a,!1,null,"43ac44d4",null)),ie=se.exports,ne={components:{DockerInfo:ie}},oe=ne,ae=Object(g["a"])(oe,i,n,!1,null,null,null);t["default"]=ae.exports},"11b2":function(e,t,s){},"1a67":function(e,t,s){"use strict";s("9af8")},"21b7":function(e,t,s){},3492:function(e,t,s){},"38d3":function(e,t,s){"use strict";s("866b")},"3a7d":function(e,t,s){"use strict";s("3492")},"4c4c":function(e,t,s){"use strict";s("71af")},"53d4":function(e,t,s){"use strict";s("b1ba")},"55a7":function(e,t,s){},"71af":function(e,t,s){},"7a74":function(e,t,s){"use strict";s("a428")},"866b":function(e,t,s){},"9af8":function(e,t,s){},a366:function(e,t,s){"use strict";s("21b7")},a428:function(e,t,s){},ac5e:function(e,t,s){"use strict";s("55a7")},b1ba:function(e,t,s){},b854:function(e,t,s){"use strict";var i=function(){var e=this,t=e._self._c;return t("div")},n=[],o=(s("0643"),s("4e3e"),s("a026")),a=s("838b"),r=s.n(a),l={name:"JsonDetailModal",methods:{showDetailModal(e,t,s={}){const i=this.$createElement,n=Math.min(s.width||1200,.9*window.innerWidth),a=Math.min(700,.6*window.innerHeight),l={search:"search-"+Date.now(),counter:"counter-"+Date.now(),theme:"theme-"+Date.now()};let c=!0;const d=i("div",{style:"display: flex; justify-content: space-between; align-items: center;"},[i("div",{style:"display: flex; align-items: center;"},[i("a-icon",{attrs:{type:"code"},style:"margin-right: 8px; font-size: 16px;"}),i("span",{style:"font-weight: 500;"},[e])]),i("div",{style:"display: flex; align-items: center;"},[i("div",{attrs:{id:l.counter},style:"margin-right: 10px; min-width: 60px; text-align: right; color: #666;"}),i("a-input",{attrs:{id:l.search,placeholder:"搜索 (Enter: ↓  Shift+Enter: ↑)",allowClear:!0,prefix:i("a-icon",{attrs:{type:"search"},style:"color: rgba(0,0,0,.25)"})},style:"width: 250px;"}),i("a-button",{attrs:{id:l.theme,type:"link",icon:"bg-colors",title:"切换主题"},style:"margin-left: 8px; color: #1890ff; font-size: 16px;"}),i("a-button",{attrs:{id:"copy-btn",type:"link",icon:"copy",title:"复制内容"},style:"margin-left: 8px; color: #1890ff; font-size: 16px;"})])]),p="object"===typeof t?i("div",{style:`height: ${a}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`,class:"json-container theme-dark",attrs:{id:"json-container"}}):i("div",{style:`height: ${a}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`},[String(t)]);this.$root.$confirm({title:d,content:p,width:n,okText:s.okText||"关闭",icon:null,cancelButtonProps:{style:{display:"none"}},class:"detail-modal",maskClosable:!1,getContainer:()=>document.body.appendChild(document.createElement("div"))}),setTimeout(()=>{if("object"===typeof t){const e=document.getElementById("json-container");if(e){const s=new o["a"]({render:e=>e(r.a,{props:{data:t,deep:1/0,showDoubleQuotes:!0,showLength:!0,showLineNumbers:!0},style:{height:"100%",overflow:"auto"}})});s.$mount(),e.appendChild(s.$el)}}const e=document.getElementById(l.search),s=document.getElementById(l.counter);let i=[],n=-1;if(e&&s){const t=e=>{if(i=[],n=-1,s.textContent="",e)try{const t=document.querySelectorAll(".vjs-key, .vjs-value"),n=new RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");if(document.querySelectorAll(".vjs-search-match").forEach(e=>{e.classList.remove("vjs-search-match")}),document.querySelectorAll(".vjs-search-current").forEach(e=>{e.classList.remove("vjs-search-current")}),t.forEach(e=>{const t=e.textContent;let s;n.lastIndex=0;while(null!==(s=n.exec(t)))i.push({node:e,text:s[0]}),s.index===n.lastIndex&&n.lastIndex++}),0===i.length)return void(s.textContent="无匹配项");s.textContent="0/"+i.length,i.forEach(e=>{e.node.classList.add("vjs-search-match")})}catch(t){console.error("搜索错误:",t)}},o=e=>{if(0===i.length)return;e=Math.max(0,Math.min(i.length-1,e)),n>=0&&n<i.length&&i[n].node.classList.remove("vjs-search-current"),n=e,s.textContent=`${n+1}/${i.length}`;const t=i[n];if(t){t.node.classList.add("vjs-search-current");let e=t.node.parentElement;while(e){if(e.classList&&e.classList.contains("vjs-tree-node")&&!e.classList.contains("is-expanded")){const t=e.querySelector(".vjs-tree-brackets");t&&t.click()}e=e.parentElement}t.node.scrollIntoView({behavior:"smooth",block:"center"})}};let a;e.addEventListener("input",e=>{a&&clearTimeout(a),a=setTimeout(()=>{t(e.target.value.trim())},300)}),e.addEventListener("keydown",e=>{"Enter"===e.key&&(e.preventDefault(),o(e.shiftKey?n-1:n+1))})}const a=document.getElementById(l.theme);a&&a.addEventListener("click",()=>{const e=document.querySelector(".json-container");e&&(e.classList.contains("theme-light")?(e.classList.remove("theme-light"),e.classList.add("theme-dark"),e.style.backgroundColor="#1e1e1e",c=!0):(e.classList.remove("theme-dark"),e.classList.add("theme-light"),e.style.backgroundColor="#fff",c=!1))});const d=document.getElementById("copy-btn");d&&d.addEventListener("click",()=>{try{const e="object"===typeof t?JSON.stringify(t,null,2):String(t);if(navigator.clipboard&&window.isSecureContext)navigator.clipboard.writeText(e).then(()=>{this.$message.success(this.$t("common.copiedToClipboard"))}).catch(e=>{console.error("复制失败:",e),this.$message.error("复制失败")});else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select();const s=document.execCommand("copy");document.body.removeChild(t),s?this.$message.success(this.$t("common.copiedToClipboard")):this.$message.error(this.$t("common.copyFailed"))}}catch(e){this.$message.error(this.$t("common.copyFailed"))}})},300)}}},c=l,d=(s("4c4c"),s("2877")),p=Object(d["a"])(c,i,n,!1,null,null,null);t["a"]=p.exports},f188:function(e,t,s){"use strict";var i=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},n=[],o=s("2f62"),a={computed:{...Object(o["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},r=a,l=s("2877"),c=Object(l["a"])(r,i,n,!1,null,"80cb1374",null);t["a"]=c.exports}}]);
//# sourceMappingURL=chunk-08a0de52.e5253694.js.map