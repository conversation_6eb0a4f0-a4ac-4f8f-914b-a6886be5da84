<template>
  <div>
    <a-layout class="layout-simple">
      <a-layout-content>
        <div class="content-wrapper">
          <router-view v-if="isRouterViewMounted" />
          <div v-else>Loading...</div>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script>
export default {
  name: 'SimpleLayout',
  data() {
    return {
      isRouterViewMounted: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.isRouterViewMounted = true;
    });
  }
}
</script>

<style lang="scss" scoped>
.layout-simple {
  min-height: 100vh;
  background: #f0f2f5;
}

.content-wrapper {
  padding: 24px;
  min-height: 100vh;
  background: #fff;
}
</style>