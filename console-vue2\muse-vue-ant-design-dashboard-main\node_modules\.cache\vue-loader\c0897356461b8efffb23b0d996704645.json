{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PortInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PortInfo.vue", "mtime": 1753170356135}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PortInfo.vue"], "names": [], "mappings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file": "PortInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\" width=\"20\" height=\"20\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.port') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchPorts\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs v-model:activeKey=\"activeTabKey\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"tcp\" tab=\"TCP\">\r\n        <a-table\r\n          :columns=\"tcpColumns\"\r\n          :data-source=\"tcpPorts\"\r\n          :rowKey=\"record => `${record.ip}_${record.port}`\"\r\n          :pagination=\"pagination.total > 0 ? pagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No TCP ports found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n\r\n      <a-tab-pane key=\"udp\" tab=\"UDP\">\r\n        <a-table\r\n          :columns=\"udpColumns\"\r\n          :data-source=\"udpPorts\"\r\n          :rowKey=\"record => `${record.ip}_${record.port}`\"\r\n          :pagination=\"udpPagination.total > 0 ? udpPagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No UDP ports found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n\r\n      <a-tab-pane key=\"unix_socket\" tab=\"UNIX Socket\">\r\n        <a-table\r\n          :columns=\"unixSocketColumns\"\r\n          :data-source=\"unixSockets\"\r\n          :rowKey=\"record => record.inode\"\r\n          :pagination=\"unixSocketPagination.total > 0 ? unixSocketPagination : false\"\r\n        >\r\n          <template #emptyText>\r\n            <a-empty description=\"No UNIX sockets found\" />\r\n          </template>\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n\r\n    <a-modal\r\n      v-model:visible=\"modalVisible\"\r\n      :title=\"modalTitle\"\r\n      @cancel=\"handleModalClose\"\r\n      width=\"600px\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleModalClose\">Cancel</a-button>\r\n      </template>\r\n      <div style=\"white-space: pre-wrap\">{{ modalContent.join('\\n') }}</div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      tcpPorts: [],\r\n      udpPorts: [],\r\n      unixSockets: [],\r\n      activeTabKey: 'tcp', // 默认选中TCP标签页\r\n      tcpColumns: [\r\n        {\r\n          title: 'Address',\r\n          key: 'address',\r\n          width: 200,\r\n          customRender: (text, record) => `${record.ip}:${record.port}`,\r\n        },\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 200,\r\n          customRender: (pid, record) => {\r\n            if (!pid) return '-';\r\n            const [pidNum, procName] = pid.split('/');\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pidNum)}>\r\n                {pid}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Protocols',\r\n          dataIndex: 'protocols',\r\n          key: 'protocols',\r\n          width: 150,\r\n          customRender: (protocols) => {\r\n            if (!protocols?.offered?.length) return '-';\r\n            return protocols.offered.join(', ');\r\n          },\r\n        },\r\n        {\r\n          title: 'Certificate',\r\n          dataIndex: 'certificate',\r\n          key: 'certificate',\r\n          width: 800,\r\n          customRender: (cert) => {\r\n            if (!cert?.summary?.length) return '-';\r\n\r\n            // 证书字段说明\r\n            const certFieldDescriptions = {\r\n              'CN:': '通用名称 - 证书所标识的实体名称',\r\n              'Issuer:': '证书颁发者 - 签发此证书的证书机构',\r\n              'Subject Alt Names:': '主题备用名 - 证书可以保护的其他域名或IP',\r\n              'Chain Status:': '证书链状态 - 验证证书信任链的完整性',\r\n              'Revocation:': '吊销状态 - 检查证书是否被吊销',\r\n              'Validity Period:': '有效期长度 - 证书的有效时间跨度',\r\n              'Expiration Status:': '过期状态 - 证书是否已过期',\r\n              'Key Size:': '密钥大小 - 证书使用的加密密钥长度',\r\n              'Signature Algorithm:': '签名算法 - 用于签发证书的加密算法',\r\n              'Client Auth:': '客户端认证 - 是否支持客户端证书认证',\r\n              'Key Usage:': '密钥用途 - 证书允许的使用场景',\r\n              'Serial Number:': '序列号 - 证书的唯一标识符',\r\n              'Fingerprint SHA256:': '指纹 - 证书的SHA256哈希值',\r\n              'Valid Until:': '有效期至 - 证书的过期时间'\r\n            };\r\n\r\n            return (\r\n              <div>\r\n                {cert.summary.map(item => {\r\n                  const fieldName = Object.keys(certFieldDescriptions).find(key => item.startsWith(key));\r\n                  const [label, ...valueParts] = item.split(/(?<=:)\\s/);\r\n                  const value = valueParts.join(' ');\r\n\r\n                  return (\r\n                    <a-tooltip key={item} placement=\"right\" title={fieldName ? certFieldDescriptions[fieldName] : ''}>\r\n                      <div class=\"cert-field\">\r\n                        <span class=\"cert-label\">{label}</span> {value}\r\n                      </div>\r\n                    </a-tooltip>\r\n                  );\r\n                })}\r\n              </div>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'HTTP Info',\r\n          dataIndex: 'http_info',\r\n          key: 'http_info',\r\n          width: 150,\r\n          customRender: (httpInfo) => {\r\n            if (!httpInfo?.raw_output) return 'No response';\r\n\r\n            // Extract status code from raw response\r\n            const statusCodeMatch = httpInfo.raw_output.match(/HTTP\\/[\\d.]+ (\\d{3})/);\r\n            const statusCode = statusCodeMatch ? statusCodeMatch[1] : 'Unknown';\r\n\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('HTTP Response', [\r\n                `Status Code: ${statusCode}`,\r\n                `Protocol: ${httpInfo.protocol}`,\r\n                '---',\r\n                'Raw Response:',\r\n                httpInfo.raw_output\r\n              ])}>\r\n                {statusCode}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Cipher Suites',\r\n          dataIndex: 'cipher_suites',\r\n          key: 'cipher_suites',\r\n          width: 150,\r\n          customRender: (cipherSuites) => {\r\n            if (!cipherSuites?.details?.length) return '-';\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('Cipher Suites', cipherSuites.details)}>\r\n                {`${cipherSuites.details.length} suites`}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Vulnerabilities',\r\n          dataIndex: 'vulnerabilities',\r\n          key: 'vulnerabilities',\r\n          width: 150,\r\n          customRender: (vulns) => {\r\n            if (!vulns?.critical?.length) return 'No vulnerabilities';\r\n            const details = vulns.critical.map(v =>\r\n              `${v.name} (${v.severity}): ${v.status}`\r\n            );\r\n            return (\r\n              <a onClick={() => this.showDetailsModal('Vulnerabilities', details)}>\r\n                {`${vulns.critical.length} vulnerabilities`}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n      ],\r\n\r\n      // UDP端口列\r\n      udpColumns: [\r\n        {\r\n          title: 'Proto',\r\n          dataIndex: 'proto',\r\n          key: 'proto',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Recv-Q',\r\n          dataIndex: 'recv_q',\r\n          key: 'recv_q',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Send-Q',\r\n          dataIndex: 'send_q',\r\n          key: 'send_q',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Local Address',\r\n          key: 'local_address',\r\n          width: 180,\r\n          customRender: (_, record) => `${record.ip}:${record.port}`,\r\n        },\r\n        {\r\n          title: 'Foreign Address',\r\n          dataIndex: 'foreign_address',\r\n          key: 'foreign_address',\r\n          width: 180,\r\n        },\r\n        {\r\n          title: 'State',\r\n          dataIndex: 'state',\r\n          key: 'state',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'PID/Program',\r\n          dataIndex: 'pid_program',\r\n          key: 'pid_program',\r\n          width: 300,\r\n          customRender: (pid_program) => {\r\n            if (!pid_program) return '-';\r\n            const parts = pid_program.split('/');\r\n            const pid = parts[0];\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pid)}>\r\n                {pid_program}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n      ],\r\n\r\n      // UNIX Socket列\r\n      unixSocketColumns: [\r\n        {\r\n          title: 'Proto',\r\n          dataIndex: 'proto',\r\n          key: 'proto',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'RefCnt',\r\n          dataIndex: 'refcnt',\r\n          key: 'refcnt',\r\n          width: 80,\r\n        },\r\n        {\r\n          title: 'Flags',\r\n          dataIndex: 'flags',\r\n          key: 'flags',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'type',\r\n          key: 'type',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'State',\r\n          dataIndex: 'state',\r\n          key: 'state',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'I-Node',\r\n          dataIndex: 'inode',\r\n          key: 'inode',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: 'PID/Program',\r\n          dataIndex: 'pid_program',\r\n          key: 'pid_program',\r\n          width: 180,\r\n          customRender: (pid_program) => {\r\n            if (!pid_program) return '-';\r\n            const parts = pid_program.split('/');\r\n            const pid = parts[0];\r\n            return (\r\n              <a onClick={() => this.navigateToProcessDetail(pid)}>\r\n                {pid_program}\r\n              </a>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: 'Path',\r\n          dataIndex: 'path',\r\n          key: 'path',\r\n          width: 400,\r\n          customRender: (path) => {\r\n            if (!path) return '-';\r\n            return <div style=\"word-break: break-word;\">{path}</div>;\r\n          },\r\n        },\r\n      ],\r\n\r\n      // TCP端口分页\r\n      pagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.pagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'tcp' }\r\n          });\r\n          this.fetchPorts('tcp');\r\n        },\r\n      },\r\n\r\n      // UDP端口分页\r\n      udpPagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.udpPagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'udp' }\r\n          });\r\n          this.fetchPorts('udp');\r\n        },\r\n      },\r\n\r\n      // UNIX Socket分页\r\n      unixSocketPagination: {\r\n        current: parseInt(this.$route.query.page) || 1,\r\n        pageSize: 50,\r\n        total: 0,\r\n        showSizeChanger: false,\r\n        showQuickJumper: false,\r\n        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,\r\n        onChange: (page) => {\r\n          this.unixSocketPagination.current = page;\r\n          this.$router.replace({\r\n            query: { ...this.$route.query, page, port_type: 'unix_socket' }\r\n          });\r\n          this.fetchPorts('unix_socket');\r\n        },\r\n      },\r\n      modalVisible: false,\r\n      modalTitle: '',\r\n      modalContent: [],\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp() {\r\n      this.fetchPorts('tcp');\r\n      this.fetchPorts('udp');\r\n      this.fetchPorts('unix_socket');\r\n    },\r\n    '$route.query.page': {\r\n      handler(newPage) {\r\n        const portType = this.$route.query.port_type || 'tcp';\r\n        if (newPage) {\r\n          if (portType === 'tcp' && parseInt(newPage) !== this.pagination.current) {\r\n            this.pagination.current = parseInt(newPage);\r\n            this.fetchPorts('tcp');\r\n          } else if (portType === 'udp' && parseInt(newPage) !== this.udpPagination.current) {\r\n            this.udpPagination.current = parseInt(newPage);\r\n            this.fetchPorts('udp');\r\n          } else if (portType === 'unix_socket' && parseInt(newPage) !== this.unixSocketPagination.current) {\r\n            this.unixSocketPagination.current = parseInt(newPage);\r\n            this.fetchPorts('unix_socket');\r\n          }\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    '$route.query.port_type': {\r\n      handler(newPortType) {\r\n        if (newPortType) {\r\n          this.activeTabKey = newPortType;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchPorts('tcp');\r\n    this.fetchPorts('udp');\r\n    this.fetchPorts('unix_socket');\r\n  },\r\n  methods: {\r\n    // 处理标签页切换\r\n    handleTabChange(activeKey) {\r\n      this.activeTabKey = activeKey;\r\n      this.$router.replace({\r\n        query: { ...this.$route.query, port_type: activeKey }\r\n      });\r\n      this.fetchPorts(activeKey);\r\n    },\r\n\r\n    async fetchPorts(portType = 'tcp') {\r\n      if (!this.selectedNodeIp) {\r\n        this.tcpPorts = [];\r\n        this.udpPorts = [];\r\n        this.unixSockets = [];\r\n        this.pagination.total = 0;\r\n        this.udpPagination.total = 0;\r\n        this.unixSocketPagination.total = 0;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        let pagination;\r\n        if (portType === 'tcp') {\r\n          pagination = this.pagination;\r\n        } else if (portType === 'udp') {\r\n          pagination = this.udpPagination;\r\n        } else if (portType === 'unix_socket') {\r\n          pagination = this.unixSocketPagination;\r\n        }\r\n\r\n        const { current, pageSize } = pagination;\r\n        const response = await axios.get(`/api/port/${this.selectedNodeIp}`, {\r\n          params: {\r\n            page: current,\r\n            page_size: pageSize,\r\n            port_type: portType,\r\n            dbFile: this.currentProject\r\n          },\r\n        });\r\n\r\n        if (portType === 'tcp') {\r\n          this.tcpPorts = response.data.data || response.data;\r\n          this.pagination.total = response.data.total || 0;\r\n        } else if (portType === 'udp') {\r\n          this.udpPorts = response.data.data || response.data;\r\n          this.udpPagination.total = response.data.total || 0;\r\n        } else if (portType === 'unix_socket') {\r\n          this.unixSockets = response.data.data || response.data;\r\n          this.unixSocketPagination.total = response.data.total || 0;\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching ${portType} ports:`, error);\r\n        this.$message.error(`Failed to fetch ${portType} ports data`);\r\n\r\n        if (portType === 'tcp') {\r\n          this.tcpPorts = [];\r\n          this.pagination.total = 0;\r\n        } else if (portType === 'udp') {\r\n          this.udpPorts = [];\r\n          this.udpPagination.total = 0;\r\n        } else if (portType === 'unix_socket') {\r\n          this.unixSockets = [];\r\n          this.unixSocketPagination.total = 0;\r\n        }\r\n      }\r\n    },\r\n    navigateToProcessDetail(pid) {\r\n      this.$router.push({\r\n        name: 'ProcessDetail',\r\n        params: { pid: pid },\r\n        query: { page: this.pagination.current }\r\n      });\r\n    },\r\n    showDetailsModal(title, content) {\r\n      this.modalTitle = title;\r\n      this.modalContent = content;\r\n      this.modalVisible = true;\r\n    },\r\n    handleModalClose() {\r\n      this.modalVisible = false;\r\n      this.modalContent = [];\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.cert-field {\r\n  margin: 2px 0;\r\n\r\n  .cert-label {\r\n    color: #d10d7d;\r\n    font-size: 0.95em;\r\n    min-width: 120px;\r\n    display: inline-block;\r\n  }\r\n}\r\n</style>\r\n"]}]}