{"version": 3, "sources": ["webpack:///./src/components/Cards/ToolsPanel.vue?294e", "webpack:///./src/views/GenerateScript.vue", "webpack:///./src/components/Cards/ToolsPanel.vue", "webpack:///./src/components/Cards/GeneralToolTab.vue", "webpack:///./src/assets/scripts/default_command.js", "webpack:///./src/assets/scripts/spider_command.js", "webpack:///./src/assets/scripts/index.js", "webpack:///./src/components/common/ScriptEditor.vue", "webpack:///src/components/common/ScriptEditor.vue", "webpack:///./src/components/common/ScriptEditor.vue?89fd", "webpack:///./src/components/common/ScriptEditor.vue?7f63", "webpack:///src/components/Cards/GeneralToolTab.vue", "webpack:///./src/components/Cards/GeneralToolTab.vue?da47", "webpack:///./src/components/Cards/GeneralToolTab.vue?2746", "webpack:///./src/components/Cards/SpiderToolTab.vue", "webpack:///src/components/Cards/SpiderToolTab.vue", "webpack:///./src/components/Cards/SpiderToolTab.vue?c64b", "webpack:///./src/components/Cards/SpiderToolTab.vue?3f38", "webpack:///src/components/Cards/ToolsPanel.vue", "webpack:///./src/components/Cards/ToolsPanel.vue?ac87", "webpack:///./src/components/Cards/ToolsPanel.vue?f58e", "webpack:///src/views/GenerateScript.vue", "webpack:///./src/views/GenerateScript.vue?1b1f", "webpack:///./src/views/GenerateScript.vue?4be5"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "borderBottom", "currentStepComputed", "scopedSlots", "_u", "key", "fn", "proxy", "staticStyle", "$t", "on", "handleToolTabChange", "ref", "isProcessing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedIp", "currentProject", "onToolPathChanged", "onGeneralScriptSaved", "onStartGeneralTool", "onSpiderScriptSaved", "on<PERSON>un<PERSON><PERSON><PERSON>", "onNodesSelected", "model", "value", "callback", "$$v", "expression", "handleProxyChange", "form", "beforeUpload", "fileList", "handleUploadChange", "_v", "_s", "fileName", "_e", "scriptTabs", "activeScriptTab", "onScriptSaved", "scriptContent", "toolConfigComplete", "startGeneralTool", "localSavePath", "defaultScriptContent", "scriptMap", "name", "content", "spiderScriptContent", "getGeneralScriptNames", "getSpiderScriptNames", "getScriptContent", "_scriptMap$key", "disabled", "showScriptModal", "confirmScript", "scriptModalVisible", "maxHeight", "overflow", "handleModalVisibleChange", "handleScriptOk", "handleScriptCancel", "handleTabChange", "_l", "tab", "minRows", "maxRows", "scriptContentInternal", "props", "type", "String", "default", "scriptType", "Array", "defaultTab", "Boolean", "filename", "successMessage", "data", "script<PERSON>ath", "computed", "get", "set", "$emit", "watch", "newVal", "methods", "loadScriptContent", "tabKey", "trim", "saveScriptToFile", "$message", "warning", "path", "success", "blob", "Blob", "file", "File", "formData", "FormData", "append", "response", "axios", "post", "headers", "error", "_error$response", "console", "message", "component", "mixins", "NotificationMixin", "components", "ScriptEditor", "required", "$form", "createForm", "uploadUrl", "toolPath", "mapState", "length", "created", "isZip", "endsWith", "uploadFile", "uploadingMessage", "loading", "fileInfo", "uid", "status", "$forceUpdate", "info", "slice", "getToolData", "$notify", "title", "spiderScriptTabs", "activeSpiderScriptTab", "run<PERSON><PERSON><PERSON>", "spiderToolPath", "spiderSavePath", "spiderScriptPath", "loadSpiderScriptContent", "requestData", "targets", "proxy_ip", "script_content", "dbFile", "TaskPollingMixin", "ProxySelector", "TaskProgressCard", "NodeSelector", "GeneralToolTab", "SpiderToolTab", "currentStep", "activeToolTab", "taskId", "_this$activeToolTask", "activeToolTask", "task_id", "$store", "dispatch", "taskInfo", "localStorage", "getItem", "projectFile", "JSON", "parse", "checkActiveTask", "removeItem", "mapActions", "ip", "$refs", "generalToolTab", "toolData", "spiderToolTab", "script_path", "result_path", "local_save_path", "startTaskGeneric", "endpoint", "errorTitle", "previousTaskInfo", "clearTaskNotificationMark", "e", "responseData", "setItem", "stringify", "initialTaskState", "nodes", "for<PERSON>ach", "host_name", "progress", "$nextTick", "progressElement", "document", "querySelector", "scrollIntoView", "behavior", "block", "pollError", "startPolling", "errorMessage", "taskCompleted", "Error", "Object", "values", "allCompleted", "every", "node", "includes", "startPollingWrapper", "activated", "handler", "newProject", "oldProject", "stopPolling", "immediate", "ToolsPanel"], "mappings": "gHAAA,W,kECAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,eAAe,IAAI,IAAI,IAEvMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,gCAAgCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,YAAa,UAAY,CAAEC,aAAc,uBAAwB,CAACN,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,UAAU,CAACG,YAAY,aAAaD,MAAM,CAAC,QAAUJ,EAAIS,oBAAoB,KAAO,UAAU,CAACP,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,cAAcU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,iBAAiBU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,cAAcU,OAAM,QAAW,IAAI,GAAGZ,EAAG,SAAS,CAACa,YAAY,CAAC,OAAS,YAAYX,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIgB,GAAG,wBAAwB,CAACd,EAAG,SAAS,CAACE,MAAM,CAAC,qBAAqB,WAAWa,GAAG,CAAC,OAASjB,EAAIkB,sBAAsB,CAAChB,EAAG,aAAa,CAACU,IAAI,UAAUR,MAAM,CAAC,IAAMJ,EAAIgB,GAAG,qBAAuB,SAAS,CAACd,EAAG,mBAAmB,CAACiB,IAAI,iBAAiBf,MAAM,CAAC,gBAAgBJ,EAAIoB,aAAa,oBAAoBpB,EAAIqB,gBAAgB,cAAcrB,EAAIsB,WAAW,kBAAkBtB,EAAIuB,gBAAgBN,GAAG,CAAC,oBAAoBjB,EAAIwB,kBAAkB,eAAexB,EAAIyB,qBAAqB,qBAAqBzB,EAAI0B,uBAAuB,GAAGxB,EAAG,aAAa,CAACU,IAAI,SAASR,MAAM,CAAC,IAAMJ,EAAIgB,GAAG,oBAAsB,aAAa,CAACd,EAAG,kBAAkB,CAACiB,IAAI,gBAAgBf,MAAM,CAAC,gBAAgBJ,EAAIoB,aAAa,oBAAoBpB,EAAIqB,gBAAgB,cAAcrB,EAAIsB,WAAW,kBAAkBtB,EAAIuB,gBAAgBN,GAAG,CAAC,eAAejB,EAAI2B,oBAAoB,aAAa3B,EAAI4B,gBAAgB,IAAI,IAAI,GAAG1B,EAAG,SAAS,CAACa,YAAY,CAAC,OAAS,YAAYX,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIgB,GAAG,2BAA2B,CAACd,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeJ,EAAIuB,eAAe,SAAWvB,EAAIoB,cAAcH,GAAG,CAAC,MAAQjB,EAAI6B,iBAAiBC,MAAM,CAACC,MAAO/B,EAAIqB,gBAAiBW,SAAS,SAAUC,GAAMjC,EAAIqB,gBAAgBY,GAAKC,WAAW,sBAAsB,GAAGhC,EAAG,SAAS,CAACa,YAAY,CAAC,gBAAgB,QAAQX,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIgB,GAAG,2BAA2B,CAACd,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAWJ,EAAIoB,cAAcH,GAAG,CAAC,OAASjB,EAAImC,mBAAmBL,MAAM,CAACC,MAAO/B,EAAIsB,WAAYU,SAAS,SAAUC,GAAMjC,EAAIsB,WAAWW,GAAKC,WAAW,iBAAiB,GAAGhC,EAAG,qBAAqB,CAACE,MAAM,CAAC,YAAY,OAAO,gBAAgBJ,EAAIoB,iBAAiB,IAEjgFd,EAAkB,G,oHCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIoC,KAAK,OAAS,aAAa,CAAClC,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIgB,GAAG,4BAA4B,CAACd,EAAG,MAAM,CAACa,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAO,YAAY,SAAS,CAACb,EAAG,MAAM,CAACa,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACb,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,SAAS,gBAAgBJ,EAAIqC,aAAa,oBAAmB,EAAK,SAAWrC,EAAIoB,aAAa,YAAYpB,EAAIsC,SAAS,OAAS,qDAAqDrB,GAAG,CAAC,OAASjB,EAAIuC,qBAAqB,CAACrC,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAIoB,eAAe,CAAClB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAIgB,GAAG,2BAA2B,MAAM,IAAI,GAAIhB,EAAI0C,SAAUxC,EAAG,OAAO,CAACa,YAAY,CAAC,cAAc,OAAO,MAAQ,OAAO,YAAY,SAAS,CAACf,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAI0C,UAAU,OAAO1C,EAAI2C,MAAM,GAAGzC,EAAG,MAAM,CAACa,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACb,EAAG,gBAAgB,CAACE,MAAM,CAAC,cAAcJ,EAAI4C,WAAW,cAAc5C,EAAI6C,gBAAgB,SAAW7C,EAAIoB,aAAa,SAAW,YAAY,kBAAkB,6BAA6BH,GAAG,CAAC,eAAejB,EAAI8C,eAAehB,MAAM,CAACC,MAAO/B,EAAI+C,cAAef,SAAS,SAAUC,GAAMjC,EAAI+C,cAAcd,GAAKC,WAAW,oBAAoB,GAAGhC,EAAG,MAAM,CAACa,YAAY,CAAC,KAAO,WAAW,YAAY,UAAU,CAACb,EAAG,WAAW,CAACG,YAAY,mBAAmBU,YAAY,CAAC,MAAQ,QAAQX,MAAM,CAAC,QAAUJ,EAAIoB,aAAa,SAAWpB,EAAIoB,eAAiBpB,EAAIgD,oBAAoB/B,GAAG,CAAC,MAAQjB,EAAIiD,mBAAmB,CAAC/C,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,iBAAiBJ,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAIgB,GAAG,eAAe,MAAM,IAAI,OAAOd,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIgB,GAAG,6BAA6B,CAACd,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,6BAA6B,SAAWJ,EAAIoB,cAAcU,MAAM,CAACC,MAAO/B,EAAIkD,cAAelB,SAAS,SAAUC,GAAMjC,EAAIkD,cAAcjB,GAAKC,WAAW,oBAAoB,IAAI,IAEv7D5B,EAAkB,G,UCDf,MAAM6C,EAAuB,kvCCAvBA,EAAuB,oxBCIvBC,EAAY,CACvB,gBAAmB,CACjBC,KAAM,kBACNC,QAASH,GAEX,eAAkB,CAChBE,KAAM,iBACNC,QAASC,IAaAC,EAAwBA,IAC5B,CAAC,CACN5C,IAAK,kBACLyC,KAAMD,EAAU,mBAAmBC,OAK1BI,EAAuBA,IAC3B,CAAC,CACN7C,IAAK,iBACLyC,KAAMD,EAAU,kBAAkBC,OAKzBK,EAAoB9C,IAAQ,IAAA+C,EACvC,OAAqB,QAAdA,EAAAP,EAAUxC,UAAI,IAAA+C,OAAA,EAAdA,EAAgBL,UAAWH,GC1CpC,IAAIpD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACa,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,SAAS,CAACb,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAI4D,UAAU3C,GAAG,CAAC,MAAQjB,EAAI6D,kBAAkB,CAAC3D,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAUJ,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAIgB,GAAG,yBAAyB,MAAM,GAAIhB,EAAI+C,cAAe7C,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAI4D,UAAU3C,GAAG,CAAC,MAAQjB,EAAI8D,gBAAgB,CAAC5D,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,kBAAkBJ,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAIgB,GAAG,uBAAuB,MAAM,GAAGhB,EAAI2C,MAAM,GAAGzC,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQJ,EAAIgB,GAAG,wBAAwB,QAAUhB,EAAI+D,mBAAmB,UAAW,EAAM,cAAe,EAAM,UAAW,EAAM,MAAQ,SAAS,UAAY,CAAEC,UAAW,OAAQC,SAAU,OAAQ1D,QAAS,QAAS,mBAAqBP,EAAIkE,0BAA0BjD,GAAG,CAAC,GAAKjB,EAAImE,eAAe,OAASnE,EAAIoE,qBAAqB,CAAClE,EAAG,SAAS,CAACa,YAAY,CAAC,gBAAgB,QAAQE,GAAG,CAAC,OAASjB,EAAIqE,iBAAiBvC,MAAM,CAACC,MAAO/B,EAAI6C,gBAAiBb,SAAS,SAAUC,GAAMjC,EAAI6C,gBAAgBZ,GAAKC,WAAW,oBAAoBlC,EAAIsE,GAAItE,EAAI4C,YAAY,SAAS2B,GAAK,OAAOrE,EAAG,aAAa,CAACU,IAAI2D,EAAI3D,IAAIR,MAAM,CAAC,IAAMmE,EAAIlB,WAAU,GAAGnD,EAAG,MAAM,CAACa,YAAY,CAAC,WAAa,UAAU,QAAU,OAAO,gBAAgB,MAAM,aAAa,UAAU,CAACb,EAAG,aAAa,CAACa,YAAY,CAAC,cAAc,2BAA2B,WAAa,UAAU,MAAQ,UAAU,OAAS,iBAAiB,YAAY,OAAO,MAAQ,OAAO,OAAS,QAAQX,MAAM,CAAC,KAAO,GAAG,YAAc,cAAc,YAAY,CAAEoE,QAAS,GAAIC,QAAS,KAAM3C,MAAM,CAACC,MAAO/B,EAAI0E,sBAAuB1C,SAAS,SAAUC,GAAMjC,EAAI0E,sBAAsBzC,GAAKC,WAAW,4BAA4B,IAAI,IAAI,IAE5vD5B,EAAkB,GCmDP,GACf+C,KAAA,eACAsB,MAAA,CACA5C,MAAA,CACA6C,KAAAC,OACAC,QAAA,IAEAC,WAAA,CACAH,KAAAC,OACAC,QAAA,WAEAlC,WAAA,CACAgC,KAAAI,MACAF,YAAA,IAEAG,WAAA,CACAL,KAAAC,OACAC,QAAA,mBAEAlB,SAAA,CACAgB,KAAAM,QACAJ,SAAA,GAEAK,SAAA,CACAP,KAAAC,OACAC,QAAA,aAEAM,eAAA,CACAR,KAAAC,OACAC,QAAA,8BAGAO,OACA,OACAtB,oBAAA,EACAW,sBAAA,KAAA3C,MACAc,gBAAA,KAAAoC,WACAK,WAAA,KAGAC,SAAA,CACAxC,cAAA,CACAyC,MACA,YAAAd,uBAEAe,IAAA1D,GACA,KAAA2C,sBAAA3C,EACA,KAAA2D,MAAA,QAAA3D,MAIA4D,MAAA,CACA5D,MAAA6D,GACA,KAAAlB,sBAAAkB,IAGAC,QAAA,CACAhC,kBAEA,KAAAa,uBACA,KAAAoB,kBAAA,KAAAjD,iBAEA,KAAAkB,oBAAA,GAGA+B,kBAAAC,GACA,KAAAlD,gBAAAkD,EACA,KAAAhD,cAAAW,EAAAqC,IAGA1B,gBAAA0B,GACA,KAAAD,kBAAAC,IAGA5B,iBACA,KAAApB,cAAAiD,QAMA,KAAAC,mBAEA,KAAAlC,oBAAA,GAPA,KAAAmC,SAAAC,QAAA,mCAUAjC,6BAIAE,qBAEA,KAAAL,oBAAA,GAIA,sBACA,SAAAhB,cAAAiD,OAEA,YADA,KAAAE,SAAAC,QAAA,kCAKA,MAAAC,QAAA,KAAAH,mBAEAG,GACA,KAAAF,SAAAG,QAAA,KAAAjB,iBAIA,yBACA,IAEA,MAAAkB,EAAA,IAAAC,KAAA,MAAAxD,eAAA,CAAA6B,KAAA,eACA4B,EAAA,IAAAC,KAAA,CAAAH,GAAA,KAAAnB,SAAA,CAAAP,KAAA,eAGA8B,EAAA,IAAAC,SACAD,EAAAE,OAAA,iBAAAJ,GAEA,MAAAK,QAAAC,OAAAC,KAAA,0BAAAL,EAAA,CACAM,QAAA,CACA,wCAIA,OAAAH,EAAAxB,MAAAwB,EAAAxB,KAAAe,MACA,KAAAd,WAAAuB,EAAAxB,KAAAe,KACA,KAAAV,MAAA,eAAAmB,EAAAxB,KAAAe,MACAS,EAAAxB,KAAAe,MAEA,KACA,MAAAa,GAAA,IAAAC,EAGA,OAFAC,QAAAF,MAAA,qBAAAA,GACA,KAAAf,SAAAe,MAAA,oCAAAC,EAAAD,EAAAJ,gBAAA,IAAAK,GAAA,QAAAA,IAAA7B,YAAA,IAAA6B,OAAA,EAAAA,EAAAD,UAAAG,UACA,SC5LoW,I,YCOhWC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAAA,E,QCwDA,GACfhE,KAAA,iBACAiE,OAAA,CAAAC,QACAC,WAAA,CACAC,gBAEA9C,MAAA,CACAvD,aAAA,CACAwD,KAAAM,QACAJ,SAAA,GAEAzD,gBAAA,CACAuD,KAAAI,MACAF,YAAA,IAEAxD,WAAA,CACAsD,KAAAC,OACAC,QAAA,MAEAvD,eAAA,CACAqD,KAAAC,OACA6C,UAAA,IAGArC,OACA,OACAjD,KAAA,KAAAuF,MAAAC,WAAA,MACAC,UAAA,qBACAC,SAAA,GACA5E,cAAA,6EACAZ,SAAA,GACAI,SAAA,GACAK,cAAA,GACAH,WAAA,GACAC,gBAAA,kBACAyC,WAAA,KAGAC,SAAA,IACAwC,eAAA,kBACA/E,qBACA,YAAA8E,UAAA,KAAA/E,eAAA,KAAAG,eAAA,KAAA7B,gBAAA2G,OAAA,QAAA1G,aAGA2G,UAEA,KAAArF,WAAAY,IAEA,KAAAsC,kBAAA,KAAAjD,kBAEAgD,QAAA,CACAC,kBAAAC,GACA,KAAAlD,gBAAAkD,EACA,KAAAhD,cAAAW,EAAAqC,IAEAjD,cAAAsD,GACA,KAAAd,WAAAc,EACA,KAAAV,MAAA,eAAAU,IAEA/D,aAAAmE,GACA,MAAA0B,EAAA,oBAAA1B,EAAA5B,MACA,iCAAA4B,EAAA5B,MACA4B,EAAAnD,KAAA8E,SAAA,QACA,OAAAD,GAKA,KAAA1B,OACA,KAAA9D,SAAA8D,EAAAnD,KAGA,KAAA+E,WAAA5B,IAEA,IAVA,KAAAN,SAAAe,MAAA,mCACA,IAWA,iBAAAT,GAEA,MAAA6B,EAAA,KAAAnC,SAAAoC,QAAA,kBAEA,IACA,MAAA5B,EAAA,IAAAC,SACAD,EAAAE,OAAA,OAAAJ,GAEA,MAAAK,QAAAC,OAAAC,KAAA,KAAAc,UAAAnB,EAAA,CACAM,QAAA,CACA,wCAOA,GAFAqB,IAEAxB,EAAAxB,MAAAwB,EAAAxB,KAAAe,KAAA,CACA,KAAA0B,SAAAjB,EAAAxB,KAAAe,KACA,KAAAV,MAAA,oBAAAmB,EAAAxB,KAAAe,MACA,KAAAF,SAAAG,QAAAG,EAAAnD,KAAA,0BAGA,MAAAkF,EAAA,CACAC,IAAAhC,EAAAgC,IACAnF,KAAAmD,EAAAnD,KACAoF,OAAA,OACA5B,WAAAxB,MAEA,KAAA/C,SAAA,CAAAiG,GAGA,KAAAG,oBAEA,KAAAxC,SAAAe,MAAAT,EAAAnD,KAAA,uCAEA,MAAA4D,GAAA,IAAAC,EAEAmB,IAEAlB,QAAAF,MAAA,gBAAAA,GACA,KAAAf,SAAAe,MAAA,GAAAT,EAAAnD,wBAAA,QAAA6D,EAAAD,EAAAJ,gBAAA,IAAAK,GAAA,QAAAA,IAAA7B,YAAA,IAAA6B,OAAA,EAAAA,EAAAD,UAAAG,aAGA7E,mBAAAoG,GACA,KAAArG,SAAA,IAAAqG,EAAArG,UAGA,KAAAA,SAAA,KAAAA,SAAAsG,OAAA,GAEA,MAAAH,EAAAE,EAAAnC,KAAAiC,OACA,SAAAA,EACAE,EAAAnC,KAAAK,UAAA8B,EAAAnC,KAAAK,SAAAT,MACA,KAAA0B,SAAAa,EAAAnC,KAAAK,SAAAT,KACA,KAAAV,MAAA,oBAAAiD,EAAAnC,KAAAK,SAAAT,MACA,KAAA1D,SAAAiG,EAAAnC,KAAAnD,KACA,KAAA6C,SAAAG,QAAAsC,EAAAnC,KAAAnD,KAAA,0BAGA,KAAAqF,gBAEA,KAAAxC,SAAAe,MAAA0B,EAAAnC,KAAAnD,KAAA,uCAEA,UAAAoF,GACA,KAAAvC,SAAAe,MAAA0B,EAAAnC,KAAAnD,KAAA,oBAGAwF,cACA,OACAf,SAAA,KAAAA,SACA/E,cAAA,KAAAA,cACAuC,WAAA,KAAAA,WACApC,cAAA,KAAAA,gBAGAD,mBAEA,KAAAD,mBAQA,KAAAD,cASA,KAAA2C,MAAA,sBACAoC,SAAA,KAAAA,SACA/E,cAAA,KAAAA,cACAuC,WAAA,KAAAA,WACApC,cAAA,KAAAA,gBAZA,KAAA4F,QAAA3C,QAAA,CACA4C,MAAA,OACA3B,QAAA,iBAVA,KAAA0B,QAAA3C,QAAA,CACA4C,MAAA,QACA3B,QAAA,oBCtOsW,ICOlW,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QClBXrH,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIoC,KAAK,OAAS,aAAa,CAAClC,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAIgB,GAAG,qBAAqB,CAACd,EAAG,MAAM,CAACa,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAO,YAAY,SAAS,CAACb,EAAG,MAAM,CAACa,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACb,EAAG,gBAAgB,CAACE,MAAM,CAAC,cAAcJ,EAAIgJ,iBAAiB,cAAchJ,EAAIiJ,sBAAsB,SAAWjJ,EAAIoB,aAAa,SAAW,mBAAmB,kBAAkB,oCAAoCH,GAAG,CAAC,eAAejB,EAAI8C,eAAehB,MAAM,CAACC,MAAO/B,EAAIuD,oBAAqBvB,SAAS,SAAUC,GAAMjC,EAAIuD,oBAAoBtB,GAAKC,WAAW,0BAA0B,GAAGhC,EAAG,MAAM,CAACa,YAAY,CAAC,KAAO,WAAW,YAAY,UAAU,CAACb,EAAG,WAAW,CAACG,YAAY,mBAAmBU,YAAY,CAAC,MAAQ,QAAQX,MAAM,CAAC,QAAUJ,EAAIoB,aAAa,SAAWpB,EAAIoB,eAAiBpB,EAAIuD,sBAAwBvD,EAAIqB,gBAAgB2G,SAAWhI,EAAIsB,YAAYL,GAAG,CAAC,MAAQjB,EAAIkJ,YAAY,CAAChJ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,iBAAiBJ,EAAIwC,GAAG,IAAIxC,EAAIyC,GAAGzC,EAAIgB,GAAG,eAAiB,YAAY,MAAM,IAAI,QAAQ,IAE7nCV,EAAkB,GCsCP,GACf+C,KAAA,gBACAmE,WAAA,CACAC,gBAEA9C,MAAA,CACAvD,aAAA,CACAwD,KAAAM,QACAJ,SAAA,GAEAzD,gBAAA,CACAuD,KAAAI,MACA0C,UAAA,GAEApG,WAAA,CACAsD,KAAAC,OACAC,QAAA,MAEAvD,eAAA,CACAqD,KAAAC,OACA6C,UAAA,IAGArC,OACA,OACAjD,KAAA,KAAAuF,MAAAC,WAAA,MACArE,oBAAA,GACAyF,iBAAA,GACAC,sBAAA,iBACAE,eAAA,4FACAC,eAAA,6EACAC,iBAAA,mEAGA9D,SAAA,IACAwC,eAAA,mBAEAE,UAEA,KAAAe,iBAAAvF,IAEA,KAAA6F,wBAAA,KAAAL,wBAEApD,QAAA,CACAyD,wBAAAvD,GACA,KAAAkD,sBAAAlD,EACA,KAAAxC,oBAAAG,EAAAqC,IAEAjD,cAAAsD,GACA,KAAAiD,iBAAAjD,EACA,KAAAV,MAAA,eAAAU,IAEA8C,YACA,SAAA7H,gBAAA2G,SAAA,KAAA1G,WAKA,YAJA,KAAAwH,QAAA3C,QAAA,CACA4C,MAAA,WACA3B,QAAA,+BAMA,MAAAmC,EAAA,CACAC,QAAA,KAAAnI,gBACAoI,SAAA,KAAAnI,WACAoI,eAAA,KAAAnG,oBACAoG,OAAA,KAAApI,gBAGA,KAAAmE,MAAA,aAAA6D,IAEAV,cACA,OACAtF,oBAAA,KAAAA,oBACA8F,iBAAA,KAAAA,iBACAF,eAAA,KAAAA,eACAC,eAAA,KAAAA,mBCpHqW,ICOjW,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QCiFA,GACf9B,OAAA,CAAAC,OAAAqC,QACApC,WAAA,CACAqC,qBACAC,wBACAC,oBACAC,iBACAC,iBAEA5E,OACA,OACAhE,gBAAA,GACAC,WAAA,KACA4I,YAAA,EACAC,cAAA,UACArC,SAAA,GACAxC,WAAA,GACAvC,cAAA,GACAG,cAAA,GACAmG,iBAAA,GACA9F,oBAAA,KAGAgC,SAAA,IACAwC,eAAA,oDACAqC,OAAA,CACA5E,MAAA,IAAA6E,EACA,eAAAA,EAAA,KAAAC,sBAAA,IAAAD,OAAA,EAAAA,EAAAE,SAEA9E,IAAA1D,GACA,KAAAyI,OAAAC,SAAA,iBAAA1I,EAAA,CAAAwI,QAAAxI,GAAA,QAGAiB,qBACA,uBAAAmH,eACA,KAAArC,UAAA,KAAA/E,eAAA,KAAAG,eAIAzC,sBACA,YAAAW,aACA,EAEA,KAAA0G,UAAA,iBAAAqC,eAGA,KAAArC,UAAA,gBAAAqC,eAAA,SAAA9I,gBAAA2G,QAGA,KAAAF,UAAA,gBAAAqC,gBAAA,KAAA9I,gBAAA2G,OAAA,SAAA1G,WACA,EAEA,EALA,GAHA,IAYA2G,UAEA,MAAAyC,EAAAC,aAAAC,QAAA,qBAAArJ,gBACA,GAAAmJ,EAAA,CACA,kBAAAG,GAAAC,KAAAC,MAAAL,GACAG,IAAA,KAAAtJ,eACA,KAAAyJ,mBAGAL,aAAAM,WAAA,qBAAA1J,gBACAoJ,aAAAM,WAAA,0BAAA1J,gBACA,KAAAiJ,OAAAC,SAAA,0BAIA5E,QAAA,IACAqF,eAAA,qBAGAhK,oBAAAN,GACA,KAAAuJ,cAAAvJ,GAIAuB,kBAAAgJ,GACA,KAAA7J,WAAA6J,GAIAtJ,gBAAAR,GACA,KAAAA,mBAIAG,kBAAA4E,GACA,KAAA0B,SAAA1B,GAIA3E,qBAAA2E,GAGA,GAFA,KAAAd,WAAAc,EAEA,KAAAgF,MAAAC,eAAA,CACA,MAAAC,EAAA,KAAAF,MAAAC,eAAAxC,cACA,KAAA9F,cAAAuI,EAAAvI,cACA,KAAAG,cAAAoI,EAAApI,gBAKAvB,oBAAAyE,GAGA,GAFA,KAAAiD,iBAAAjD,EAEA,KAAAgF,MAAAG,cAAA,CACA,MAAAD,EAAA,KAAAF,MAAAG,cAAA1C,cACA,KAAAtF,oBAAA+H,EAAA/H,sBAKA,yBAAA+H,GAEA,MAAA/B,EAAA,CACAC,QAAA,KAAAnI,gBACAoI,SAAA,KAAAnI,WACAkK,YAAAF,EAAAxD,SACA4B,eAAA4B,EAAAvI,cACA0I,YAAA,aACAC,gBAAAJ,EAAApI,cACAyG,OAAA,KAAApI,gBAIA6I,QAAA,KAAAuB,iBACApC,EACA,MACA,YACA,cAIA,GAAAa,EAEA,IACA,MAAAvD,QAAAC,OAAAtB,IAAA,eAAA4E,GACAvD,EAAAxB,OAEA,KAAAmF,OAAAC,SAAA,iBAAA5D,EAAAxB,MAGA,KAAAqD,gBAEA,MAAAzB,GACAE,QAAAF,MAAA,cAAAA,KAMA,kBAAAsC,SACA,KAAAoC,iBACApC,EACA,aACA,cACA,iBAcA,uBAAAA,EAAAqC,EAAAxG,EAAAyG,GACA,KAAAzK,cAAA,EAGA,MAAA0K,EAAAnB,aAAAC,QAAA,qBAAArJ,gBACA,GAAAuK,EACA,IACA,aAAA1B,GAAAU,KAAAC,MAAAe,GACA1B,GACA,KAAA2B,0BAAA3B,EAAA,YAAA7I,gBAEA,MAAAyK,GACA7E,QAAAF,MAAA,6CAAA+E,GAIA,IAGA,MAAA3G,KAAA4G,SAAAnF,OAAAC,KAAA,eAAA6E,EAAArC,GAEA,GAAA0C,KAAA1B,QAAA,CAEAI,aAAAuB,QAAA,qBAAA3K,eAAAuJ,KAAAqB,UAAA,CACA/B,OAAA6B,EAAA1B,QACAM,YAAA,KAAAtJ,kBAEAoJ,aAAAM,WAAA,0BAAA1J,gBAGA,KAAA6I,OAAA6B,EAAA1B,QAGA,MAAA6B,EAAA,CACA7B,QAAA0B,EAAA1B,QACA8B,MAAA,IAIA,KAAAhL,gBAAAiL,QAAAnB,IACAiB,EAAAC,MAAAlB,GAAA,CACAA,KACAoB,UAAApB,EACA1C,OAAA,aACA+D,SAAA,KAKA,KAAAhC,OAAAC,SAAA,iBAAA2B,GAGA,KAAAK,UAAA,KAEA,MAAAC,EAAAC,SAAAC,cAAA,iBACAF,GACAA,EAAAG,eAAA,CAAAC,SAAA,SAAAC,MAAA,aAKA,IACA,MAAAlG,QAAAC,OAAAtB,IAAA,eAAAyG,EAAA1B,SAGA1D,EAAAxB,MAEA,KAAAmF,OAAAC,SAAA,iBAAA5D,EAAAxB,MAEA,MAAA2H,GACA7F,QAAAF,MAAA,UAAA+F,GAEA,QAEA,KAAAC,aAAAhB,EAAA1B,QAAA,iBASA,OALA,KAAArE,SAAAG,QAAAjB,GAAA,SAGA,KAAAsD,eAEAuD,EAAA1B,QAEA,YACA,MAAAtD,GACAE,QAAAF,MAAA,KAAA2E,SAAA3E,GACA,IAAAiG,EAAA,WAaA,OAXAjG,EAAAG,QACA8F,EAAAjG,EAAAG,QACAH,EAAAJ,UAAAI,EAAAJ,SAAAxB,MAAA4B,EAAAJ,SAAAxB,KAAA4B,QACAiG,EAAAjG,EAAAJ,SAAAxB,KAAA4B,OAGA,KAAA6B,QAAA7B,MAAA,CACA8B,MAAA8C,GAAA,SACAzE,QAAA8F,IAEA,KAAA9L,cAAA,EACA,OAKA,wBACA,IACA,MAAAsJ,EAAAC,aAAAC,QAAA,qBAAArJ,gBACA4L,EAAAxC,aAAAC,QAAA,0BAAArJ,gBAEA,GAAAmJ,EAAA,CACA,aAAAN,EAAA,YAAAS,GAAAC,KAAAC,MAAAL,GAEA,GAAAG,IAAA,KAAAtJ,eACA,UAAA6L,MAAA,qCAGA,MAAAvG,QAAAC,OAAAtB,IAAA,eAAA4E,GAEA,GAAAvD,EAAAxB,OACA,KAAAmF,OAAAC,SAAA,iBAAA5D,EAAAxB,MAEAwB,EAAAxB,KAAAgH,OAAA,CACA,MAAAA,EAAAgB,OAAAC,OAAAzG,EAAAxB,KAAAgH,OACAkB,EAAAlB,EAAAmB,MAAAC,GACA,qBAAAC,SAAAD,EAAAhF,SAGA8E,GAAAJ,EAGAI,IACA,KAAAnM,cAAA,EACAuJ,aAAAuB,QAAA,0BAAA3K,eAAA,UAJA,KAAAH,cAAA,EACA,KAAA6L,aAAA7C,EAAA,oBAQA,MAAAnD,GACAE,QAAAF,MAAA,8BAAAA,GACA0D,aAAAM,WAAA,qBAAA1J,gBACAoJ,aAAAM,WAAA,0BAAA1J,kBAKAoM,oBAAAvD,GACA,KAAA6C,aAAA7C,EAAA,kBAGAwD,YAEA,KAAA5C,oBAGArF,MAAA,CAEApE,eAAA,CACAsM,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAAvD,OAAAC,SAAA,uBACA,KAAAuD,cAEA,KAAAhD,oBAGAiD,WAAA,KCzbkW,ICQ9V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCJA,GACfzG,WAAA,CACA0G,eCjBuV,ICOnV,EAAY,eACd,EACAnO,EACAO,GACA,EACA,KACA,KACA,MAIa,e", "file": "static/js/chunk-60b6d59a.301947dd.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=style&index=0&id=0809a573&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('ToolsPanel')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full task-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: '8px 16px' },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }}},[_c('div',{staticClass:\"steps-container\"},[_c('a-steps',{staticClass:\"steps-flow\",attrs:{\"current\":_vm.currentStepComputed,\"size\":\"small\"}},[_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"upload\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"apartment\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"global\"}})]},proxy:true}])})],1)],1),_c('a-card',{staticStyle:{\"margin\":\"0 0 16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('tool.configureTool')}},[_c('a-tabs',{attrs:{\"default-active-key\":\"general\"},on:{\"change\":_vm.handleToolTabChange}},[_c('a-tab-pane',{key:\"general\",attrs:{\"tab\":_vm.$t('tool.generalTool') || '通用工具'}},[_c('general-tool-tab',{ref:\"generalToolTab\",attrs:{\"is-processing\":_vm.isProcessing,\"selected-row-keys\":_vm.selectedRowKeys,\"selected-ip\":_vm.selectedIp,\"current-project\":_vm.currentProject},on:{\"tool-path-changed\":_vm.onToolPathChanged,\"script-saved\":_vm.onGeneralScriptSaved,\"start-general-tool\":_vm.onStartGeneralTool}})],1),_c('a-tab-pane',{key:\"spider\",attrs:{\"tab\":_vm.$t('tool.spiderTool') || 'Spider工具'}},[_c('spider-tool-tab',{ref:\"spiderToolTab\",attrs:{\"is-processing\":_vm.isProcessing,\"selected-row-keys\":_vm.selectedRowKeys,\"selected-ip\":_vm.selectedIp,\"current-project\":_vm.currentProject},on:{\"script-saved\":_vm.onSpiderScriptSaved,\"run-spider\":_vm.onRunSpider}})],1)],1)],1),_c('a-card',{staticStyle:{\"margin\":\"0 0 16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('node-selector',{attrs:{\"project-file\":_vm.currentProject,\"disabled\":_vm.isProcessing},on:{\"input\":_vm.onNodesSelected},model:{value:(_vm.selectedRowKeys),callback:function ($$v) {_vm.selectedRowKeys=$$v},expression:\"selectedRowKeys\"}})],1),_c('a-card',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.isProcessing},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedIp),callback:function ($$v) {_vm.selectedIp=$$v},expression:\"selectedIp\"}})],1),_c('task-progress-card',{attrs:{\"task-type\":'tool',\"is-processing\":_vm.isProcessing}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.uploadToolPackage')}},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"12px\",\"flex-wrap\":\"wrap\"}},[_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('a-upload',{attrs:{\"name\":\"script\",\"before-upload\":_vm.beforeUpload,\"show-upload-list\":true,\"disabled\":_vm.isProcessing,\"file-list\":_vm.fileList,\"accept\":\".zip,application/zip,application/x-zip-compressed\"},on:{\"change\":_vm.handleUploadChange}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.isProcessing}},[_c('a-icon',{attrs:{\"type\":\"upload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.selectToolPackage'))+\" \")],1)],1),(_vm.fileName)?_c('span',{staticStyle:{\"margin-left\":\"10px\",\"color\":\"#666\",\"font-size\":\"12px\"}},[_vm._v(\" \"+_vm._s(_vm.fileName)+\" \")]):_vm._e()],1),_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('script-editor',{attrs:{\"script-tabs\":_vm.scriptTabs,\"default-tab\":_vm.activeScriptTab,\"disabled\":_vm.isProcessing,\"filename\":'script.sh',\"success-message\":'Script saved successfully'},on:{\"script-saved\":_vm.onScriptSaved},model:{value:(_vm.scriptContent),callback:function ($$v) {_vm.scriptContent=$$v},expression:\"scriptContent\"}})],1),_c('div',{staticStyle:{\"flex\":\"0 0 auto\",\"min-width\":\"100px\"}},[_c('a-button',{staticClass:\"nav-style-button\",staticStyle:{\"width\":\"100%\"},attrs:{\"loading\":_vm.isProcessing,\"disabled\":_vm.isProcessing || !_vm.toolConfigComplete},on:{\"click\":_vm.startGeneralTool}},[_c('a-icon',{attrs:{\"type\":\"play-circle\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.start'))+\" \")],1)],1)])]),_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.localSaveDirectory')}},[_c('a-input',{attrs:{\"placeholder\":\"e.g., results/tool_results\",\"disabled\":_vm.isProcessing},model:{value:(_vm.localSavePath),callback:function ($$v) {_vm.localSavePath=$$v},expression:\"localSavePath\"}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// 默认脚本内容\r\nexport const defaultScriptContent = `#!/bin/bash\r\n\r\n# ----------------------- 第一步：上传并解压工具包 -----------------------\r\n#\r\n# 以下变量会自动替换，无需修改：\r\n# {work_dir}            - 任务工作目录，格式为 \"/root/.test/script_task_{task_id}\"\r\n# {proxy_ip}            - 服务器IP地址\r\n# {server_port}         - 服务器端口\r\n# {download_script_info} - 下载信息（自动编码）\r\n# {output_file_name}    - 上传的工具包名称\r\n#\r\ncd {work_dir}\r\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\r\nunzip -o {output_file_name}\r\n\r\n# ----------------------- 第二步：执行工具命令 --------------------------\r\n#\r\n# 以下为工具执行示例（以s-spider工具为例）\r\n# 请根据实际工具修改此部分命令\r\n#\r\n# 可用变量：无需修改\r\n# {node_ip}   - 目标节点IP\r\n# {node_name} - 目标节点名称\r\n#\r\ncd scan_tools\r\nchmod 777 run.sh\r\ndos2unix run.sh || true\r\nsleep 0.5\r\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\r\nsleep 0.5\r\n\r\n# ----------------------- 第三步：回传结果文件 --------------------------\r\n#\r\n# 重要说明：\r\n# 1. 所有工具运行结果必须打包为 {node_name}_{node_ip}.tar 格式\r\n# 2. {result_file} 变量会被自动替换为正确的结果文件名\r\n# 3. {upload_script_info} 为回传参数（已编码，无需修改）\r\n#\r\n# 如果您的工具生成了其他格式的文件，请使用以下命令将其转换：\r\n# tar -cf {result_file} your_result_files\r\n# 或\r\n# mv your_result_file.ext {result_file}\r\n#\r\ncurl -F \"file=@{result_file}\" \"http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}\"\r\n`;", "// 预置spider\r\nexport const defaultScriptContent = `#!/bin/bash\r\n# This script will be executed on the remote node\r\n\r\n# 以下示例以运行s-spider工具为例：\r\n\r\n# 第一步：上传并解压zip包，\r\ncd {work_dir}\r\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\r\nunzip -o scan_tools.zip\r\n\r\n# 第二步： 执行运行命令，按照实际工具执行步骤往下写即可\r\ncd scan_tools\r\nchmod 777 run.sh\r\ndos2unix run.sh || true\r\nsleep 0.5\r\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\r\nsleep 0.5\r\n\r\n# 第三步： 回传结果文件\r\n# 注意：所有工具运行结果必须打包为{node_name}_{node_ip}.tar格式\r\n# 如果您的工具生成了其他格式的文件，请将其打包为tar格式\r\n# 例如：tar -cf {node_name}_{node_ip}.tar your_result_files\r\n# 或者：mv your_result_file.ext {node_name}_{node_ip}.tar\r\n\r\n# 上传结果文件到服务器\r\ncurl -F \"file=@{result_file}\" \"http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}\"`;", "// 导入所有脚本文件\r\nimport { defaultScriptContent } from './default_command';\r\nimport { defaultScriptContent as spiderScriptContent } from './spider_command';\r\n\r\n// 脚本映射表\r\nexport const scriptMap = {\r\n  'default_command': {\r\n    name: 'Default Command',\r\n    content: defaultScriptContent\r\n  },\r\n  'spider_command': {\r\n    name: 'Spider Command',\r\n    content: spiderScriptContent\r\n  }\r\n};\r\n\r\n// 获取所有脚本名称（通用）\r\nexport const getScriptNames = () => {\r\n  return Object.keys(scriptMap).map(key => ({\r\n    key,\r\n    name: scriptMap[key].name\r\n  }));\r\n};\r\n\r\n// 获取通用工具脚本名称\r\nexport const getGeneralScriptNames = () => {\r\n  return [{\r\n    key: 'default_command',\r\n    name: scriptMap['default_command'].name\r\n  }];\r\n};\r\n\r\n// 获取Spider工具脚本名称\r\nexport const getSpiderScriptNames = () => {\r\n  return [{\r\n    key: 'spider_command',\r\n    name: scriptMap['spider_command'].name\r\n  }];\r\n};\r\n\r\n// 根据key获取脚本内容\r\nexport const getScriptContent = (key) => {\r\n  return scriptMap[key]?.content || defaultScriptContent;\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"10px\"}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.disabled},on:{\"click\":_vm.showScriptModal}},[_c('a-icon',{attrs:{\"type\":\"code\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.editShellScript'))+\" \")],1),(_vm.scriptContent)?_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.disabled},on:{\"click\":_vm.confirmScript}},[_c('a-icon',{attrs:{\"type\":\"check-circle\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.confirmScript'))+\" \")],1):_vm._e()],1),_c('a-modal',{attrs:{\"title\":_vm.$t('tool.editShellScript'),\"visible\":_vm.scriptModalVisible,\"closable\":false,\"maskClosable\":false,\"keyboard\":false,\"width\":\"1200px\",\"bodyStyle\":{ maxHeight: '90vh', overflow: 'auto', padding: '20px' },\"afterVisibleChange\":_vm.handleModalVisibleChange},on:{\"ok\":_vm.handleScriptOk,\"cancel\":_vm.handleScriptCancel}},[_c('a-tabs',{staticStyle:{\"margin-bottom\":\"16px\"},on:{\"change\":_vm.handleTabChange},model:{value:(_vm.activeScriptTab),callback:function ($$v) {_vm.activeScriptTab=$$v},expression:\"activeScriptTab\"}},_vm._l((_vm.scriptTabs),function(tab){return _c('a-tab-pane',{key:tab.key,attrs:{\"tab\":tab.name}})}),1),_c('div',{staticStyle:{\"background\":\"#1e1e1e\",\"padding\":\"16px\",\"border-radius\":\"4px\",\"min-height\":\"700px\"}},[_c('a-textarea',{staticStyle:{\"font-family\":\"'Courier New', monospace\",\"background\":\"#1e1e1e\",\"color\":\"#d4d4d4\",\"border\":\"1px solid #444\",\"font-size\":\"14px\",\"width\":\"100%\",\"resize\":\"none\"},attrs:{\"rows\":30,\"placeholder\":\"#!/bin/bash\",\"auto-size\":{ minRows: 30, maxRows: 40 }},model:{value:(_vm.scriptContentInternal),callback:function ($$v) {_vm.scriptContentInternal=$$v},expression:\"scriptContentInternal\"}})],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <!-- 脚本编辑按钮和确认按钮 -->\r\n    <div style=\"display: flex; align-items: center; gap: 10px;\">\r\n      <a-button @click=\"showScriptModal\" :disabled=\"disabled\" class=\"nav-style-button\">\r\n        <a-icon type=\"code\" /> {{ $t('tool.editShellScript') }}\r\n      </a-button>\r\n      <a-button\r\n        class=\"nav-style-button\"\r\n        v-if=\"scriptContent\"\r\n        @click=\"confirmScript\"\r\n        :disabled=\"disabled\"\r\n      >\r\n        <a-icon type=\"check-circle\" /> {{ $t('tool.confirmScript') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <!-- 脚本编辑模态框 -->\r\n    <a-modal\r\n      :title=\"$t('tool.editShellScript')\"\r\n      :visible=\"scriptModalVisible\"\r\n      :closable=\"false\"\r\n      :maskClosable=\"false\"\r\n      :keyboard=\"false\"\r\n      @ok=\"handleScriptOk\"\r\n      @cancel=\"handleScriptCancel\"\r\n      width=\"1200px\"\r\n      :bodyStyle=\"{ maxHeight: '90vh', overflow: 'auto', padding: '20px' }\"\r\n      :afterVisibleChange=\"handleModalVisibleChange\"\r\n    >\r\n      <!-- 脚本选项卡 -->\r\n      <a-tabs v-model=\"activeScriptTab\" @change=\"handleTabChange\" style=\"margin-bottom: 16px;\">\r\n        <a-tab-pane v-for=\"tab in scriptTabs\" :key=\"tab.key\" :tab=\"tab.name\" />\r\n      </a-tabs>\r\n\r\n      <div style=\"background: #1e1e1e; padding: 16px; border-radius: 4px; min-height: 700px;\">\r\n        <a-textarea\r\n          v-model=\"scriptContentInternal\"\r\n          :rows=\"30\"\r\n          style=\"font-family: 'Courier New', monospace; background: #1e1e1e; color: #d4d4d4; border: 1px solid #444; font-size: 14px; width: 100%; resize: none;\"\r\n          placeholder=\"#!/bin/bash\"\r\n          :auto-size=\"{ minRows: 30, maxRows: 40 }\"\r\n        />\r\n      </div>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState } from 'vuex';\r\nimport { getScriptContent } from '@/assets/scripts';\r\n\r\nexport default {\r\n  name: 'ScriptEditor',\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    scriptType: {\r\n      type: String,\r\n      default: 'general' // 'general' 或 'spider'\r\n    },\r\n    scriptTabs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defaultTab: {\r\n      type: String,\r\n      default: 'default_command'\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    filename: {\r\n      type: String,\r\n      default: 'script.sh'\r\n    },\r\n    successMessage: {\r\n      type: String,\r\n      default: 'Script saved successfully'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      scriptModalVisible: false,\r\n      scriptContentInternal: this.value,\r\n      activeScriptTab: this.defaultTab,\r\n      scriptPath: ''\r\n    };\r\n  },\r\n  computed: {\r\n    scriptContent: {\r\n      get() {\r\n        return this.scriptContentInternal;\r\n      },\r\n      set(value) {\r\n        this.scriptContentInternal = value;\r\n        this.$emit('input', value);\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    value(newVal) {\r\n      this.scriptContentInternal = newVal;\r\n    }\r\n  },\r\n  methods: {\r\n    showScriptModal() {\r\n      // 确保脚本内容已初始化\r\n      if (!this.scriptContentInternal) {\r\n        this.loadScriptContent(this.activeScriptTab);\r\n      }\r\n      this.scriptModalVisible = true;\r\n    },\r\n\r\n    loadScriptContent(tabKey) {\r\n      this.activeScriptTab = tabKey;\r\n      this.scriptContent = getScriptContent(tabKey);\r\n    },\r\n\r\n    handleTabChange(tabKey) {\r\n      this.loadScriptContent(tabKey);\r\n    },\r\n\r\n    handleScriptOk() {\r\n      if (!this.scriptContent.trim()) {\r\n        this.$message.warning('Script content cannot be empty');\r\n        return;\r\n      }\r\n\r\n      // 保存脚本内容到文件\r\n      this.saveScriptToFile();\r\n\r\n      this.scriptModalVisible = false;\r\n    },\r\n\r\n    handleModalVisibleChange() {\r\n      // 脚本内容已在data中初始化，不需要额外的处理\r\n    },\r\n\r\n    handleScriptCancel() {\r\n      // 关闭模态框，不保存脚本内容\r\n      this.scriptModalVisible = false;\r\n    },\r\n\r\n    // 直接确认脚本（不打开编辑对话框）\r\n    async confirmScript() {\r\n      if (!this.scriptContent.trim()) {\r\n        this.$message.warning('Script content cannot be empty');\r\n        return;\r\n      }\r\n\r\n      // 保存脚本内容到文件\r\n      const path = await this.saveScriptToFile();\r\n\r\n      if (path) {\r\n        this.$message.success(this.successMessage);\r\n      }\r\n    },\r\n\r\n    async saveScriptToFile() {\r\n      try {\r\n        // 创建一个包含脚本内容的文件对象\r\n        const blob = new Blob([this.scriptContent], { type: 'text/plain' });\r\n        const file = new File([blob], this.filename, { type: 'text/plain' });\r\n\r\n        // 上传脚本文件\r\n        const formData = new FormData();\r\n        formData.append('script_content', file);\r\n\r\n        const response = await axios.post('/api/script/save_script', formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        });\r\n\r\n        if (response.data && response.data.path) {\r\n          this.scriptPath = response.data.path;\r\n          this.$emit('script-saved', response.data.path);\r\n          return response.data.path;\r\n        }\r\n        return null;\r\n      } catch (error) {\r\n        console.error('Script save error:', error);\r\n        this.$message.error(`Failed to save script: ${error.response?.data?.error || error.message}`);\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScriptEditor.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScriptEditor.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ScriptEditor.vue?vue&type=template&id=2ec0ad04\"\nimport script from \"./ScriptEditor.vue?vue&type=script&lang=js\"\nexport * from \"./ScriptEditor.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <a-form :form=\"form\" layout=\"vertical\">\r\n    <!-- 工具配置 - 上传包和编辑脚本在一行 -->\r\n    <a-form-item :label=\"$t('tool.uploadToolPackage')\">\r\n      <div style=\"display: flex; align-items: center; gap: 12px; flex-wrap: wrap;\">\r\n        <!-- 工具包上传 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <a-upload\r\n            name=\"script\"\r\n            :before-upload=\"beforeUpload\"\r\n            :show-upload-list=\"true\"\r\n            @change=\"handleUploadChange\"\r\n            :disabled=\"isProcessing\"\r\n            :file-list=\"fileList\"\r\n            accept=\".zip,application/zip,application/x-zip-compressed\"\r\n          >\r\n            <a-button\r\n              :disabled=\"isProcessing\"\r\n              class=\"nav-style-button\"\r\n            >\r\n              <a-icon type=\"upload\" /> {{ $t('tool.selectToolPackage') }}\r\n            </a-button>\r\n          </a-upload>\r\n          <span v-if=\"fileName\" style=\"margin-left: 10px; color: #666; font-size: 12px;\">\r\n            {{ fileName }}\r\n          </span>\r\n        </div>\r\n        \r\n        <!-- 脚本编辑 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <script-editor\r\n            v-model=\"scriptContent\"\r\n            :script-tabs=\"scriptTabs\"\r\n            :default-tab=\"activeScriptTab\"\r\n            :disabled=\"isProcessing\"\r\n            :filename=\"'script.sh'\"\r\n            :success-message=\"'Script saved successfully'\"\r\n            @script-saved=\"onScriptSaved\"\r\n          />\r\n        </div>\r\n        \r\n        <!-- 启动按钮 -->\r\n        <div style=\"flex: 0 0 auto; min-width: 100px;\">\r\n          <a-button\r\n            class=\"nav-style-button\"\r\n            :loading=\"isProcessing\"\r\n            :disabled=\"isProcessing || !toolConfigComplete\"\r\n            @click=\"startGeneralTool\"\r\n            style=\"width: 100%;\"\r\n          >\r\n            <a-icon type=\"play-circle\" /> {{ $t('tool.start')}}\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </a-form-item>\r\n\r\n    <!-- 本地保存路径 -->\r\n    <a-form-item :label=\"$t('tool.localSaveDirectory')\">\r\n      <a-input\r\n        v-model=\"localSavePath\"\r\n        placeholder=\"e.g., results/tool_results\"\r\n        :disabled=\"isProcessing\"\r\n      />\r\n    </a-form-item>\r\n  </a-form>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState } from 'vuex';\r\nimport { getGeneralScriptNames, getScriptContent } from '@/assets/scripts';\r\nimport ScriptEditor from '@/components/common/ScriptEditor.vue';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\n\r\nexport default {\r\n  name: 'GeneralToolTab',\r\n  mixins: [NotificationMixin],\r\n  components: {\r\n    ScriptEditor\r\n  },\r\n  props: {\r\n    isProcessing: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    selectedRowKeys: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    selectedIp: {\r\n      type: String,\r\n      default: null\r\n    },\r\n    currentProject: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      uploadUrl: '/api/script/upload',\r\n      toolPath: '',\r\n      localSavePath: 'eg : D:\\\\_Projects_python\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\upload\\\\task',\r\n      fileList: [],\r\n      fileName: '',\r\n      scriptContent: '',\r\n      scriptTabs: [],\r\n      activeScriptTab: 'default_command',\r\n      scriptPath: ''\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['sidebarColor']),\r\n    toolConfigComplete() {\r\n      return this.toolPath && this.scriptContent && this.localSavePath && this.selectedRowKeys.length > 0 && this.selectedIp;\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化通用脚本选项卡\r\n    this.scriptTabs = getGeneralScriptNames();\r\n    // 加载默认通用脚本内容\r\n    this.loadScriptContent(this.activeScriptTab);\r\n  },\r\n  methods: {\r\n    loadScriptContent(tabKey) {\r\n      this.activeScriptTab = tabKey;\r\n      this.scriptContent = getScriptContent(tabKey);\r\n    },\r\n    onScriptSaved(path) {\r\n      this.scriptPath = path;\r\n      this.$emit('script-saved', path);\r\n    },\r\n    beforeUpload(file) {\r\n      const isZip = file.type === 'application/zip' ||\r\n                    file.type === 'application/x-zip-compressed' ||\r\n                    file.name.endsWith('.zip');\r\n      if (!isZip) {\r\n        this.$message.error('You can only upload ZIP files!');\r\n        return false;\r\n      }\r\n\r\n      this.file = file;\r\n      this.fileName = file.name; // 设置文件名\r\n\r\n      // 手动上传文件\r\n      this.uploadFile(file);\r\n\r\n      return false;  // 阻止自动上传\r\n    },\r\n    async uploadFile(file) {\r\n      // 显示上传中的消息\r\n      const uploadingMessage = this.$message.loading('Uploading...', 0);\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n\r\n        const response = await axios.post(this.uploadUrl, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        });\r\n\r\n        // 关闭上传中消息\r\n        uploadingMessage();\r\n\r\n        if (response.data && response.data.path) {\r\n          this.toolPath = response.data.path;\r\n          this.$emit('tool-path-changed', response.data.path);\r\n          this.$message.success(`${file.name} uploaded successfully`);\r\n\r\n          // 更新fileList以触发handleUploadChange\r\n          const fileInfo = {\r\n            uid: file.uid,\r\n            name: file.name,\r\n            status: 'done',\r\n            response: response.data\r\n          };\r\n          this.fileList = [fileInfo];\r\n\r\n          // 手动触发计算属性更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          this.$message.error(`${file.name} upload response missing path field`);\r\n        }\r\n      } catch (error) {\r\n        // 确保在错误情况下也关闭上传消息\r\n        uploadingMessage();\r\n\r\n        console.error('Upload error:', error);\r\n        this.$message.error(`${file.name} upload failed: ${error.response?.data?.error || error.message}`);\r\n      }\r\n    },\r\n    handleUploadChange(info) {\r\n      this.fileList = [...info.fileList];\r\n\r\n      // 限制只显示最后一个上传的文件\r\n      this.fileList = this.fileList.slice(-1);\r\n\r\n      const status = info.file.status;\r\n      if (status === 'done') {\r\n        if (info.file.response && info.file.response.path) {\r\n          this.toolPath = info.file.response.path;\r\n          this.$emit('tool-path-changed', info.file.response.path);\r\n          this.fileName = info.file.name; // 确保这里也设置文件名\r\n          this.$message.success(`${info.file.name} uploaded successfully`);\r\n\r\n          // 手动触发计算属性更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          this.$message.error(`${info.file.name} upload response missing path field`);\r\n        }\r\n      } else if (status === 'error') {\r\n        this.$message.error(`${info.file.name} upload failed.`);\r\n      }\r\n    },\r\n    getToolData() {\r\n      return {\r\n        toolPath: this.toolPath,\r\n        scriptContent: this.scriptContent,\r\n        scriptPath: this.scriptPath,\r\n        localSavePath: this.localSavePath\r\n      };\r\n    },\r\n    startGeneralTool() {\r\n      // 检查配置是否完整\r\n      if (!this.toolConfigComplete) {\r\n        this.$notify.warning({\r\n          title: '配置不完整',\r\n          message: '请完成所有工具配置字段。'\r\n        });\r\n        return;\r\n      }\r\n\r\n      if (!this.scriptContent) {\r\n        this.$notify.warning({\r\n          title: '没有脚本',\r\n          message: '请先创建shell脚本。'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 触发启动事件，让父组件处理\r\n      this.$emit('start-general-tool', {\r\n        toolPath: this.toolPath,\r\n        scriptContent: this.scriptContent,\r\n        scriptPath: this.scriptPath,\r\n        localSavePath: this.localSavePath\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GeneralToolTab.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GeneralToolTab.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./GeneralToolTab.vue?vue&type=template&id=685a8716\"\nimport script from \"./GeneralToolTab.vue?vue&type=script&lang=js\"\nexport * from \"./GeneralToolTab.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.editScript')}},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"12px\",\"flex-wrap\":\"wrap\"}},[_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('script-editor',{attrs:{\"script-tabs\":_vm.spiderScriptTabs,\"default-tab\":_vm.activeSpiderScriptTab,\"disabled\":_vm.isProcessing,\"filename\":'spider_script.sh',\"success-message\":'Spider script saved successfully'},on:{\"script-saved\":_vm.onScriptSaved},model:{value:(_vm.spiderScriptContent),callback:function ($$v) {_vm.spiderScriptContent=$$v},expression:\"spiderScriptContent\"}})],1),_c('div',{staticStyle:{\"flex\":\"0 0 auto\",\"min-width\":\"100px\"}},[_c('a-button',{staticClass:\"nav-style-button\",staticStyle:{\"width\":\"100%\"},attrs:{\"loading\":_vm.isProcessing,\"disabled\":_vm.isProcessing || !_vm.spiderScriptContent || !_vm.selectedRowKeys.length || !_vm.selectedIp},on:{\"click\":_vm.runSpider}},[_c('a-icon',{attrs:{\"type\":\"play-circle\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.start') || '运行Spider')+\" \")],1)],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-form :form=\"form\" layout=\"vertical\">\r\n    <!-- Spider工具配置 - 编辑脚本和运行按钮在一行 -->\r\n    <a-form-item :label=\"$t('tool.editScript')\">\r\n      <div style=\"display: flex; align-items: center; gap: 12px; flex-wrap: wrap;\">\r\n        <!-- 脚本编辑 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <script-editor\r\n            v-model=\"spiderScriptContent\"\r\n            :script-tabs=\"spiderScriptTabs\"\r\n            :default-tab=\"activeSpiderScriptTab\"\r\n            :disabled=\"isProcessing\"\r\n            :filename=\"'spider_script.sh'\"\r\n            :success-message=\"'Spider script saved successfully'\"\r\n            @script-saved=\"onScriptSaved\"\r\n          />\r\n        </div>\r\n        \r\n        <!-- 运行按钮 -->\r\n        <div style=\"flex: 0 0 auto; min-width: 100px;\">  \r\n          <a-button\r\n            class=\"nav-style-button\"\r\n            :loading=\"isProcessing\"\r\n            :disabled=\"isProcessing || !spiderScriptContent || !selectedRowKeys.length || !selectedIp\"\r\n            @click=\"runSpider\"\r\n            style=\"width: 100%;\"\r\n          >\r\n            <a-icon type=\"play-circle\" /> {{ $t('tool.start') || '运行Spider' }}\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </a-form-item>\r\n  </a-form>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport { getSpiderScriptNames, getScriptContent } from '@/assets/scripts';\r\nimport ScriptEditor from '@/components/common/ScriptEditor.vue';\r\n\r\nexport default {\r\n  name: 'SpiderToolTab',\r\n  components: {\r\n    ScriptEditor\r\n  },\r\n  props: {\r\n    isProcessing: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    selectedRowKeys: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    selectedIp: {\r\n      type: String,\r\n      default: null\r\n    },\r\n    currentProject: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      spiderScriptContent: '',\r\n      spiderScriptTabs: [],\r\n      activeSpiderScriptTab: 'spider_command',\r\n      spiderToolPath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\static_scan\\\\scan_tools.zip',\r\n      spiderSavePath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\upload\\\\task',\r\n      spiderScriptPath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\start.bat',\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['sidebarColor'])\r\n  },\r\n  created() {\r\n    // 初始化Spider脚本选项卡\r\n    this.spiderScriptTabs = getSpiderScriptNames();\r\n    // 加载默认Spider脚本内容\r\n    this.loadSpiderScriptContent(this.activeSpiderScriptTab);\r\n  },\r\n  methods: {\r\n    loadSpiderScriptContent(tabKey) {\r\n      this.activeSpiderScriptTab = tabKey;\r\n      this.spiderScriptContent = getScriptContent(tabKey);\r\n    },\r\n    onScriptSaved(path) {\r\n      this.spiderScriptPath = path;\r\n      this.$emit('script-saved', path);\r\n    },\r\n    runSpider() {\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: '未选择节点或代理',\r\n          message: '请选择一个或多个节点和一个可用的代理IP来运行工具。'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 准备请求数据\r\n      const requestData = {\r\n        targets: this.selectedRowKeys,\r\n        proxy_ip: this.selectedIp,\r\n        script_content: this.spiderScriptContent,\r\n        dbFile: this.currentProject\r\n      };\r\n\r\n      this.$emit('run-spider', requestData);\r\n    },\r\n    getToolData() {\r\n      return {\r\n        spiderScriptContent: this.spiderScriptContent,\r\n        spiderScriptPath: this.spiderScriptPath,\r\n        spiderToolPath: this.spiderToolPath,\r\n        spiderSavePath: this.spiderSavePath\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpiderToolTab.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpiderToolTab.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SpiderToolTab.vue?vue&type=template&id=04398dd0\"\nimport script from \"./SpiderToolTab.vue?vue&type=script&lang=js\"\nexport * from \"./SpiderToolTab.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <a-card\n    :bordered=\"false\"\n    class=\"header-solid h-full task-card\"\n    :bodyStyle=\"{ padding: '8px 16px' }\"\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\n  >\n    <!-- 流程图 -->\n    <div class=\"steps-container\">\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\n        <a-step>\n          <template #icon>\n            <a-icon type=\"upload\" class=\"step-icon\" />\n          </template>\n        </a-step>\n\n        <a-step>\n          <template #icon>\n            <a-icon type=\"apartment\" class=\"step-icon\" />\n          </template>\n        </a-step>\n\n        <a-step>\n          <template #icon>\n            <a-icon type=\"global\" class=\"step-icon\" />\n          </template>\n        </a-step>\n\n\n      </a-steps>\n    </div>\n\n    <!-- 工具配置区域 -->\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('tool.configureTool')\">\n      <a-tabs default-active-key=\"general\" @change=\"handleToolTabChange\">\n        <!-- 通用工具标签页 -->\n        <a-tab-pane key=\"general\" :tab=\"$t('tool.generalTool') || '通用工具'\">\n          <general-tool-tab\n            ref=\"generalToolTab\"\n            :is-processing=\"isProcessing\"\n            :selected-row-keys=\"selectedRowKeys\"\n            :selected-ip=\"selectedIp\"\n            :current-project=\"currentProject\"\n            @tool-path-changed=\"onToolPathChanged\"\n            @script-saved=\"onGeneralScriptSaved\"\n            @start-general-tool=\"onStartGeneralTool\"\n          />\n        </a-tab-pane>\n\n        <!-- Spider工具标签页 -->\n        <a-tab-pane key=\"spider\" :tab=\"$t('tool.spiderTool') || 'Spider工具'\">\n          <spider-tool-tab\n            ref=\"spiderToolTab\"\n            :is-processing=\"isProcessing\"\n            :selected-row-keys=\"selectedRowKeys\"\n            :selected-ip=\"selectedIp\"\n            :current-project=\"currentProject\"\n            @script-saved=\"onSpiderScriptSaved\"\n            @run-spider=\"onRunSpider\"\n          />\n        </a-tab-pane>\n      </a-tabs>\n    </a-card>\n\n    <!-- 节点选择区域 -->\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\n      <node-selector\n        v-model=\"selectedRowKeys\"\n        :project-file=\"currentProject\"\n        :disabled=\"isProcessing\"\n        @input=\"onNodesSelected\"\n      />\n    </a-card>\n\n    <!-- 代理配置区域 -->\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\n      <proxy-selector\n        v-model=\"selectedIp\"\n        :disabled=\"isProcessing\"\n        @change=\"handleProxyChange\"\n      />\n    </a-card>\n\n    <!-- 任务状态和日志 - 始终显示 -->\n    <task-progress-card :task-type=\"'tool'\" :is-processing=\"isProcessing\" />\n  </a-card>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex';\nimport axios from '@/api/axiosInstance';\nimport NotificationMixin from '@/mixins/NotificationMixin';\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\nimport ProxySelector from '@/components/common/ProxySelector.vue';\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\nimport NodeSelector from '@/components/common/NodeSelector.vue';\nimport GeneralToolTab from '@/components/Cards/GeneralToolTab.vue';\nimport SpiderToolTab from '@/components/Cards/SpiderToolTab.vue';\n\nexport default {\n  mixins: [NotificationMixin, TaskPollingMixin],\n  components: {\n    ProxySelector,\n    TaskProgressCard,\n    NodeSelector,\n    GeneralToolTab,\n    SpiderToolTab\n  },\n  data() {\n    return {\n      selectedRowKeys: [],\n      selectedIp: null,\n      currentStep: 0,\n      activeToolTab: 'general',\n      toolPath: '',\n      scriptPath: '',\n      scriptContent: '',\n      localSavePath: '',\n      spiderScriptPath: '',\n      spiderScriptContent: ''\n    };\n  },\n  computed: {\n    ...mapState(['activeToolTask', 'currentProject', 'sidebarColor']),\n    taskId: {\n      get() {\n        return this.activeToolTask?.task_id;\n      },\n      set(value) {\n        this.$store.dispatch('updateToolTask', value ? { task_id: value } : null);\n      }\n    },\n    toolConfigComplete() {\n      if (this.activeToolTab === 'general') {\n        return this.toolPath && this.scriptContent && this.localSavePath;\n      }\n      return true;\n    },\n    currentStepComputed() {\n      if (this.isProcessing) {\n        return 2;  // 运行中时，点亮前三个图标\n      }\n      if (!this.toolPath && this.activeToolTab === 'general') {\n        return -1;  // 没有上传工具包，所有图标不点亮\n      }\n      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length === 0) {\n        return 0;   // 上传了工具包但未选择节点，点亮第一步图标和连接线\n      }\n      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length > 0 && !this.selectedIp) {\n        return 1;   // 上传了工具包且选择了节点但未选择IP，点亮前两步图标和连接线\n      }\n      return 2;     // 全部选择完成且未在运行时，点亮前三个图标（删除了启动按钮）\n    },\n\n  },\n  created() {\n    // 检查当前项目的活动任务\n    const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\n    if (taskInfo) {\n      const { projectFile } = JSON.parse(taskInfo);\n      if (projectFile === this.currentProject) {\n        this.checkActiveTask();\n      } else {\n        // 清除任务信息如果属于不同项目\n        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);\n        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\n        this.$store.dispatch('updateToolTask', null);\n      }\n    }\n  },\n  methods: {\n    ...mapActions(['addNotification']),\n\n    // 切换工具标签页\n    handleToolTabChange(key) {\n      this.activeToolTab = key;\n    },\n\n    // 处理代理IP变化\n    handleProxyChange(ip) {\n      this.selectedIp = ip;\n    },\n\n    // 处理节点选择变化\n    onNodesSelected(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys;\n    },\n\n    // 处理工具路径变化\n    onToolPathChanged(path) {\n      this.toolPath = path;\n    },\n\n    // 处理通用脚本保存\n    onGeneralScriptSaved(path) {\n      this.scriptPath = path;\n      // 从子组件获取最新的脚本内容\n      if (this.$refs.generalToolTab) {\n        const toolData = this.$refs.generalToolTab.getToolData();\n        this.scriptContent = toolData.scriptContent;\n        this.localSavePath = toolData.localSavePath;\n      }\n    },\n\n    // 处理Spider脚本保存\n    onSpiderScriptSaved(path) {\n      this.spiderScriptPath = path;\n      // 从子组件获取最新的脚本内容\n      if (this.$refs.spiderToolTab) {\n        const toolData = this.$refs.spiderToolTab.getToolData();\n        this.spiderScriptContent = toolData.spiderScriptContent;\n      }\n    },\n\n    // 处理通用工具启动\n    async onStartGeneralTool(toolData) {\n      // 准备请求数据\n      const requestData = {\n        targets: this.selectedRowKeys,\n        proxy_ip: this.selectedIp,\n        script_path: toolData.toolPath,\n        script_content: toolData.scriptContent,\n        result_path: 'result.txt', // 默认结果文件路径\n        local_save_path: toolData.localSavePath,\n        dbFile: this.currentProject\n      };\n\n      // 保存返回的任务ID\n      const taskId = await this.startTaskGeneric(\n        requestData,\n        'run',\n        '通用工具任务已启动',\n        '通用工具任务启动失败'\n      );\n\n      // 如果成功获取到任务ID，确保任务状态被更新\n      if (taskId) {\n        // 立即执行一次轮询，获取初始状态\n        try {\n          const response = await axios.get(`/api/script/${taskId}`);\n          if (response.data) {\n            // 确保更新到Vuex\n            this.$store.dispatch('updateToolTask', response.data);\n\n            // 强制更新视图\n            this.$forceUpdate();\n          }\n        } catch (error) {\n          console.error('获取初始任务状态失败:', error);\n        }\n      }\n    },\n\n    // 处理Spider运行\n    async onRunSpider(requestData) {\n      await this.startTaskGeneric(\n        requestData,\n        'run_spider',\n        'Spider任务已启动',\n        'Spider任务启动失败'\n      );\n    },\n\n\n\n    /**\n     * 通用任务启动方法\n     * @param {Object} requestData - 请求数据\n     * @param {string} endpoint - API端点\n     * @param {string} successMessage - 成功消息\n     * @param {string} errorTitle - 错误标题\n     * @returns {Promise<string|null>} - 返回任务ID或null\n     */\n    async startTaskGeneric(requestData, endpoint, successMessage, errorTitle) {\n      this.isProcessing = true;\n\n      // 清除之前的工具任务通知记录\n      const previousTaskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\n      if (previousTaskInfo) {\n        try {\n          const { taskId } = JSON.parse(previousTaskInfo);\n          if (taskId) {\n            this.clearTaskNotificationMark(taskId, 'tool', this.currentProject);\n          }\n        } catch (e) {\n          console.error('Error clearing previous tool notification:', e);\n        }\n      }\n\n      try {\n\n\n        const { data: responseData } = await axios.post(`/api/script/${endpoint}`, requestData);\n\n        if (responseData && responseData.task_id) {\n          // 保存任务信息到本地存储\n          localStorage.setItem(`toolTaskInfo_${this.currentProject}`, JSON.stringify({\n            taskId: responseData.task_id,\n            projectFile: this.currentProject\n          }));\n          localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\n\n          // 更新任务ID\n          this.taskId = responseData.task_id;\n\n          // 创建初始任务状态对象并更新到Vuex\n          const initialTaskState = {\n            task_id: responseData.task_id,\n            nodes: {}\n          };\n\n          // 为每个选中的节点创建初始状态\n          this.selectedRowKeys.forEach(ip => {\n            initialTaskState.nodes[ip] = {\n              ip: ip,\n              host_name: ip, // 使用IP作为主机名，因为我们不再维护nodes数组\n              status: 'processing',\n              progress: 0\n            };\n          });\n\n          // 立即更新到Vuex，确保UI立即显示进度条\n          this.$store.dispatch('updateToolTask', initialTaskState);\n\n          // 显示任务进度区域\n          this.$nextTick(() => {\n            // 确保DOM已更新\n            const progressElement = document.querySelector('.ant-progress');\n            if (progressElement) {\n              progressElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n            }\n          });\n\n          // 立即执行一次轮询，获取初始状态\n          try {\n            const response = await axios.get(`/api/script/${responseData.task_id}`);\n\n\n            if (response.data) {\n              // 更新Vuex中的任务状态\n              this.$store.dispatch('updateToolTask', response.data);\n            }\n          } catch (pollError) {\n            console.error('初始轮询错误:', pollError);\n            // 即使初始轮询失败，也不影响后续的定期轮询\n          } finally {\n            // 开始定期轮询\n            this.startPolling(responseData.task_id, 'tool', 'script');\n          }\n\n          // 显示成功消息\n          this.$message.success(successMessage || '任务已启动');\n\n          // 强制更新视图\n          this.$forceUpdate();\n\n          return responseData.task_id;\n        }\n        return null;\n      } catch (error) {\n        console.error(`启动${endpoint}任务出错:`, error);\n        let errorMessage = '服务器连接错误。';\n\n        if (error.message) {\n          errorMessage = error.message;\n        } else if (error.response && error.response.data && error.response.data.error) {\n          errorMessage = error.response.data.error;\n        }\n\n        this.$notify.error({\n          title: errorTitle || '任务启动失败',\n          message: errorMessage,\n        });\n        this.isProcessing = false;\n        return null;\n      }\n    },\n\n    // 重写 checkActiveTask 方法，调用混入中的方法\n    async checkActiveTask() {\n      try {\n        const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\n        const taskCompleted = localStorage.getItem(`toolTaskCompleted_${this.currentProject}`);\n\n        if (taskInfo) {\n          const { taskId, projectFile } = JSON.parse(taskInfo);\n\n          if (projectFile !== this.currentProject) {\n            throw new Error('Task belongs to different project');\n          }\n\n          const response = await axios.get(`/api/script/${taskId}`);\n\n          if (response.data) {\n            this.$store.dispatch('updateToolTask', response.data);\n\n            if (response.data.nodes) {\n              const nodes = Object.values(response.data.nodes);\n              const allCompleted = nodes.every(node =>\n                ['success', 'failed'].includes(node.status)\n              );\n\n              if (!allCompleted && !taskCompleted) {\n                this.isProcessing = true;\n                this.startPolling(taskId, 'tool', 'script');\n              } else if (allCompleted) {\n                this.isProcessing = false;\n                localStorage.setItem(`toolTaskCompleted_${this.currentProject}`, 'true');\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error checking active task:', error);\n        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);\n        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\n      }\n    },\n\n    // 使用混入中的 startPolling 方法，但需要提供特定参数\n    startPollingWrapper(taskId) {\n      this.startPolling(taskId, 'tool', 'script');\n    },\n\n    activated() {\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\n      this.checkActiveTask();\n    }\n  },\n  watch: {\n    // 监听 currentProject 变化\n    currentProject: {\n      handler(newProject, oldProject) {\n        if (newProject !== oldProject) {\n          // 清除之前项目的任务状态\n          this.$store.dispatch('updateToolTask', null);\n          this.stopPolling();\n          // 检查新项目的活动任务\n          this.checkActiveTask();\n        }\n      },\n      immediate: true\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n// 基础卡片样式\n.task-card {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  background-color: #fff;\n  padding: 20px;\n}\n\n// 步骤容器\n.steps-container {\n  width: 50%;\n  margin: 0 auto 24px;\n  padding: 12px 0;\n}\n\n// 深度选择器样式集中管理\n::v-deep {\n  .ant-card {\n    border-radius: 8px;\n    overflow: hidden;\n    .ant-card-head {\n      background: #f0f2f5;\n    }\n  }\n\n  .ant-progress {\n    border-radius: 3px;\n  }\n  .ant-tooltip-inner {\n    max-width: 500px;\n    white-space: pre-wrap;\n  }\n  .ant-table-tbody > tr:last-child > td {\n    border-bottom: 1px solid #f0f0f0;\n  }\n  .steps-flow {\n    .ant-steps-item {\n      &-process,\n      &-finish {\n        .ant-steps-item-container {\n          .ant-steps-item-content {\n            .ant-steps-item-title::after {\n              background-color: #3b4149 !important;\n              height: 2px !important;\n              top: 25px !important;\n            }\n          }\n        }\n      }\n\n      &-wait {\n        .ant-steps-item-container {\n          .ant-steps-item-content {\n            .ant-steps-item-title::after {\n              background-color: #d9d9d9 !important;\n              height: 2px !important;\n              top: 25px !important;\n            }\n          }\n        }\n\n        .step-icon {\n          color: #d9d9d9 !important;\n        }\n      }\n\n      &-icon {\n        width: 88px;\n        height: 88px;\n        line-height: 80px;\n        padding: 4px;\n        font-size: 40px;\n        border-width: 2px;\n        margin-top: -20px;\n        color: #3b4149;\n\n        .step-icon {\n          font-size: 40px;\n          color: #3b4149;\n        }\n      }\n\n      &-tail::after {\n        height: 2px;\n      }\n\n      &:last-child {\n        .step-icon {\n          color: #d9d9d9 !important;\n        }\n\n        &.ant-steps-item-process,\n        &.ant-steps-item-finish {\n          .step-icon {\n            color: #3b4149 !important;\n          }\n        }\n      }\n    }\n  }\n  .ready-to-start {\n    animation: pulse 1.2s infinite;\n  }\n\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n      opacity: 1;\n    }\n    50% {\n      transform: scale(1.1);\n      opacity: 0.8;\n    }\n    100% {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n\n  .clickable {\n    cursor: pointer;\n    &:hover {\n      opacity: 0.8;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ToolsPanel.vue?vue&type=template&id=0809a573&scoped=true\"\nimport script from \"./ToolsPanel.vue?vue&type=script&lang=js\"\nexport * from \"./ToolsPanel.vue?vue&type=script&lang=js\"\nimport style0 from \"./ToolsPanel.vue?vue&type=style&index=0&id=0809a573&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0809a573\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<ToolsPanel></ToolsPanel>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport ToolsPanel from \"@/components/Cards/ToolsPanel.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        ToolsPanel,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GenerateScript.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GenerateScript.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./GenerateScript.vue?vue&type=template&id=1233a260\"\nimport script from \"./GenerateScript.vue?vue&type=script&lang=js\"\nexport * from \"./GenerateScript.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}