import os
import sys
import threading
from os.path import abspath, join, dirname
from snapshot.utils import run_command
from log.log_config import logger


root_path = join(abspath(dirname(__file__)), ".")
sys.path.insert(0, root_path)
sys.path.insert(1, join(root_path, "run_env/lib/python3.8/site-packages"))


def execute_script(script_name, node_name, ip):
    script_path = os.path.join('snapshot', script_name)
    # 设置 PYTHONPATH 环境变量，包含当前目录和依赖路径  
    pythonpath_parts = [
        root_path,
        join(root_path, "run_env/lib/python3.8/site-packages")
    ]
    pythonpath = os.pathsep.join(pythonpath_parts)
    
    # 在命令前设置 PYTHONPATH 环境变量
    command = f'PYTHONPATH="{pythonpath}" run_env/bin/python.exe {script_path} {node_name} {ip}'
    logger.info(f"Executing script: {script_name}, node: {node_name}, IP: {ip}")
    return_code, output = run_command(command)

    if return_code != 0:
        logger.error(f"Script execution failed {script_name}: {output}")
    else:
        logger.info(f"Script execution succeeded {script_name}: {output}")


def run_script_threaded(script_name, host_name, ip):
    """Runs a script in a separate thread."""
    execute_script(script_name, host_name, ip)


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Invalid arguments: python run_snapshots.py <hostname> <ip>")
        sys.exit(1)

    host_name = sys.argv[1]
    ip = sys.argv[2]
    logger.info(f"Starting snapshot collection program, hostname: {host_name}, IP: {ip}")

    scripts = [
        'process_snapshot.py',
        'os_package_snapshot.py',
        'hardware_snapshot.py',
        'filesystem_snapshot.py',
        'kubernetes_snapshot.py',
        'docker_snapshot.py',
        # 'crictl_snapshot.py',
        # 'port_snapshot.py',
    ]

    threads = []
    logger.info(f"Starting execution of {len(scripts)} snapshot scripts")
    for script in scripts:
        thread = threading.Thread(target=run_script_threaded, args=(script, host_name, ip))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

    logger.info("All snapshot scripts execution completed")
