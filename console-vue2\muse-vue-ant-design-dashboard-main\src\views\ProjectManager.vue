<template>
  <div>
    <a-card title="项目列表">
      <template #extra>
        <a-button type="primary" @click="createNewProject">
          创建新项目
        </a-button>
      </template>
      <a-table
        :columns="columns"
        :data-source="projects"
        :row-key="record => record.dbFile"
        :customRow="onCustomRow"
        class="project-table"
      />
    </a-card>
  </div>
</template>

<script>
import axios from '@/api/axiosInstance';

export default {
  name: 'ProjectManager',
  data() {
    return {
      columns: [
        {
          title: '项目名称',
          dataIndex: 'name',
          key: 'name',
          width: '250px',
          ellipsis: true,
          customRender: (text) => {
            return (
            <span>
            <a-icon type="folder" style="margin-right: 8px;" />
            {text}
            </span>
            );
          }
        },
        {
          title: '数据库文件',
          dataIndex: 'dbFile',
          key: 'dbFile',
          width: '280px',
          ellipsis: true
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: '160px',
          customRender: (text) => {
            return new Date(text).toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            });
          }
        },
        {
          title: '操作',
          key: 'action',
          width: '180px',
          align: 'center',
          fixed: 'right',
          customRender: (text, record) => {
            return (
              <a-space size={8}>
                <a-button
                  type="primary"
                  onClick={() => this.selectProject(record)}
                >
                  进入项目
                </a-button>
                <a-popconfirm
                  title="确定要删除这个项目吗？"
                  onConfirm={() => this.deleteProject(record)}
                  okText="确定"
                  cancelText="取消"
                >
                  <a-button type="danger">删除</a-button>
                </a-popconfirm>
              </a-space>
            );
          }
        }
      ],
      projects: [],
      tempProjectName: ''
    };
  },
  methods: {
    onCustomRow(record) {
      return {
        on: {
          click: () => {
            // 如果需要行点击事件的话，在这里处理
          }
        }
      };
    },
    async fetchProjects() {
      try {
        const response = await axios.get('/api/projects');
        if (Array.isArray(response.data)) {
          this.projects = response.data.map(project => ({
            name: project.name || '',
            dbFile: project.dbFile || '',
            createdAt: project.createdAt || '',
            key: project.dbFile || Date.now().toString()
          }));
        } else {
          this.projects = [];
          console.error('项目数据格式无效：', response.data);
        }
      } catch (error) {
        console.error('获取项目列表失败：', error);
        this.$message.error('获取项目列表失败');
        this.projects = [];
      }
    },

    async selectProject(project) {
      if (!project?.dbFile) {
        console.error('项目数据无效：', project);
        this.$message.error('无效的项目数据');
        return;
      }

      try {
        const encodedDbFile = encodeURIComponent(project.dbFile);
        const validationUrl = `/api/projects/validate/${encodedDbFile}`;

        const response = await axios.get(validationUrl);
        if (response.data.valid) {
          await this.$store.dispatch('switchProject', { 
            dbFile: project.dbFile, 
            projectName: project.name 
          });
          await this.$store.dispatch('fetchNodes');

          // 清除所有任务相关的localStorage
          localStorage.removeItem('activeTaskId');
          localStorage.removeItem('taskCompleted');
          localStorage.removeItem('activeUploadTaskId');
          localStorage.removeItem('activeDownloadTaskId');
          localStorage.removeItem('activeToolTaskId');

          await this.$router.push('/task');
          this.$message.success('成功进入项目');
        } else {
          console.error('验证失败：', response.data.error);
          this.$message.error(response.data.error || '数据库文件无效或已损坏');
        }
      } catch (error) {
        console.error('项目验证出错：', error);
        this.$message.error(error.response?.data?.error || '验证项目失败');
      }
    },

    async deleteProject(project) {
      if (!project?.dbFile) {
        this.$message.error('无效的项目数据');
        return;
      }

      try {
        await axios.delete(`/api/projects/${encodeURIComponent(project.dbFile)}`);
        this.$message.success('项目删除成功');
        await this.fetchProjects();
      } catch (error) {
        console.error('Error deleting project:', error);
        this.$message.error('删除项目失败');
      }
    },

    async createNewProject() {
      try {
        const projectName = await new Promise((resolve, reject) => {
          this.$confirm({
            title: '创建新项目',
            content: h => (
              <div>
                <a-input
                  placeholder="请输入项目名称"
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^a-zA-Z0-9_-]/g, '');
                    this.tempProjectName = value;
                    e.target.value = value;
                  }}
                />
                <div class="project-hint-text" style="font-size: 12px; margin-top: 8px;">
                  只允许输入大小写字母、数字、下划线和连字符
                </div>
              </div>
            ),
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              if (!this.tempProjectName) {
                this.$message.warning('请输入项目名称');
                return Promise.reject();
              }
              // 验证项目名称格式
              if (!/^[a-zA-Z0-9_-]+$/.test(this.tempProjectName)) {
                this.$message.warning('项目名称只能包含大小写字母、数字、下划线和连字符');
                return Promise.reject();
              }
              resolve(this.tempProjectName);
            },
            onCancel: () => {
              reject();
            }
          });
        });

        if (projectName) {
          const response = await axios.post('/api/projects/new', {
            name: projectName
          });

          await this.fetchProjects();
          this.$message.success('新项目创建成功');
          if (response.data?.dbFile) {
            await this.selectProject(response.data);
          }
        }
      } catch (error) {
        if (error) { // 用户取消操作时不显示错误
          console.error('Error creating new project:', error);
          this.$message.error('创建新项目失败');
        }
      }
    }
  },
  created() {
    this.fetchProjects();
  }
};
</script>

<style scoped>

</style>
