{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753165665823}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "moment", "mapState", "RefreshButton", "TestCaseDetailModal", "components", "name", "data", "loading", "testcases", "total", "currentPage", "detailsVisible", "selectedTestcase", "searchForm", "level", "undefined", "prepare_condition", "test_steps", "expected_result", "tableComponents", "header", "cell", "h", "props", "children", "key", "restProps", "col", "columns", "find", "k", "dataIndex", "width", "style", "position", "right", "top", "bottom", "cursor", "zIndex", "on", "mousedown", "e", "startX", "clientX", "startWidth", "onMouseMove", "newWidth", "map", "c", "onMouseUp", "document", "removeEventListener", "addEventListener", "created", "fetchTestcases", "computed", "$createElement", "title", "align", "customRender", "_", "__", "index", "$t", "ellipsis", "text", "record", "click", "viewDetails", "slots", "methods", "page", "params", "page_size", "response", "get", "error", "console", "$message", "handleSearch", "resetSearch", "formatDate", "date", "format", "getResultColor", "result", "colors", "getLevelColor", "handlePageChange"], "sources": ["src/components/Cards/TestCaseInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item :label=\"$t('caseColumn.name')\">\r\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.level')\">\r\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\r\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\r\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              {{ $t('testcase.searchButton') }}\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('testcase.resetButton') }}\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n        bordered\r\n        :components=\"tableComponents\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      tableComponents: {\r\n        header: {\r\n          cell: (h, props, children) => {\r\n            const { key, ...restProps } = props;\r\n            const col = this.columns.find(col => {\r\n              const k = col.dataIndex || col.key;\r\n              return k === key;\r\n            });\r\n            if (!col || !col.width) {\r\n              return h('th', { ...restProps }, children);\r\n            }\r\n\r\n            return h(\r\n              'th',\r\n              {\r\n                ...restProps,\r\n                style: { position: 'relative' }\r\n              },\r\n              [\r\n                ...children,\r\n                h('div', {\r\n                  style: {\r\n                    position: 'absolute',\r\n                    right: '-5px',\r\n                    top: 0,\r\n                    bottom: 0,\r\n                    width: '10px',\r\n                    cursor: 'col-resize',\r\n                    zIndex: 1\r\n                  },\r\n                  on: {\r\n                    mousedown: (e) => {\r\n                      const startX = e.clientX;\r\n                      const startWidth = col.width;\r\n                      const onMouseMove = (e) => {\r\n                        const newWidth = startWidth + (e.clientX - startX);\r\n                        if (newWidth > 50) {\r\n                          this.columns = this.columns.map(c => {\r\n                            if (c.key === key || c.dataIndex === key) {\r\n                              return { ...c, width: newWidth };\r\n                            }\r\n                            return c;\r\n                          });\r\n                        }\r\n                      };\r\n                      const onMouseUp = () => {\r\n                        document.removeEventListener('mousemove', onMouseMove);\r\n                        document.removeEventListener('mouseup', onMouseUp);\r\n                      };\r\n                      document.addEventListener('mousemove', onMouseMove);\r\n                      document.addEventListener('mouseup', onMouseUp);\r\n                    }\r\n                  }\r\n                })\r\n              ]\r\n            );\r\n          }\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    columns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (_, __, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n      ];\r\n    },\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n// 列拖动样式\r\n:deep(.ant-table-thead > tr > th) {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    .resize-handle {\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.resize-handle) {\r\n  position: absolute;\r\n  right: -5px;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 10px;\r\n  cursor: col-resize;\r\n  z-index: 1;\r\n  opacity: 0;\r\n  transition: opacity 0.2s;\r\n\r\n  &:hover {\r\n    opacity: 1;\r\n    background-color: rgba(24, 144, 255, 0.2);\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"], "mappings": ";;;AAiHA,OAAAA,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,QAAA;AACA,OAAAC,aAAA;AACA,OAAAC,mBAAA;AAEA;EACAC,UAAA;IACAF,aAAA;IACAC;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACAC,eAAA;QACAC,MAAA;UACAC,IAAA,EAAAA,CAAAC,CAAA,EAAAC,KAAA,EAAAC,QAAA;YACA;cAAAC,GAAA;cAAA,GAAAC;YAAA,IAAAH,KAAA;YACA,MAAAI,GAAA,QAAAC,OAAA,CAAAC,IAAA,CAAAF,GAAA;cACA,MAAAG,CAAA,GAAAH,GAAA,CAAAI,SAAA,IAAAJ,GAAA,CAAAF,GAAA;cACA,OAAAK,CAAA,KAAAL,GAAA;YACA;YACA,KAAAE,GAAA,KAAAA,GAAA,CAAAK,KAAA;cACA,OAAAV,CAAA;gBAAA,GAAAI;cAAA,GAAAF,QAAA;YACA;YAEA,OAAAF,CAAA,CACA,MACA;cACA,GAAAI,SAAA;cACAO,KAAA;gBAAAC,QAAA;cAAA;YACA,GACA,CACA,GAAAV,QAAA,EACAF,CAAA;cACAW,KAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAL,KAAA;gBACAM,MAAA;gBACAC,MAAA;cACA;cACAC,EAAA;gBACAC,SAAA,EAAAC,CAAA;kBACA,MAAAC,MAAA,GAAAD,CAAA,CAAAE,OAAA;kBACA,MAAAC,UAAA,GAAAlB,GAAA,CAAAK,KAAA;kBACA,MAAAc,WAAA,GAAAJ,CAAA;oBACA,MAAAK,QAAA,GAAAF,UAAA,IAAAH,CAAA,CAAAE,OAAA,GAAAD,MAAA;oBACA,IAAAI,QAAA;sBACA,KAAAnB,OAAA,QAAAA,OAAA,CAAAoB,GAAA,CAAAC,CAAA;wBACA,IAAAA,CAAA,CAAAxB,GAAA,KAAAA,GAAA,IAAAwB,CAAA,CAAAlB,SAAA,KAAAN,GAAA;0BACA;4BAAA,GAAAwB,CAAA;4BAAAjB,KAAA,EAAAe;0BAAA;wBACA;wBACA,OAAAE,CAAA;sBACA;oBACA;kBACA;kBACA,MAAAC,SAAA,GAAAA,CAAA;oBACAC,QAAA,CAAAC,mBAAA,cAAAN,WAAA;oBACAK,QAAA,CAAAC,mBAAA,YAAAF,SAAA;kBACA;kBACAC,QAAA,CAAAE,gBAAA,cAAAP,WAAA;kBACAK,QAAA,CAAAE,gBAAA,YAAAH,SAAA;gBACA;cACA;YACA,GAEA;UACA;QACA;MACA;IACA;EACA;EACAI,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,GAAAvD,QAAA;IACA2B,QAAA;MAAA,MAAAN,CAAA,QAAAmC,cAAA;MACA,QACA;QACAC,KAAA;QACA3B,SAAA;QACAN,GAAA;QACAO,KAAA;QACA2B,KAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,EAAA,EAAAC,KAAA;UACA,aAAArD,WAAA,cAAAqD,KAAA;QACA;MACA,GACA;QACAL,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACAiC,QAAA;QACAL,YAAA,EAAAA,CAAAM,IAAA,EAAAC,MAAA;UACA,OAAA7C,CAAA;YAAA;cAAA,SAAA8C,CAAA,UAAAC,WAAA,CAAAF,MAAA;YAAA;YAAA;UAAA,IAAAD,IAAA;QACA;MACA,GACA;QACAR,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACA;MACA,GACA;QACA0B,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACAsC,KAAA;UAAAV,YAAA;QAAA;MACA,GACA;QACAF,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACAiC,QAAA;MACA,GACA;QACAP,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACAiC,QAAA;MACA,GACA;QACAP,KAAA,OAAAM,EAAA;QACAjC,SAAA;QACAN,GAAA;QACAO,KAAA;QACAiC,QAAA;MACA,EACA;IACA;EACA;EACAM,OAAA;IACA,MAAAhB,eAAAiB,IAAA;MACA,KAAAjE,OAAA;MACA;QACA;QACA,MAAAkE,MAAA;UACAD,IAAA,EAAAA,IAAA;UACAE,SAAA;QACA;;QAEA;QACA,SAAA7D,UAAA,CAAAR,IAAA,EAAAoE,MAAA,CAAApE,IAAA,QAAAQ,UAAA,CAAAR,IAAA;QACA,SAAAQ,UAAA,CAAAC,KAAA,EAAA2D,MAAA,CAAA3D,KAAA,QAAAD,UAAA,CAAAC,KAAA;QACA,SAAAD,UAAA,CAAAG,iBAAA,EAAAyD,MAAA,CAAAzD,iBAAA,QAAAH,UAAA,CAAAG,iBAAA;QACA,SAAAH,UAAA,CAAAI,UAAA,EAAAwD,MAAA,CAAAxD,UAAA,QAAAJ,UAAA,CAAAI,UAAA;QACA,SAAAJ,UAAA,CAAAK,eAAA,EAAAuD,MAAA,CAAAvD,eAAA,QAAAL,UAAA,CAAAK,eAAA;QAEA,MAAAyD,QAAA,SAAA5E,KAAA,CAAA6E,GAAA;UAAAH;QAAA;QACA,KAAAjE,SAAA,GAAAmE,QAAA,CAAArE,IAAA,CAAAA,IAAA;QACA,KAAAG,KAAA,GAAAkE,QAAA,CAAArE,IAAA,CAAAG,KAAA;MACA,SAAAoE,KAAA;QACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAtE,OAAA;MACA;IACA;IAEA;IACAyE,aAAA;MACA,KAAAtE,WAAA;MACA,KAAA6C,cAAA;IACA;IAEA;IACA0B,YAAA;MACA,KAAApE,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA,KAAAR,WAAA;MACA,KAAA6C,cAAA;IACA;IACA2B,WAAAC,IAAA;MACA,OAAAA,IAAA,GAAAnF,MAAA,CAAAmF,IAAA,EAAAC,MAAA;IACA;IACAC,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IACAE,cAAA1E,KAAA;MACA,MAAAyE,MAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAzE,KAAA;IACA;IACAuD,YAAAF,MAAA;MACA,KAAAvD,gBAAA,GAAAuD,MAAA;MACA,KAAAxD,cAAA;IACA;IACA8E,iBAAAjB,IAAA;MACA,KAAA9D,WAAA,GAAA8D,IAAA;MACA,KAAAjB,cAAA,CAAAiB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}