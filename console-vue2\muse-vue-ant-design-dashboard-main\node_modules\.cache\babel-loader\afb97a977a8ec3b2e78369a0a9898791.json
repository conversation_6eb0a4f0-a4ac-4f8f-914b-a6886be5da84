{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753166417821}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "moment", "mapState", "RefreshButton", "TestCaseDetailModal", "ResizableTable", "components", "name", "data", "loading", "testcases", "total", "currentPage", "detailsVisible", "selectedTestcase", "searchForm", "level", "undefined", "prepare_condition", "test_steps", "expected_result", "tableColumns", "created", "initializeColumns", "fetchTestcases", "computed", "methods", "h", "$createElement", "title", "dataIndex", "key", "width", "align", "customRender", "_", "__", "index", "$t", "ellipsis", "text", "record", "click", "viewDetails", "slots", "handleColumnsChange", "columns", "page", "params", "page_size", "response", "get", "error", "console", "$message", "handleSearch", "resetSearch", "formatDate", "date", "format", "getResultColor", "result", "colors", "getLevelColor", "handlePageChange"], "sources": ["src/components/Cards/TestCaseInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item :label=\"$t('caseColumn.name')\">\r\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.level')\">\r\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\r\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\r\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              {{ $t('testcase.searchButton') }}\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('testcase.resetButton') }}\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <ResizableTable\r\n        :columns=\"tableColumns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n        @columns-change=\"handleColumnsChange\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </ResizableTable>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\nimport ResizableTable from '../common/ResizableTable.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal,\r\n    ResizableTable\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      tableColumns: []\r\n    };\r\n  },\r\n  created() {\r\n    this.initializeColumns();\r\n    this.fetchTestcases();\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\r\n  },\r\n  methods: {\r\n    initializeColumns() {\r\n      this.tableColumns = [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (_, __, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n      ];\r\n    },\r\n\r\n    handleColumnsChange(columns) {\r\n      this.tableColumns = columns;\r\n    },\r\n\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\r\n\r\n\r\n</style>\r\n"], "mappings": "AAgHA,OAAAA,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,QAAA;AACA,OAAAC,aAAA;AACA,OAAAC,mBAAA;AACA,OAAAC,cAAA;AAEA;EACAC,UAAA;IACAH,aAAA;IACAC,mBAAA;IACAC;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,GAAAvB,QAAA;EACA;EACAwB,OAAA;IACAH,kBAAA;MAAA,MAAAI,CAAA,QAAAC,cAAA;MACA,KAAAP,YAAA,IACA;QACAQ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,EAAA,EAAAC,KAAA;UACA,aAAAzB,WAAA,cAAAyB,KAAA;QACA;MACA,GACA;QACAR,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;QACAL,YAAA,EAAAA,CAAAM,IAAA,EAAAC,MAAA;UACA,OAAAd,CAAA;YAAA;cAAA,SAAAe,CAAA,UAAAC,WAAA,CAAAF,MAAA;YAAA;YAAA;UAAA,IAAAD,IAAA;QACA;MACA,GACA;QACAX,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAY,KAAA;UAAAV,YAAA;QAAA;MACA,GACA;QACAL,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,GACA;QACAV,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,GACA;QACAV,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,EACA;IACA;IAEAM,oBAAAC,OAAA;MACA,KAAAzB,YAAA,GAAAyB,OAAA;IACA;IAEA,MAAAtB,eAAAuB,IAAA;MACA,KAAAtC,OAAA;MACA;QACA;QACA,MAAAuC,MAAA;UACAD,IAAA,EAAAA,IAAA;UACAE,SAAA;QACA;;QAEA;QACA,SAAAlC,UAAA,CAAAR,IAAA,EAAAyC,MAAA,CAAAzC,IAAA,QAAAQ,UAAA,CAAAR,IAAA;QACA,SAAAQ,UAAA,CAAAC,KAAA,EAAAgC,MAAA,CAAAhC,KAAA,QAAAD,UAAA,CAAAC,KAAA;QACA,SAAAD,UAAA,CAAAG,iBAAA,EAAA8B,MAAA,CAAA9B,iBAAA,QAAAH,UAAA,CAAAG,iBAAA;QACA,SAAAH,UAAA,CAAAI,UAAA,EAAA6B,MAAA,CAAA7B,UAAA,QAAAJ,UAAA,CAAAI,UAAA;QACA,SAAAJ,UAAA,CAAAK,eAAA,EAAA4B,MAAA,CAAA5B,eAAA,QAAAL,UAAA,CAAAK,eAAA;QAEA,MAAA8B,QAAA,SAAAlD,KAAA,CAAAmD,GAAA;UAAAH;QAAA;QACA,KAAAtC,SAAA,GAAAwC,QAAA,CAAA1C,IAAA,CAAAA,IAAA;QACA,KAAAG,KAAA,GAAAuC,QAAA,CAAA1C,IAAA,CAAAG,KAAA;MACA,SAAAyC,KAAA;QACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAA3C,OAAA;MACA;IACA;IAEA;IACA8C,aAAA;MACA,KAAA3C,WAAA;MACA,KAAAY,cAAA;IACA;IAEA;IACAgC,YAAA;MACA,KAAAzC,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA,KAAAR,WAAA;MACA,KAAAY,cAAA;IACA;IACAiC,WAAAC,IAAA;MACA,OAAAA,IAAA,GAAAzD,MAAA,CAAAyD,IAAA,EAAAC,MAAA;IACA;IACAC,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IACAE,cAAA/C,KAAA;MACA,MAAA8C,MAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAA9C,KAAA;IACA;IACA2B,YAAAF,MAAA;MACA,KAAA3B,gBAAA,GAAA2B,MAAA;MACA,KAAA5B,cAAA;IACA;IACAmD,iBAAAjB,IAAA;MACA,KAAAnC,WAAA,GAAAmC,IAAA;MACA,KAAAvB,cAAA,CAAAuB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}