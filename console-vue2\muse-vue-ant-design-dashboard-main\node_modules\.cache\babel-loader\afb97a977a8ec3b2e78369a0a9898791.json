{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753180839980}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "moment", "mapState", "RefreshButton", "TestCaseDetailModal", "ResizableTable", "components", "name", "data", "loading", "testcases", "total", "currentPage", "detailsVisible", "selectedTestcase", "searchForm", "level", "undefined", "prepare_condition", "test_steps", "expected_result", "testcase_feature", "tableColumns", "cacheData", "created", "initializeColumns", "fetchTestcases", "computed", "methods", "h", "$createElement", "title", "dataIndex", "key", "width", "align", "customRender", "_", "__", "index", "$t", "ellipsis", "text", "record", "click", "viewDetails", "slots", "scopedSlots", "handleColumnsChange", "columns", "page", "params", "page_size", "response", "get", "map", "item", "editable", "error", "console", "$message", "handleChange", "value", "id", "column", "target", "find", "edit", "save", "originalData", "Testcase_Feature", "put", "Testcase_Number", "then", "success", "Object", "assign", "catch", "message", "cancel", "handleSearch", "resetSearch", "formatDate", "date", "format", "getResultColor", "result", "colors", "getLevelColor", "handlePageChange"], "sources": ["src/components/Cards/TestCaseInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"layout-content\">\n    <a-card :bordered=\"false\">\n      <template #title>\n        <div class=\"card-header-wrapper\">\n          <div class=\"header-wrapper\">\n            <div class=\"logo-wrapper\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\n              </svg>\n            </div>\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\n          </div>\n          <div>\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\n          </div>\n        </div>\n      </template>\n\n      <!-- 搜索表单 -->\n      <div class=\"search-form\">\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\n          <a-form-item :label=\"$t('caseColumn.name')\">\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.level')\">\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.feature')\">\n            <a-input v-model=\"searchForm.testcase_feature\" :placeholder=\"$t('caseColumn.feature')\" allowClear />\n          </a-form-item>\n          <a-form-item>\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              {{ $t('testcase.searchButton') }}\n            </a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\n              <a-icon type=\"reload\" />\n              {{ $t('testcase.resetButton') }}\n            </a-button>\n          </a-form-item>\n        </a-form>\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <ResizableTable\n        :columns=\"tableColumns\"\n        :data-source=\"testcases\"\n        :loading=\"loading\"\n        :pagination=\"{\n          total: total,\n          pageSize: 100,\n          current: currentPage,\n          showSizeChanger: false,\n          showQuickJumper: true,\n          onChange: handlePageChange\n        }\"\n        :scroll=\"{ x: 1500 }\"\n        @columns-change=\"handleColumnsChange\"\n      >\n        <!-- Custom column renders -->\n        <template #Testcase_LastResult=\"{ text }\">\n          <a-tag :color=\"getResultColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Level=\"{ text }\">\n          <a-tag :color=\"getLevelColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Feature=\"{ text, record }\">\n          <div>\n            <a-input\n                v-if=\"record.editable\"\n                style=\"margin: -5px 0\"\n                :value=\"text\"\n                @change=\"e => handleChange(e.target.value, record.id, 'Testcase_Feature')\"\n                @pressEnter=\"() => save(record.id)\"\n            />\n            <div\n                v-else\n                class=\"editable-cell-value-wrap\"\n                style=\"padding-right: 24px\"\n                @click=\"() => edit(record.id)\"\n            >\n              {{ text }}\n            </div>\n          </div>\n        </template>\n\n        <template #lastModified=\"{ text }\">\n          {{ formatDate(text) }}\n        </template>\n\n        <template #action=\"{ record }\">\n          <a-space>\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\n              View Details\n            </a-button>\n          </a-space>\n        </template>\n      </ResizableTable>\n\n      <!-- Details Modal -->\n      <TestCaseDetailModal\n        :visible=\"detailsVisible\"\n        :testcase=\"selectedTestcase\"\n        @close=\"detailsVisible = false\"\n      />\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\nimport moment from 'moment';\nimport {mapState} from \"vuex\";\nimport RefreshButton from '../Widgets/RefreshButton.vue';\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\nimport ResizableTable from '../common/ResizableTable.vue';\n\nexport default {\n  components: {\n    RefreshButton,\n    TestCaseDetailModal,\n    ResizableTable\n  },\n  name: 'TestCases',\n  data() {\n    return {\n      loading: false,\n      testcases: [],\n      total: 0,\n      currentPage: 1,\n      detailsVisible: false,\n      selectedTestcase: null,\n      searchForm: {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      },\n      tableColumns: [],\n      cacheData: [], // 用于存储原始数据，以便在取消编辑时恢复\n    };\n  },\n  created() {\n    this.initializeColumns();\n    this.fetchTestcases();\n  },\n  computed: {\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\n  },\n  methods: {\n    initializeColumns() {\n      this.tableColumns = [\n        {\n          title: '#',\n          dataIndex: 'index',\n          key: 'index',\n          width: 100,\n          align: 'center',\n          customRender: (_, __, index) => {\n            return ((this.currentPage - 1) * 100) + index + 1;\n          }\n        },\n        {\n          title: this.$t('caseColumn.number'),\n          dataIndex: 'Testcase_Number',\n          key: 'Testcase_Number',\n          width: 130,\n          ellipsis: true,\n          customRender: (text, record) => {\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\n          }\n        },\n        {\n          title: this.$t('caseColumn.name'),\n          dataIndex: 'Testcase_Name',\n          key: 'Testcase_Name',\n          width: 200,\n          // ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.level'),\n          dataIndex: 'Testcase_Level',\n          key: 'Testcase_Level',\n          width: 100,\n          slots: { customRender: 'Testcase_Level' },\n        },\n        {\n          title: this.$t('caseColumn.prepareCondition'),\n          dataIndex: 'Testcase_PrepareCondition',\n          key: 'Testcase_PrepareCondition',\n          width: 250,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.testSteps'),\n          dataIndex: 'Testcase_TestSteps',\n          key: 'Testcase_TestSteps',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.expectedResult'),\n          dataIndex: 'Testcase_ExpectedResult',\n          key: 'Testcase_ExpectedResult',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.feature'),\n          dataIndex: 'Testcase_Feature',\n          key: 'Testcase_Feature',\n          width: 200,\n          scopedSlots: { customRender: 'Testcase_Feature' },\n        },        \n      ];\n    },\n\n    handleColumnsChange(columns) {\n      this.tableColumns = columns;\n    },\n\n    async fetchTestcases(page = 1) {\n      this.loading = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: page,\n          page_size: 100\n        };\n\n        // 添加搜索参数\n        if (this.searchForm.name) params.name = this.searchForm.name;\n        if (this.searchForm.level) params.level = this.searchForm.level;\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\n        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;\n\n        const response = await axios.get('/api/testcase/', { params });\n        this.testcases = response.data.data.map(item => ({...item, editable: false}));\n        this.cacheData = this.testcases.map(item => ({ ...item }));\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Error fetching testcases:', error);\n        this.$message.error('Failed to load test cases');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    handleChange(value, id, column) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        target[column] = value;\n      }\n    },\n    edit(id) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        target.editable = true;\n      }\n    },\n    save(id) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        // 检查值是否发生变化\n        const originalData = this.cacheData.find(item => item.id === id);\n        if (originalData && originalData.Testcase_Feature !== target.Testcase_Feature) {\n          // 调用后端API保存更改\n          axios.put(`/api/testcase/${target.Testcase_Number}`, { Testcase_Feature: target.Testcase_Feature })\n              .then(() => {\n                this.$message.success('用例特性更新成功');\n                target.editable = false;\n                // 更新缓存数据\n                Object.assign(originalData, target);\n              })\n              .catch(error => {\n                this.$message.error('用例特性更新失败: ' + error.message);\n                // 恢复原始值\n                Object.assign(target, originalData);\n                target.editable = false;\n              });\n        } else {\n          target.editable = false;\n        }\n      }\n    },\n    cancel(id) {\n      const target = this.testcases.find(item => item.id === id);\n      const originalData = this.cacheData.find(item => item.id === id);\n      if (target && originalData) {\n        Object.assign(target, originalData);\n        target.editable = false;\n      }\n    },\n\n    // 搜索处理函数\n    handleSearch() {\n      this.currentPage = 1; // 重置到第一页\n      this.fetchTestcases(1);\n    },\n\n    // 重置搜索表单\n    resetSearch() {\n      this.searchForm = {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      };\n      this.currentPage = 1;\n      this.fetchTestcases(1);\n    },\n    formatDate(date) {\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\n    },\n    getResultColor(result) {\n      const colors = {\n        'PASS': 'success',\n        'FAIL': 'error',\n        'BLOCKED': 'warning',\n        'NOT RUN': 'default',\n      };\n      return colors[result] || 'default';\n    },\n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange',\n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n      };\n      return colors[level] || 'default';\n    },\n    viewDetails(record) {\n      this.selectedTestcase = record;\n      this.detailsVisible = true;\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchTestcases(page);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n\n// .criclebox {\n//   background: #fff;\n//   border-radius: 12px;\n// }\n\n.search-form {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fafafa;\n  border-radius: 8px;\n\n  .ant-form-item {\n    margin-bottom: 12px;\n  }\n\n  .search-result-count {\n    margin-top: 1px;\n    padding: 0 1px;\n  }\n}\n\n\n</style>\n"], "mappings": ";;;AAuIA,OAAAA,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,QAAA;AACA,OAAAC,aAAA;AACA,OAAAC,mBAAA;AACA,OAAAC,cAAA;AAEA;EACAC,UAAA;IACAH,aAAA;IACAC,mBAAA;IACAC;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;QACAC,gBAAA;MACA;MACAC,YAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,GAAAzB,QAAA;EACA;EACA0B,OAAA;IACAH,kBAAA;MAAA,MAAAI,CAAA,QAAAC,cAAA;MACA,KAAAR,YAAA,IACA;QACAS,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,EAAA,EAAAC,KAAA;UACA,aAAA3B,WAAA,cAAA2B,KAAA;QACA;MACA,GACA;QACAR,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;QACAL,YAAA,EAAAA,CAAAM,IAAA,EAAAC,MAAA;UACA,OAAAd,CAAA;YAAA;cAAA,SAAAe,CAAA,UAAAC,WAAA,CAAAF,MAAA;YAAA;YAAA;UAAA,IAAAD,IAAA;QACA;MACA,GACA;QACAX,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACA;MACA,GACA;QACAH,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAY,KAAA;UAAAV,YAAA;QAAA;MACA,GACA;QACAL,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,GACA;QACAV,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,GACA;QACAV,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAO,QAAA;MACA,GACA;QACAV,KAAA,OAAAS,EAAA;QACAR,SAAA;QACAC,GAAA;QACAC,KAAA;QACAa,WAAA;UAAAX,YAAA;QAAA;MACA,EACA;IACA;IAEAY,oBAAAC,OAAA;MACA,KAAA3B,YAAA,GAAA2B,OAAA;IACA;IAEA,MAAAvB,eAAAwB,IAAA;MACA,KAAAzC,OAAA;MACA;QACA;QACA,MAAA0C,MAAA;UACAD,IAAA,EAAAA,IAAA;UACAE,SAAA;QACA;;QAEA;QACA,SAAArC,UAAA,CAAAR,IAAA,EAAA4C,MAAA,CAAA5C,IAAA,QAAAQ,UAAA,CAAAR,IAAA;QACA,SAAAQ,UAAA,CAAAC,KAAA,EAAAmC,MAAA,CAAAnC,KAAA,QAAAD,UAAA,CAAAC,KAAA;QACA,SAAAD,UAAA,CAAAG,iBAAA,EAAAiC,MAAA,CAAAjC,iBAAA,QAAAH,UAAA,CAAAG,iBAAA;QACA,SAAAH,UAAA,CAAAI,UAAA,EAAAgC,MAAA,CAAAhC,UAAA,QAAAJ,UAAA,CAAAI,UAAA;QACA,SAAAJ,UAAA,CAAAK,eAAA,EAAA+B,MAAA,CAAA/B,eAAA,QAAAL,UAAA,CAAAK,eAAA;QACA,SAAAL,UAAA,CAAAM,gBAAA,EAAA8B,MAAA,CAAA9B,gBAAA,QAAAN,UAAA,CAAAM,gBAAA;QAEA,MAAAgC,QAAA,SAAArD,KAAA,CAAAsD,GAAA;UAAAH;QAAA;QACA,KAAAzC,SAAA,GAAA2C,QAAA,CAAA7C,IAAA,CAAAA,IAAA,CAAA+C,GAAA,CAAAC,IAAA;UAAA,GAAAA,IAAA;UAAAC,QAAA;QAAA;QACA,KAAAlC,SAAA,QAAAb,SAAA,CAAA6C,GAAA,CAAAC,IAAA;UAAA,GAAAA;QAAA;QACA,KAAA7C,KAAA,GAAA0C,QAAA,CAAA7C,IAAA,CAAAG,KAAA;MACA,SAAA+C,KAAA;QACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAjD,OAAA;MACA;IACA;IAEAoD,aAAAC,KAAA,EAAAC,EAAA,EAAAC,MAAA;MACA,MAAAC,MAAA,QAAAvD,SAAA,CAAAwD,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;MACA,IAAAE,MAAA;QACAA,MAAA,CAAAD,MAAA,IAAAF,KAAA;MACA;IACA;IACAK,KAAAJ,EAAA;MACA,MAAAE,MAAA,QAAAvD,SAAA,CAAAwD,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;MACA,IAAAE,MAAA;QACAA,MAAA,CAAAR,QAAA;MACA;IACA;IACAW,KAAAL,EAAA;MACA,MAAAE,MAAA,QAAAvD,SAAA,CAAAwD,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;MACA,IAAAE,MAAA;QACA;QACA,MAAAI,YAAA,QAAA9C,SAAA,CAAA2C,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;QACA,IAAAM,YAAA,IAAAA,YAAA,CAAAC,gBAAA,KAAAL,MAAA,CAAAK,gBAAA;UACA;UACAtE,KAAA,CAAAuE,GAAA,kBAAAN,MAAA,CAAAO,eAAA;YAAAF,gBAAA,EAAAL,MAAA,CAAAK;UAAA,GACAG,IAAA;YACA,KAAAb,QAAA,CAAAc,OAAA;YACAT,MAAA,CAAAR,QAAA;YACA;YACAkB,MAAA,CAAAC,MAAA,CAAAP,YAAA,EAAAJ,MAAA;UACA,GACAY,KAAA,CAAAnB,KAAA;YACA,KAAAE,QAAA,CAAAF,KAAA,gBAAAA,KAAA,CAAAoB,OAAA;YACA;YACAH,MAAA,CAAAC,MAAA,CAAAX,MAAA,EAAAI,YAAA;YACAJ,MAAA,CAAAR,QAAA;UACA;QACA;UACAQ,MAAA,CAAAR,QAAA;QACA;MACA;IACA;IACAsB,OAAAhB,EAAA;MACA,MAAAE,MAAA,QAAAvD,SAAA,CAAAwD,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;MACA,MAAAM,YAAA,QAAA9C,SAAA,CAAA2C,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAAO,EAAA,KAAAA,EAAA;MACA,IAAAE,MAAA,IAAAI,YAAA;QACAM,MAAA,CAAAC,MAAA,CAAAX,MAAA,EAAAI,YAAA;QACAJ,MAAA,CAAAR,QAAA;MACA;IACA;IAEA;IACAuB,aAAA;MACA,KAAApE,WAAA;MACA,KAAAc,cAAA;IACA;IAEA;IACAuD,YAAA;MACA,KAAAlE,UAAA;QACAR,IAAA;QACAS,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;QACAC,gBAAA;MACA;MACA,KAAAT,WAAA;MACA,KAAAc,cAAA;IACA;IACAwD,WAAAC,IAAA;MACA,OAAAA,IAAA,GAAAlF,MAAA,CAAAkF,IAAA,EAAAC,MAAA;IACA;IACAC,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IACAE,cAAAxE,KAAA;MACA,MAAAuE,MAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAvE,KAAA;IACA;IACA6B,YAAAF,MAAA;MACA,KAAA7B,gBAAA,GAAA6B,MAAA;MACA,KAAA9B,cAAA;IACA;IACA4E,iBAAAvC,IAAA;MACA,KAAAtC,WAAA,GAAAsC,IAAA;MACA,KAAAxB,cAAA,CAAAwB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}