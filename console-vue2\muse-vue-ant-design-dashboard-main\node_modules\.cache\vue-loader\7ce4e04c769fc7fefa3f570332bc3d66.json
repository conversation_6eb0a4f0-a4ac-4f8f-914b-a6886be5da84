{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=style&index=0&id=6981ce2c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753166063730}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCi5jcmljbGVib3ggew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQouc2VhcmNoLWZvcm0gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQoNCiAgLmFudC1mb3JtLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogIH0NCg0KICAuc2VhcmNoLXJlc3VsdC1jb3VudCB7DQogICAgbWFyZ2luLXRvcDogMXB4Ow0KICAgIHBhZGRpbmc6IDAgMXB4Ow0KICB9DQp9DQoNCi8vIOWIl+aLluWKqOagt+W8jw0KOmRlZXAoLmFudC10YWJsZS10aGVhZCA+IHRyID4gdGgpIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICY6aG92ZXIgew0KICAgIC5yZXNpemUtaGFuZGxlIHsNCiAgICAgIG9wYWNpdHk6IDE7DQogICAgfQ0KICB9DQp9DQoNCjpkZWVwKC5yZXNpemUtaGFuZGxlKSB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgcmlnaHQ6IC01cHg7DQogIHRvcDogMDsNCiAgYm90dG9tOiAwOw0KICB3aWR0aDogMTBweDsNCiAgY3Vyc29yOiBjb2wtcmVzaXplOw0KICB6LWluZGV4OiAxOw0KICBvcGFjaXR5OiAwOw0KICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuMnM7DQoNCiAgJjpob3ZlciB7DQogICAgb3BhY2l0eTogMTsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsNCiAgfQ0KfQ0KDQoNCg=="}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";;AAoSA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item :label=\"$t('caseColumn.name')\">\r\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.level')\">\r\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\r\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\r\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              {{ $t('testcase.searchButton') }}\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              {{ $t('testcase.resetButton') }}\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <ResizableTable\r\n        :columns=\"tableColumns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n        @columns-change=\"handleColumnsChange\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </ResizableTable>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\nimport ResizableTable from '../common/ResizableTable.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal,\r\n    ResizableTable\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      tableColumns: []\r\n    };\r\n  },\r\n  created() {\r\n    this.initializeColumns();\r\n    this.fetchTestcases();\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    columns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (_, __, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('caseColumn.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n      ];\r\n    },\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n// 列拖动样式\r\n:deep(.ant-table-thead > tr > th) {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    .resize-handle {\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.resize-handle) {\r\n  position: absolute;\r\n  right: -5px;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 10px;\r\n  cursor: col-resize;\r\n  z-index: 1;\r\n  opacity: 0;\r\n  transition: opacity 0.2s;\r\n\r\n  &:hover {\r\n    opacity: 1;\r\n    background-color: rgba(24, 144, 255, 0.2);\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}