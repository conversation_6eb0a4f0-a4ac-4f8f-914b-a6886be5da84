{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=style&index=0&id=6981ce2c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753180839980}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgovLyAuY3JpY2xlYm94IHsKLy8gICBiYWNrZ3JvdW5kOiAjZmZmOwovLyAgIGJvcmRlci1yYWRpdXM6IDEycHg7Ci8vIH0KCi5zZWFyY2gtZm9ybSB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBwYWRkaW5nOiAxNnB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7CiAgYm9yZGVyLXJhZGl1czogOHB4OwoKICAuYW50LWZvcm0taXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogIH0KCiAgLnNlYXJjaC1yZXN1bHQtY291bnQgewogICAgbWFyZ2luLXRvcDogMXB4OwogICAgcGFkZGluZzogMCAxcHg7CiAgfQp9CgoK"}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";;AA0XA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\n  <div class=\"layout-content\">\n    <a-card :bordered=\"false\">\n      <template #title>\n        <div class=\"card-header-wrapper\">\n          <div class=\"header-wrapper\">\n            <div class=\"logo-wrapper\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\n              </svg>\n            </div>\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\n          </div>\n          <div>\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\n          </div>\n        </div>\n      </template>\n\n      <!-- 搜索表单 -->\n      <div class=\"search-form\">\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\n          <a-form-item :label=\"$t('caseColumn.name')\">\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.level')\">\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.feature')\">\n            <a-input v-model=\"searchForm.testcase_feature\" :placeholder=\"$t('caseColumn.feature')\" allowClear />\n          </a-form-item>\n          <a-form-item>\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              {{ $t('testcase.searchButton') }}\n            </a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\n              <a-icon type=\"reload\" />\n              {{ $t('testcase.resetButton') }}\n            </a-button>\n          </a-form-item>\n        </a-form>\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <ResizableTable\n        :columns=\"tableColumns\"\n        :data-source=\"testcases\"\n        :loading=\"loading\"\n        :pagination=\"{\n          total: total,\n          pageSize: 100,\n          current: currentPage,\n          showSizeChanger: false,\n          showQuickJumper: true,\n          onChange: handlePageChange\n        }\"\n        :scroll=\"{ x: 1500 }\"\n        @columns-change=\"handleColumnsChange\"\n      >\n        <!-- Custom column renders -->\n        <template #Testcase_LastResult=\"{ text }\">\n          <a-tag :color=\"getResultColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Level=\"{ text }\">\n          <a-tag :color=\"getLevelColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Feature=\"{ text, record }\">\n          <div>\n            <a-input\n                v-if=\"record.editable\"\n                style=\"margin: -5px 0\"\n                :value=\"text\"\n                @change=\"e => handleChange(e.target.value, record.id, 'Testcase_Feature')\"\n                @pressEnter=\"() => save(record.id)\"\n            />\n            <div\n                v-else\n                class=\"editable-cell-value-wrap\"\n                style=\"padding-right: 24px\"\n                @click=\"() => edit(record.id)\"\n            >\n              {{ text }}\n            </div>\n          </div>\n        </template>\n\n        <template #lastModified=\"{ text }\">\n          {{ formatDate(text) }}\n        </template>\n\n        <template #action=\"{ record }\">\n          <a-space>\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\n              View Details\n            </a-button>\n          </a-space>\n        </template>\n      </ResizableTable>\n\n      <!-- Details Modal -->\n      <TestCaseDetailModal\n        :visible=\"detailsVisible\"\n        :testcase=\"selectedTestcase\"\n        @close=\"detailsVisible = false\"\n      />\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\nimport moment from 'moment';\nimport {mapState} from \"vuex\";\nimport RefreshButton from '../Widgets/RefreshButton.vue';\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\nimport ResizableTable from '../common/ResizableTable.vue';\n\nexport default {\n  components: {\n    RefreshButton,\n    TestCaseDetailModal,\n    ResizableTable\n  },\n  name: 'TestCases',\n  data() {\n    return {\n      loading: false,\n      testcases: [],\n      total: 0,\n      currentPage: 1,\n      detailsVisible: false,\n      selectedTestcase: null,\n      searchForm: {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      },\n      tableColumns: [],\n      cacheData: [], // 用于存储原始数据，以便在取消编辑时恢复\n    };\n  },\n  created() {\n    this.initializeColumns();\n    this.fetchTestcases();\n  },\n  computed: {\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\n  },\n  methods: {\n    initializeColumns() {\n      this.tableColumns = [\n        {\n          title: '#',\n          dataIndex: 'index',\n          key: 'index',\n          width: 100,\n          align: 'center',\n          customRender: (_, __, index) => {\n            return ((this.currentPage - 1) * 100) + index + 1;\n          }\n        },\n        {\n          title: this.$t('caseColumn.number'),\n          dataIndex: 'Testcase_Number',\n          key: 'Testcase_Number',\n          width: 130,\n          ellipsis: true,\n          customRender: (text, record) => {\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\n          }\n        },\n        {\n          title: this.$t('caseColumn.name'),\n          dataIndex: 'Testcase_Name',\n          key: 'Testcase_Name',\n          width: 200,\n          // ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.level'),\n          dataIndex: 'Testcase_Level',\n          key: 'Testcase_Level',\n          width: 100,\n          slots: { customRender: 'Testcase_Level' },\n        },\n        {\n          title: this.$t('caseColumn.prepareCondition'),\n          dataIndex: 'Testcase_PrepareCondition',\n          key: 'Testcase_PrepareCondition',\n          width: 250,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.testSteps'),\n          dataIndex: 'Testcase_TestSteps',\n          key: 'Testcase_TestSteps',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.expectedResult'),\n          dataIndex: 'Testcase_ExpectedResult',\n          key: 'Testcase_ExpectedResult',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.feature'),\n          dataIndex: 'Testcase_Feature',\n          key: 'Testcase_Feature',\n          width: 200,\n          scopedSlots: { customRender: 'Testcase_Feature' },\n        },        \n      ];\n    },\n\n    handleColumnsChange(columns) {\n      this.tableColumns = columns;\n    },\n\n    async fetchTestcases(page = 1) {\n      this.loading = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: page,\n          page_size: 100\n        };\n\n        // 添加搜索参数\n        if (this.searchForm.name) params.name = this.searchForm.name;\n        if (this.searchForm.level) params.level = this.searchForm.level;\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\n        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;\n\n        const response = await axios.get('/api/testcase/', { params });\n        this.testcases = response.data.data.map(item => ({...item, editable: false}));\n        this.cacheData = this.testcases.map(item => ({ ...item }));\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Error fetching testcases:', error);\n        this.$message.error('Failed to load test cases');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    handleChange(value, id, column) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        target[column] = value;\n      }\n    },\n    edit(id) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        target.editable = true;\n      }\n    },\n    save(id) {\n      const target = this.testcases.find(item => item.id === id);\n      if (target) {\n        // 检查值是否发生变化\n        const originalData = this.cacheData.find(item => item.id === id);\n        if (originalData && originalData.Testcase_Feature !== target.Testcase_Feature) {\n          // 调用后端API保存更改\n          axios.put(`/api/testcase/${target.Testcase_Number}`, { Testcase_Feature: target.Testcase_Feature })\n              .then(() => {\n                this.$message.success('用例特性更新成功');\n                target.editable = false;\n                // 更新缓存数据\n                Object.assign(originalData, target);\n              })\n              .catch(error => {\n                this.$message.error('用例特性更新失败: ' + error.message);\n                // 恢复原始值\n                Object.assign(target, originalData);\n                target.editable = false;\n              });\n        } else {\n          target.editable = false;\n        }\n      }\n    },\n    cancel(id) {\n      const target = this.testcases.find(item => item.id === id);\n      const originalData = this.cacheData.find(item => item.id === id);\n      if (target && originalData) {\n        Object.assign(target, originalData);\n        target.editable = false;\n      }\n    },\n\n    // 搜索处理函数\n    handleSearch() {\n      this.currentPage = 1; // 重置到第一页\n      this.fetchTestcases(1);\n    },\n\n    // 重置搜索表单\n    resetSearch() {\n      this.searchForm = {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      };\n      this.currentPage = 1;\n      this.fetchTestcases(1);\n    },\n    formatDate(date) {\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\n    },\n    getResultColor(result) {\n      const colors = {\n        'PASS': 'success',\n        'FAIL': 'error',\n        'BLOCKED': 'warning',\n        'NOT RUN': 'default',\n      };\n      return colors[result] || 'default';\n    },\n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange',\n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n      };\n      return colors[level] || 'default';\n    },\n    viewDetails(record) {\n      this.selectedTestcase = record;\n      this.detailsVisible = true;\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchTestcases(page);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n\n// .criclebox {\n//   background: #fff;\n//   border-radius: 12px;\n// }\n\n.search-form {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fafafa;\n  border-radius: 8px;\n\n  .ant-form-item {\n    margin-bottom: 12px;\n  }\n\n  .search-result-count {\n    margin-top: 1px;\n    padding: 0 1px;\n  }\n}\n\n\n</style>\n"]}]}