{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=style&index=0&id=6981ce2c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1753239386765}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZWFyY2gtZm9ybSB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBwYWRkaW5nOiAxNnB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7CiAgYm9yZGVyLXJhZGl1czogOHB4OwoKICAuYW50LWZvcm0taXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogIH0KCiAgLnNlYXJjaC1yZXN1bHQtY291bnQgewogICAgbWFyZ2luLXRvcDogMXB4OwogICAgcGFkZGluZzogMCAxcHg7CiAgfQp9CgovLyAuZmVhdHVyZS1jb250ZW50IHsKLy8gICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1OwovLyAgIHBhZGRpbmc6IDZweDsKLy8gICBib3JkZXItcmFkaXVzOiAxcHg7Ci8vICAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwovLyAgIHdvcmQtd3JhcDogYnJlYWstd29yZDsKLy8gICBtYXgtaGVpZ2h0OiAxMDBweDsKLy8gICBvdmVyZmxvdy15OiBhdXRvOwovLyAgIGxpbmUtaGVpZ2h0OiAxLjU7Ci8vIH0KCgo="}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";AA+ZA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\n  <div class=\"layout-content\">\n    <a-card :bordered=\"false\">\n      <template #title>\n        <div class=\"card-header-wrapper\">\n          <div class=\"header-wrapper\">\n            <div class=\"logo-wrapper\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\n              </svg>\n            </div>\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\n          </div>\n          <div>\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\n          </div>\n        </div>\n      </template>\n\n      <!-- 搜索表单 -->\n      <div class=\"search-form\">\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\n          <a-form-item :label=\"$t('caseColumn.name')\">\n            <a-input v-model=\"searchForm.name\" :placeholder=\"$t('caseColumn.name')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.level')\">\n            <a-select v-model=\"searchForm.level\" :placeholder=\"$t('caseColumn.level')\" style=\"width: 120px\" allowClear>\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.prepareCondition')\">\n            <a-input v-model=\"searchForm.prepare_condition\" :placeholder=\"$t('caseColumn.prepareCondition')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.testSteps')\">\n            <a-input v-model=\"searchForm.test_steps\" :placeholder=\"$t('caseColumn.testSteps')\" allowClear />\n          </a-form-item>\n          <a-form-item :label=\"$t('caseColumn.expectedResult')\">\n            <a-input v-model=\"searchForm.expected_result\" :placeholder=\"$t('caseColumn.expectedResult')\" allowClear />\n          </a-form-item>\n          <a-form-item>\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              {{ $t('testcase.searchButton') }}\n            </a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\n              <a-icon type=\"reload\" />\n              {{ $t('testcase.resetButton') }}\n            </a-button>\n          </a-form-item>\n        </a-form>\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <ResizableTable\n        :columns=\"tableColumns\"\n        :data-source=\"testcases\"\n        :loading=\"loading\"\n        :pagination=\"{\n          total: total,\n          pageSize: 100,\n          current: currentPage,\n          showSizeChanger: false,\n          showQuickJumper: true,\n          onChange: handlePageChange\n        }\"\n        :scroll=\"{ x: 1500 }\"\n        @columns-change=\"handleColumnsChange\"\n      >\n        <!-- Custom column renders -->\n        <template #Testcase_LastResult=\"{ text }\">\n          <a-tag :color=\"getResultColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #Testcase_Level=\"{ text }\">\n          <a-tag :color=\"getLevelColor(text)\">\n            {{ text || 'N/A' }}\n          </a-tag>\n        </template>\n\n        <template #lastModified=\"{ text }\">\n          {{ formatDate(text) }}\n        </template>\n\n        <template #action=\"{ record }\">\n          <a-space>\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\n              View Details\n            </a-button>\n          </a-space>\n        </template>\n      </ResizableTable>\n\n      <!-- Details Modal -->\n      <TestCaseDetailModal\n        :visible=\"detailsVisible\"\n        :testcase=\"selectedTestcase\"\n        @close=\"detailsVisible = false\"\n      />\n\n      <!-- Feature Modal -->\n      <a-modal\n        :visible=\"featureModalVisible\"\n        :title=\"featureModalMode === 'view' ? '查看用例特性' : '编辑用例特性'\"\n        @ok=\"handleModalOk\"\n        @cancel=\"closeFeatureModal\"\n        :footer=\"featureModalMode === 'view' ? null : undefined\"\n        :confirmLoading=\"featureSaving\"\n        width=\"700px\"\n      >\n        <div v-if=\"featureModalMode === 'view'\" class=\"feature-content\">\n          {{ selectedFeature || '暂无特性内容' }}\n        </div>\n        <a-textarea\n          v-else\n          v-model=\"editingFeature\"\n          :rows=\"8\"\n          placeholder=\"请输入用例特性内容...\"\n        />\n      </a-modal>\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\nimport moment from 'moment';\nimport {mapState} from \"vuex\";\nimport RefreshButton from '../Widgets/RefreshButton.vue';\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\nimport ResizableTable from '../common/ResizableTable.vue';\n\nexport default {\n  components: {\n    RefreshButton,\n    TestCaseDetailModal,\n    ResizableTable\n  },\n  name: 'TestCases',\n  data() {\n    return {\n      loading: false,\n      testcases: [],\n      total: 0,\n      currentPage: 1,\n      detailsVisible: false,\n      selectedTestcase: null,\n      searchForm: {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      },\n      tableColumns: [],\n      // 用例特性相关状态\n      featureModalVisible: false,\n      featureModalMode: 'view', // 'view' 或 'edit'\n      selectedFeature: '',\n      editingFeature: '',\n      currentEditingRecord: null,\n      featureSaving: false,\n    };\n  },\n  created() {\n    this.initializeColumns();\n    this.fetchTestcases();\n  },\n  computed: {\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor'])\n  },\n  methods: {\n    initializeColumns() {\n      this.tableColumns = [\n        {\n          title: '#',\n          dataIndex: 'index',\n          key: 'index',\n          width: 100,\n          align: 'center',\n          customRender: (_, __, index) => {\n            return ((this.currentPage - 1) * 100) + index + 1;\n          }\n        },\n        {\n          title: this.$t('caseColumn.number'),\n          dataIndex: 'Testcase_Number',\n          key: 'Testcase_Number',\n          width: 130,\n          ellipsis: true,\n          customRender: (text, record) => {\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\n          }\n        },\n        {\n          title: this.$t('caseColumn.name'),\n          dataIndex: 'Testcase_Name',\n          key: 'Testcase_Name',\n          width: 200,\n          // ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.level'),\n          dataIndex: 'Testcase_Level',\n          key: 'Testcase_Level',\n          width: 100,\n          slots: { customRender: 'Testcase_Level' },\n        },\n        {\n          title: this.$t('caseColumn.prepareCondition'),\n          dataIndex: 'Testcase_PrepareCondition',\n          key: 'Testcase_PrepareCondition',\n          width: 250,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.testSteps'),\n          dataIndex: 'Testcase_TestSteps',\n          key: 'Testcase_TestSteps',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.expectedResult'),\n          dataIndex: 'Testcase_ExpectedResult',\n          key: 'Testcase_ExpectedResult',\n          width: 400,\n          ellipsis: true,\n        },\n        {\n          title: this.$t('caseColumn.feature'),\n          dataIndex: 'Testcase_Feature',\n          key: 'Testcase_Feature',\n          width: 200,\n          customRender: (text, record) => {\n            const featureText = text || ''; // 确保 text 始终是字符串\n            return (\n              <div style=\"display: flex; gap: 1px;\">\n                <a-button type=\"link\" onClick={() => this.viewFeature(record)}>\n                  查看\n                </a-button>\n                <a-button type=\"link\" onClick={() => this.editFeature(record)}>\n                  编辑\n                </a-button>\n                <a-button type=\"link\" onClick={() => this.clearFeature(record)} disabled={!featureText}>\n                  清空\n                </a-button>\n              </div>\n            );\n          },\n        },        \n      ];\n    },\n\n    handleColumnsChange(columns) {\n      this.tableColumns = columns;\n    },\n\n    async fetchTestcases(page = 1) {\n      this.loading = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: page,\n          page_size: 100\n        };\n\n        // 添加搜索参数\n        if (this.searchForm.name) params.name = this.searchForm.name;\n        if (this.searchForm.level) params.level = this.searchForm.level;\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\n        if (this.searchForm.testcase_feature) params.testcase_feature = this.searchForm.testcase_feature;\n\n        const response = await axios.get('/api/testcase/', { params });\n        this.testcases = response.data.data.map(item => ({\n          ...item,\n          Testcase_Feature: item.Testcase_Feature || ''\n        }));\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Error fetching testcases:', error);\n        this.$message.error('Failed to load test cases');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 用例特性操作方法\n    viewFeature(record) {\n      this.selectedFeature = record.Testcase_Feature || '';\n      this.featureModalMode = 'view';\n      this.featureModalVisible = true;\n    },\n\n    editFeature(record) {\n      this.currentEditingRecord = record;\n      this.editingFeature = record.Testcase_Feature || '';\n      this.featureModalMode = 'edit';\n      this.featureModalVisible = true;\n    },\n\n    handleModalOk() {\n      if (this.featureModalMode === 'edit') {\n        this.saveFeature();\n      }\n    },\n\n    async saveFeature() {\n      this.featureSaving = true;\n      try {\n        console.log('Saving feature:', this.editingFeature);\n        console.log('Testcase Number:', this.currentEditingRecord.Testcase_Number);\n\n        await axios.put(`/api/testcase/${this.currentEditingRecord.Testcase_Number}`, {\n          Testcase_Feature: this.editingFeature\n        });\n        this.currentEditingRecord.Testcase_Feature = this.editingFeature;\n        this.$message.success('保存成功');\n        this.closeFeatureModal();\n      } catch (error) {\n        console.error('Save error:', error);\n        this.$message.error('保存失败');\n      } finally {\n        this.featureSaving = false;\n      }\n    },\n\n    closeFeatureModal() {\n      this.featureModalVisible = false;\n      this.currentEditingRecord = null;\n      this.editingFeature = '';\n      this.selectedFeature = '';\n    },\n\n    clearFeature(record) {\n      this.$confirm({\n        title: '确认清空',\n        content: '确定要清空该用例的特性内容吗？',\n        onOk: async () => {\n          try {\n            await axios.put(`/api/testcase/${record.Testcase_Number}`, { Testcase_Feature: '' });\n            record.Testcase_Feature = '';\n            this.$message.success('已清空');\n          } catch (error) {\n            this.$message.error('清空失败');\n          }\n        }\n      });\n    },\n\n    // 搜索处理函数\n    handleSearch() {\n      this.currentPage = 1; // 重置到第一页\n      this.fetchTestcases(1);\n    },\n\n    // 重置搜索表单\n    resetSearch() {\n      this.searchForm = {\n        name: '',\n        level: undefined,\n        prepare_condition: '',\n        test_steps: '',\n        expected_result: '',\n        testcase_feature: '',\n      };\n      this.currentPage = 1;\n      this.fetchTestcases(1);\n    },\n    formatDate(date) {\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\n    },\n    getResultColor(result) {\n      const colors = {\n        'PASS': 'success',\n        'FAIL': 'error',\n        'BLOCKED': 'warning',\n        'NOT RUN': 'default',\n      };\n      return colors[result] || 'default';\n    },\n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange',\n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n      };\n      return colors[level] || 'default';\n    },\n    viewDetails(record) {\n      this.selectedTestcase = record;\n      this.detailsVisible = true;\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchTestcases(page);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.search-form {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fafafa;\n  border-radius: 8px;\n\n  .ant-form-item {\n    margin-bottom: 12px;\n  }\n\n  .search-result-count {\n    margin-top: 1px;\n    padding: 0 1px;\n  }\n}\n\n// .feature-content {\n//   background-color: #f5f5f5;\n//   padding: 6px;\n//   border-radius: 1px;\n//   white-space: pre-wrap;\n//   word-wrap: break-word;\n//   max-height: 100px;\n//   overflow-y: auto;\n//   line-height: 1.5;\n// }\n\n\n</style>\n"]}]}