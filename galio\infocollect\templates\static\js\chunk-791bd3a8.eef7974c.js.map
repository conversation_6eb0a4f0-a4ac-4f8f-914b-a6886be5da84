{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/components/Cards/FileTransferProgress.vue", "webpack:///src/components/Cards/FileTransferProgress.vue", "webpack:///./src/components/Cards/FileTransferProgress.vue?d051", "webpack:///./src/components/Cards/FileTransferProgress.vue?a7e0", "webpack:///./src/mixins/NotificationMixin.js", "webpack:///./src/components/Cards/FileTransferProgress.vue?4565", "webpack:///./src/components/common/ProxySelector.vue", "webpack:///src/components/common/ProxySelector.vue", "webpack:///./src/components/common/ProxySelector.vue?a770", "webpack:///./src/components/common/ProxySelector.vue?1de0", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.every.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/core-js/internals/array-reduce.js", "webpack:///./src/views/FileDownload.vue", "webpack:///src/views/FileDownload.vue", "webpack:///./src/views/FileDownload.vue?eef0", "webpack:///./src/views/FileDownload.vue?dbcb", "webpack:///./src/views/FileDownload.vue?7023"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "render", "_vm", "_c", "_self", "staticClass", "staticStyle", "attrs", "title", "overallProgress", "slot", "_v", "_s", "_e", "progressBarStatus", "progressData", "progressColumns", "scopedSlots", "_u", "key", "fn", "text", "record", "error_detail", "time", "type", "message", "staticRenderFns", "name", "props", "String", "required", "taskType", "activeTask", "Object", "default", "formatBytes", "Function", "computed", "h", "$createElement", "columns", "$t", "dataIndex", "width", "ellipsis", "customRender", "colorMap", "color", "status", "bytes_transferred", "file_size", "nodes", "keys", "map", "ip", "node", "host_name", "progress", "speed", "values", "totalProgress", "sum", "Math", "round", "some", "every", "includes", "component", "methods", "addTaskCompletionNotification", "taskId", "projectId", "titles", "templates", "statusMapping", "notificationSent<PERSON>ey", "localStorage", "getItem", "defaultStatusMapping", "success", "failure", "finalStatusMapping", "successNodes", "filter", "failedNodes", "hasFailures", "notificationTitle", "notificationMessage", "error", "getDefaultErrorTitle", "getDefaultSuccessTitle", "addNotification", "setItem", "clearTaskNotificationMark", "removeItem", "isDetecting", "disabled", "on", "fetchReachableIps", "reachableIps", "model", "value", "selectedIpValue", "callback", "$$v", "expression", "_l", "Boolean", "data", "watch", "newValue", "$emit", "response", "axios", "get", "reachable_ips", "console", "$notify", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped", "reducer", "noInitial", "accumulator", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "i", "right", "class", "sidebarColor", "downloading", "handleDownload", "handleProxyChange", "selectedProxyIp", "nodesWithPath", "pageSize", "total", "showSizeChanger", "rowSelection", "e", "updateRemotePath", "remotePath", "$set", "activeDownloadTask", "mixins", "NotificationMixin", "components", "ProxySelector", "FileTransferProgress", "selectedNodes", "nodeRemotePaths", "pollInterval", "mapState", "set", "$store", "dispatch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "created", "checkDatabaseStatus", "taskInfo", "currentProject", "projectFile", "JSON", "parse", "checkActiveDownloadTask", "mapActions", "$router", "push", "log", "validatePath", "path", "startsWith", "$message", "validatePaths", "selectedPaths", "item", "bytes", "k", "sizes", "floor", "parseFloat", "pow", "toFixed", "warning", "previousTaskInfo", "post", "encodeURIComponent", "proxyIp", "task_id", "stringify", "startPolling", "_error$response", "clearInterval", "pollStatus", "Error", "allCompleted", "_error$response2", "setInterval", "taskCompleted", "<PERSON><PERSON><PERSON><PERSON>", "handler", "newProject", "oldProject", "immediate"], "mappings": "kHACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,oCChB7F,IAAIC,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,YAAY,eAAeC,YAAY,CAAC,aAAa,QAAQC,MAAM,CAAC,MAAQL,EAAIM,QAAQ,MAA0BR,IAAxBE,EAAIO,gBAA+BN,EAAG,WAAW,CAACO,KAAK,SAAS,CAACP,EAAG,OAAO,CAACD,EAAIS,GAAG,qBAAqBT,EAAIU,GAAGV,EAAIO,iBAAiB,SAASP,EAAIW,KAAKV,EAAG,aAAa,CAACG,YAAY,CAAC,gBAAgB,QAAQC,MAAM,CAAC,QAAUL,EAAIO,gBAAgB,OAASP,EAAIY,qBAAqBX,EAAG,UAAU,CAACI,MAAM,CAAC,WAAaL,EAAIa,aAAa,QAAUb,EAAIc,gBAAgB,OAAS,KAAK,YAAa,GAAOC,YAAYf,EAAIgB,GAAG,CAAC,CAACC,IAAI,cAAcC,GAAG,SAASC,EAAMC,GAAQ,MAAO,CAAEA,GAAUA,EAAOC,aAAcpB,EAAG,YAAY,CAACI,MAAM,CAAC,UAAY,YAAY,CAACJ,EAAG,WAAW,CAACO,KAAK,WAAW,CAACP,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGU,EAAOC,aAAaC,SAASrB,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGU,EAAOC,aAAaE,SAAStB,EAAG,IAAI,CAACD,EAAIS,GAAG,YAAYT,EAAIU,GAAGU,EAAOC,aAAaG,cAAcvB,EAAG,SAAS,CAACG,YAAY,CAAC,MAAQ,WAAWC,MAAM,CAAC,KAAO,kBAAkB,GAAGL,EAAIW,aAAa,IAElgCc,EAAkB,GCmCP,G,4DAAA,CACfC,KAAA,kBACAC,MAAA,CACArB,MAAA,CACAiB,KAAAK,OACAC,UAAA,GAEAC,SAAA,CACAP,KAAAK,OACAC,UAAA,GAEAE,WAAA,CACAR,KAAAS,OACAC,QAAA,MAEAC,YAAA,CACAX,KAAAY,SACAN,UAAA,IAGAO,SAAA,CACAtB,kBAAA,MAAAuB,EAAA,KAAAC,eACAC,EAAA,CACA,CACAjC,MAAA,KAAAkC,GAAA,gCACAC,UAAA,KACAxB,IAAA,KACAyB,MAAA,SAEA,CACApC,MAAA,KAAAkC,GAAA,+BACAC,UAAA,YACAxB,IAAA,YACAyB,MAAA,QACAC,UAAA,GAEA,CACArC,MAAA,KAAAkC,GAAA,uBACAC,UAAA,SACAxB,IAAA,SACAyB,MAAA,QACAE,aAAAzB,IACA,MAAA0B,EAAA,CACA,kBACA,sBACA,iBACA,kBACA,oBACA,iBACA,uBAEAC,EAAAD,EAAA1B,IAAA,OACA,OAAAkB,EAAA,eAAAS,UAAA,CAAA3B,MAGA,CACAb,MAAA,KAAAkC,GAAA,yBACAC,UAAA,WACAxB,IAAA,WACAyB,MAAA,QACAE,cAAAzB,EAAAC,IAAAiB,EAAA,OAAAA,EAAA,6BAGAlB,GAAA,OACA,eACA,WAAAC,EAAA2B,OAAA,YACA,WAAA3B,EAAA2B,OAAA,cAAAjD,KAAAuC,EAAA,aAEA,iCACA,KAAAH,YAAAd,EAAA4B,mBAAA,WAAAd,YAAAd,EAAA6B,gBAKA,CACA3C,MAAA,KAAAkC,GAAA,sBACAC,UAAA,QACAxB,IAAA,QACAyB,MAAA,SAEA,CACApC,MAAA,KAAAkC,GAAA,yBACAC,UAAA,YACAxB,IAAA,YACAyB,MAAA,QACAE,aAAAzB,GAAA,KAAAe,YAAAf,IAEA,CACAb,MAAA,KAAAkC,GAAA,6BACAC,UAAA,eACAxB,IAAA,eACAyB,MAAA,OACA3B,YAAA,CAAA6B,aAAA,iBAIA,OAAAL,GAEA1B,eACA,YAAAkB,YAAA,KAAAA,WAAAmB,MACAlB,OAAAmB,KAAA,KAAApB,WAAAmB,OAAAE,IAAAC,IACA,MAAAC,EAAA,KAAAvB,WAAAmB,MAAAG,GACA,OACAA,KACAE,UAAAD,EAAAC,UACAR,OAAAO,EAAAP,OACAS,SAAAF,EAAAE,UAAA,EACAC,MAAAH,EAAAG,OAAA,IACAT,kBAAAM,EAAAN,kBACA3B,aAAAiC,EAAAjC,aACA4B,UAAAK,EAAAL,aAXA,IAeA1C,kBACA,SAAAwB,aAAA,KAAAA,WAAAmB,MAAA,SACA,MAAAA,EAAAlB,OAAA0B,OAAA,KAAA3B,WAAAmB,OACA,OAAAA,EAAArD,OAAA,SAEA,MAAA8D,EAAAT,EAAAzD,OAAA,CAAAmE,EAAAN,IAAAM,GAAAN,EAAAE,UAAA,MACA,OAAAK,KAAAC,MAAAH,EAAAT,EAAArD,SAEAe,oBACA,SAAAmB,aAAA,KAAAA,WAAAmB,MAAA,eAEA,MAAAA,EAAAlB,OAAA0B,OAAA,KAAA3B,WAAAmB,OAAA,IACA,WAAAA,EAAArD,OAAA,SAEAqD,EAAAa,KAAAT,GAAA,WAAAA,EAAAP,QAAA,YACAG,EAAAc,MAAAV,GAAA,wBAAAW,SAAAX,EAAAP,SAAA,UACA,aCvK4W,I,wBCQxWmB,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,OAAAyC,E,0FCfA,QACbC,QAAS,CAYPC,+BAA8B,OAC5BC,EAAM,SACNvC,EAAQ,MACRoB,EAAK,UACLoB,EAAS,OACTC,EAAS,GAAE,UACXC,EAAY,GAAE,cACdC,EAAgB,KAGhB,MAAMC,EAAsB,GAAG5C,aAAoBwC,KAAaD,IAChE,GAAIM,aAAaC,QAAQF,GACvB,OAIF,MAAMG,EAAuB,CAC3BC,QAAS,CAAC,UAAW,aACrBC,QAAS,CAAC,WAINC,EAAqB,CACzBF,QAAS,IAAKL,EAAcK,SAAW,MAAQD,EAAqBC,SACpEC,QAAS,IAAKN,EAAcM,SAAW,MAAQF,EAAqBE,UAIhEE,EAAe/B,EAAMgC,OAAO5B,GAChC0B,EAAmBF,QAAQb,SAASX,EAAKP,UAAYO,EAAKjC,cAC1DxB,OACIsF,EAAcjC,EAAMgC,OAAO5B,GAC/B0B,EAAmBD,QAAQd,SAASX,EAAKP,SAAWO,EAAKjC,cACzDxB,OACIuF,EAAcD,EAAc,EAGlC,IAAIE,EAQAC,EANFD,EADED,EACkBb,EAAOgB,OAAS5F,KAAK6F,qBAAqB1D,GAE1CyC,EAAOO,SAAWnF,KAAK8F,uBAAuB3D,GAMlEwD,EADEF,EACoBZ,EAAUe,OAC9B,GAAGN,mCAA8CE,kBAE7BX,EAAUM,SAC9B,OAAO5B,EAAMrD,uCAIjBF,KAAK+F,gBAAgB,CACnBpF,MAAO+E,EACP7D,QAAS8D,EACT/D,KAAM6D,EAAc,QAAU,UAC9Bf,OAAQA,IAIVM,aAAagB,QAAQjB,EAAqB,SAQ5Ce,uBAAuB3D,GACrB,MAAMyC,EAAS,CACb,KAAQ,iBACR,OAAU,wBACV,SAAY,0BACZ,KAAQ,4BAEV,OAAOA,EAAOzC,IAAa,uBAQ7B0D,qBAAqB1D,GACnB,MAAMyC,EAAS,CACb,KAAQ,6BACR,OAAU,oCACV,SAAY,sCACZ,KAAQ,wCAEV,OAAOA,EAAOzC,IAAa,mCAS7B8D,0BAA0BvB,EAAQvC,EAAUwC,GAC1C,MAAMI,EAAsB,GAAG5C,aAAoBwC,KAAaD,IAChEM,aAAakB,WAAWnB,O,oCCzH9B,W,oCCAA,IAAI3E,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,QAAUL,EAAI8F,YAAY,SAAW9F,EAAI+F,UAAUC,GAAG,CAAC,MAAQhG,EAAIiG,oBAAoB,CAACjG,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIwC,GAAG,8BAAgC,UAAU,OAAQxC,EAAIkG,aAAarG,OAAQI,EAAG,WAAW,CAACG,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQC,MAAM,CAAC,YAAcL,EAAIwC,GAAG,2BAA6B,SAAS,SAAWxC,EAAI+F,UAAUI,MAAM,CAACC,MAAOpG,EAAIqG,gBAAiBC,SAAS,SAAUC,GAAMvG,EAAIqG,gBAAgBE,GAAKC,WAAW,oBAAoBxG,EAAIyG,GAAIzG,EAAIkG,cAAc,SAAS7C,GAAI,OAAOpD,EAAG,kBAAkB,CAACgB,IAAIoC,EAAGhD,MAAM,CAAC,MAAQgD,IAAK,CAACrD,EAAIS,GAAG,IAAIT,EAAIU,GAAG2C,GAAI,UAAS,GAAGrD,EAAIW,MAAM,IAEpvBc,EAAkB,G,YC6BP,GACfC,KAAA,gBACAC,MAAA,CAEAoE,SAAA,CACAxE,KAAAmF,QACAzE,SAAA,GAGAmE,MAAA,CACA7E,KAAAK,OACAK,QAAA,OAGA0E,OACA,OACAb,aAAA,EACAI,aAAA,GACAG,gBAAA,KAAAD,QAGAhE,SAAA,GAEAwE,MAAA,CAEAR,MAAAS,GACA,KAAAR,gBAAAQ,GAGAR,gBAAAQ,GACA,KAAAC,MAAA,QAAAD,GACA,KAAAC,MAAA,SAAAD,KAGA1C,QAAA,CACA,0BACA,KAAA2B,aAAA,EACA,IACA,MAAAiB,QAAAC,OAAAC,IAAA,qBACA,KAAAf,aAAAa,EAAAJ,KAAAO,cACA,KAAAhB,aAAArG,SACA,KAAAwG,gBAAA,KAAAH,aAAA,IAEA,MAAAX,GACA4B,QAAA5B,MAAA,iCAAAA,GACA,KAAA6B,QAAA7B,MAAA,CACAjF,MAAA,QACAkB,QAAA,mCAEA,QACA,KAAAsE,aAAA,MCjFqW,I,YCOjW5B,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,OAAAyC,E,gCClBf,IAAImD,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCDhC,IAAI3I,EAAI,EAAQ,QACZ4I,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9I,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsI,MAAM,GAAQ,CACjD7D,MAAO,SAAe9C,GAGpB,OAFA0G,EAASjI,MACTgI,EAAUzG,IACFwG,EAAQ/H,MAAM,SAAUyG,EAAO0B,GACrC,IAAK5G,EAAGkF,GAAQ,OAAO0B,MACtB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAInJ,EAAI,EAAQ,QACZ4I,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9I,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsI,MAAM,GAAQ,CACjD9D,KAAM,SAAc7C,GAGlB,OAFA0G,EAASjI,MACTgI,EAAUzG,GACHwG,EAAQ/H,MAAM,SAAUyG,EAAO0B,GACpC,GAAI5G,EAAGkF,GAAQ,OAAO0B,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAInJ,EAAI,EAAQ,QACZ4I,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9I,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsI,MAAM,GAAQ,CACjDpI,OAAQ,SAAgByI,GACtBN,EAASjI,MACTgI,EAAUO,GACV,IAAIC,EAAYvI,UAAUC,OAAS,EAC/BuI,EAAcD,OAAYrI,EAAYF,UAAU,GASpD,GARA8H,EAAQ/H,MAAM,SAAUyG,GAClB+B,GACFA,GAAY,EACZC,EAAchC,GAEdgC,EAAcF,EAAQE,EAAahC,KAEpC,CAAE2B,aAAa,IACdI,EAAW,MAAME,UAAU,kDAC/B,OAAOD,M,kCCrBX,IAAIE,EAAQ,EAAQ,QAEpBf,EAAOC,QAAU,SAAUe,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,4CCP5D,IAAIb,EAAY,EAAQ,QACpBgB,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMtJ,EAAYuJ,EAAiBC,GAClDvB,EAAUjI,GACV,IAAIyJ,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrBtJ,EAASgJ,EAASM,EAAEtJ,QACpBwJ,EAAQN,EAAWlJ,EAAS,EAAI,EAChCyJ,EAAIP,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASC,EACT,MAGF,GADAD,GAASC,EACLP,EAAWM,EAAQ,EAAIxJ,GAAUwJ,EACnC,MAAMhB,UAAU,+CAGpB,KAAMU,EAAWM,GAAS,EAAIxJ,EAASwJ,EAAOA,GAASC,EAAOD,KAASD,IACrEF,EAAOxJ,EAAWwJ,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIX3B,EAAOC,QAAU,CAGfxI,KAAM8J,GAAa,GAGnBS,MAAOT,GAAa,K,yCCtCtB,IAAI/I,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,CAAC,QAAU,QAAQ,CAACH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACuJ,MAAM,QAAQxJ,EAAIyJ,aAAepJ,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACJ,EAAG,OAAO,CAACI,MAAM,CAAC,KAAO,eAAe,EAAI,+cAA+cJ,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACH,EAAIS,GAAGT,EAAIU,GAAGV,EAAIwC,GAAG,gCAAgCvC,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,QAAUL,EAAI0J,aAAa1D,GAAG,CAAC,MAAQhG,EAAI2J,iBAAiB,CAAC3J,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIwC,GAAG,+BAA+B,QAAQ,GAAGvC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeE,MAAM,CAAC,KAAO,QAAQ,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,iBAAiB,CAACI,MAAM,CAAC,SAAWL,EAAI0J,aAAa1D,GAAG,CAAC,OAAShG,EAAI4J,mBAAmBzD,MAAM,CAACC,MAAOpG,EAAI6J,gBAAiBvD,SAAS,SAAUC,GAAMvG,EAAI6J,gBAAgBtD,GAAKC,WAAW,qBAAqBvG,EAAG,IAAI,CAACG,YAAY,CAAC,aAAa,OAAO,MAAQ,OAAO,YAAY,SAAS,CAACJ,EAAIS,GAAG,gEAAgE,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeE,MAAM,CAAC,KAAO,QAAQ,MAAQL,EAAIwC,GAAG,2BAA2B,CAACvC,EAAG,UAAU,CAACE,YAAY,uBAAuBE,MAAM,CAAC,WAAaL,EAAI8J,cAAc,QAAU9J,EAAIuC,QAAQ,OAAS,KAAK,WAAa,CACt8DwH,SAAU,GACVC,MAAOhK,EAAIkD,MAAMrD,OACjBoK,iBAAiB,GACjB,aAAejK,EAAIkK,cAAcnJ,YAAYf,EAAIgB,GAAG,CAAC,CAACC,IAAI,aAAaC,GAAG,SAASC,EAAMC,GAAQ,MAAO,CAACnB,EAAG,UAAU,CAACI,MAAM,CAAC,YAAcL,EAAIwC,GAAG,mCAAmCwD,GAAG,CAAC,OAAUmE,GAAMnK,EAAIoK,iBAAiBhJ,EAAOiC,GAAI8G,EAAE7K,OAAO8G,QAAQD,MAAM,CAACC,MAAOhF,EAAOiJ,WAAY/D,SAAS,SAAUC,GAAMvG,EAAIsK,KAAKlJ,EAAQ,aAAcmF,IAAMC,WAAW,+BAA+B,IAAI,KAAKvG,EAAG,uBAAuB,CAACI,MAAM,CAAC,MAAQL,EAAIwC,GAAG,iCAAiC,SAAW,WAAW,WAAaxC,EAAIuK,mBAAmB,YAAcvK,EAAIkC,gBAAgB,IAEnkBT,EAAkB,G,sGCqEP,GACfC,KAAA,eACA8I,OAAA,CAAAC,QACAC,WAAA,CACAC,qBACAC,6BAEAjE,OACA,OACAkE,cAAA,GACAC,gBAAA,GACApB,aAAA,EACAG,gBAAA,KACAkB,aAAA,OAGA3I,SAAA,IACA4I,eAAA,gEACAjJ,WAAA,CACAkF,MACA,YAAAsD,oBAEAU,IAAA7E,GACA,KAAA8E,OAAAC,SAAA,qBAAA/E,KAGA7D,UACA,OACA,CACAjC,MAAA,KAAAkC,GAAA,+BACAC,UAAA,YACAxB,IAAA,aAEA,CACAX,MAAA,KAAAkC,GAAA,gCACAC,UAAA,KACAxB,IAAA,MAEA,CACAX,MAAA,cACAmC,UAAA,aACA1B,YAAA,CAAA6B,aAAA,iBAIAkH,gBACA,YAAA5G,MAAAE,IAAAE,IAAA,IACAA,EACA+G,WAAA,KAAAS,gBAAAxH,EAAAD,KAAA,OAGA6G,eACA,OACAkB,gBAAA,KAAAP,cACAQ,SAAAD,IACA,KAAAP,cAAAO,MAKAE,UACA,SAAAC,sBACA,OAEA,KAAAL,OAAAC,SAAA,cAEA,MAAAK,EAAA7G,aAAAC,QAAA,qBAAA6G,gBACA,GAAAD,EAAA,CACA,kBAAAE,GAAAC,KAAAC,MAAAJ,GACAE,IAAA,KAAAD,eACA,KAAAI,2BAGAlH,aAAAkB,WAAA,qBAAA4F,gBACA9G,aAAAkB,WAAA,8BAAA4F,gBACA,KAAAP,OAAAC,SAAA,8BAIAhH,QAAA,IACA2H,eAAA,qBACAP,sBACA,aAAAE,iBACA,KAAArE,QAAA7B,MAAA,CACAjF,MAAA,iBACAkB,QAAA,iEAEA,KAAAuK,QAAAC,KAAA,cACA,IAKApC,kBAAAvG,GACA8D,QAAA8E,IAAA,oBAAA5I,GACA,KAAAwG,gBAAAxG,GAEA6I,aAAAC,GAEA,OAAAA,EAAAC,WAAA,KAIAD,EAAAlI,SAAA,OAAAkI,EAAAlI,SAAA,OAAAkI,EAAAlI,SAAA,KACA,mCAGA,KAPA,wCASAmG,iBAAA/G,EAAA8I,GACA,MAAA5G,EAAA,KAAA2G,aAAAC,GACA,GAAA5G,EAIA,OAHA,KAAA8G,SAAA9G,cAEA,KAAA+E,KAAA,KAAAQ,gBAAAzH,EAAA,IAGA,KAAAiH,KAAA,KAAAQ,gBAAAzH,EAAA8I,IAEAG,gBACA,MAAAC,EAAA,KAAA1B,cACAzH,IAAAC,IAAA,CACAA,KACA8I,KAAA,KAAArB,gBAAAzH,IAAA,MAEA6B,OAAAsH,KAAAL,MAEA,IAAAI,EAAA1M,OAEA,OADA,KAAAwM,SAAA9G,MAAA,gDACA,KAIA,aAAAlC,EAAA,KAAA8I,KAAAI,EAAA,CACA,MAAAhH,EAAA,KAAA2G,aAAAC,GACA,GAAA5G,EAEA,OADA,KAAA8G,SAAA9G,MAAA,oBAAAlC,MAAAkC,KACA,KAIA,OAAAgH,GAEArK,YAAAuK,GACA,IAAAA,EAAA,YACA,MAAAC,EAAA,KACAC,EAAA,qBACArD,EAAAzF,KAAA+I,MAAA/I,KAAAoI,IAAAQ,GAAA5I,KAAAoI,IAAAS,IACA,SAAAG,YAAAJ,EAAA5I,KAAAiJ,IAAAJ,EAAApD,IAAAyD,QAAA,OAAAJ,EAAArD,MAEA,uBACA,SAAAuB,cAAAhL,OAEA,YADA,KAAAwM,SAAAW,QAAA,mCAIA,SAAAnD,gBAEA,YADA,KAAAwC,SAAAW,QAAA,4BAIA,MAAA9J,EAAA,KAAA2H,cAAAzH,IAAAC,IAAA,CACAA,KACA8I,KAAA,KAAArB,gBAAAzH,IAAA,MAGA,IACA,KAAAqG,aAAA,EAGA,MAAAuD,EAAAtI,aAAAC,QAAA,qBAAA6G,gBACA,GAAAwB,EACA,IACA,aAAA5I,GAAAsH,KAAAC,MAAAqB,GACA5I,GACA,KAAAuB,0BAAAvB,EAAA,gBAAAoH,gBAEA,MAAAtB,GACAhD,QAAA5B,MAAA,iDAAA4E,GAGA,MAAApD,QAAAC,OAAAkG,KACA,4CAAAC,mBAAA,KAAA1B,gBACA,CACAvI,QACAkK,QAAA,KAAAvD,kBAIAxF,EAAA0C,EAAAJ,KAAA0G,QACA1I,aAAAgB,QAAA,qBAAA8F,eAAAE,KAAA2B,UAAA,CACAjJ,SACAqH,YAAA,KAAAD,kBAEA9G,aAAAkB,WAAA,8BAAA4F,gBAEA,KAAA8B,aAAAlJ,GAEA,MAAAkB,GAAA,IAAAiI,EACArG,QAAA5B,MAAA,kBAAAA,GACA,KAAAmE,aAAA,EACA,KAAA2C,SAAA9G,OAAA,QAAAiI,EAAAjI,EAAAwB,gBAAA,IAAAyG,GAAA,QAAAA,IAAA7G,YAAA,IAAA6G,OAAA,EAAAA,EAAAjI,QAAA,8BAGA,mBAAAlB,GACA,KAAA0G,cACA0C,cAAA,KAAA1C,cAGA,MAAA2C,EAAA,UACA,IACA,SAAAjC,eACA,UAAAkC,MAAA,gCAGA,MAAA5G,QAAAC,OAAAC,IACA,sCAAA5C,YAAA8I,mBAAA,KAAA1B,mBAGA,KAAAP,OAAAC,SAAA,qBAAApE,EAAAJ,MAEA,MAAAiH,EAAA5L,OAAA0B,OAAAqD,EAAAJ,KAAAzD,OAAAc,MACAV,GAAA,uBAAAW,SAAAX,EAAAP,SAGA,GAAA6K,EAAA,CACAH,cAAA,KAAA1C,cACA,KAAArB,aAAA,EACA/E,aAAAgB,QAAA,8BAAA8F,eAAA,QAEA,MAAAvI,EAAAlB,OAAA0B,OAAAqD,EAAAJ,KAAAzD,OAGA,KAAAkB,8BAAA,CACAC,SACAvC,SAAA,WACAoB,QACAoB,UAAA,KAAAmH,eACAjH,UAAA,CACAM,QAAA,uCAAA5B,EAAArD,gBACA0F,MAAA,GAAArC,EAAAgC,OAAA5B,GAAA,cAAAA,EAAAP,QAAAlD,sDAAAqD,EAAAgC,OAAA5B,GAAA,WAAAA,EAAAP,QAAAlD,wBAGA4E,cAAA,CACAK,QAAA,cACAC,QAAA,eAIA,MAAAQ,GAAA,IAAAsI,EACA1G,QAAA5B,MAAA,wBAAAA,GACA,eAAAsI,EAAAtI,EAAAwB,gBAAA,IAAA8G,OAAA,EAAAA,EAAA9K,UACA0K,cAAA,KAAA1C,cACA,KAAArB,aAAA,EACA/E,aAAAkB,WAAA,qBAAA4F,gBACA9G,aAAAkB,WAAA,8BAAA4F,yBAKAiC,IACA,KAAA3C,aAAA+C,YAAAJ,EAAA,MAEA,gCACA,IACA,MAAAlC,EAAA7G,aAAAC,QAAA,qBAAA6G,gBACAsC,EAAApJ,aAAAC,QAAA,8BAAA6G,gBAEA,GAAAD,EAAA,CACA,aAAAnH,EAAA,YAAAqH,GAAAC,KAAAC,MAAAJ,GAEA,GAAAE,IAAA,KAAAD,eACA,UAAAkC,MAAA,qCAGA,SAAAlC,eACA,UAAAkC,MAAA,gCAGA,MAAA5G,QAAAC,OAAAC,IACA,sCAAA5C,YAAA8I,mBAAA,KAAA1B,mBAGA,GAAA1E,EAAAJ,KAAA,CACA,KAAAuE,OAAAC,SAAA,qBAAApE,EAAAJ,MAEA,MAAAiH,EAAA5L,OAAA0B,OAAAqD,EAAAJ,KAAAzD,OAAAc,MACAV,GAAA,uBAAAW,SAAAX,EAAAP,SAGA,GAAA6K,GAAAG,GAGA,GAAAH,EAAA,CACA,KAAAlE,aAAA,EACA/E,aAAAgB,QAAA,8BAAA8F,eAAA,QAEA,MAAAvI,EAAAlB,OAAA0B,OAAAqD,EAAAJ,KAAAzD,OAGA,KAAAkB,8BAAA,CACAC,SACAvC,SAAA,WACAoB,QACAoB,UAAA,KAAAmH,eACAjH,UAAA,CACAM,QAAA,uCAAA5B,EAAArD,gBACA0F,MAAA,GAAArC,EAAAgC,OAAA5B,GAAA,cAAAA,EAAAP,QAAAlD,sDAAAqD,EAAAgC,OAAA5B,GAAA,WAAAA,EAAAP,QAAAlD,wBAGA4E,cAAA,CACAK,QAAA,cACAC,QAAA,oBArBA,KAAA2E,aAAA,EACA,KAAA6D,aAAAlJ,KA0BA,MAAAkB,GACA4B,QAAA5B,MAAA,uCAAAA,GACAZ,aAAAkB,WAAA,qBAAA4F,gBACA9G,aAAAkB,WAAA,8BAAA4F,kBAGAuC,gBACA,KAAAjD,cACA0C,cAAA,KAAA1C,cAGA,KAAAG,OAAAC,SAAA,6BAGAvE,MAAA,CAEA6E,eAAA,CACAwC,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAAjD,OAAAC,SAAA,2BACA,KAAAJ,cACA0C,cAAA,KAAA1C,cAGA,KAAAc,4BAGAuC,WAAA,KCraqV,I,wBCQjVlK,EAAY,eACd,EACAnE,EACA0B,GACA,EACA,KACA,WACA,MAIa,aAAAyC,E,2CCnBf", "file": "static/js/chunk-791bd3a8.eef7974c.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"compact-card\",staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.title}},[(_vm.overallProgress !== undefined)?_c('template',{slot:\"extra\"},[_c('span',[_vm._v(\"Overall Progress: \"+_vm._s(_vm.overallProgress)+\"%\")])]):_vm._e(),_c('a-progress',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"percent\":_vm.overallProgress,\"status\":_vm.progressBarStatus}}),_c('a-table',{attrs:{\"dataSource\":_vm.progressData,\"columns\":_vm.progressColumns,\"rowKey\":\"ip\",\"pagination\":false},scopedSlots:_vm._u([{key:\"errorDetail\",fn:function(text, record){return [(record && record.error_detail)?_c('a-popover',{attrs:{\"placement\":\"topLeft\"}},[_c('template',{slot:\"content\"},[_c('p',[_vm._v(\"Time: \"+_vm._s(record.error_detail.time))]),_c('p',[_vm._v(\"Type: \"+_vm._s(record.error_detail.type))]),_c('p',[_vm._v(\"Message: \"+_vm._s(record.error_detail.message))])]),_c('a-icon',{staticStyle:{\"color\":\"#ff4d4f\"},attrs:{\"type\":\"info-circle\"}})],2):_vm._e()]}}])})],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <a-card\n    :title=\"title\"\n    style=\"margin-top: 16px;\"\n    class=\"compact-card\"\n  >\n    <template v-if=\"overallProgress !== undefined\" slot=\"extra\">\n      <span>Overall Progress: {{ overallProgress }}%</span>\n    </template>\n\n    <a-progress\n      :percent=\"overallProgress\"\n      :status=\"progressBarStatus\"\n      style=\"margin-bottom: 16px;\"\n    />\n\n    <a-table\n      :dataSource=\"progressData\"\n      :columns=\"progressColumns\"\n      rowKey=\"ip\"\n      :pagination=\"false\"\n    >\n      <template slot=\"errorDetail\" slot-scope=\"text, record\">\n        <a-popover v-if=\"record && record.error_detail\" placement=\"topLeft\">\n          <template slot=\"content\">\n            <p>Time: {{ record.error_detail.time }}</p>\n            <p>Type: {{ record.error_detail.type }}</p>\n            <p>Message: {{ record.error_detail.message }}</p>\n          </template>\n          <a-icon type=\"info-circle\" style=\"color: #ff4d4f\" />\n        </a-popover>\n      </template>\n    </a-table>\n  </a-card>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressDisplay',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    taskType: {\n      type: String,\n      required: true\n    },\n    activeTask: {\n      type: Object,\n      default: null\n    },\n    formatBytes: {\n      type: Function,\n      required: true\n    }\n  },\n  computed: {\n    progressColumns() {\n      const columns = [\n        {\n          title: this.$t('hostConfig.columns.ipAddress'),\n          dataIndex: 'ip',\n          key: 'ip',\n          width: '120px'\n        },\n        {\n          title: this.$t('hostConfig.columns.hostName'),\n          dataIndex: 'host_name',\n          key: 'host_name',\n          width: '150px',\n          ellipsis: true\n        },\n        {\n          title: this.$t('tool.columns.status'),\n          dataIndex: 'status',\n          key: 'status',\n          width: '100px',\n          customRender: (text) => {\n            const colorMap = {\n              'pending': '#1890ff',\n              'in_progress': '#1890ff',\n              'paused': '#faad14',\n              'success': '#52c41a',\n              'completed': '#52c41a',\n              'failed': '#f5222d',\n              'downloading': '#1890ff'\n            };\n            const color = colorMap[text] || '#000';\n            return <span style={{ color }}>{text}</span>;\n          }\n        },\n        {\n          title: this.$t('tool.columns.progress'),\n          dataIndex: 'progress',\n          key: 'progress',\n          width: '200px',\n          customRender: (text, record) => (\n            <div>\n              <a-progress\n                percent={text || 0}\n                size=\"small\"\n                status={record.status === 'failed' ? 'exception' :\n                       record.status === 'paused' ? 'normal' : undefined}\n              />\n              <div style=\"font-size: 12px; color: #999\">\n                {this.formatBytes(record.bytes_transferred)} / {this.formatBytes(record.file_size)}\n              </div>\n            </div>\n          )\n        },\n        {\n          title: this.$t('tool.columns.speed'),\n          dataIndex: 'speed',\n          key: 'speed',\n          width: '100px'\n        },\n        {\n          title: this.$t('tool.columns.fileSize'),\n          dataIndex: 'file_size',\n          key: 'file_size',\n          width: '100px',\n          customRender: (text) => this.formatBytes(text)\n        },\n        {\n          title: this.$t('tool.columns.errorDetails'),\n          dataIndex: 'error_detail',\n          key: 'error_detail',\n          width: '60px',\n          scopedSlots: { customRender: 'errorDetail' }\n        }\n      ];\n\n      return columns;\n    },\n    progressData() {\n      if (!this.activeTask || !this.activeTask.nodes) return [];\n      return Object.keys(this.activeTask.nodes).map(ip => {\n        const node = this.activeTask.nodes[ip];\n        return {\n          ip,\n          host_name: node.host_name,\n          status: node.status,\n          progress: node.progress || 0,\n          speed: node.speed || '-',\n          bytes_transferred: node.bytes_transferred,\n          error_detail: node.error_detail,\n          file_size: node.file_size\n        };\n      });\n    },\n    overallProgress() {\n      if (!this.activeTask || !this.activeTask.nodes) return 0;\n      const nodes = Object.values(this.activeTask.nodes);\n      if (nodes.length === 0) return 0;\n\n      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);\n      return Math.round(totalProgress / nodes.length);\n    },\n    progressBarStatus() {\n      if (!this.activeTask || !this.activeTask.nodes) return 'normal';\n\n      const nodes = Object.values(this.activeTask.nodes || {});\n      if (nodes.length === 0) return 'normal';\n\n      if (nodes.some(node => node.status === 'failed')) return 'exception';\n      if (nodes.every(node => ['success', 'completed'].includes(node.status))) return 'success';\n      return 'active';\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* You can add specific styles for ProgressDisplay component here if needed */\n/* Shared styles will be handled in the parent view components or global styles */\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileTransferProgress.vue?vue&type=template&id=258e2dfa&scoped=true\"\nimport script from \"./FileTransferProgress.vue?vue&type=script&lang=js\"\nexport * from \"./FileTransferProgress.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileTransferProgress.vue?vue&type=style&index=0&id=258e2dfa&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"258e2dfa\",\n  null\n  \n)\n\nexport default component.exports", "/**\r\n * 通知混入 - 提供通用的通知处理逻辑\r\n * 用于任务完成、文件上传/下载完成等场景\r\n */\r\nexport default {\r\n  methods: {\r\n    /**\r\n     * 添加任务完成通知\r\n     * @param {Object} options - 通知选项\r\n     * @param {string} options.taskId - 任务ID\r\n     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)\r\n     * @param {Array} options.nodes - 节点数组\r\n     * @param {string} options.projectId - 项目ID\r\n     * @param {Object} options.titles - 自定义标题 {success, error}\r\n     * @param {Object} options.templates - 自定义消息模板 {success, error}\r\n     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}\r\n     */\r\n    addTaskCompletionNotification({\r\n      taskId,\r\n      taskType,\r\n      nodes,\r\n      projectId,\r\n      titles = {},\r\n      templates = {},\r\n      statusMapping = {}\r\n    }) {\r\n      // 检查是否已经发送过通知\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      if (localStorage.getItem(notificationSentKey)) {\r\n        return; // 已经发送过通知，不再重复发送\r\n      }\r\n\r\n      // 设置默认状态映射\r\n      const defaultStatusMapping = {\r\n        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'\r\n        failure: ['failed']                // 失败状态通常是'failed'\r\n      };\r\n\r\n      // 合并自定义状态映射\r\n      const finalStatusMapping = {\r\n        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],\r\n        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]\r\n      };\r\n\r\n      // 计算成功和失败的节点数量\r\n      const successNodes = nodes.filter(node =>\r\n        finalStatusMapping.success.includes(node.status) && !node.error_detail\r\n      ).length;\r\n      const failedNodes = nodes.filter(node =>\r\n        finalStatusMapping.failure.includes(node.status) || node.error_detail\r\n      ).length;\r\n      const hasFailures = failedNodes > 0;\r\n\r\n      // 准备通知标题\r\n      let notificationTitle;\r\n      if (hasFailures) {\r\n        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);\r\n      } else {\r\n        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);\r\n      }\r\n\r\n      // 准备通知内容\r\n      let notificationMessage;\r\n      if (hasFailures) {\r\n        notificationMessage = templates.error ||\r\n          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;\r\n      } else {\r\n        notificationMessage = templates.success ||\r\n          `All ${nodes.length} nodes completed successfully.`;\r\n      }\r\n\r\n      // 添加到全局通知中心\r\n      this.addNotification({\r\n        title: notificationTitle,\r\n        message: notificationMessage,\r\n        type: hasFailures ? 'error' : 'success',\r\n        taskId: taskId\r\n      });\r\n\r\n      // 标记已发送通知\r\n      localStorage.setItem(notificationSentKey, 'true');\r\n    },\r\n\r\n    /**\r\n     * 获取默认的成功标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultSuccessTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed',\r\n        'upload': 'File Upload Completed',\r\n        'download': 'File Download Completed',\r\n        'tool': 'Tool Execution Completed'\r\n      };\r\n      return titles[taskType] || 'Operation Completed';\r\n    },\r\n\r\n    /**\r\n     * 获取默认的错误标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultErrorTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed with Errors',\r\n        'upload': 'File Upload Completed with Errors',\r\n        'download': 'File Download Completed with Errors',\r\n        'tool': 'Tool Execution Completed with Errors'\r\n      };\r\n      return titles[taskType] || 'Operation Completed with Errors';\r\n    },\r\n\r\n    /**\r\n     * 清除任务通知标记\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型\r\n     * @param {string} projectId - 项目ID\r\n     */\r\n    clearTaskNotificationMark(taskId, taskType, projectId) {\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      localStorage.removeItem(notificationSentKey);\r\n    }\r\n  }\r\n};\r\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileTransferProgress.vue?vue&type=style&index=0&id=258e2dfa&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"proxy-selector\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.isDetecting,\"disabled\":_vm.disabled},on:{\"click\":_vm.fetchReachableIps}},[_vm._v(\" \"+_vm._s(_vm.$t('common.detectReachableIps') || '检测可达IP')+\" \")]),(_vm.reachableIps.length)?_c('a-select',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"16px\"},attrs:{\"placeholder\":_vm.$t('tool.selectReachableIp') || '选择可达IP',\"disabled\":_vm.disabled},model:{value:(_vm.selectedIpValue),callback:function ($$v) {_vm.selectedIpValue=$$v},expression:\"selectedIpValue\"}},_vm._l((_vm.reachableIps),function(ip){return _c('a-select-option',{key:ip,attrs:{\"value\":ip}},[_vm._v(\" \"+_vm._s(ip)+\" \")])}),1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"proxy-selector\">\r\n    <!-- 检测IP按钮 -->\r\n    <a-button\r\n      class=\"nav-style-button\"\r\n      @click=\"fetchReachableIps\"\r\n      :loading=\"isDetecting\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      {{ $t('common.detectReachableIps') || '检测可达IP' }}\r\n    </a-button>\r\n\r\n    <!-- IP选择下拉框 -->\r\n    <a-select\r\n      v-if=\"reachableIps.length\"\r\n      v-model=\"selectedIpValue\"\r\n      style=\"width: 100%; margin-top: 16px;\"\r\n      :placeholder=\"$t('tool.selectReachableIp') || '选择可达IP'\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <a-select-option v-for=\"ip in reachableIps\" :key=\"ip\" :value=\"ip\">\r\n        {{ ip }}\r\n      </a-select-option>\r\n    </a-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProxySelector',\r\n  props: {\r\n    // 是否禁用控件\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 初始选中的IP\r\n    value: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isDetecting: false,\r\n      reachableIps: [],\r\n      selectedIpValue: this.value\r\n    };\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {\r\n    // 监听外部传入的value变化\r\n    value(newValue) {\r\n      this.selectedIpValue = newValue;\r\n    },\r\n    // 监听内部selectedIpValue变化，向外发送事件\r\n    selectedIpValue(newValue) {\r\n      this.$emit('input', newValue);\r\n      this.$emit('change', newValue);\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchReachableIps() {\r\n      this.isDetecting = true;\r\n      try {\r\n        const response = await axios.get('/api/proxy/detect');\r\n        this.reachableIps = response.data.reachable_ips;\r\n        if (this.reachableIps.length) {\r\n          this.selectedIpValue = this.reachableIps[0];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error detecting reachable IPs:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to detect reachable IPs'\r\n        });\r\n      } finally {\r\n        this.isDetecting = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProxySelector.vue?vue&type=template&id=46f0b65a&scoped=true\"\nimport script from \"./ProxySelector.vue?vue&type=script&lang=js\"\nexport * from \"./ProxySelector.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46f0b65a\",\n  null\n  \n)\n\nexport default component.exports", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  every: function every(fn) {\n    anObject(this);\n    aFunction(fn);\n    return !iterate(this, function (value, stop) {\n      if (!fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"2px\"}},[_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.fileDownload')))])]),_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.downloading},on:{\"click\":_vm.handleDownload}},[_vm._v(\" \"+_vm._s(_vm.$t('fileDownload.startDownload'))+\" \")])],1),_c('div',{staticClass:\"main-content\"},[_c('div',{staticClass:\"left-section\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.downloading},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedProxyIp),callback:function ($$v) {_vm.selectedProxyIp=$$v},expression:\"selectedProxyIp\"}}),_c('p',{staticStyle:{\"margin-top\":\"16px\",\"color\":\"gray\",\"font-size\":\"12px\"}},[_vm._v(\" Default download path: \\\\infocollect\\\\cache\\\\download\\\\ \")])],1)],1),_c('div',{staticClass:\"right-section config-table\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('a-table',{staticClass:\"bordered-nodes-table\",attrs:{\"dataSource\":_vm.nodesWithPath,\"columns\":_vm.columns,\"rowKey\":\"ip\",\"pagination\":{\n            pageSize: 10,\n            total: _vm.nodes.length,\n            showSizeChanger: false\n          },\"rowSelection\":_vm.rowSelection},scopedSlots:_vm._u([{key:\"remotePath\",fn:function(text, record){return [_c('a-input',{attrs:{\"placeholder\":_vm.$t('fileDownload.enterDownloadPath')},on:{\"change\":(e) => _vm.updateRemotePath(record.ip, e.target.value)},model:{value:(record.remotePath),callback:function ($$v) {_vm.$set(record, \"remotePath\", $$v)},expression:\"record.remotePath\"}})]}}])})],1)],1)]),_c('FileTransferProgress',{attrs:{\"title\":_vm.$t('fileDownload.downloadProgress'),\"taskType\":'download',\"activeTask\":_vm.activeDownloadTask,\"formatBytes\":_vm.formatBytes}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"padding: 2px;\">\r\n    <div class=\"card-header-wrapper\">\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"logo-wrapper\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n            <path :fill=\"'currentColor'\" d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"/>\r\n          </svg>\r\n        </div>\r\n        <h6 class=\"font-semibold m-0\">{{ $t('headTopic.fileDownload') }}</h6>\r\n      </div>\r\n      <a-button @click=\"handleDownload\" :loading=\"downloading\" class=\"nav-style-button\">\r\n        {{ $t('fileDownload.startDownload') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <div class=\"left-section\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureProxy')\">\r\n          <proxy-selector\r\n            v-model=\"selectedProxyIp\"\r\n            :disabled=\"downloading\"\r\n            @change=\"handleProxyChange\"\r\n          />\r\n          <p style=\"margin-top: 16px; color: gray; font-size: 12px;\">\r\n            Default download path: \\infocollect\\cache\\download\\\r\n          </p>\r\n        </a-card>\r\n      </div>\r\n\r\n      <div class=\"right-section config-table\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureNodes')\">\r\n          <a-table\r\n            :dataSource=\"nodesWithPath\"\r\n            :columns=\"columns\"\r\n            rowKey=\"ip\"\r\n            :pagination=\"{\r\n              pageSize: 10,\r\n              total: nodes.length,\r\n              showSizeChanger: false\r\n            }\"\r\n            :rowSelection=\"rowSelection\"\r\n            class=\"bordered-nodes-table\"\r\n          >\r\n            <template slot=\"remotePath\" slot-scope=\"text, record\">\r\n              <a-input\r\n                v-model=\"record.remotePath\"\r\n                :placeholder=\"$t('fileDownload.enterDownloadPath')\"\r\n                @change=\"(e) => updateRemotePath(record.ip, e.target.value)\"\r\n              />\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- Add progress card after the alert -->\r\n    <FileTransferProgress\r\n      :title=\"$t('fileDownload.downloadProgress')\"\r\n      :taskType=\"'download'\"\r\n      :activeTask=\"activeDownloadTask\"\r\n      :formatBytes=\"formatBytes\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport FileTransferProgress from '@/components/Cards/FileTransferProgress.vue';\r\n\r\nexport default {\r\n  name: 'FileDownload',\r\n  mixins: [NotificationMixin],\r\n  components: {\r\n    ProxySelector,\r\n    FileTransferProgress\r\n  },\r\n  data() {\r\n    return {\r\n      selectedNodes: [],\r\n      nodeRemotePaths: {},\r\n      downloading: false,\r\n      selectedProxyIp: null,\r\n      pollInterval: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['nodes', 'activeDownloadTask', 'currentProject', 'sidebarColor']),\r\n    activeTask: {\r\n      get() {\r\n        return this.activeDownloadTask;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateDownloadTask', value);\r\n      }\r\n    },\r\n    columns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        },\r\n        {\r\n          title: 'Remote Path',\r\n          dataIndex: 'remotePath',\r\n          scopedSlots: { customRender: 'remotePath' }\r\n        }\r\n      ];\r\n    },\r\n    nodesWithPath() {\r\n      return this.nodes.map(node => ({\r\n        ...node,\r\n        remotePath: this.nodeRemotePaths[node.ip] || ''\r\n      }));\r\n    },\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedNodes,\r\n        onChange: (selectedRowKeys) => {\r\n          this.selectedNodes = selectedRowKeys;\r\n        },\r\n      };\r\n    },\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    this.$store.dispatch('fetchNodes');\r\n    // Only check active download task if we're in the same project\r\n    const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveDownloadTask();\r\n      } else {\r\n        // Clear task info if it belongs to a different project\r\n        localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateDownloadTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      console.log('Proxy IP changed:', ip);\r\n      this.selectedProxyIp = ip;\r\n    },\r\n    validatePath(path) {\r\n      // Check for absolute path and disallowed characters\r\n      if (!path.startsWith('/')) {\r\n        return 'Path must be absolute (start with /)';\r\n      }\r\n\r\n      if (path.includes('..') || path.includes('./') || path.includes('~')) {\r\n        return 'Path cannot contain ./, ../ or ~';\r\n      }\r\n\r\n      return null; // Path is valid\r\n    },\r\n    updateRemotePath(ip, path) {\r\n      const error = this.validatePath(path);\r\n      if (error) {\r\n        this.$message.error(error);\r\n        // Clear invalid path\r\n        this.$set(this.nodeRemotePaths, ip, '');\r\n        return;\r\n      }\r\n      this.$set(this.nodeRemotePaths, ip, path);\r\n    },\r\n    validatePaths() {\r\n      const selectedPaths = this.selectedNodes\r\n        .map(ip => ({\r\n          ip,\r\n          path: this.nodeRemotePaths[ip] || ''\r\n        }))\r\n        .filter(item => item.path);\r\n\r\n      if (!selectedPaths.length) {\r\n        this.$message.error('Please enter remote paths for selected nodes');\r\n        return null;\r\n      }\r\n\r\n      // Validate all paths\r\n      for (const {ip, path} of selectedPaths) {\r\n        const error = this.validatePath(path);\r\n        if (error) {\r\n          this.$message.error(`Invalid path for ${ip}: ${error}`);\r\n          return null;\r\n        }\r\n      }\r\n\r\n      return selectedPaths;\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    async handleDownload() {\r\n      if (!this.selectedNodes.length) {\r\n        this.$message.warning('Please select at least one node');\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedProxyIp) {\r\n        this.$message.warning('Please select a proxy IP');\r\n        return;\r\n      }\r\n\r\n      const nodes = this.selectedNodes.map(ip => ({\r\n        ip,\r\n        path: this.nodeRemotePaths[ip] || ''\r\n      }));\r\n\r\n      try {\r\n        this.downloading = true;\r\n\r\n        // 清除之前的下载任务通知记录\r\n        const previousTaskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n        if (previousTaskInfo) {\r\n          try {\r\n            const { taskId } = JSON.parse(previousTaskInfo);\r\n            if (taskId) {\r\n              this.clearTaskNotificationMark(taskId, 'download', this.currentProject);\r\n            }\r\n          } catch (e) {\r\n            console.error('Error clearing previous download notification:', e);\r\n          }\r\n        }\r\n        const response = await axios.post(\r\n          `/api/file_transfer/download/start?dbFile=${encodeURIComponent(this.currentProject)}`,\r\n          {\r\n            nodes,\r\n            proxyIp: this.selectedProxyIp\r\n          }\r\n        );\r\n\r\n        const taskId = response.data.task_id;\r\n        localStorage.setItem(`downloadTask_${this.currentProject}`, JSON.stringify({\r\n          taskId,\r\n          projectFile: this.currentProject\r\n        }));\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n\r\n        this.startPolling(taskId);\r\n\r\n      } catch (error) {\r\n        console.error('Download error:', error);\r\n        this.downloading = false;\r\n        this.$message.error(error.response?.data?.error || 'Failed to start download');\r\n      }\r\n    },\r\n    async startPolling(taskId) {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n\r\n      const pollStatus = async () => {\r\n        try {\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          this.$store.dispatch('updateDownloadTask', response.data);\r\n\r\n          const allCompleted = Object.values(response.data.nodes).every(\r\n            node => ['completed', 'failed'].includes(node.status)\r\n          );\r\n\r\n          if (allCompleted) {\r\n            clearInterval(this.pollInterval);\r\n            this.downloading = false;\r\n            localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n            const nodes = Object.values(response.data.nodes);\r\n\r\n            // 使用混入中的方法添加下载完成通知\r\n            this.addTaskCompletionNotification({\r\n              taskId,\r\n              taskType: 'download',\r\n              nodes,\r\n              projectId: this.currentProject,\r\n              templates: {\r\n                success: `File downloaded successfully to all ${nodes.length} nodes.`,\r\n                error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n              },\r\n              // 指定下载任务的状态映射\r\n              statusMapping: {\r\n                success: ['completed'],\r\n                failure: ['failed']\r\n              }\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Error polling status:', error);\r\n          if (error.response?.status === 404) {\r\n            clearInterval(this.pollInterval);\r\n            this.downloading = false;\r\n            localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n            localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n          }\r\n        }\r\n      };\r\n\r\n      await pollStatus();\r\n      this.pollInterval = setInterval(pollStatus, 10000); // 降低轮询频率到10秒\r\n    },\r\n    async checkActiveDownloadTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`downloadTaskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateDownloadTask', response.data);\r\n\r\n            const allCompleted = Object.values(response.data.nodes).every(\r\n              node => ['completed', 'failed'].includes(node.status)\r\n            );\r\n\r\n            if (!allCompleted && !taskCompleted) {\r\n              this.downloading = true;\r\n              this.startPolling(taskId);\r\n            } else if (allCompleted) {\r\n              this.downloading = false;\r\n              localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n              const nodes = Object.values(response.data.nodes);\r\n\r\n              // 使用混入中的方法添加下载完成通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType: 'download',\r\n                nodes,\r\n                projectId: this.currentProject,\r\n                templates: {\r\n                  success: `File downloaded successfully to all ${nodes.length} nodes.`,\r\n                  error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n                },\r\n                // 指定下载任务的状态映射\r\n                statusMapping: {\r\n                  success: ['completed'],\r\n                  failure: ['failed']\r\n                }\r\n              });\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active download task:', error);\r\n        localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n    beforeDestroy() {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n      // Clear download task when component is destroyed\r\n      this.$store.dispatch('updateDownloadTask', null);\r\n    },\r\n  },\r\n  watch: {\r\n    // Add watcher for currentProject changes\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // Clear previous project's task status\r\n          this.$store.dispatch('updateDownloadTask', null);\r\n          if (this.pollInterval) {\r\n            clearInterval(this.pollInterval);\r\n          }\r\n          // Check for active tasks in new project\r\n          this.checkActiveDownloadTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 卡片头部样式 */\r\n.card-header-wrapper {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo-wrapper {\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n}\r\n\r\n/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */\r\n.main-content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 2fr;\r\n  gap: 16px;\r\n}\r\n\r\n/* 左侧区域的Flexbox布局 */\r\n.left-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧卡片样式 */\r\n.left-section .compact-card:first-child {\r\n  margin-bottom: 8px;\r\n  flex: 2; /* Takes up 2/3 of the available space */\r\n}\r\n\r\n.left-section .compact-card:last-child {\r\n  flex: 1; /* Takes up 1/3 of the available space */\r\n}\r\n\r\n/* 响应式布局 - 在移动设备上切换为单列布局 */\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 调整输入框样式，使其更紧凑 */\r\n.bordered-nodes-table .ant-input {\r\n  height: 28px;\r\n  padding: 0 8px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileDownload.vue?vue&type=template&id=36748c18&scoped=true\"\nimport script from \"./FileDownload.vue?vue&type=script&lang=js\"\nexport * from \"./FileDownload.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileDownload.vue?vue&type=style&index=0&id=36748c18&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36748c18\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=style&index=0&id=36748c18&prod&scoped=true&lang=css\""], "sourceRoot": ""}