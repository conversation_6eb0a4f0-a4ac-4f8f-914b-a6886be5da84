{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\PackageInfo.vue", "mtime": 1753170222127}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbWFwU3RhdGUgfSBmcm9tICd2dWV4JzsKaW1wb3J0IGF4aW9zIGZyb20gJ0AvYXBpL2F4aW9zSW5zdGFuY2UnOwppbXBvcnQgUmVmcmVzaEJ1dHRvbiBmcm9tICcuLi9XaWRnZXRzL1JlZnJlc2hCdXR0b24udnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFJlZnJlc2hCdXR0b24KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBwYWNrYWdlczogW10sCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICdQYWNrYWdlIE5hbWUnLAogICAgICAgIGRhdGFJbmRleDogJ3BhY2thZ2VfbmFtZScsCiAgICAgICAga2V5OiAncGFja2FnZV9uYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICdQYWNrYWdlIFR5cGUnLAogICAgICAgIGRhdGFJbmRleDogJ3BhY2thZ2VfdHlwZScsCiAgICAgICAga2V5OiAncGFja2FnZV90eXBlJwogICAgICB9XSwKICAgICAgcGFnaW5hdGlvbjogewogICAgICAgIHBhZ2VTaXplOiAxMDAKICAgICAgfQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBTdGF0ZShbJ3NlbGVjdGVkTm9kZUlwJywgJ2N1cnJlbnRQcm9qZWN0JywgJ3NpZGViYXJDb2xvciddKQogIH0sCiAgd2F0Y2g6IHsKICAgIHNlbGVjdGVkTm9kZUlwKG5ld0lwKSB7CiAgICAgIC8vIOW9kyBzZWxlY3RlZE5vZGVJcCDlj5jljJbml7bph43mlrDojrflj5bljIXmlbDmja4KICAgICAgdGhpcy5mZXRjaFBhY2thZ2VzKCk7CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5mZXRjaFBhY2thZ2VzKCk7IC8vIOWIneWni+WKoOi9veaXtuiwg+eUqAogIH0sCiAgbWV0aG9kczogewogICAgYXN5bmMgZmV0Y2hQYWNrYWdlcygpIHsKICAgICAgY29uc29sZS5sb2coJ1NlbGVjdGVkIE5vZGUgSVA6JywgdGhpcy5zZWxlY3RlZE5vZGVJcCk7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZE5vZGVJcCkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ05vZGUgSVAgaXMgbm90IGRlZmluZWQnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgL2FwaS9wYWNrYWdlcy8ke3RoaXMuc2VsZWN0ZWROb2RlSXB9YCwgewogICAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICAgIGRiRmlsZTogdGhpcy5jdXJyZW50UHJvamVjdCAvLyDmt7vliqAgZGJGaWxlIOWPguaVsAogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIHRoaXMucGFja2FnZXMgPSByZXNwb25zZS5kYXRhOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBhY2thZ2VzOicsIGVycm9yKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["mapState", "axios", "RefreshButton", "components", "data", "packages", "columns", "title", "dataIndex", "key", "pagination", "pageSize", "computed", "watch", "selectedNodeIp", "newIp", "fetchPackages", "mounted", "methods", "console", "log", "error", "response", "get", "params", "dbFile", "currentProject"], "sources": ["src/components/Cards/PackageInfo.vue"], "sourcesContent": ["<template>\r\n  <!-- Packages Table Card -->\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n                <path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\" clip-rule=\"evenodd\"/>\r\n              </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.package') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchPackages\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"packages\"\r\n      :rowKey=\"(record) => record.package_name\"\r\n      :pagination=\"pagination\"\r\n    >\r\n       <template #bodyCell=\"{ column, record }\">\r\n        <template v-if=\"column.key === 'package_name'\">\r\n          <div class=\"table-package-info\">\r\n            <span>{{ record.package_name }}</span>\r\n            <span>{{ record.package_type }}</span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"column.key === 'action'\">\r\n          <a-button type=\"link\" class=\"btn-edit\">Edit</a-button>\r\n        </template>\r\n      </template>\r\n    </a-table>\r\n  </a-card>\r\n  <!-- / Packages Table Card -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      packages: [],\r\n      columns: [\r\n        {\r\n          title: 'Package Name',\r\n          dataIndex: 'package_name',\r\n          key: 'package_name',\r\n        },\r\n        {\r\n          title: 'Package Type',\r\n          dataIndex: 'package_type',\r\n          key: 'package_type',\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      // 当 selectedNodeIp 变化时重新获取包数据\r\n      this.fetchPackages();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchPackages(); // 初始加载时调用\r\n  },\r\n  methods: {\r\n    async fetchPackages() {\r\n      console.log('Selected Node IP:', this.selectedNodeIp);\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/packages/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject // 添加 dbFile 参数\r\n          }\r\n        });\r\n        this.packages = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching packages:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "mappings": "AAkDA,SAAAA,QAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,KAAA;IACA;MACAC,QAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;MACA,GACA;QACAF,KAAA;QACAC,SAAA;QACAC,GAAA;MACA,EACA;MACAC,UAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAZ,QAAA;EACA;EACAa,KAAA;IACAC,eAAAC,KAAA;MACA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,OAAA;IACA,MAAAF,cAAA;MACAG,OAAA,CAAAC,GAAA,2BAAAN,cAAA;MACA,UAAAA,cAAA;QACAK,OAAA,CAAAE,KAAA;QACA;MACA;MACA;QACA,MAAAC,QAAA,SAAArB,KAAA,CAAAsB,GAAA,uBAAAT,cAAA;UACAU,MAAA;YACAC,MAAA,OAAAC,cAAA;UACA;QACA;QACA,KAAArB,QAAA,GAAAiB,QAAA,CAAAlB,IAAA;MACA,SAAAiB,KAAA;QACAF,OAAA,CAAAE,KAAA,6BAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}