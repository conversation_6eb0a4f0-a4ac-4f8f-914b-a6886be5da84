<template>
  <div>
    <!-- Dify Chatbot 配置和脚本 -->
  </div>
</template>

<script>
export default {
  name: 'DifyChatBot',
  mounted() {
    // 添加 Dify 配置
    window.difyChatbotConfig = {
      token: 'GAi2PqkyQkz1L6jm',
      baseUrl: 'http://**************',
      systemVariables: {
        // user_id: 'YOU CAN DEFINE USER ID HERE',
        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',
      },
      userVariables: {
        // avatar_url: 'YOU CAN DEFINE USER AVATAR URL HERE',
        // name: 'YOU CAN DEFINE USER NAME HERE',
      },
      containerProps: {},
      // 启用拖拽功能
      draggable: true,
      // 允许在 x 和 y 轴上拖动
      dragAxis: 'both',
    };

    // 添加 Dify 脚本
    const script = document.createElement('script');
    script.src = 'http://**************/embed.min.js';
    script.id = 'GAi2PqkyQkz1L6jm';
    script.defer = true;
    document.head.appendChild(script);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      #dify-chatbot-bubble-button {
        background-color: #1C64F2 !important;
      }
      #dify-chatbot-bubble-window {
        width: 32rem !important;
        height: 48rem !important;
      }
    `;
    document.head.appendChild(style);
  }
}
</script>

<style scoped>
/* 组件样式 */
</style>
