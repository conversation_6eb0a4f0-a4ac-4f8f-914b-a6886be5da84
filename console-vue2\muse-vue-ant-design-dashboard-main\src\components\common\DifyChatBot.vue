<template>
  <div>
    <!-- Dify Chatbot -->
    <!-- 当Dify服务器无法访问时，显示一个占位符按钮 -->
    <a-button
        v-if="showPlaceholder"
        class="chat-bubble-button"
        type="primary"
        shape="circle"
        icon="message"
        size="large"
        @click="handlePlaceholderClick"
    />
  </div>
</template>

<script>
export default {
  name: 'DifyChatBot',
  data() {
    return {
      showPlaceholder: true, // 默认显示占位符
      serverReachable: true, // 假设服务器可达
    };
  },
  mounted() {
    // 添加 Dify 配置
    window.difyChatbotConfig = {
      token: 'GAi2PqkyQkz1L6jm',
      baseUrl: 'http://**************',
      systemVariables: {
        // user_id: 'YOU CAN DEFINE USER ID HERE',
        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',
      },
      userVariables: {
        // avatar_url: 'YOU CAN DEFINE USER AVATAR URL HERE',
        // name: 'YOU CAN DEFINE USER NAME HERE',
      },
      containerProps: {},
      // 启用拖拽功能
      draggable: true,
      // 允许在 x 和 y 轴上拖动
      dragAxis: 'both',
    };

    // 添加 Dify 脚本
    const script = document.createElement('script');
    script.src = 'http://**************/embed.min.js';
    script.id = 'GAi2PqkyQkz1L6jm';
    script.defer = true;

    script.onload = () => {
      // 脚本加载成功，Dify会创建自己的按钮，所以隐藏我们的占位符
      this.showPlaceholder = false;
      this.serverReachable = true;
    };

    script.onerror = () => {
      // 脚本加载失败，保持占位符显示，并标记服务器不可达
      this.serverReachable = false;
      console.error('Failed to load Dify script. The server might be unreachable.');
    };

    document.head.appendChild(script);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      #dify-chatbot-bubble-button {
        background-color: #1C64F2 !important;
      }
      #dify-chatbot-bubble-window {
        width: 32rem !important;
        height: 48rem !important;
      }
    `;
    document.head.appendChild(style);
  },
  methods: {
    handlePlaceholderClick() {
      if (!this.serverReachable) {
        this.$notification.error({
          message: '连接失败',
          description: '无法连接到聊天服务器，请检查网络或稍后重试。',
        });
      } else {
        // 如果服务器是可达的，但占位符仍在显示，说明脚本正在加载中
        this.$notification.info({
          message: '请稍候',
          description: '聊天机器人正在加载中...',
        });
      }
    },
  },
}
</script>

<style scoped>
.chat-bubble-button {
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  z-index: 1000;
}
</style>
