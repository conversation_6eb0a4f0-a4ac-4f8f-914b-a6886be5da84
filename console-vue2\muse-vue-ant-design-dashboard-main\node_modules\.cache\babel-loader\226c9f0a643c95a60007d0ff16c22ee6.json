{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\i18n\\locales\\zh-CN.js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\i18n\\locales\\zh-CN.js", "mtime": 1753234253933}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["common", "home", "title", "selectNode", "selectProject", "settings", "notifications", "clearAll", "noNotifications", "language", "configureNodes", "configureProxy", "detectReachableIps", "taskProgress", "refresh", "darkMode", "lightMode", "selectedNodes", "copiedToClipboard", "copyFailed", "clear", "save", "cancel", "actions", "edit", "copy", "delete", "configuratorset", "sidebarColor", "sidenavType", "navbarFixed", "configurator", "headTop<PERSON>", "process", "package", "hardware", "mount", "port", "docker", "k8s", "fileUpload", "fileDownload", "aiBash", "testcase", "tool", "configureTool", "spiderTool", "generalTool", "uploadToolPackage", "selectToolPackage", "editScript", "start", "editShellScript", "confirmScript", "scriptReady", "localSaveDirectory", "viewResult", "selectReachableIp", "columns", "hostName", "ip", "status", "progress", "result", "errorDetails", "speed", "fileSize", "failed", "sidebar", "taskPanel", "envAwareness", "processInfo", "packageInfo", "hardwareInfo", "filesystemInfo", "portInfo", "dockerInfo", "k8sInfo", "codeInfo", "materialInfo", "securityTool", "fileDown", "llmAutoTesting", "testcaseManagement", "executeCase", "aiTaintAnalysis", "smartOrchestration", "toolPanel", "hostConfig", "cbhConfig", "repositoryConfig", "selectFile", "clickToSelect", "uploadPath", "enterUploadPath", "startUpload", "uploadProgress", "uploadResults", "enterDownloadPath", "startDownload", "downloadProgress", "addHost", "exportSelected", "deleteSelected", "downloadTemplate", "uploadTemplate", "ip<PERSON><PERSON><PERSON>", "sshPort", "loginUser", "loginPassword", "switchRootCmd", "switchRootPwd", "addRepository", "downloadSelected", "selectDownloadPath", "microservice", "repositoryUrl", "branchName", "validation", "invalidUrl", "unsupportedFormat", "missing<PERSON><PERSON><PERSON>", "parseError", "download", "selectPath", "downloading", "starting", "success", "partialSuccess", "cloneError", "repositoryDownload", "total", "pending", "completed", "log", "viewLogs", "currentNode", "noNodeSelected", "selectLevel", "noLogs", "fetchError", "detail", "searchButton", "resetButton", "caseColumn", "number", "name", "level", "similarity", "prepareCondition", "testSteps", "expectedResult", "feature", "smartAnalysis", "startAnalysis", "caseAnalysis", "naturalLanguageQuery", "queryPlaceholder", "inputRequired", "searchFailed", "searchError", "resultsCleared", "topK", "scoreThreshold", "searchResults", "foundResults"], "sources": ["D:/_Projects_python/Tools/console-vue2/muse-vue-ant-design-dashboard-main/src/i18n/locales/zh-CN.js"], "sourcesContent": ["export default {\r\n  common: {\r\n    home: '首页',\r\n    title: '安全测试助手',\r\n    selectNode: '选择节点',\r\n    selectProject: '选择项目',\r\n    settings: '设置',\r\n    notifications: '通知',\r\n    clearAll: '清除全部',\r\n    noNotifications: '暂无通知',\r\n    language: '语言',\r\n    configureNodes: '节点配置',\r\n    configureProxy: '代理配置',\r\n    detectReachableIps: '检测可用IP',\r\n    taskProgress: '任务进度',\r\n    refresh: '刷新',\r\n    darkMode: '夜间模式',\r\n    lightMode: '日间模式',\r\n    selectedNodes: \"已选择 {count} 个节点\",\r\n    copiedToClipboard: '已复制到剪贴板',\r\n    copyFailed: '复制失败',\r\n    clear: '清除',\r\n    save: '保存',\r\n    cancel: '取消',\r\n    actions: '操作',\r\n    edit: '编辑',\r\n    copy: '复制',\r\n    delete: '删除'\r\n  },\r\n  configuratorset:{\r\n    sidebarColor: '侧边栏颜色',\r\n    sidenavType: '侧边栏类型',\r\n    navbarFixed: '导航栏固定',\r\n    configurator: '配置器',\r\n  },\r\n  headTopic:{\r\n    process: '进程列表',\r\n    package: '安装包列表',\r\n    hardware: '硬件列表',\r\n    mount: '挂载列表',\r\n    port: '端口列表',\r\n    docker: '容器列表',\r\n    k8s: '集群列表',\r\n    fileUpload: '文件上传',\r\n    fileDownload: '文件下载',\r\n    aiBash: '命令行',\r\n    testcase: '用例列表',\r\n  },\r\n  tool: {\r\n    configureTool: '工具配置',\r\n    spiderTool: 'SSP工具',\r\n    generalTool: '通用工具',\r\n    uploadToolPackage: '上传工具包 (zip)',\r\n    selectToolPackage: '选择包',\r\n    editScript: '编辑脚本',\r\n    start: '启动',\r\n    editShellScript: '编辑Shell',\r\n    confirmScript: '确认',\r\n    scriptReady: '脚本已就绪',\r\n    localSaveDirectory: '本地保存目录',\r\n    viewResult: '查看结果',\r\n    selectReachableIp: '选择可访问的IP',\r\n    columns: {\r\n      hostName: '主机名',\r\n      ip: 'IP地址',\r\n      status: '状态',\r\n      progress: '进度',\r\n      result: '结果',\r\n      errorDetails: '错误详情',\r\n      speed: '速度',\r\n      fileSize: '文件大小'\r\n    },\r\n    status: {\r\n      failed: '失败'\r\n    }\r\n  },\r\n  sidebar: {\r\n    taskPanel: '任务面板',\r\n    envAwareness: '环境感知',\r\n    processInfo: '进程信息',\r\n    packageInfo: '安装包信息',\r\n    hardwareInfo: '硬件信息',\r\n    filesystemInfo: '文件系统信息',\r\n    portInfo: '端口信息',\r\n    dockerInfo: '容器信息',\r\n    k8sInfo: '集群信息',\r\n    codeInfo: '代码信息',\r\n    materialInfo: '资料信息',\r\n    securityTool: 'AI安全工具',\r\n    fileUpload: '文件上传',\r\n    fileDown: '文件下载',\r\n    aiBash: 'AI Bash',\r\n    llmAutoTesting: 'LLM自动化测试',\r\n    testcaseManagement: '用例管理',\r\n    executeCase: '用例执行',\r\n    aiTaintAnalysis: '智能污点分析',\r\n    smartOrchestration: '预留',\r\n    toolPanel: '工具面板',\r\n    hostConfig: '主机配置',\r\n    cbhConfig: '堡垒机配置',\r\n    repositoryConfig: '代码仓配置',\r\n  },\r\n  fileUpload: {\r\n    selectFile: '选择文件',\r\n    clickToSelect: '选择文件',\r\n    uploadPath: '上传目录路径',\r\n    enterUploadPath: '请输入上传目录',\r\n    startUpload: '开始上传',\r\n    uploadProgress: '上传进度',\r\n    uploadResults: '上传结果'\r\n  },\r\n  fileDownload: {\r\n    enterDownloadPath: '请输入下载路径',\r\n    startDownload: '开始下载',\r\n    downloadProgress: '下载进度',\r\n  },\r\n  hostConfig: {\r\n    title: '主机配置',\r\n    addHost: '添加主机',\r\n    exportSelected: '导出选中',\r\n    deleteSelected: '删除选中',\r\n    downloadTemplate: '下载模板',\r\n    uploadTemplate: '上传模板',\r\n    columns: {\r\n      hostName: '主机名',\r\n      ipAddress: 'IP地址',\r\n      sshPort: 'SSH端口',\r\n      loginUser: '登录用户',\r\n      loginPassword: '登录密码',\r\n      switchRootCmd: 'root切换命令',\r\n      switchRootPwd: 'root切换密码'\r\n    },\r\n  },\r\n  repositoryConfig: {\r\n    title: '代码仓配置',\r\n    addRepository: '添加代码仓',\r\n    exportSelected: '导出选中',\r\n    deleteSelected: '删除选中',\r\n    downloadSelected: '下载选中',\r\n    downloadTemplate: '下载模板',\r\n    uploadTemplate: '上传模板',\r\n    selectDownloadPath: '选择下载路径',\r\n    downloadProgress: '下载进度',\r\n    columns: {\r\n      microservice: '微服务',\r\n      repositoryUrl: '仓库地址',\r\n      branchName: '送检代码分支'\r\n    },\r\n    validation: {\r\n      invalidUrl: '无效的代码仓地址',\r\n      unsupportedFormat: '不支持的代码仓格式',\r\n      missingBranch: '缺少分支名称',\r\n      parseError: '解析失败'\r\n    },\r\n    download: {\r\n      selectPath: '请选择下载路径',\r\n      downloading: '正在下载...',\r\n      starting: '正在启动下载...',\r\n      success: '下载成功',\r\n      failed: '下载失败',\r\n      partialSuccess: '部分下载成功',\r\n      cloneError: 'Git克隆失败'\r\n    }\r\n  },\r\n  repositoryDownload: {\r\n    title: '代码仓下载结果',\r\n    total: '总计',\r\n    success: '成功',\r\n    failed: '失败',\r\n    downloading: '下载中',\r\n    pending: '等待中',\r\n    completed: '已完成',\r\n    progress: '进度'\r\n  },\r\n  log: {\r\n    title: '日志查看器',\r\n    viewLogs: '查看日志',\r\n    currentNode: '节点',\r\n    noNodeSelected: '未选择节点',\r\n    selectLevel: '选择级别',\r\n    refresh: '刷新',\r\n    noLogs: '暂无日志',\r\n    fetchError: '获取日志失败',\r\n  },\r\n  testcase: {\r\n    title: '测试用例',\r\n    detail: '用例详情',\r\n    selectLevel: '选择级别',\r\n    searchButton: '搜索',\r\n    resetButton: '重置',\r\n  },\r\n  caseColumn: {\r\n    number: '用例编号',\r\n    name: '用例名称',\r\n    level: '用例级别',\r\n    similarity: '相似度',\r\n    prepareCondition: '前置条件',\r\n    testSteps: '测试步骤',\r\n    expectedResult: '预期结果',\r\n    feature: '用例特性',\r\n  },\r\n  smartOrchestration: {\r\n    title: '智能编排',\r\n    smartAnalysis: '智能测试用例分析',\r\n    startAnalysis: '开始智能分析',\r\n    caseAnalysis: '用例分析',\r\n    naturalLanguageQuery: '自然语言查询',\r\n    queryPlaceholder: '请输入自然语言查询',\r\n    inputRequired: '请输入查询',\r\n    searchFailed: '搜索失败',\r\n    searchError: '搜索错误',\r\n    resultsCleared: '搜索结果清除',\r\n    topK: 'Top K',\r\n    scoreThreshold: '得分阈值',\r\n    searchResults: '搜索结果',\r\n    foundResults: '找到 {count} 个结果',\r\n  },\r\n};"], "mappings": "AAAA,eAAe;EACbA,MAAM,EAAE;IACNC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE,MAAM;IAClBC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE,MAAM;IACvBC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,MAAM;IACtBC,kBAAkB,EAAE,QAAQ;IAC5BC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,SAAS;IAC5BC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE;EACV,CAAC;EACDC,eAAe,EAAC;IACdC,YAAY,EAAE,OAAO;IACrBC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,OAAO;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,SAAS,EAAC;IACRC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE,MAAM;IACXC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE;IACJC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,OAAO;IACnBC,WAAW,EAAE,MAAM;IACnBC,iBAAiB,EAAE,aAAa;IAChCC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,IAAI;IACXC,eAAe,EAAE,SAAS;IAC1BC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,OAAO;IACpBC,kBAAkB,EAAE,QAAQ;IAC5BC,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,UAAU;IAC7BC,OAAO,EAAE;MACPC,QAAQ,EAAE,KAAK;MACfC,EAAE,EAAE,MAAM;MACVC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,MAAM;MACpBC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;IACZ,CAAC;IACDL,MAAM,EAAE;MACNM,MAAM,EAAE;IACV;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,SAAS,EAAE,MAAM;IACjBC,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,OAAO;IACpBC,YAAY,EAAE,MAAM;IACpBC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,QAAQ;IACtBxC,UAAU,EAAE,MAAM;IAClByC,QAAQ,EAAE,MAAM;IAChBvC,MAAM,EAAE,SAAS;IACjBwC,cAAc,EAAE,UAAU;IAC1BC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,QAAQ;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,OAAO;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDlD,UAAU,EAAE;IACVmD,UAAU,EAAE,MAAM;IAClBC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,QAAQ;IACpBC,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE;EACjB,CAAC;EACDxD,YAAY,EAAE;IACZyD,iBAAiB,EAAE,SAAS;IAC5BC,aAAa,EAAE,MAAM;IACrBC,gBAAgB,EAAE;EACpB,CAAC;EACDZ,UAAU,EAAE;IACVtF,KAAK,EAAE,MAAM;IACbmG,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,MAAM;IACtBC,gBAAgB,EAAE,MAAM;IACxBC,cAAc,EAAE,MAAM;IACtB/C,OAAO,EAAE;MACPC,QAAQ,EAAE,KAAK;MACf+C,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,MAAM;MACrBC,aAAa,EAAE,UAAU;MACzBC,aAAa,EAAE;IACjB;EACF,CAAC;EACDrB,gBAAgB,EAAE;IAChBxF,KAAK,EAAE,OAAO;IACd8G,aAAa,EAAE,OAAO;IACtBV,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,MAAM;IACtBU,gBAAgB,EAAE,MAAM;IACxBT,gBAAgB,EAAE,MAAM;IACxBC,cAAc,EAAE,MAAM;IACtBS,kBAAkB,EAAE,QAAQ;IAC5Bd,gBAAgB,EAAE,MAAM;IACxB1C,OAAO,EAAE;MACPyD,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE;IACd,CAAC;IACDC,UAAU,EAAE;MACVC,UAAU,EAAE,UAAU;MACtBC,iBAAiB,EAAE,WAAW;MAC9BC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRC,UAAU,EAAE,SAAS;MACrBC,WAAW,EAAE,SAAS;MACtBC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,MAAM;MACf5D,MAAM,EAAE,MAAM;MACd6D,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;IACd;EACF,CAAC;EACDC,kBAAkB,EAAE;IAClBhI,KAAK,EAAE,SAAS;IAChBiI,KAAK,EAAE,IAAI;IACXJ,OAAO,EAAE,IAAI;IACb5D,MAAM,EAAE,IAAI;IACZ0D,WAAW,EAAE,KAAK;IAClBO,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBvE,QAAQ,EAAE;EACZ,CAAC;EACDwE,GAAG,EAAE;IACHpI,KAAK,EAAE,OAAO;IACdqI,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,OAAO;IACvBC,WAAW,EAAE,MAAM;IACnB5H,OAAO,EAAE,IAAI;IACb6H,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE;EACd,CAAC;EACDjG,QAAQ,EAAE;IACRzC,KAAK,EAAE,MAAM;IACb2I,MAAM,EAAE,MAAM;IACdH,WAAW,EAAE,MAAM;IACnBI,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE;EACf,CAAC;EACDC,UAAU,EAAE;IACVC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,MAAM;IACxBC,SAAS,EAAE,MAAM;IACjBC,cAAc,EAAE,MAAM;IACtBC,OAAO,EAAE;EACX,CAAC;EACDlE,kBAAkB,EAAE;IAClBpF,KAAK,EAAE,MAAM;IACbuJ,aAAa,EAAE,UAAU;IACzBC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,MAAM;IACpBC,oBAAoB,EAAE,QAAQ;IAC9BC,gBAAgB,EAAE,WAAW;IAC7BC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,QAAQ;IACxBC,IAAI,EAAE,OAAO;IACbC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE,MAAM;IACrBC,YAAY,EAAE;EAChB;AACF,CAAC", "ignoreList": []}]}