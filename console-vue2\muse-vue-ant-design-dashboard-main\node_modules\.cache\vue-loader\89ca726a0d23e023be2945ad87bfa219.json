{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\RepositoryConfig.vue", "mtime": 1753187071945}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RepositoryConfig.vue"], "names": [], "mappings": ";AAk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file": "RepositoryConfig.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <div>\r\n    <a-card :bordered=\"false\" class=\"header-solid repository-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('repositoryConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('repositoryConfig.addRepository') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"download\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"downloadSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"deleteSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                type=\"default\"\r\n                icon=\"download\"\r\n                class=\"nav-style-button\"\r\n                @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                :show-upload-list=\"false\"\r\n                :custom-request=\"handleUpload\"\r\n                accept=\".csv\"\r\n              >\r\n                <a-button\r\n                    type=\"default\"\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('repositoryConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"repositories\"\r\n        :rowKey=\"(record) => record.key\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: repositories.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"col === 'repository_url' && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"col === 'repository_url' && text ? 'cursor: pointer' : ''\"\r\n              @click=\"col === 'repository_url' && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('common.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('common.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyRepository(record)\">\r\n              {{ $t('common.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteRepository(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n\r\n  <!-- 下载结果显示 -->\r\n  <repository-download-results />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport RepositoryDownloadResults from './RepositoryDownloadResults.vue';\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n    RepositoryDownloadResults,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n  },\r\n  data() {\r\n    return {\r\n      repositories: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'microservice_name',\r\n        'repository_url',\r\n        'branch_name',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      downloadTaskId: null,\r\n      downloadPolling: false,\r\n      downloadPollInterval: null,\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.microservice'),\r\n          dataIndex: 'microservice_name',\r\n          scopedSlots: { customRender: 'microservice_name' },\r\n          width: 200,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.repositoryUrl'),\r\n          dataIndex: 'repository_url',\r\n          scopedSlots: { customRender: 'repository_url' },\r\n          width: 300,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.branchName'),\r\n          dataIndex: 'branch_name',\r\n          scopedSlots: { customRender: 'branch_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('common.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchRepositoryConfig();\r\n  },\r\n  methods: {\r\n    copyRepository(record) {\r\n      const newKey = `new-${Date.now()}`;\r\n      const newRecord = {\r\n        ...record,\r\n        key: newKey,\r\n        editable: true,\r\n        isNew: true,\r\n        microservice_name: `${record.microservice_name}_copy`,\r\n      };\r\n      \r\n      this.repositories = [newRecord, ...this.repositories];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    getColumnTitle(col) {\r\n      const titleMap = {\r\n        'microservice_name': this.$t('repositoryConfig.columns.microservice'),\r\n        'repository_url': this.$t('repositoryConfig.columns.repositoryUrl'),\r\n        'branch_name': this.$t('repositoryConfig.columns.branchName'),\r\n      };\r\n      return titleMap[col] || col;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    edit(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target.editable = true;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        delete target.editable;\r\n        this.repositories = newData;\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n\r\n        try {\r\n          this.saving = true;\r\n          const repositoryData = {\r\n            microservice_name: target.microservice_name,\r\n            repository_url: target.repository_url,\r\n            branch_name: target.branch_name,\r\n          };\r\n\r\n          const response = await axios.post('/api/repositories', {\r\n            repositories: [repositoryData]\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          if (response.data.success) {\r\n            const { successful, failed } = response.data.data;\r\n\r\n            if (failed.length > 0) {\r\n              // 显示验证错误\r\n              const errorMsg = failed[0].error;\r\n              this.$message.error(`${this.$t('repositoryConfig.validation.parseError')}: ${errorMsg}`);\r\n              return;\r\n            }\r\n\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success('Repository saved successfully');\r\n          } else {\r\n            this.$message.error(response.data.error || 'Failed to save repository');\r\n          }\r\n        } catch (error) {\r\n          this.$message.error(error.response?.data?.error || 'Failed to save repository');\r\n          await this.fetchRepositoryConfig();\r\n        } finally {\r\n          this.saving = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.repositories.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.repositories[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.repositories = this.repositories.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.repositories];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.repositories = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.repositories = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          microservice_name: '',\r\n          repository_url: '',\r\n          branch_name: 'main',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.repositories,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    async fetchRepositoryConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/repositories`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.repositories = response.data.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `repo_${item.microservice_name}`,\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.repositories.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load repositories');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteRepository(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/repositories/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repository');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async deleteSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to delete');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        if (selectedIds.length > 0) {\r\n          await axios.post('/api/repositories/batch-delete', {\r\n            ids: selectedIds\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n        }\r\n\r\n        this.repositories = this.repositories.filter(r => !this.selectedRowKeys.includes(r.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Selected repositories deleted successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repositories');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    async exportSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to export');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        const response = await axios.post('/api/repositories/export', {\r\n          ids: selectedIds\r\n        }, {\r\n          params: { dbFile: this.currentDbFile },\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repositories_export.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Repositories exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export repositories');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/repositories/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repository_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        const response = await axios.post('/api/repositories/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const { successful, failed } = response.data.data;\r\n\r\n          if (failed.length > 0) {\r\n            // 显示验证失败的代码仓\r\n            const failedMessages = failed.map(repo =>\r\n              `${repo.microservice_name || 'Unknown'}: ${repo.error}`\r\n            ).join('\\n');\r\n\r\n            this.$confirm({\r\n              title: this.$t('repositoryConfig.validation.parseError'),\r\n              content: failedMessages,\r\n              showCancelButton: false,\r\n              confirmButtonText: 'OK',\r\n              type: 'warning'\r\n            });\r\n          }\r\n\r\n          if (successful.length > 0) {\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success(`Successfully imported ${successful.length} repositories`);\r\n          }\r\n\r\n          if (failed.length > 0 && successful.length === 0) {\r\n            this.$message.error('No valid repositories found in the file');\r\n          }\r\n        } else {\r\n          this.$message.error(response.data.error || 'Failed to import repositories');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import repositories');\r\n      }\r\n    },\r\n\r\n    async downloadSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to download');\r\n        return;\r\n      }\r\n\r\n      // 获取选中的代码仓\r\n      const selectedRepositories = this.selectedRowKeys\r\n        .map(key => this.repositories.find(r => r.key === key))\r\n        .filter(repo => repo && repo.id);\r\n\r\n      if (selectedRepositories.length === 0) {\r\n        this.$message.warning('No valid repositories selected');\r\n        return;\r\n      }\r\n\r\n      // 弹出输入框让用户输入下载路径\r\n      const downloadPath = prompt(this.$t('repositoryConfig.download.selectPath'), 'D:\\\\downloads');\r\n\r\n      if (!downloadPath || !downloadPath.trim()) {\r\n        return;\r\n      }\r\n\r\n      // 显示开始下载的消息\r\n      this.$message.loading(this.$t('repositoryConfig.download.starting'), 0);\r\n\r\n      try {\r\n        const requestData = {\r\n          repositories: selectedRepositories.map(repo => ({\r\n            id: repo.id,\r\n            microservice_name: repo.microservice_name,\r\n            repository_url: repo.repository_url,\r\n            branch_name: repo.branch_name\r\n          })),\r\n          download_path: downloadPath.trim()\r\n        };\r\n\r\n        const response = await axios.post('/api/repositories/download', requestData, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        this.$message.destroy();\r\n\r\n        if (response.data.success) {\r\n          const taskId = response.data.data.task_id;\r\n          this.downloadTaskId = taskId;\r\n\r\n          // 初始化下载结果状态 - 显示正在进行的状态\r\n          const initialResults = {\r\n            task_id: taskId,\r\n            status: 'running',\r\n            successful: [],\r\n            failed: [],\r\n            repositories: selectedRepositories.reduce((acc, repo) => {\r\n              const key = `${repo.microservice_name}_${repo.repository_url}`;\r\n              acc[key] = {\r\n                microservice_name: repo.microservice_name,\r\n                repository_url: repo.repository_url,\r\n                branch_name: repo.branch_name,\r\n                status: 'pending',\r\n                progress: 0,\r\n                error_detail: null,\r\n                download_path: null\r\n              };\r\n              return acc;\r\n            }, {}),\r\n            timestamp: new Date().toLocaleString()\r\n          };\r\n\r\n          // 存储初始状态到store\r\n          this.$store.dispatch('updateRepositoryDownloadResults', initialResults);\r\n\r\n          this.$message.success('Repository download task started successfully');\r\n\r\n          // 开始轮询任务状态\r\n          this.startDownloadPolling(taskId);\r\n        } else {\r\n          this.$message.error(response.data.error || this.$t('repositoryConfig.download.failed'));\r\n        }\r\n      } catch (error) {\r\n        this.$message.destroy();\r\n        this.$message.error(error.response?.data?.error || error.message || this.$t('repositoryConfig.download.failed'));\r\n      }\r\n    },\r\n\r\n    startDownloadPolling(taskId) {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n      }\r\n\r\n      this.downloadPolling = true;\r\n\r\n      // 立即执行一次查询\r\n      this.pollDownloadStatus(taskId);\r\n\r\n      // 每5秒轮询一次\r\n      this.downloadPollInterval = setInterval(() => {\r\n        this.pollDownloadStatus(taskId);\r\n      }, 5000);\r\n\r\n      console.log(`开始轮询代码仓下载任务 ${taskId}，轮询间隔: 5秒`);\r\n    },\r\n\r\n    async pollDownloadStatus(taskId) {\r\n      try {\r\n        const response = await axios.get(`/api/repositories/download/status/${taskId}`, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const taskData = response.data.data;\r\n\r\n          // 更新store中的下载结果\r\n          this.$store.dispatch('updateRepositoryDownloadResults', {\r\n            ...taskData,\r\n            timestamp: new Date().toLocaleString()\r\n          });\r\n\r\n          // 检查任务是否完成\r\n          if (taskData.status === 'success' || taskData.status === 'failed' || taskData.status === 'partial_success') {\r\n            this.stopDownloadPolling();\r\n\r\n            // 显示完成消息\r\n            if (taskData.status === 'success') {\r\n              this.$message.success(this.$t('repositoryConfig.download.success'));\r\n            } else if (taskData.status === 'failed') {\r\n              this.$message.error(this.$t('repositoryConfig.download.failed'));\r\n            } else {\r\n              this.$message.warning(this.$t('repositoryConfig.download.partialSuccess'));\r\n            }\r\n\r\n            console.log('代码仓下载任务完成，停止轮询');\r\n          }\r\n        } else {\r\n          console.error('获取下载状态失败:', response.data.error);\r\n        }\r\n      } catch (error) {\r\n        console.error('轮询下载状态出错:', error);\r\n        \r\n        // 如果是404错误，可能任务不存在，停止轮询\r\n        if (error.response?.status === 404) {\r\n          this.stopDownloadPolling();\r\n          this.$message.error('Download task not found');\r\n        }\r\n      }\r\n    },\r\n\r\n    stopDownloadPolling() {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n        this.downloadPollInterval = null;\r\n      }\r\n      this.downloadPolling = false;\r\n      this.downloadTaskId = null;\r\n    },\r\n\r\n    checkActiveDownloadTask() {\r\n      // 检查是否有活跃的下载任务\r\n      const taskInfo = localStorage.getItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n      if (taskInfo) {\r\n        try {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n          if (projectFile === this.currentDbFile) {\r\n            this.downloadTaskId = taskId;\r\n            this.startDownloadPolling(taskId);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing repository download task info:', e);\r\n          localStorage.removeItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n        }\r\n      }\r\n    },\r\n\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRepositoryConfig();\r\n    // 检查是否有活跃的下载任务\r\n    this.checkActiveDownloadTask();\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 组件销毁前停止轮询\r\n    this.stopDownloadPolling();\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.repository-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}