{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue?vue&type=template&id=229ff433&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TaskPanel.vue", "mtime": 1753175269721}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}